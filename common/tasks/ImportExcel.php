<?php

namespace common\tasks;

use import\models\ImportCsv;
use import\services\ImportService;
use ReflectionException;
use Throwable;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Exception;

class ImportExcel extends TaskHandler
{
    public $action;
    public $sourceId;

    public function rules(): array
    {
        return [
            [['action'], 'default', 'value' => 'import'],
            [['action', 'sourceId'], 'required'],
            [['sourceId'], 'integer'],
        ];
    }

    /**
     * @return TaskResult
     * @throws Exception
     * @throws InvalidConfigException
     * @throws ReflectionException
     * @throws Throwable
     * @throws UserException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function process(): TaskResult
    {
        $csv = $this->find($this->sourceId);

        $config = ImportCsv::config();

        $importService = new ImportService((string)$csv->download(), (array)($config[$csv->type] ?? []));

        $importResponse = $importService->process($this->action);

        $csv->success_line = $importResponse->getSuccessLine();
        $csv->status       = ImportCsv::SUCCESS_STATUS;

        if (!$csv->save(false)) {
            throw new UserException(json_encode($csv->getErrors()));
        }

        return TaskResult::success($importResponse->getSuccessLine());
    }

    /**
     * @param int $id
     *
     * @return ImportCsv
     * @throws UserException
     */
    protected function find(int $id): ImportCsv
    {
        $csv = ImportCsv::findOne($id);
        if (!$csv) {
            throw new UserException('数据不存在!');
        }

        return $csv;
    }
}
