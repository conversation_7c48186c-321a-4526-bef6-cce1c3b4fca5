<?php

use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use yii\base\Event;
use yii\base\InvalidConfigException;
use yii\db\Connection;
use yii\di\Instance;

$root = dirname(__DIR__, 2);

Yii::setAlias('@common', dirname(__DIR__));
Yii::set<PERSON>lia<PERSON>('@backend', $root . '/backend');
Yii::setAlias('@console', $root . '/console');

// modules
Yii::setAlias('@system', $root . '/modules/system');
Yii::setAlias('@finance', $root . '/modules/finance');
Yii::setAlias('@api', $root . '/modules/api');
Yii::set<PERSON>lia<PERSON>('@grant', $root . '/modules/grant');
Yii::setAlias('@payment', $root . '/modules/payment');
Yii::setAlias('@pcrawler', $root . '/modules/pcrawler');
Yii::setAlias('@repay', $root . '/modules/repay');
Yii::set<PERSON>lias('@dashboard', $root . '/modules/dashboard');
Yii::set<PERSON>lia<PERSON>('@crm', $root . '/modules/crm');
Yii::setAlias('@EventManager', $root . '/modules/EventManager');
Yii::setAlias('@kuainiu', $root . '/vendor/kuainiu/yii2-kuainiu');

Carbon::macro('toSystemDateTimeString', function ($self = null) {
    /** @var Carbon $this */
    if (isset($self) && !$self instanceof Carbon) {
        $self = Carbon::parse($self);
    } elseif (isset($this)) {
        $self = $this;
    }

    return $self->format('Y-m-d H:i:sP');
});

Carbon::macro('withSystemTimezone', function ($tz = null) {
    /** @var Carbon $this */
    return $this->setTimezone($tz ?? Yii::$app->getTimeZone());
});

Event::on(Connection::class, Connection::EVENT_AFTER_OPEN, function (Event $event) {
    /** @var Connection $db */
    $db = $event->sender;

    $timeZone = CarbonTimeZone::create(Yii::$app->timeZone)->toOffsetName();
    $db->createCommand('set time_zone = :tz', ['tz' => $timeZone])->execute();
});

/**
 * @param $name
 *
 * @return Connection
 * @throws InvalidConfigException
 */
function instanceEnsureDb($name): Connection
{
    return Instance::ensure($name, Connection::class);
}
