<?php

use backend\behaviors\FormatterBehavior;
use cmdb\components\CmdbComponent;
use Codingheping\StatementComponent\component\StatementGateComponent;
use common\behaviors\ImportExpressionFunctionProvider;
use common\caching\PredisCache;
use common\event\OssListener;
use datasource\components\DataSourceComponent;
use datasource\components\NotifyComponent;
use finance\components\OaHttpComponent;
use finance\components\RiskControlHttpComponent;
use finance\models\BizPartner;
use finance\procedures\BiAudit;
use finance\procedures\OfflineTradeRecord;
use finance\procedures\PaymentAutoWithdraw;
use finance\procedures\PaymentTransfer;
use finance\procedures\PayrollPayment;
use finance\procedures\TransferRecord;
use grant\components\GrantHttpComponent;
use grant\components\RouterHttpComponent;
use import\behaviors\ImportDataFormatterBehavior;
use import\models\BizOssFile;
use import\models\ImportCsv;
use kvmanager\components\NacosComponent;
use kvmanager\models\KeyValue;
use mdm\admin\components\DbManager;
use payment\models\MemberCard;
use repay\components\RepayHttpComponent;
use system\components\AliOssComponent;
use system\components\CapitalHttpComponent;
use system\components\DcsHttpComponent;
use system\components\ExpressionComponent;
use system\components\FrontendAliOssComponent;
use system\components\GateComponent;
use system\components\MessageQueueComponent;
use system\components\PaymentHttpComponent;
use system\components\PcrawlerComponent;
use waterank\audit\provider\AuditProvider;
use waterank\audit\provider\AvoidProvider;
use xlerr\common\common\lib\proxy\CapitalProxy;
use Xlerr\CostManagement\Model\DataReportFinanceGeneralCost;
use Xlerr\CostManagement\Model\DataReportFinanceIncomeStatement;
use Xlerr\CostManagement\Model\DataReportFinancePaymentFee;
use xlerr\desensitise\Desensitise;
use xlerr\desensitise\FormatterBehavior as DesensitiseFormatter;
use xlerr\jasypt\Jasypt;
use xlerr\jasypt\Module;
use Xlerr\SettlementFlow\Models\Account;
use Xlerr\SettlementFlow\Models\Adjust;
use Xlerr\SettlementFlow\Models\Order;
use yii\base\Event;
use yii\base\InvalidConfigException;
use yii\db\BaseActiveRecord;
use yii\db\Connection;
use yii\di\Instance;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\UploadedFile;

use function externalComponentConfig as ecc;
use function waterank\audit\config;

$injectCpopIncomeMetricFunction = static function () {
    if (!function_exists('dbInstance')) {
        function dbInstance(): Connection
        {
            return Yii::$app->get('rbizDb');
        }
    }

    if (!function_exists('adminChannelList')) {
        function adminChannelList($channel): array
        {
            return BizPartner::find()
                ->where(['admin_channel' => $channel])
                ->orWhere(['channel' => $channel])
                ->select('channel')
                ->indexBy('channel')
                ->column();
        }
    }
};

return [
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
    ],
    'vendorPath' => dirname(__DIR__, 2) . '/vendor',
    'bootstrap' => ['dataplatform', 'metric', 'cpop-income', 'cpop-settlement', 'cost-management'],
    'modules' => [
        'dataplatform' => \datasource\Module::class,
        'cost-management' => [
            'class' => \Xlerr\CostManagement\Module::class,
            'on init' => static function () {
                $config = KeyValue::take('cost_management_config');
                $paymentFee = $config['payment_fee'] ?? [];
                $incomeStatement = $config['income_statement'] ?? [];
                $financeGeneralCost = $config['finance_general_cost'] ?? [];
                $formatter = Yii::$app->formatter;
                DataReportFinancePaymentFee::setDb('rbizDb');
                DataReportFinancePaymentFee::setThreshold($paymentFee['threshold'] ?? 0);
                DataReportFinancePaymentFee::setIsEnglishRemarkValid(false);
                DataReportFinancePaymentFee::setViewColumns([
                    [
                        'attribute' => 'merchant',
                        'label' => '商户主体'
                    ],
                    [
                        'attribute' => 'subject',
                        'label' => '我方主体'
                    ],
                    [
                        'attribute' => 'type',
                        'label' => '成本类型'
                    ],
                    [
                        'attribute' => 'channel_name',
                        'label' => '通道名称'
                    ],
                    [
                        'attribute' => 'data_month',
                        'label' => '上报月份',
                    ],
                    [
                        'label' => '生成时间',
                        'value' => function ($model) {
                            if ($model['id']) {
                                return DataReportFinancePaymentFee::findOne($model['id'])->generation_at;
                            }
                            return '-';
                        },
                    ],
                    [
                        'attribute' => 'lastReportDate',
                        'label' => '上次上报时间',
                        'value' => function ($model) {
                            return $model['lastReportDate'] ?? '';
                        },
                    ],
                    [
                        'attribute' => 'initAmount',
                        'label' => '本次上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['initAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }
                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #e6f7ff;'], // 设置背景色
                    ],
                    [
                        'attribute' => 'lastAmount',
                        'label' => '已上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['lastAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }
                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #fffbe6;']
                    ],
                    [
                        'attribute' => 'diff',
                        'label' => '差异额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['diff'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }
                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #ffcccc;']
                    ],
                    [
                        'label' => '差异率',
                        'value' => function ($model) {
                            return $model['differenceRate'] . '%';
                        },
                        'contentOptions' => function ($model) {
                            $threshold = DataReportFinancePaymentFee::$threshold;
                            if ($model['differenceRate'] > (float)$threshold) {
                                return ['style' => 'background-color: #ff4c4c; color: #ffffff;'];
                            }
                            return ['style' => 'background-color: #d4edda;'];
                        }
                    ],
                    [
                        'label' => '备注',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['id']) {
                                $inputId = 'remark-input-' . $model['id'];
                                $saveButtonId = 'save-button-' . $model['id'];
                                $cancelButtonId = 'cancel-button-' . $model['id'];
                                $text = DataReportFinancePaymentFee::findOne($model['id'])->memo;
                                $text = !empty($text) ? $text : Yii::t('common', 'Difference Rate:{percentage}%', ['percentage' => $model['differenceRate']], 'en');
                                return Html::tag('span', $text, [
                                        'id' => 'remark-display-' . $model['id'],
                                        'class' => 'remark-display',
                                    ]) .
                                    Html::a('<i class="fa fa-pencil"></i>', '#', [
                                        'class' => 'show-input',
                                        'data-id' => $model['id'],
                                        'style' => 'margin-left: 5px;',
                                    ]) .
                                    Html::textInput('remark', $text, [
                                        'id' => $inputId,
                                        'class' => 'remark-input',
                                        'style' => 'display:none; margin-right: 5px;',
                                        'data-id' => $model['id'],
                                    ]) .
                                    Html::button('保存', [
                                        'id' => $saveButtonId,
                                        'class' => 'save-button btn btn-success btn-sm',
                                        'style' => 'display:none; margin-right: 5px;',
                                        'data-id' => $model['id'],
                                    ]) .
                                    Html::button('取消', [
                                        'id' => $cancelButtonId,
                                        'class' => 'cancel-button btn btn-secondary btn-sm',
                                        'style' => 'display:none;',
                                        'data-id' => $model['id'],
                                    ]);
                            }
                            return '';
                        }
                    ],
                ]);
                DataReportFinancePaymentFee::setAuditColumns([
                    [
                        'attribute' => 'merchant',
                        'label' => '商户主体'
                    ],
                    [
                        'attribute' => 'subject',
                        'label' => '我方主体'
                    ],
                    [
                        'attribute' => 'type',
                        'label' => '成本类型'
                    ],
                    [
                        'attribute' => 'channel_name',
                        'label' => '通道名称'
                    ],
                    [
                        'attribute' => 'data_month',
                        'label' => '上报月份',
                    ],
                    [
                        'attribute' => 'initAmount',
                        'label' => '本次上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['initAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }
                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #e6f7ff;'], // 设置背景色
                    ],
                    [
                        'attribute' => 'lastAmount',
                        'label' => '已上报金额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['lastAmount'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }
                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #fffbe6;']
                    ],
                    [
                        'attribute' => 'diff',
                        'label' => '差异额',
                        'value' => function ($model) use ($formatter) {
                            $amount = $model['diff'];
                            if ($amount !== null) {
                                return $formatter->format($amount, ['f2y', true]);
                            }
                            return '无';
                        },
                        'contentOptions' => ['style' => 'background-color: #ffcccc;']
                    ],
                    [
                        'label' => '差异率',
                        'value' => function ($model) {
                            return $model['differenceRate'] . '%';
                        },
                        'contentOptions' => function ($model) {
                            $threshold = DataReportFinancePaymentFee::$threshold;
                            if ($model['differenceRate'] > (float)$threshold) {
                                return ['style' => 'background-color: #ff4c4c; color: #ffffff;'];
                            }
                            return ['style' => 'background-color: #d4edda;'];
                        }
                    ],
                    [
                        'label' => '备注',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['id']) {
                                $text = DataReportFinancePaymentFee::findOne($model['id'])->memo;
                                $text = !empty($text) ? $text : Yii::t('common', 'Difference Rate:{percentage}%', ['percentage' => $model['differenceRate']], 'en');
                                return Html::tag('span', $text, [
                                    'id' => 'remark-display-' . $model['id'],
                                    'class' => 'remark-display',
                                ]);
                            }
                            return '';
                        }
                    ],
                ]);
                DataReportFinancePaymentFee::setDiffSql($paymentFee['sql'], $paymentFee['params'], [
                    'data_month' => 'data_month',
                    'type' => 'type',
                    'subject' => 'subject',
                    'merchant' => 'merchant',
                    'beginDiffRate' => 'differenceRate',
                    'endDiffRate' => 'differenceRate',
                ]);
                DataReportFinanceIncomeStatement::setDb('rbizDb');
                DataReportFinanceIncomeStatement::setIsEnglishRemarkValid(false);
                DataReportFinanceIncomeStatement::setThreshold($incomeStatement['threshold'] ?? 0);
                DataReportFinanceIncomeStatement::setDiffSql($incomeStatement['sql'], $incomeStatement['params']);
                DataReportFinanceIncomeStatement::setSystem('Finance');

                DataReportFinanceGeneralCost::setDb('rbizDb');
                DataReportFinanceGeneralCost::setIsEnglishRemarkValid(true);
                DataReportFinanceGeneralCost::setThreshold($financeGeneralCost['threshold'] ?? 0);
                DataReportFinanceGeneralCost::setDiffSql($financeGeneralCost['sql'], $financeGeneralCost['params']);
                DataReportFinanceGeneralCost::setSystem('Finance_general_cost');
            }
        ],
        'jasypt' => Module::class,
        'webtask' => \Codingheping\WebTask\Module::class,
        'cpop-settlement' => [
            'class' => \Xlerr\SettlementFlow\Module::class,
            'injectionFunctions' => static function () use ($injectCpopIncomeMetricFunction) {
                $injectCpopIncomeMetricFunction();

                function settleCallback(Order $order)
                {
                    Adjust::updateAll([
                        'status' => Adjust::STATUS_ACTIVE,
                    ], [
                        'settlement_order_id' => $order->id,
                        'status' => Adjust::STATUS_NEW,
                    ]);
                }

                function cpop_settlement_accounts(): array
                {
                    $withdrawChannels = KeyValue::take('withdraw_channel_list');
                    $withdrawChannels = array_flip($withdrawChannels);
                    foreach ($withdrawChannels as $channel => &$option) {
                        $option = [
                            'type' => 'withdraw_channel',
                            'name' => $channel,
                            'channel' => $channel,
                        ];
                    }

                    return array_merge(
                        MemberCard::cpopSettlementAccounts(),
                        $withdrawChannels,
                        Account::innerAll(),
                    );
                }

                function lackBankFlow(): array
                {
                    return [];
                }

                function lackPaymentVoucher(): array
                {
                    return [];
                }

                function capitalGrantAt(string $channel): string
                {
                    return '2024-01-01';
                }

                function attachmentFilepath(string $filename): string
                {
                    $country = Yii::$app->params['country'] ?? '';
                    if (empty($country)) {
                        throw new InvalidConfigException('缺少country配置');
                    }

                    return "portal-$country/cpop-order/$filename";
                }

                function uploadAttachment(UploadedFile $file, string $filename): string
                {
                    $client = GateComponent::instance();
                    $client->upload($filename, $file->tempName);

                    $client->signedUrl($client->getData(), '30 minutes');

                    return $client->getData();
                }

                function deleteAttachment(string $key): void
                {
                    // kos 不支持删除文件
                    // 忽略删除文件操作
                }

                function urlSign(string $url): string
                {
                    $client = GateComponent::instance();
                    $client->signedUrl($url, '30 minutes');

                    return $client->getData();
                }
            },
        ],
        'metric' => [
            'class' => \Xlerr\Metric\Module::class,
            'injectionFunctions' => $injectCpopIncomeMetricFunction,
        ],
        'cpop-income' => [
            'class' => \Xlerr\CpopIncome\Module::class,
            'injectionFunctions' => $injectCpopIncomeMetricFunction,
        ],
        'import' => [
            'class' => \import\Module::class,
            'on init' => static function () {
                ImportCsv::$config = fn(): array => KeyValue::takeAsArray('import_template');

                ExpressionComponent::instance()->registerProvider(new ImportExpressionFunctionProvider());

                //事件handler
                $importHandler = static function (Event $event): void {
                    $listener = new OssListener();
                    $listener->process($event);
                };

                //创建导入task
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_AFTER_INSERT, $importHandler);
                //重新导入
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_AFTER_UPDATE, $importHandler);
                //上传文件
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_BEFORE_INSERT, $importHandler);
                //删除
                Event::on(BizOssFile::class, BaseActiveRecord::EVENT_AFTER_DELETE, $importHandler);
                //下载
                Event::on(BizOssFile::class, BizOssFile::EVENT_DOWNLOAD, $importHandler);
            },
        ],
    ],
    'components' => [
        TransferRecord::key() => TransferRecord::class,
        BiAudit::key() => BiAudit::class,
        PayrollPayment::key() => PayrollPayment::class,
        PaymentAutoWithdraw::key() => PaymentAutoWithdraw::class,
        PaymentTransfer::key() => PaymentTransfer::class,
        OfflineTradeRecord::key() => OfflineTradeRecord::class,
        'authManager' => [
            'class' => DbManager::class,
        ],
        'cache' => [
            'class' => PredisCache::class,
        ],
        'formatter' => [
            'as CustomFormatter' => FormatterBehavior::class,
            'as DesensitiseFormatter' => DesensitiseFormatter::class,
            'as ImportDataFormatter' => ImportDataFormatterBehavior::class,
        ],
        'audit' => static fn() => new AuditProvider(config('oauth')),
        'audit_weidu' => static fn() => new AuditProvider(config('oauth_weidu')),
        'audit_local' => static fn() => Yii::$app->get('audit_' . Yii::$app->params['country']),
        'audit_mex' => static fn() => new AuditProvider(config('oauth_mex')),
        'audit_tha' => static fn() => new AuditProvider(config('oauth_tha')),
        'audit_pak' => static fn() => new AuditProvider(config('oauth_pak')),
        'audit_idn' => static fn() => new AuditProvider(config('oauth_idn')),
        'audit_phl' => static fn() => new AuditProvider(config('oauth_phl')),
        'audit_sssper' => static fn() => new AuditProvider(config('audit_sssper')),
        'avoid_audit' => static fn() => new AvoidProvider(config('avoid')),
        ExpressionComponent::class => ExpressionComponent::class,
        Desensitise::class => static fn() => new Desensitise(ecc('desensitise')),
        MessageQueueComponent::class => static fn() => new MessageQueueComponent(ecc('queue')),
        CapitalHttpComponent::class => static fn() => new CapitalHttpComponent(ecc('capital')),
        DcsHttpComponent::class => static fn() => new DcsHttpComponent(ecc('dcs')),
        PaymentHttpComponent::class => static fn() => new PaymentHttpComponent(ecc('payment')),
        PcrawlerComponent::class => static fn() => new PcrawlerComponent(ecc('pcrawler')),
        CmdbComponent::class => static fn() => new CmdbComponent(ecc('cmdb')),
        OaHttpComponent::class => static fn() => new OaHttpComponent(ecc('oa')),
        RepayHttpComponent::class => static fn() => new RepayHttpComponent(ecc('repay')),
        GrantHttpComponent::class => static fn() => new GrantHttpComponent(ecc('grant')),
        StatementGateComponent::class => static fn() => new StatementGateComponent(ecc('statement')),
        RouterHttpComponent::class => static fn() => new RouterHttpComponent(ecc('router')),
        RiskControlHttpComponent::class => static fn() => Instance::ensure(
            KeyValue::take('risk_control_config')
        ),
        DataSourceComponent::class => static fn() => new DataSourceComponent(ecc('main_portal')),
        NotifyComponent::class => static fn() => new NotifyComponent(ecc('msgsvr')),
        NacosComponent::class => static fn() => new NacosComponent([
            'baseUri' => ArrayHelper::getValue(KeyValue::take(NacosComponent::CONFIG_KEY), 'baseUri'),
            'username' => $_SERVER['BIZ_NACOS_CONFIG_USERNAME'] ?? '',
            'password' => $_SERVER['BIZ_NACOS_CONFIG_PASSWORD'] ?? '',
        ]),
        Jasypt::class => static function () {
            $binConfig = KeyValue::take('bin_config');

            return new Jasypt($binConfig['java'] ?? null, null, [
                'password' => $_SERVER['JASYPT_PASSWD'] ?? '123456',
            ]);
        },
        AliOssComponent::class => ossClientCreator(AliOssComponent::class, 'oss_config'),
        FrontendAliOssComponent::class => ossClientCreator(
            FrontendAliOssComponent::class,
            'frontend_oss_config'
        ),
        GateComponent::class => static fn() => new  GateComponent(ecc('filegate')),
        CapitalProxy::class => static fn() => new CapitalProxy(ecc('capital')),
    ],
];
