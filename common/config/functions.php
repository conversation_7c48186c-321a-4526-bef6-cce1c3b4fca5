<?php

use backend\behaviors\FormatterBehavior;
use finance\models\BizPartner;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use OSS\OssClient;

/**
 * @param string $system
 *
 * @return array
 * @throws KVException
 */
function externalComponentConfig(string $system): array
{
    $config = KeyValue::takeAsArray('external_system_base_urls');

    return (array)($config[$system] ?? []) + ['baseUri' => null];
}

/**
 * @param string|array $url
 *
 * @return string
 * @throws KVException
 */
function buildSelfAccessUrl($url = ''): string
{
    $config = externalComponentConfig('self');

    if (!is_string($url)) {
        $url = vsprintf('%s%s', [
            array_shift($url),
            empty($url) ? '' : '?' . http_build_query($url),
        ]);
    }

    return vsprintf('%s/%s', [
        rtrim((string)($config['baseUri'] ?? ''), '/'),
        ltrim($url, '/'),
    ]);
}

/**
 * @param class-string<OssClient> $ossClientClassName
 * @param string $configKey
 *
 * @return callable
 */
function ossClientCreator(string $ossClientClassName, string $configKey): callable
{
    return static function () use ($ossClientClassName, $configKey): OssClient {
        $config = KeyValue::take($configKey);
        $accessKeyId = $config['accessKeyId'] ?? null;
        $accessKeySecret = $config['accessKeySecret'] ?? null;
        $endpoint = $config['endpoint'] ?? null;
        $isCName = $config['isCName'] ?? false;
        $securityToken = $config['securityToken'] ?? null;
        $requestProxy = $config['requestProxy'] ?? null;

        return new $ossClientClassName(
            $accessKeyId,
            $accessKeySecret,
            $endpoint,
            $isCName,
            $securityToken,
            $requestProxy
        );
    };
}

if (!function_exists('capitalChannelName')) {
    function capitalChannelName(string $code): string
    {
        return $code;
    }
}
if (!function_exists('capitalChannelList')) {
    function capitalChannelList(bool $active = false): array
    {
        return BizPartner::find()
            ->select('channel')
            ->indexBy('channel')
            ->orderBy(['id' => SORT_DESC])
            ->column();
    }
}

if (!function_exists('formatAmount')) {
    /**
     * @param int|float $amount
     * @param bool $format
     *
     * @return float|string
     * @throws Exception
     */
    function formatAmount($amount, bool $format = true)
    {
        /** @var FormatterBehavior $formatter */
        $formatter = Yii::$app->formatter;

        return $formatter->asFormatAmount($amount, $format);
    }
}

if (!function_exists('convertFenToKiloYuan')) {
    /**
     * @param $amount
     * @param bool $format
     *
     * @return string
     */
    function convertFenToKiloYuan($amount, bool $format = true): string
    {
        $formatter = Yii::$app->formatter;
        $amount = $formatter->format($amount, ['formatAmount', false]);
        $amount = round($amount / 1000, 2);
        if ($format) {
            $amount = number_format($amount, 2);
        }

        return $amount;
    }
}