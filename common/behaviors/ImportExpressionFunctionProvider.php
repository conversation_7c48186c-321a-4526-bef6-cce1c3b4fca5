<?php

namespace common\behaviors;

use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionFunctionProviderInterface;

class ImportExpressionFunctionProvider implements ExpressionFunctionProviderInterface
{

    /**
     * @return array
     */
    public static function methods(): array
    {
        return [
        ];
    }

    /**
     * @return array|ExpressionFunction[]
     */
    public function getFunctions(): array
    {
        $methods = [];
        foreach (self::methods() as $name => $method) {
            $methods[] = new ExpressionFunction($name, function () use ($method) {
                return vsprintf('\%s::%s(%s)', [
                    $method[0],
                    $method[1],
                    implode(', ', func_get_args()),
                ]);
            }, function () use ($method) {
                return call_user_func_array($method, array_slice(func_get_args(), 1));
            });
        }

        return $methods;
    }
}