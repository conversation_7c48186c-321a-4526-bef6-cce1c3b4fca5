<?php

namespace common\components;

use GuzzleHttp\RequestOptions;
use Xlerr\ApplicationPayment\Interfaces\OaComponentInterface;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class OaComponent extends RequestClient implements OaComponentInterface
{
    use ComponentTrait;

    public const FROM_SYSTEM = 'BIZ';

    /**
     * @param array $data
     *
     * @return bool
     * @see https://git.kuainiujinke.com/b/capital-docs/-/wikis/apis/OA%E7%B3%BB%E7%BB%9F/BIZ-API-%E5%8F%91%E8%B5%B7%E4%BB%98%E6%AC%BE%E7%94%B3%E8%AF%B7
     */
    public function createEntries(array $data): bool
    {
        return $this->post('api/entries/apiCreate', [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * 通过邮箱获取员工基本信息
     *
     * @param string $email
     *
     * @return bool
     */
    public function employeeInfoByEmail(string $email): bool
    {
        return $this->get('api/employee/basic-employee-by-email/' . $email);
    }
}
