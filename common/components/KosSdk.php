<?php

namespace common\components;

use Exception;
use system\components\GateComponent;
use xlerr\httpca\ComponentTrait;
use yii\base\InvalidConfigException;

/**
 * Class QCloudSdk
 *
 * @package common\components
 */
class KosSdk
{
    use ComponentTrait;

    public string $bucket;

    /**
     * QCloudSdk constructor.
     *
     * @param array $config
     *
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->bucket = $config['bucket'] ?? '';
    }

    /**
     * 获取云资源的链接
     *
     * @param string $expired
     * @param string $path
     *
     * @return string
     * @throws InvalidConfigException
     */
    public static function getCloudUrl(string $expired, string $path): string
    {
        return GateComponent::instance()->signedUrl($path, $expired);
    }
}
