<?php

namespace common\components;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Yii;
use yii\base\BaseObject;
use yii\base\InvalidConfigException;
use yii\data\ActiveDataProvider;
use yii\db\Query;

class ExcelComponent extends BaseObject
{
    public $source;
    public array $columns = [];
    public $key;
    public $formatter;
    public bool $showFooter = false;
    public string $emptyCell = '';
    public string $writer = IOFactory::WRITER_XLS;

    public const PHPOFFICE_WRITER_MAP = [
        IOFactory::WRITER_XLS => Xls::class,
        IOFactory::WRITER_XLSX => Xlsx::class,
    ];

    public static function build($source, array $columns = [], string $writer = IOFactory::WRITER_XLS, callable $getter = null): string
    {
        if (null === $getter) {
            $getter = self::getter($source);
        }

        $self = Yii::createObject([
            'class' => self::class,
            'source' => $source,
            'columns' => $columns,
            'writer' => $writer,
        ]);

        return $self->render($getter);
    }

    public static function getter(&$source): \Closure
    {
        if (is_array($source)) {
            return function () use ($source) {
                foreach ($source as $row) {
                    yield $row;
                }
            };
        }

        if ($source instanceof ActiveDataProvider) {
            $sort = $source->getSort();
            $source = $source->query;
            if ($sort !== false) {
                $source->addOrderBy($sort->getOrders());
            }
        }

        if ($source instanceof Query) {
            return function () use ($source) {
                foreach ($source->each(200) as $row) {
                    yield $row;
                }
            };
        }

        throw new InvalidConfigException('Unknown source type.');
    }

    public function init()
    {
        parent::init();
        $this->initFormatter();
        $this->initColumns();
    }

    protected function initFormatter()
    {
        if ($this->formatter === null) {
            $this->formatter = Yii::$app->formatter;
        } elseif (is_array($this->formatter)) {
            $this->formatter = Yii::createObject($this->formatter);
        }
    }

    public function render(callable $method): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $columnIndex = 1;
        $columnBinder = [];

        foreach ($this->columns as $i => $column) {
            if (!empty($column['cellValueBinder'])) {
                $IBinder = new $column['cellValueBinder']();
                $columnBinder[$i] = $IBinder;
            }
            $sheet->setCellValue([$columnIndex, 1], $column['label'] ?? $column['attribute']);
            $columnIndex++;
        }

        $rowIndex = 2;

        foreach ($method($this->source) as $dataRow) {
            $columnIndex = 1;
            foreach ($this->columns as $index => $column) {
                $value = $this->getDataCellValue($dataRow, $column);
                $IBinder = $columnBinder[$index] ?? null;
                $sheet->setCellValue([$columnIndex, $rowIndex], $value, $IBinder);
                $columnIndex++;
            }
            $rowIndex++;
        }

        $writerClass = self::PHPOFFICE_WRITER_MAP[$this->writer];
        $writer = new $writerClass($spreadsheet);
        $filePath = tempnam(sys_get_temp_dir(), 'excel') . '.' . strtolower($this->writer);
        $writer->save($filePath);

        return $filePath;
    }

    protected function getDataCellValue($dataRow, $column)
    {
        if (isset($column['value']) && is_callable($column['value'])) {
            return call_user_func($column['value'], $dataRow);
        } elseif (isset($column['attribute'])) {
            return $dataRow[$column['attribute']] ?? $this->emptyCell;
        }

        return $this->emptyCell;
    }

    protected function initColumns()
    {
        foreach ($this->columns as &$column) {
            if (is_string($column)) {
                $column = $this->createDataColumn($column);
            }
        }
        unset($column);
    }

    protected function createDataColumn($text): array
    {
        if (!preg_match('/^([^:]+)(:(\w*))?(:(.*))?$/', $text, $matches)) {
            throw new InvalidConfigException('The column format is incorrect. Use "attribute", "attribute:format" or "attribute:format:label"');
        }

        return [
            'attribute' => $matches[1],
            'format' => $matches[3] ?? 'text',
            'label' => $matches[5] ?? null,
        ];
    }
}
