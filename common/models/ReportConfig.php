<?php

namespace common\models;

use Carbon\Carbon;
use Exception;

class ReportConfig
{
    public bool $changeLog = true;
    public ?string $archiveTimeline = null;
    protected string $channel;
    protected string $category;
    protected string $subset;
    protected string $date;
    public array $reportTypes;

    public function __construct(array $config)
    {
        $this->changeLog = (bool)($config['changeLog'] ?? true);
        $this->channel = $config['channel'];
        $this->category = $config['category'] ?? '';
        $this->subset = $config['subset'] ?? '';
        $this->date = $config['date'];
        if (isset($config['archiveTimeline'])) {
            $this->archiveTimeline = Carbon::parse($config['archiveTimeline'])->toDateString();
        }
        $this->reportTypes = $this->createReportType($config['mapping']);
    }

    /**
     * @param array $mapping
     *
     * @return ReportType[]
     * @throws Exception
     */
    protected function createReportType(array $mapping): array
    {
        $types = [];
        foreach ($mapping as $type => $prop) {
            $rules = preg_split('/\s*:\s*/', $type);
            $typeId = array_shift($rules);

            $types[] = new ReportType($typeId, $prop, $rules);
        }

        return $types;
    }

    public function channel(array $row): string
    {
        return $row[$this->channel];
    }

    public function category(array $row): string
    {
        if ($this->category === '') {
            return '';
        }

        return $row[$this->category] ?? '';
    }

    public function subset(array $row): string
    {
        if ($this->subset === '') {
            return '';
        }

        return $row[$this->subset] ?? '';
    }

    public function date(array $row): string
    {
        return $row[$this->date];
    }
}
