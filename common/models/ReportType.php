<?php

namespace common\models;

use Exception;
use Xlerr\Metric\Models\BizMetricInfo;

class ReportType
{
    public string $id;
    public string $prop;
    public array $rules;

    /**
     * @param string $id
     * @param string $prop
     * @param array $rules
     *
     * @throws Exception
     */
    public function __construct(string $id, string $prop, array $rules = [])
    {
        $isExists = BizMetricInfo::find()
            ->where([
                'name' => $id,
                'status' => BizMetricInfo::STATUS_NORMAL
            ])
            ->exists();
        if (!$isExists) {
            throw new Exception(sprintf('标准指标[%s]已被废弃或不存在!', $id));
        }

        $this->id = $id;
        $this->prop = $prop;
        $this->rules = $rules;
    }
}
