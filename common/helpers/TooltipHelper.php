<?php

namespace common\helpers;

use finance\models\IndicatorExplanation;
use Yii;
use yii\helpers\Html;

class TooltipHelper
{
    /**
     * 生成带 tooltip 的 label
     * @param string $label
     * @param array $iconOptions 图标 HTML 属性
     * @return string
     */
    public static function labelWithTooltip(string $label, array $iconOptions = []): string
    {
        $path = Yii::$app->getRequest()->getPathInfo();
        $tooltip = IndicatorExplanation::getTooltip($path, $label);

        if (empty($tooltip)) {
            return $label;
        }

        $icon = Html::tag('i', '', array_merge([
            'class' => 'glyphicon glyphicon-question-sign',
            'data-toggle' => 'tooltip',
            'title' => $tooltip,
            'style' => 'cursor: help; margin-left:4px; color:#999;'
        ], $iconOptions));
        return $label . ' ' . $icon;
    }

}
