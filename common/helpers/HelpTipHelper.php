<?php

namespace common\helpers;

use yii\helpers\Html;

class HelpTipHelper
{
    /**
     * 创建问号提示HTML
     * 
     * @param string $tipText 提示文本内容
     * @param string $icon 问号图标文本，默认为 "?"
     * @param array $options HTML选项
     * @return string
     */
    public static function create(string $tipText, string $icon = '?', array $options = []): string
    {
        $options = array_merge([
            'class' => 'help-tip',
            'data-tip' => $tipText,
            'title' => '', // 清除默认title，避免冲突
        ], $options);
        
        return Html::tag('span', $icon, $options);
    }
    
    /**
     * 为GridView列标题创建带问号提示的标题
     *
     * @param string $label 列标题
     * @param string $tipText 提示文本内容
     * @param string $icon 问号图标文本，默认为 "?"
     * @return string
     */
    public static function createForColumn(string $label, string $tipText, string $icon = '?'): string
    {
        return $label . ' ' . self::create($tipText, $icon);
    }

    /**
     * 从IndicatorExplanation模型获取提示内容并创建问号提示
     *
     * @param string $label 列标题
     * @param string $indicatorName 指标名称
     * @param string $pagePath 页面路径，默认为当前页面
     * @param string $tableName 表格名称，默认为空
     * @param string $icon 问号图标文本，默认为 "?"
     * @return string
     */
    public static function createFromDb(string $label, string $indicatorName, string $pagePath = null, string $tableName = '', string $icon = '?'): string
    {
        if ($pagePath === null) {
            $pagePath = \Yii::$app->request->pathInfo;
        }

        $tipText = self::getTipFromDatabase($indicatorName, $pagePath, $tableName);

        if (empty($tipText)) {
            return $label; // 如果没有找到提示内容，只返回标题
        }

        return self::createForColumn($label, $tipText, $icon);
    }

    /**
     * 从IndicatorExplanation表获取提示内容
     *
     * @param string $indicatorName 指标名称
     * @param string $pagePath 页面路径
     * @param string $tableName 表格名称
     * @return string
     */
    private static function getTipFromDatabase(string $indicatorName, string $pagePath, string $tableName): string
    {
        try {
            // 使用缓存提高性能
            $cacheKey = 'help_tip_' . md5($pagePath . '_' . $tableName . '_' . $indicatorName);
            $result = \Yii::$app->cache->get($cacheKey);

            if ($result === false) {
                // 查询IndicatorExplanation表
                $query = \Yii::$app->db->createCommand(
                    'SELECT explanation FROM indicator_explanation WHERE indicator_name = :indicator AND page_path = :path'
                );
                $query->bindValue(':indicator', $indicatorName);
                $query->bindValue(':path', $pagePath);

                // 如果指定了表格名称，添加到查询条件
                if (!empty($tableName)) {
                    $query = \Yii::$app->db->createCommand(
                        'SELECT explanation FROM indicator_explanation WHERE indicator_name = :indicator AND page_path = :path AND table_name = :table'
                    );
                    $query->bindValue(':indicator', $indicatorName);
                    $query->bindValue(':path', $pagePath);
                    $query->bindValue(':table', $tableName);
                }

                $result = $query->queryScalar();

                // 缓存结果1小时
                \Yii::$app->cache->set($cacheKey, $result ?: '', 3600);
            }

            return $result ?: '';

        } catch (\Exception $e) {
            \Yii::error('获取指标解释失败: ' . $e->getMessage(), __METHOD__);
            return '';
        }
    }
}
