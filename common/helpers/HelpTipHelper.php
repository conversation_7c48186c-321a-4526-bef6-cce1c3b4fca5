<?php

namespace common\helpers;

use yii\helpers\Html;

class HelpTipHelper
{
    /**
     * 创建问号提示HTML
     * 
     * @param string $tipText 提示文本内容
     * @param string $icon 问号图标文本，默认为 "?"
     * @param array $options HTML选项
     * @return string
     */
    public static function create(string $tipText, string $icon = '?', array $options = []): string
    {
        $options = array_merge([
            'class' => 'help-tip',
            'data-tip' => $tipText,
            'title' => '', // 清除默认title，避免冲突
        ], $options);
        
        return Html::tag('span', $icon, $options);
    }
    
    /**
     * 为GridView列标题创建带问号提示的标题
     * 
     * @param string $label 列标题
     * @param string $tipText 提示文本内容
     * @param string $icon 问号图标文本，默认为 "?"
     * @return string
     */
    public static function createForColumn(string $label, string $tipText, string $icon = '?'): string
    {
        return $label . ' ' . self::create($tipText, $icon);
    }
}
