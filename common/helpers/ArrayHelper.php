<?php

namespace common\helpers;

use yii\helpers\Inflector;

class <PERSON>rrayHelper extends \yii\helpers\ArrayHelper
{
    public static function snakeToCamel($data)
    {
        if (!is_array($data)) {
            return $data;
        }
        $camelData = [];
        foreach ($data as $key => $value) {
            $camelKey = Inflector::variablize($key);
            $camelData[$camelKey] = $value;
        }
        return $camelData;
    }

    public static function camelToSnake($data)
    {
        if (!is_array($data)) {
            return $data;
        }
        $snakeData = [];
        foreach ($data as $key => $value) {
            $snakeKey = Inflector::underscore($key);
            $snakeData[$snakeKey] = $value;
        }
        return $snakeData;
    }
}
