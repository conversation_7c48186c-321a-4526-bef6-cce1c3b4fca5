<?php

namespace finance\models;

use yii\data\ActiveDataProvider;

/**
 * AssetReverseSearch represents the model behind the search form about `AssetReverse`.
 */
class IndicatorExplanationSearch extends IndicatorExplanation
{
    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['page_path', 'table_name', 'indicator_name'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params): ActiveDataProvider
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['id'],
                'defaultOrder' => ['id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'and',
            ['page_path' => $this->page_path,],
            ['table_name' => $this->table_name,],
            ['like', 'indicator_name', $this->indicator_name]

        ]);

        return $dataProvider;
    }
}
