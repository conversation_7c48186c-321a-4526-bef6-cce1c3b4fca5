<?php

namespace finance\models;

use Carbon\Carbon;
use common\models\User;
use waterank\audit\models\Audit;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "biz_partner".
 *
 * @property int $id
 * @property string $channel channel
 * @property int $channel_status channel_status 0：pending order、1：trial order、 2：loan、3: stock asset、4: has dueAt and not settle、5: have already settled
 * @property string|null $grant_start_at grant start at
 * @property string|null $desc desc
 * @property int $create_user create user
 * @property int $update_user update user
 * @property string $create_at create at
 * @property string $update_at update at
 * @property int $company_type company type(1.self 2. without 3.guarantee company 4.technology company)
 */
class BizPartner extends \yii\db\ActiveRecord
{

    /**
     * @var Audit
     */
    public $audit;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'biz_partner';
    }

    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    public const SELF = 1;

    public const WITHOUT = 2;
    public const GUARANTEE_COMPANY = 3;
    public const TECHNOLOGY_COMPANY = 4;
    public const PAYMENT_CHANNEL = 5;

    // 键值对数组
    public const COMPANY_TYPE_LIST = [
        self::SELF => '自营资方',
        self::WITHOUT => '外部资方',
        self::GUARANTEE_COMPANY => '担保公司',
        self::TECHNOLOGY_COMPANY => '科技公司',
        self::PAYMENT_CHANNEL => '支付通道',
    ];

    public const PENDING_ORDER = 0;
    public const TRIAL_ORDER = 1;
    public const LOAN = 2;
    public const STOCK_ASSET = 3;
    public const HAS_DUE_NOT_SETTLED = 4;
    public const SETTLED = 5;

    // 键值对数组，用于描述各状态
    public const STATUS_LIST = [
        self::PENDING_ORDER => '待灰度',
        self::TRIAL_ORDER => '已灰度未放量',
        self::LOAN => '放量中',
        self::STOCK_ASSET => '存量资产',
        self::HAS_DUE_NOT_SETTLED => '已到期未结清',
        self::SETTLED => '已结清',
    ];

    public function behaviors()
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'create_user',
                'updatedByAttribute' => 'update_user',
                'value' => fn($event) => Yii::$app->user->id,
            ],
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'create_at',
                'updatedAtAttribute' => 'update_at',
                'value' => Carbon::now()->toDateTimeString()
            ],
        ];
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['channel', 'channel_status', 'company_type', 'grant_start_at'], 'required'],
            [['channel_status', 'create_user', 'update_user', 'company_type'], 'integer'],
            [['create_at', 'update_at'], 'safe'],
            [['channel'], 'string', 'max' => 64],
            [['desc'], 'string', 'max' => 512],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'channel' => '资金方',
            'channel_status' => '状态',
            'grant_start_at' => '放款开始时间',
            'desc' => '描述',
            'create_user' => '创建人',
            'update_user' => '修改人',
            'create_at' => '创建时间',
            'update_at' => '修改时间',
            'company_type' => '资金方性质',
        ];
    }

    public function getCreateUser(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'create_user']);
    }

    public function getUpdateUser(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'update_user']);
    }
}
