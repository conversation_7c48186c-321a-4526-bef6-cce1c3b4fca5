<?php

namespace finance\models;

use backend\behaviors\FormatterBehavior;
use Carbon\Carbon;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%account_turnover_supplement}}".
 *
 * @property int $id              主键
 * @property int|null $oa_id        OA流程编号
 * @property string $order_no        订单号
 * @property string $payment_channel 支付通道
 * @property string $channel_name 通道名称
 * @property string $account         虚户出户,account表中的identity
 * @property string $real_account    提现到的实户，account表中real_account
 * @property float $amount          金额，单位为分
 * @property string $status          状态：new-新建，process-提现中，success-提现成功
 * @property string $type            类型
 * @property string $comment         备注
 * @property string $finish_at       完成时间
 * @property string $create_user     创建人
 * @property string $create_at       创建时间
 * @property string $update_at       更新时间
 * @property string $currency        币种
 * @property string $order_from_type 交易类型
 */
class AccountTurnoverSupplement extends ActiveRecord
{
    public const MANUAL_ORDER_TYPE = 'manual';
    public const PAYSVR_ORDER_TYPE = 'paysvr';
    public const ORDER_FROM_TYPE = [
        self::MANUAL_ORDER_TYPE => '手动',
        self::PAYSVR_ORDER_TYPE => '支付',
    ];

    public const STATUS_SUCCESS = 'success';

    public static function status(): array
    {
        return [
            'new' => '新建',
            'process' => '提现中',
            self::STATUS_SUCCESS => '交易成功',
        ];
    }

    /**
     * @return array
     */
    public static function withdrawTypes(): array
    {
        static $withdraws;
        if (!$withdraws) {
            $withdraws = Withdraw::find()->distinct()->select('withdraw_type')->indexBy('withdraw_type')->column();
        }
        return $withdraws;
    }

    public static function withholdTypes(): array
    {
        static $withholds;
        if (!$withholds) {
            $withholds = self::find()->distinct()->select('type')->indexBy('type')->column();
        }
        return $withholds;
    }

    public static function getPaymentChannel()
    {
        $config = KeyValue::take('withdraw_channel_account_map');

        $paymentChannelList = array_keys($config);

        return array_combine($paymentChannelList, $paymentChannelList);
    }

    public static function inAccountList()
    {
        $config = KeyValue::take('withdrawal_manual_config');
        $account = [];
        foreach ((array)($config['in_account'] ?? []) as $vAccount => $options) {
            $account[$vAccount] = $options['name'];
        }

        return $account;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%account_turnover_supplement}}';
    }

    public function transactions()
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('dcsDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        /**
         * @var FormatterBehavior $formatter
         */
        $formatter = Yii::$app->getFormatter();

        return [
            [['finish_at'], 'default', 'value' => Carbon::now()->toDateString()],
            [['create_user'], 'default', 'value' => Yii::$app->getUser()->getIdentity()->username],
            [['order_from_type'], 'default', 'value' => self::MANUAL_ORDER_TYPE],
            [['status'], 'default', 'value' => self::STATUS_SUCCESS],
            [['account'], 'default', 'value' => ''],
            [['oa_id'], 'default', 'value' => 0],
            [['type', 'amount', 'channel_name', 'finish_at', 'order_no'], 'required'],
            [
                ['amount'],
                'number',
                'min' => 1,
                'max' => ***********,
                'tooSmall' => vsprintf('金额不能低于%s%s', [
                    $formatter->asFormatAmount(1, true),
                    FormatterBehavior::currencyUnit(),
                ]),
                'tooBig' => vsprintf('金额不能超过%s%s', [
                    $formatter->asFormatAmount(***********, true),
                    FormatterBehavior::currencyUnit(),
                ]),
            ],
            [['finish_at', 'create_at', 'update_at'], 'safe'],
            [['oa_id'], 'integer'],
            [['order_no', 'channel_name'], 'string', 'max' => 64],
            [['payment_channel', 'account', 'real_account'], 'string', 'max' => 128],
            [['status'], 'string', 'max' => 32],
            [['comment', 'create_user'], 'string', 'max' => 100],
            [['currency'], 'string', 'max' => 16],
            [['order_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'oa_id' => 'OA流程编号',
            'order_no' => '订单号',
            'payment_channel' => '通道',
            'channel_name' => '通道名称',
            'account' => '出款虚户',
            'real_account' => '收款账户',
            'amount' => '金额',
            'status' => '状态',
            'type' => '类型',
            'comment' => '备注',
            'finish_at' => '完成时间',
            'create_user' => '创建人',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'currency' => '币种',
            'order_from_type' => '交易类型',
        ];
    }
}
