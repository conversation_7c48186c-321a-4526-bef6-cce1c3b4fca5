<?php

namespace finance\models;

use backend\behaviors\FormatterBehavior;
use Exception;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use RuntimeException;
use waterank\audit\provider\AuditProvider;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

use function waterank\audit\authClient;

class OaWithdraw extends Withdraw
{
    public $pendingTasksId;
    public $pendingOaId;

    public $attachmentUrl;

    public function rules(): array
    {
        return [
            [
                [
                    'withdraw_amount',
                    'withdraw_status',
                    'withdraw_type',
                    'withdraw_channel',
                    'withdraw_receive_account',
                    'withdraw_receive_number',
                    'withdraw_create_user',
                    'withdraw_reason',
                    'withdraw_biz_type',
                ],
                'required',
            ],
            [
                ['attachmentUrl'],
                'filter',
                'filter' => function ($val) {
                    $val = (int)$val;
                    if ($val) {
                        return self::parsePreviewUrl($val, 86400 * 30);
                    }

                    return null;
                },
                'when' => function () {
                    return $this->forceAttachment();
                },
            ],
            [
                ['attachmentUrl'],
                'required',
                'when' => function () {
                    return $this->forceAttachment();
                },
            ],
            [
                ['withdraw_amount'],
                'integer',
                'min' => 1,
                'tooSmall' => vsprintf('金额不能低于%s%s', [
                    Yii::$app->getFormatter()->asFormatAmount(1, true),
                    FormatterBehavior::currencyUnit(),
                ]),
            ],
            [
                [
                    'withdraw_created_at',
                    'withdraw_updated_at',
                    'withdraw_oa_id',
                    'withdraw_merchant_key',
                    'withdraw_receive_uuid',
                    'withdraw_receive_userid',
                    'withdraw_virtual_account',
                    'withdraw_oa_created_at',
                    'withdraw_payment_created_at',
                    'withdraw_payment_info',
                    'trade_no',
                    'withdraw_create_user_email',
                    'withdraw_biz_type',
                    'pendingTasksId',
                    'pendingOaId',
                ],
                'safe',
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return array_merge(parent::attributeLabels(), [
            'attachmentUrl' => '协议文件',
        ]);
    }

    public function forceAttachment(): bool
    {
        $country = ArrayHelper::getValue(Yii::$app->params, 'country');

        return $country === 'tha'
            && $this->withdraw_type === '跨境提现-公司';
    }


    /**
     * @param string|AuditProvider $client
     *
     * @return AuditProvider
     */
    protected static function authClient($client = 'audit'): AuditProvider
    {
        if (is_string($client)) {
            /** @var AuditProvider $authClient */
            $authClient = authClient($client);
        } else {
            $authClient = $client;
        }

        return $authClient;
    }

    /**
     * @param int $oaId
     *
     * @return array|null
     * @throws Exception
     */
    public function applyAttachments(int $oaId): ?array
    {
        if (!$this->forceAttachment()) {
            return null;
        }

        $authClient = self::authClient();

        [, $rawBody] = $authClient->approvalDetailQueryAndCheck($oaId, 0, false);
        $response = Json::decode($rawBody);

        return (array)ArrayHelper::getValue($response, ['data', 0, 'form_data', 'file', 'value']);
    }

    /**
     * @param int $attachmentId
     * @param int $expire
     *
     * @return callable
     * @throws IdentityProviderException
     */
    public static function parsePreviewUrl(int $attachmentId, int $expire = 300): string
    {
        $authClient = self::authClient();

        $response = $authClient->download([
            'id' => $attachmentId,
            'type' => 'url',
            'expiration' => $expire,
        ]);

        if ($response['code'] !== 0) {
            throw new RuntimeException('获取附件失败: ' . $attachmentId);
        }


        return (string)ArrayHelper::getValue($response, 'data.data');
    }
}
