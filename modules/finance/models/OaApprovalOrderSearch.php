<?php

namespace finance\models;

use yii\data\ActiveDataProvider;

/**
 * Class OaApprovalSubscribeMessageSearch
 * @package finance\models
 *
 * <AUTHOR> <<EMAIL>>
 * @date   2025/6/24 10:47
 */
class OaApprovalOrderSearch extends OaApprovalOrder
{
    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'oa_tenant_id',
                    'oa_flow_id',
                    'oa_flow_key',
                    'status'
                ],
                'safe'
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = OaApprovalOrder::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'id' => SORT_DESC,
                ]
            ]
        ]);

        $this->load($params);
        if ($this->status === null) {
            $this->status = [
                OaApprovalOrder::STATUS_INIT,
                OaApprovalOrder::STATUS_PROCESSING,
                OaApprovalOrder::STATUS_HOLD
            ];
        }

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query
            ->andFilterWhere([
                'oa_tenant_id' => $this->oa_tenant_id,
                'oa_flow_id' => $this->oa_flow_id,
                'oa_flow_key' => $this->oa_flow_key,
                'status' => $this->status,
            ]);

        return $dataProvider;
    }
}
