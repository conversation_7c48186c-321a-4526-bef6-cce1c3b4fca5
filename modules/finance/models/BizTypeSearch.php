<?php

namespace finance\models;

use yii\data\ActiveDataProvider;

/**
 * AssetReverseSearch represents the model behind the search form about `AssetReverse`.
 */
class BizTypeSearch extends BizType
{
    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['biz_type_fund_flow', 'biz_type_category', 'biz_type_subtype'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['biz_type_id'],
                'defaultOrder' => ['biz_type_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'and',
            ['biz_type_fund_flow' => $this->biz_type_fund_flow,],
            ['biz_type_category' => $this->biz_type_category,],
            ['like', 'biz_type_subtype', $this->biz_type_subtype]

        ])->orderBy([
            new \yii\db\Expression("CASE WHEN biz_type_scope = 'system' THEN 1 ELSE 0 END"),
        ]);

        return $dataProvider;
    }
}
