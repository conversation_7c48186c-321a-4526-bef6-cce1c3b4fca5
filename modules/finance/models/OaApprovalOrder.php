<?php

namespace finance\models;

use finance\models\oaApprovalDto\OaApprovalDto;
use finance\services\OaService;
use xlerr\common\db\SaveTrait;
use xlerr\common\exceptions\SaveException;
use yii\base\UserException;

/**
 * This is the model class for table "oa_approval_order".
 *
 * @property int $id PK
 * @property int $oa_tenant_id 租户ID
 * @property int $oa_flow_id OA审批ID
 * @property string $oa_flow_key 审批流程类型
 * @property int $status 状态
 * @property int $oa_status OA状态
 * @property string $oa_request OA回调消息
 * @property string $oa_response OA接口查询数据
 * @property string $response_data 处理结果数据
 * @property string $memo 备注
 * @property string|null $finish_at 完成时间
 * @property string $create_at 创建时间
 * @property string $update_at 更新时间
 */
class OaApprovalOrder extends \yii\db\ActiveRecord
{
    use SaveTrait;

    public const STATUS_INIT = 0;
    public const STATUS_PROCESSING = 1;
    public const STATUS_SUCCESS = 2;
    public const STATUS_FAIL = 3;
    public const STATUS_HOLD = 4;
    public const STATUS_TERMINATED = 5;

    public static $statusMap = [
        self::STATUS_INIT       => '待处理',
        self::STATUS_PROCESSING => '处理中',
        self::STATUS_SUCCESS    => '成功',
        self::STATUS_FAIL       => '失败',
        self::STATUS_HOLD       => '待人工处理',
        self::STATUS_TERMINATED => '终止',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'oa_approval_order';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['oa_tenant_id', 'oa_flow_id', 'oa_flow_key', 'status'], 'required'],
            [['id', 'oa_tenant_id', 'oa_flow_id', 'status', 'oa_status'], 'integer'],
            [['oa_request', 'oa_response', 'response_data'], 'string'],
            [['finish_at', 'create_at', 'update_at'], 'safe'],
            [['oa_flow_key'], 'string', 'max' => 64],
            [['memo'], 'string', 'max' => 256],
            [['oa_tenant_id', 'oa_flow_id'], 'unique', 'targetAttribute' => ['oa_tenant_id', 'oa_flow_id']],
            [['id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'            => 'ID',
            'oa_tenant_id'     => '租户ID',
            'oa_flow_id'       => 'OA ID',
            'oa_flow_key'      => '审批流程类型',
            'status'        => '处理状态',
            'oa_status'     => 'OA审批状态',
            'oa_request'   => 'OA请求数据',
            'oa_response'  => 'OA接口查询数据',
            'response_data' => '处理结果数据',
            'memo'          => '备注',
            'finish_at'     => '完成时间',
            'create_at'     => '创建时间',
            'update_at'     => '更新时间',
        ];
    }

    /**
     * @param $tenantId
     * @param $flowId
     * @param $flowKey
     * @param $statusCode
     * @param $oaRequest
     * @return array|\yii\db\ActiveRecord|self
     * @throws SaveException
     */
    public static function addRecord($tenantId, $flowId, $flowKey, $statusCode, $oaRequest): self
    {
        $obj = new self();
        $obj->oa_tenant_id = $tenantId;
        $obj->oa_flow_id   = $flowId;
        $obj->oa_flow_key  = $flowKey;
        $obj->oa_status    = $statusCode;
        $obj->oa_request   = $oaRequest;
        $obj->status       = self::STATUS_INIT;
        $obj->memo         = '';
        $obj->saveOrException();
        return $obj;
    }

    /**
     * @return OaApprovalDto
     * @throws SaveException
     * @throws UserException
     */
    public function getOaApprovalDto()
    {
        $oaApprovalDto = OaService::getOaApprovalDto(
            $this->oa_tenant_id,
            $this->oa_flow_id
        );
        if (!$this->oa_response) {
            $this->oa_response = json_encode($oaApprovalDto->raw, JSON_UNESCAPED_UNICODE);
            $this->saveOrException();
        }
        return $oaApprovalDto;
    }
}
