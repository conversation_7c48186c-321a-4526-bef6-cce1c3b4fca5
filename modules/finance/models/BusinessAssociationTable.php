<?php

namespace finance\models;

/**
 * This is the model class for table "business_association_table".
 *
 * @property int    $id
 * @property int    $main_id       主业务表ID
 * @property int    $side_id       副业务表ID
 * @property string $created_at
 * @property string $update_at
 * @property string $business_type 业务类型
 */
class BusinessAssociationTable extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'business_association_table';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['main_id', 'side_id'], 'integer'],
            [['created_at', 'update_at'], 'safe'],
            [['business_type'], 'string', 'max' => 64],
            [
                ['business_type', 'main_id', 'side_id'],
                'unique',
                'targetAttribute' => ['business_type', 'main_id', 'side_id'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'main_id' => '主业务表ID',
            'side_id' => '副业务表ID',
            'created_at' => 'Created At',
            'update_at' => 'Update At',
            'business_type' => '业务类型',
        ];
    }
}
