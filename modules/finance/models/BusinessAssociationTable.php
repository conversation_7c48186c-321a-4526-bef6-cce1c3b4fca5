<?php

namespace finance\models;

use xlerr\common\db\SaveTrait;
use xlerr\common\exceptions\SaveException;

/**
 * This is the model class for table "business_association_table".
 *
 * @property int    $id
 * @property int    $main_id       主业务表ID
 * @property int    $side_id       副业务表ID
 * @property string $created_at
 * @property string $update_at
 * @property string $business_type 业务类型
 */
class BusinessAssociationTable extends \yii\db\ActiveRecord
{
    use SaveTrait;

    public const BUSINESS_TYPE_OA_APPLY = 'oa_apply'; //OA发起的申请
    public const BUSINESS_TYPE_OA_ORDER = 'oa_order'; //oa发起付款申请生产的订单
    public const BUSINESS_TYPE_OA_OFFLINE_TRADE = 'oa_offline_trade'; //oa发起付款申请生产的线下补录

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'business_association_table';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['main_id', 'side_id'], 'integer'],
            [['created_at', 'update_at'], 'safe'],
            [['business_type'], 'string', 'max' => 64],
            [
                ['business_type', 'main_id', 'side_id'],
                'unique',
                'targetAttribute' => ['business_type', 'main_id', 'side_id'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'main_id' => '主业务表ID',
            'side_id' => '副业务表ID',
            'created_at' => 'Created At',
            'update_at' => 'Update At',
            'business_type' => '业务类型',
        ];
    }

    /**
     * @param $businessType
     * @param $mainId
     * @param $sideId
     * @return BusinessAssociationTable
     * @throws SaveException
     */
    public static function addBusinessAssociationTable($businessType, $mainId, $sideId): BusinessAssociationTable
    {
        $businessAssociationTable = new BusinessAssociationTable();
        $businessAssociationTable->business_type = $businessType;
        $businessAssociationTable->main_id = $mainId;
        $businessAssociationTable->side_id = $sideId;
        $businessAssociationTable->saveOrException();
        return $businessAssociationTable;
    }
}
