<?php

namespace finance\models;

use Carbon\Carbon;
use grant\models\AssetBorrower;
use grant\models\AssetLoanRecord;
use kvmanager\models\KeyValue;
use repay\models\AccountRepayLog;
use repay\models\AssetDelay;
use repay\models\AssetExtend;
use repay\models\WithholdDetail;
use repay\models\WithholdDetailHis;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;
use payment\models\Account as PaymentAccount;

/**
 * This is the model class for table "{{%asset}}".
 *
 * @property int $asset_id
 * @property string $asset_item_no
 * @property string $asset_type paydayloan
 * @property string $asset_sub_type
 * @property string $asset_period_type
 * @property int $asset_period_count
 * @property string $asset_product_category
 * @property string $asset_cmdb_product_number cmdb rate no
 * @property string $asset_grant_at
 * @property string $asset_effect_at Contract Signing Date
 * @property string $asset_actual_grant_at
 * @property string $asset_due_at
 * @property string $asset_payoff_at
 * @property string $asset_from_system
 * @property string $asset_status
 * @property int $asset_credit_amount
 * @property int $asset_principal_amount Contract principal
 * @property int $asset_granted_principal_amount Actual amount received
 * @property int $asset_decrease_principal_amount
 * @property string $asset_loan_channel
 * @property string $asset_alias_name
 * @property int $asset_interest_amount
 * @property int $asset_decrease_interest_amount
 * @property int $asset_fee_amount
 * @property int $asset_tax_amount
 * @property int $asset_decrease_fee_amount
 * @property int $asset_decrease_tax_amount
 * @property int $asset_balance_amount
 * @property int $asset_repaid_amount
 * @property int $asset_total_amount
 * @property float $asset_interest_rate annualized interest rate
 * @property string $asset_create_at
 * @property string $asset_rbiz_create_at
 * @property string $asset_update_at
 * @property string $asset_rbiz_update_at
 * @property string $asset_last_sync_time
 * @property int|null $asset_channel_id
 * @property string $asset_from_system_name
 * @property string $asset_owner
 * @property string|null $asset_actual_payoff_at
 * @property int|null $asset_late_amount
 * @property string|null $asset_due_bill_no
 * @property int|null $asset_repaid_principal_amount
 * @property int|null $asset_repaid_interest_amount
 * @property int|null $asset_repaid_fee_amount
 * @property int $asset_repaid_tax_amount
 * @property int|null $asset_repaid_late_amount
 * @property int|null $asset_decrease_late_amount
 * @property string|null $asset_from_app
 * @property string $asset_repayment_app
 * @property string $asset_withdraw_channel Asset Withdraw Channel for Disbursement
 * @property-read \repay\models\Asset $repayAsset
 * @property-read WithholdDetail[] $withholdDetail
 * @property-read WithholdDetailHis[] $withholdDetailHis
 * @property-read AssetDelay[] $assetDelays
 * @property-read AssetBorrower $borrower
 * @property-read AssetTran[] $repayPlans
 * @property-read AccountRepay[] $repayDetails
 * @property-read AssetLoanRecord[] $loanRecords
 */
class Asset extends ActiveRecord
{
    const  STATUS_SIGN = 'sign';
    const  STATUS_SALE = 'sale';
    const  STATUS_REPAY = 'repay';
    const  STATUS_PAYOFF = 'payoff';
    const  STATUS_VOID = 'void';
    const  STATUS_WRITEOFF = 'writeoff';
    const  STATUS_LATE = 'late';
    const  STATUS_LATEOFF = 'lateoff';

    public $withdraw_record_order_no;
    public $withdraw_record_channel;

    /**
     * @return array
     */
    public static function channelList()
    {
        return \grant\models\Asset::channelList();
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset}}';
    }

    /**
     * @inheritDoc
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    public static function statusList()
    {
        return \grant\models\Asset::statusList();
    }

    public static function periodList()
    {
        $result = [];
        $periodInfo = array_keys(KeyValue::take('period_category'));
        foreach ($periodInfo as $period) {
            $result[$period] = $period . '期';
        }

        return $result;
    }

    public static function productCategoryList()
    {
        return [
            '7' => '7天',
            '14' => '14天',
            '30' => '30天',
            '3' => '3个月',
            '6' => '6个月',
        ];
    }

    /**
     * {@inheritdoc}
     */
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['asset_period_type', 'asset_status'], 'string'],
            [
                [
                    'asset_period_count',
                    'asset_credit_amount',
                    'asset_principal_amount',
                    'asset_granted_principal_amount',
                    'asset_decrease_principal_amount',
                    'asset_interest_amount',
                    'asset_decrease_interest_amount',
                    'asset_fee_amount',
                    'asset_tax_amount',
                    'asset_decrease_fee_amount',
                    'asset_decrease_tax_amount',
                    'asset_balance_amount',
                    'asset_repaid_amount',
                    'asset_total_amount',
                    'asset_channel_id',
                    'asset_late_amount',
                    'asset_repaid_principal_amount',
                    'asset_repaid_interest_amount',
                    'asset_repaid_fee_amount',
                    'asset_repaid_tax_amount',
                    'asset_repaid_late_amount',
                    'asset_decrease_late_amount',
                ],
                'integer',
            ],
            [
                [
                    'asset_grant_at',
                    'asset_effect_at',
                    'asset_actual_grant_at',
                    'asset_due_at',
                    'asset_payoff_at',
                    'asset_create_at',
                    'asset_rbiz_create_at',
                    'asset_update_at',
                    'asset_rbiz_update_at',
                    'asset_last_sync_time',
                    'asset_actual_payoff_at',
                ],
                'safe',
            ],
            [['asset_interest_rate'], 'number'],
            [['asset_item_no', 'asset_cmdb_product_number'], 'string', 'max' => 48],
            [['asset_type', 'asset_sub_type', 'asset_loan_channel', 'asset_withdraw_channel'], 'string', 'max' => 32],
            [['asset_product_category'], 'string', 'max' => 16],
            [['asset_from_system', 'asset_from_system_name', 'asset_owner'], 'string', 'max' => 24],
            [['asset_alias_name'], 'string', 'max' => 64],
            [['asset_due_bill_no'], 'string', 'max' => 50],
            [['asset_from_app'], 'string', 'max' => 30],
            [['asset_repayment_app'], 'string', 'max' => 10],
            [['asset_item_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_id' => Yii::t('finance', '主键'),
            'asset_item_no' => Yii::t('finance', '资产编号'),
            'asset_type' => Yii::t('finance', '资产类型'),
            'asset_sub_type' => Yii::t('finance', '子类型'),
            'asset_period_type' => Yii::t('finance', '还款周期类型'),
            'asset_period_count' => Yii::t('finance', '还款总期数'),
            'asset_product_category' => Yii::t('finance', '资产类别'),
            'asset_cmdb_product_number' => Yii::t('finance', '费率编号'),
            'asset_grant_at' => Yii::t('finance', '预计放款时间'),
            'asset_effect_at' => Yii::t('finance', '合同生效日'),
            'asset_actual_grant_at' => Yii::t('finance', '实际放款时间'),
            'asset_due_at' => Yii::t('finance', '资产到期日'),
            'asset_payoff_at' => Yii::t('finance', '结清时间'),
            'asset_from_system' => Yii::t('finance', '所属系统'),
            'asset_status' => Yii::t('finance', '资产状态'),
            'asset_credit_amount' => '授信金额',
            'asset_principal_amount' => Yii::t('finance', '借款本金'),
            'asset_granted_principal_amount' => Yii::t('finance', '实际放款金额'),
            'asset_decrease_principal_amount' => Yii::t('finance', '本金减免'),
            'asset_loan_channel' => Yii::t('finance', '资金方'),
            'asset_alias_name' => 'Asset Alias Name',
            'asset_interest_amount' => Yii::t('finance', '利息'),
            'asset_decrease_interest_amount' => Yii::t('finance', '利息减免'),
            'asset_fee_amount' => Yii::t('finance', '费用'),
            'asset_tax_amount' => '税费',
            'asset_decrease_fee_amount' => Yii::t('finance', '费用减免'),
            'asset_decrease_tax_amount' => '税费减免',
            'asset_balance_amount' => Yii::t('finance', '剩余未偿还总金额'),
            'asset_repaid_amount' => Yii::t('finance', '已偿还总金额'),
            'asset_total_amount' => Yii::t('finance', '本息费总额'),
            'asset_interest_rate' => Yii::t('finance', '利率'),
            'asset_create_at' => '创建时间',
            'asset_rbiz_create_at' => 'Rbiz创建时间',
            'asset_update_at' => '更新时间',
            'asset_rbiz_update_at' => 'Rbiz更新时间',
            'asset_last_sync_time' => '最后同步时间',
            'asset_channel_id' => 'Channel-ID',
            'asset_from_system_name' => Yii::t('finance', '资产来源'),
            'asset_owner' => '资产所有者',
            'asset_actual_payoff_at' => Yii::t('finance', '实际结清时间'),
            'asset_late_amount' => Yii::t('finance', '逾期费'),
            'asset_due_bill_no' => Yii::t('finance', '资方合同编号'),
            'asset_repaid_principal_amount' => Yii::t('finance', '已还本金'),
            'asset_repaid_interest_amount' => Yii::t('finance', '已还利息'),
            'asset_repaid_fee_amount' => Yii::t('finance', '已还费用'),
            'asset_repaid_tax_amount' => '已还税费',
            'asset_repaid_late_amount' => Yii::t('finance', '已还逾期费'),
            'asset_decrease_late_amount' => Yii::t('finance', '逾期费减免'),
            'asset_from_app' => Yii::t('finance', '来源包'),
            'asset_repayment_app' => Yii::t('finance', '还款包'),
            'asset_withdraw_channel' => '提现通道',
        ];
    }


    public function getBorrower(): ActiveQuery
    {
        return $this->hasOne(AssetBorrower::class, ['asset_borrower_item_no' => 'asset_item_no']);
    }

    public function getLoanRecords(): ActiveQuery
    {
        return $this->hasMany(AssetLoanRecord::class, ['asset_loan_record_asset_item_no' => 'asset_item_no']);
    }

    public function getRepayAsset(): ActiveQuery
    {
        return $this->hasOne(\repay\models\Asset::class, ['asset_item_no' => 'asset_item_no']);
    }

    public function getAssetDelays(): ActiveQuery
    {
        return $this->hasMany(AssetDelay::class, ['asset_delay_item_no' => 'asset_item_no']);
    }

    /**
     * 还款计划
     *
     * @return ActiveQuery
     */
    public function getRepayPlans()
    {
        return $this->hasMany(AssetTran::class, ['asset_tran_asset_item_no' => 'asset_item_no']);
    }

    public function getRepayDetails()
    {
        return $this->hasMany(\repay\models\AccountRepay::class, ['account_repay_order_no' => 'asset_item_no']);
    }

    public function getRepayLog()
    {
        return $this->hasMany(AccountRepayLog::class, ['account_repay_log_order_no' => 'asset_item_no']);
    }

    public function getWithholdDetail()
    {
        return $this->hasMany(WithholdDetail::class, ['withhold_detail_asset_item_no' => 'asset_item_no']);
    }

    public function getWithholdDetailHis()
    {
        return $this->hasMany(WithholdDetailHis::class, ['withhold_detail_asset_item_no' => 'asset_item_no']);
    }

    public function getActualPayoffAt(): string
    {
        return $this->asset_actual_payoff_at ?? '-';
    }

    public function getGrantAssetLoanRecord(string $assetItemNo): ActiveQuery
    {
        return AssetLoanRecord::find()->where([
            'asset_loan_record_asset_item_no' => $assetItemNo,
        ]);
    }

    public function getChangeAssetGrantAtRecord(string $assetItemNo): ActiveQuery
    {
        return OperateAssetGrantAtLog::find()->where([
            'asset_item_no' => $assetItemNo,
        ]);
    }

    public function fields()
    {
        $aliasFields = [
            'id' => 'asset_id',
            "item_no" => 'asset_item_no',
            "type" => 'asset_type',
            "sub_type" => 'asset_sub_type',
            "period_type" => 'asset_period_type',
            "period_count" => 'asset_period_count',
            "product_category" => 'asset_product_category',
            "cmdb_product_number" => 'asset_cmdb_product_number',
            "grant_at" => 'asset_grant_at',
            "effect_at" => 'asset_effect_at',
            "actual_grant_at" => 'asset_actual_grant_at',
            "due_at" => 'asset_due_at',
            "payoff_at" => 'asset_payoff_at',
            "from_system" => 'asset_from_system',
            "status" => 'asset_status',
            "principal_amount" => 'asset_principal_amount',
            "granted_principal_amount" => 'asset_granted_principal_amount',
            "decrease_principal_amount" => 'asset_decrease_principal_amount',
            "loan_channel" => 'asset_loan_channel',
            "interest_amount" => 'asset_interest_amount',
            "decrease_interest_amount" => 'asset_decrease_interest_amount',
            "fee_amount" => 'asset_fee_amount',
            "decrease_fee_amount" => 'asset_decrease_fee_amount',
            "tax_amount" => 'asset_tax_amount',
            "decrease_tax_amount" => 'asset_decrease_tax_amount',
            "balance_amount" => 'asset_balance_amount',
            "repaid_amount" => 'asset_repaid_amount',
            "total_amount" => 'asset_total_amount',
            "interest_rate" => 'asset_interest_rate',
            "create_at" => 'asset_create_at',
            "update_at" => 'asset_update_at',
            "from_system_name" => 'asset_from_system_name',
            "actual_payoff_at" => 'asset_actual_payoff_at',
            "late_amount" => 'asset_late_amount',
            "due_bill_no" => 'asset_due_bill_no',
            "repaid_principal_amount" => 'asset_repaid_principal_amount',
            "repaid_interest_amount" => 'asset_repaid_interest_amount',
            "repaid_fee_amount" => 'asset_repaid_fee_amount',
            "repaid_tax_amount" => 'asset_repaid_tax_amount',
            "repaid_late_amount" => 'asset_repaid_late_amount',
            "decrease_late_amount" => 'asset_decrease_late_amount',
            "from_app" => 'asset_from_app',
            "repayment_app" => 'asset_repayment_app',
            "asset_owner" => 'asset_owner',
        ];

        foreach ($aliasFields as $key => $value) {
            $aliasFields[$key] = function ($model) use ($value) {
                return $model->{$value};
            };
        }

        $aliasFields['version'] = function () {
            return round(microtime(true) * 1000);
        };
        $aliasFields['charge_type'] = function ($model) {
            return $this->getAssetExtendOne($model, 'charge_type');
        };
        $aliasFields['withholding_amount'] = function ($model) {
            return $this->getAssetExtendOne($model, 'withholding_amount', 0);
        };
        $aliasFields['ref_order_type'] = function ($model) {
            return $this->getAssetExtendOne($model, 'ref_order_type');
        };
        $aliasFields['ref_order_no'] = function ($model) {
            return $this->getAssetExtendOne($model, 'ref_order_no');
        };

        return $aliasFields;
    }

    protected function getAssetExtendOne($model, $filed, $defaultValue = null)
    {
        $assetExtend = AssetExtend::find()->where([
            'asset_extend_asset_item_no' => $model->asset_item_no,
        ])->indexBy('asset_extend_type')->column();

        return $assetExtend[$filed] ?? $defaultValue;
    }

    public function getAssetExtend(): ActiveQuery
    {
        return $this->hasMany(AssetExtend::class, ['asset_extend_asset_item_no' => 'asset_item_no']);
    }


    public function extraFields(): array
    {
        $fields = [
            'create_at',
            'update_at',
            'payoff_at',
            'due_at',
            'actual_grant_at',
            'effect_at',
            'grant_at',
            'actual_payoff_at',
        ];

        return array_fill_keys($fields, function (Asset $asset, string $attr): string {
            $attr = 'asset_' . $attr;

            return Carbon::toSystemDateTimeString($asset->$attr);
        });
    }
}
