<?php

namespace finance\models;

use payment\models\ChannelOfflineTrade;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

use function waterank\audit\config;

/**
 * This is the model class for table "payment_withdrawal_pending_tasks".
 *
 * @property int $id
 * @property int $tenant_id                租户
 * @property int $oa_id                财务系统记录的OA流程单号
 * @property string $oa_create_at         oa流程的创建时间
 * @property int $payment_amount       付款金额
 * @property string $currency             货币单位
 * @property string $payment_from         出款方
 * @property string $payee                收款方
 * @property string $payee_account_number 收款账户
 * @property string $payee_account_name   收款账户名称
 * @property string $payee_bank           收款银行
 * @property string $fund_direction       资金去向：用于区分出金方向；用于筛选判断是否为通道出金向，通道出金对应-提现-公司、确认收入、保证金退回、提现-个人、支付运营款
 * @property int $status               处理状态:三种状态:0:待处理，1:处理中,2:已处理
 * @property string $created_at
 * @property string $updated_at           更新时间
 * @property int $successAmount
 * @property Withdraw[] $withdraw
 * @property AccountTurnoverSupplement[] $accountTurnoverSupplement
 * @property ChannelOfflineTrade[] $channelOfflineTrade
 */
class PaymentWithdrawalPendingTasks extends ActiveRecord
{
    public const BUSINESS_TYPE_OA_WITHDRAW = 'OaWithdraw';
    public const BUSINESS_TYPE_ACCOUNT_TURNOVER_SUPPLEMENT = 'AccountTurnoverSupplement';
    public const BUSINESS_TYPE_CHANNEL_OFFLINE_TRADE = 'ChannelOfflineTrade';

    public const STATUS_TODO = 0;
    public const STATUS_ING = 1;
    public const STATUS_SUCCESS = 2;

    public const STATUS_TEXT = [
        self::STATUS_TODO => '待处理',
        self::STATUS_ING => '处理中',
        self::STATUS_SUCCESS => '已处理',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'payment_withdrawal_pending_tasks';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['tenant_id', 'oa_id', 'payment_amount', 'oa_create_at'], 'required'],
            [['tenant_id', 'oa_id', 'payment_amount', 'status'], 'integer'],
            [['oa_create_at', 'created_at', 'updated_at'], 'safe'],
            [['currency', 'fund_direction'], 'string', 'max' => 32],
            [['payment_from', 'payee_bank'], 'string', 'max' => 256],
            [['payee', 'payee_account_number', 'payee_account_name'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'tenant_id' => '租户',
            'oa_id' => 'OA流程单号',
            'oa_create_at' => 'OA创建时间',
            'payment_amount' => '付款金额',
            'currency' => '货币单位',
            'payment_from' => '出款方',
            'payee' => '收款方',
            'payee_account_number' => '收款账户',
            'payee_account_name' => '收款账户名称',
            'payee_bank' => '收款银行',
            'fund_direction' => '资金去向',
            'status' => '处理状态',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    private $_successAmount;

    public function getSuccessAmount(): int
    {
        if ($this->_successAmount === null) {
            $withdrawAmount = (int)$this->getWithdraw()
                ->andWhere(['withdraw_status' => Withdraw::STATUS_SUCCESS])
                ->sum('withdraw_amount');
            $turnoverAmount = (int)$this->getChannelOfflineTrade()
//                ->andWhere(['status' => AccountTurnoverSupplement::STATUS_SUCCESS])
                ->sum('channel_offline_trade_amount');

            $this->_successAmount = $withdrawAmount + $turnoverAmount;
        }

        return $this->_successAmount;
    }

    public function getWithdraw(): ActiveQuery
    {
        return $this->hasMany(Withdraw::class, ['withdraw_id' => 'side_id',])
            ->via('businessAssociationTable', function (ActiveQuery $query) {
                $query->onCondition(['business_type' => self::BUSINESS_TYPE_OA_WITHDRAW]);
            });
    }

    public function getAccountTurnoverSupplement(): ActiveQuery
    {
        return $this->hasMany(AccountTurnoverSupplement::class, ['id' => 'side_id',])
            ->via('businessAssociationTable', function (ActiveQuery $query) {
                $query->onCondition(['business_type' => self::BUSINESS_TYPE_ACCOUNT_TURNOVER_SUPPLEMENT]);
            });
    }

    public function getChannelOfflineTrade(): ActiveQuery
    {
        return $this->hasMany(ChannelOfflineTrade::class, ['channel_offline_trade_id' => 'side_id',])
            ->via('businessAssociationTable', function (ActiveQuery $query) {
                $query->onCondition(['business_type' => self::BUSINESS_TYPE_CHANNEL_OFFLINE_TRADE]);
            });
    }

    /**
     * @return ActiveQuery
     */
    public function getBusinessAssociationTable(): ActiveQuery
    {
        return $this->hasMany(BusinessAssociationTable::class, ['main_id' => 'id']);
    }

    /**
     * @return bool
     */
    public static function isSubmitToCreate()
    {
        return config('paymentPendingTasks.submit_to_create', false);
    }

    /**
     * @return array<string, string[]>
     */
    public static function matchRules(): array
    {
        $matchRules = (array)config('paymentPendingTasks.matchRules');

        $rules = [];
        foreach ($matchRules as $key => $rule) {
            $key = sprintf('%s:%s', $rule['tenantId'], $rule['flowKey']);
            $rules[$key] = $rule['capitalFlow'];
        }

        return $rules;
    }
}
