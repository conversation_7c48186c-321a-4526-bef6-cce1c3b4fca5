<?php


namespace finance\models;

use Carbon\Carbon;
use dashboard\grid\MoneyTotalDataColumn;
use kvmanager\models\KeyValue;
use Xlerr\Metric\Models\BizReport;
use yii\data\SqlDataProvider;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;

class BizReportSearch extends BizReport
{
    public $startDate;
    public $endDate;
    public $biz_report_type;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::parse('30 days ago')->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::parse('yesterday')->toDateString()],
            [['biz_report_type'], 'default', 'value' => 'v2401_pc_acct_flowinout_amount_daily'],
            [['channel', 'biz_report_type'], 'safe'],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'channel' => '通道',
            'startDate' => '开始日期',
            'endDate' => '结束日期',
        ];
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function search(array $params)
    {
        $this->load($params);
        $this->validate();

        $query = self::find()
            ->where([
                'type' => $this->biz_report_type
            ]);

        $categoryTypes = (clone $query)->select('subset')->indexBy('category')->column();

        $categoryList = BizType::find()
            ->where([
                'biz_type_subtype' => array_keys($categoryTypes),
            ])
            ->select('biz_type_memo')
            ->indexBy('biz_type_subtype')
            ->column();

        if (empty($categoryList)) {
            $query->andWhere('1 = 0');
        } else {
            $query
                ->andFilterWhere([
                    'channel' => $this->channel,
                ])
                ->andWhere([
                    'and',
                    ['>=', 'date', $this->startDate],
                    ['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()],
                ])
                ->select(
                    array_map(static function ($category) {
                        $selectSql = vsprintf(
                            'sum(if(`category` = \'%s\', `value`, 0)) as `%s`',
                            [
                                $category,
                                $category,
                            ]
                        );

                        return new Expression($selectSql);
                    }, array_keys($categoryList))
                );

            $subIn = implode(',', array_keys($categoryList));

            $query->addSelect([
                new Expression(
                    'sum(if(`subset` = \'in\' and not find_in_set(`category`, :subIn), `value`, 0)) as `in_other`'
                ),
                new Expression(
                    'sum(if(`subset` = \'out\' and not find_in_set(`category`, :subIn), `value`, 0)) as `out_other`'
                ),
            ]);

            $query->addParams([
                'subIn' => $subIn,
            ]);

            $query->addSelect([
                'date',
                'channel',
            ])->groupBy([
                'date',
                'channel',
            ]);
        }

        $inColumns = [];
        $outColumns = [];
        $bizTypeMap = KeyValue::take('biz_type_map');

        foreach ($categoryTypes as $attribute => $type) {
            if (!array_key_exists($attribute, $categoryList)) {
                continue;
            }
            if ($type === 'in') {
                $inColumns[] = [
                    'label' => $bizTypeMap[$type][$attribute] ?? $categoryList[$attribute],
                    'attribute' => $attribute,
                    'class' => MoneyTotalDataColumn::class,
                    'headerOptions' => [
                        'class' => 'bg-success text-right',
                    ],
                    'contentOptions' => [
                        'class' => 'bg-success text-right',
                    ],
                ];
            } else {
                $outColumns[] = [
                    'label' => $bizTypeMap[$type][$attribute] ?? $categoryList[$attribute],
                    'attribute' => $attribute,
                    'class' => MoneyTotalDataColumn::class,
                    'headerOptions' => [
                        'class' => 'bg-danger text-right',
                    ],
                    'contentOptions' => [
                        'class' => 'bg-danger text-right',
                    ],
                ];
            }
        }


        $inTypes = [...array_column($inColumns, 'attribute'), 'in_other'];
        $outTypes = [...array_column($outColumns, 'attribute'), 'out_other'];

        //rdl 增加期初期末
        $balanceColumns = [];
        if ($this->biz_report_type === 'v2401_pc_rdl_acct_flowinout_amount_daily') {
            $this->buildBalance($query, $inTypes, $outTypes, $balanceColumns);
        }

        $columns = [
            [
                'label' => '日期',
                'attribute' => 'date',
                'footer' => '合计',
            ],
            [
                'label' => '通道',
                'attribute' => 'channel',
                'footer' => '-',
            ],
            ...$balanceColumns,
            [
                'label' => '总流入 - 总流出',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    return array_sum(ArrayHelper::filter($data, $inTypes))
                        - array_sum(ArrayHelper::filter($data, $outTypes));
                },
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #fcf8e3;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #fcf8e3;'
                ],
            ],
            [
                'label' => '总流入',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'value' => function ($data) use ($inTypes) {
                    return array_sum(ArrayHelper::filter($data, $inTypes));
                },
            ],
            ...$inColumns,
            [
                'label' => '未知流入',
                'attribute' => 'in_other',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-success text-right',
                ],
            ],
            [
                'label' => '总流出',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'value' => function ($data) use ($outTypes) {
                    return array_sum(ArrayHelper::filter($data, $outTypes));
                },
            ],
            ...$outColumns,
            [
                'label' => '未知流出',
                'attribute' => 'out_other',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-danger text-right',
                ],
            ],
        ];

        $command = $query->createCommand();

        return [
            new SqlDataProvider([
                'sql' => $command->sql,
                'params' => $command->params,
                'db' => $command->db,
                'sort' => [
                    'attributes' => ['date'],
                    'defaultOrder' => ['date' => SORT_DESC],
                ],
            ]),
            $columns,
        ];
    }


    protected function buildBalance(Query $query, array $inTypes, array $outTypes, array &$columns)
    {
        $query->addSelect([
            new Expression(vsprintf(
                'sum(if(`category` = \'%s\', `value`, 0)) as `%s`',
                [
                    'opening_balance',
                    'opening_balance',
                ]
            )),
            new Expression(vsprintf(
                'sum(if(`category` = \'%s\', `value`, 0)) as `%s`',
                [
                    'closing_balance',
                    'closing_balance',
                ]
            ))
        ]);

        $columns = [
            [
                'label' => '差额',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    $startBalance = (int)($data['opening_balance'] ?? 0);
                    $endBalance = (int)($data['closing_balance'] ?? 0);
                    //差额(（期末-期初 ） - （总流入-总流出))
                    return
                        ($endBalance - $startBalance) - (
                        (array_sum(ArrayHelper::filter($data, $inTypes))
                            - array_sum(ArrayHelper::filter($data, $outTypes))));
                },
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #e4759d;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #e4759d;'
                ],
            ],
            [
                'label' => '期初余额',
                'attribute' => 'opening_balance',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
            ],
            [
                'label' => '期末余额',
                'attribute' => 'closing_balance',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
            ],
            [
                'label' => '期末-期初',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) {
                    $startBalance = (int)($data['opening_balance'] ?? 0);
                    $endBalance = (int)($data['closing_balance'] ?? 0);

                    return $endBalance - $startBalance;
                },
                'headerOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
                'contentOptions' => [
                    'class' => 'text-right',
                    'style' => 'background-color: #eaf3f6;'
                ],
            ]
        ];
    }
}