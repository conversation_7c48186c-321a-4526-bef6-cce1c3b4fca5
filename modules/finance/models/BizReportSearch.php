<?php

namespace finance\models;

use Carbon\Carbon;
use dashboard\grid\MoneyTotalDataColumn;
use xlerr\common\grid\MoneyDataColumn;
use Xlerr\Metric\Models\BizReport;
use yii\data\SqlDataProvider;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class BizReportSearch extends BizReport
{
    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::parse('30 days ago')->toDateString()],
            [['endDate'], 'default', 'value' => Carbon::parse('yesterday')->toDateString()],
            [['channel'], 'safe'],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'channel' => '通道',
            'startDate' => '开始日期',
            'endDate' => '结束日期',
        ];
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function search(array $params)
    {
        $this->load($params);
        $this->validate();

        $query = self::find()
            ->where([
                'type' => [
                    'v2401_pc_acct_flowinout_amount_daily',
                    'v2501_pc_merchant_acct_eod_balance_daily', // 通道余额数据
                ],
            ]);

        $categoryTypes = (clone $query)->select('subset')->indexBy('category')->column();

        $categoryList = BizType::find()
            ->where([
                'biz_type_subtype' => array_keys($categoryTypes),
            ])
            ->select('biz_type_memo')
            ->indexBy('biz_type_subtype')
            ->column();

        if (empty($categoryList)) {
            $query->andWhere('1 = 0');
        } else {
            $query
                ->andFilterWhere([
                    'channel' => $this->channel,
                ])
                ->andWhere([
                    'and',
                    ['>=', 'date', $this->startDate],
                    ['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()],
                ])
                ->select(
                    array_map(static function ($category) {
                        $selectSql = vsprintf(
                            'sum(if(`category` = \'%s\', `value`, 0)) as `%s`',
                            [
                                $category,
                                $category,
                            ]
                        );

                        return new Expression($selectSql);
                    }, array_keys($categoryList))
                );

            $subIn = implode(',', array_keys($categoryList));

            $query->addSelect([
                new Expression('sum(if(`category` = \'opening\', `value`, 0)) as `opening`'),
                new Expression('sum(if(`category` = \'closing\', `value`, 0)) as `closing`'),
                new Expression(
                    'sum(if(`subset` = \'in\' and not find_in_set(`category`, :subIn), `value`, 0)) as `in_other`'
                ),
                new Expression(
                    'sum(if(`subset` = \'out\' and not find_in_set(`category`, :subIn), `value`, 0)) as `out_other`'
                ),
            ]);

            $query->addParams([
                'subIn' => $subIn,
            ]);

            $query->addSelect([
                'date',
                'channel',
            ])->groupBy([
                'date',
                'channel',
            ]);
        }

        $command = $query->createCommand();

        $inColumns = [];
        $outColumns = [];

        foreach ($categoryTypes as $attribute => $type) {
            if (!array_key_exists($attribute, $categoryList)) {
                continue;
            }
            if ($type === 'in') {
                $inColumns[] = [
                    'label' => $categoryList[$attribute],
                    'attribute' => $attribute,
                    'class' => MoneyTotalDataColumn::class,
                    'headerOptions' => [
                        'class' => 'bg-success text-right',
                    ],
                    'contentOptions' => [
                        'class' => 'bg-success text-right',
                    ],
                ];
            } else {
                $outColumns[] = [
                    'label' => $categoryList[$attribute],
                    'attribute' => $attribute,
                    'class' => MoneyTotalDataColumn::class,
                    'headerOptions' => [
                        'class' => 'bg-danger text-right',
                    ],
                    'contentOptions' => [
                        'class' => 'bg-danger text-right',
                    ],
                ];
            }
        }

        $inTypes = [...array_column($inColumns, 'attribute'), 'in_other'];
        $outTypes = [...array_column($outColumns, 'attribute'), 'out_other'];

        $columns = [
            [
                'label' => '日期',
                'attribute' => 'date',
                'footer' => '合计',
            ],
            [
                'label' => '通道',
                'attribute' => 'channel',
                'footer' => '-',
            ],
            [
                'label' => '通道日终余额',
                'attribute' => 'closing',
                'class' => MoneyDataColumn::class,
            ],
            [
                'label' => '我方日终余额',
                'class' => MoneyDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    return $data['opening']
                        + array_sum(ArrayHelper::filter($data, $inTypes))
                        - array_sum(ArrayHelper::filter($data, $outTypes));
                },
            ],
            [
                'label' => '余额差异',
                'class' => MoneyDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    return $data['closing'] - ($data['opening']
                            + array_sum(ArrayHelper::filter($data, $inTypes))
                            - array_sum(ArrayHelper::filter($data, $outTypes)));
                },
            ],
            [
                'label' => '差异率',
                'format' => 'percent',
                'value' => function ($data) use ($inTypes, $outTypes) {
                    $amount = $data['opening']
                        + array_sum(ArrayHelper::filter($data, $inTypes))
                        - array_sum(ArrayHelper::filter($data, $outTypes));
                    if ($amount !== 0) {
                        return ($data['closing'] - $amount) / $amount;
                    }

                    return 0;
                },
                'footer' => '-',
            ],
            [
                'label' => '总流入 - 总流出',
                'class' => MoneyTotalDataColumn::class,
                'value' => function ($data) use ($inTypes, $outTypes) {
                    return array_sum(ArrayHelper::filter($data, $inTypes))
                        - array_sum(ArrayHelper::filter($data, $outTypes));
                },
            ],
            [
                'label' => '总流入',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'value' => function ($data) use ($inTypes) {
                    return array_sum(ArrayHelper::filter($data, $inTypes));
                },
            ],
            ...$inColumns,
            [
                'label' => '未知流入',
                'attribute' => 'in_other',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-success text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-success text-right',
                ],
            ],
            [
                'label' => '总流出',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'value' => function ($data) use ($outTypes) {
                    return array_sum(ArrayHelper::filter($data, $outTypes));
                },
            ],
            ...$outColumns,
            [
                'label' => '未知流出',
                'attribute' => 'out_other',
                'class' => MoneyTotalDataColumn::class,
                'headerOptions' => [
                    'class' => 'bg-danger text-right',
                ],
                'contentOptions' => [
                    'class' => 'bg-danger text-right',
                ],
            ],
        ];

        return [
            new SqlDataProvider([
                'sql' => $command->sql,
                'params' => $command->params,
                'db' => $command->db,
                'sort' => [
                    'attributes' => ['date'],
                    'defaultOrder' => ['date' => SORT_DESC],
                ],
            ]),
            $columns,
        ];
    }
}