<?php

namespace finance\models;

use Carbon\Carbon;
use finance\components\risk\exception\RiskControlException;
use finance\components\RiskControlHttpComponent;
use grant\models\AssetBorrower;
use grant\models\WithdrawOrder;
use grant\models\WithdrawRecord;
use xlerr\desensitise\Desensitise;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\Model;
use yii\data\ArrayDataProvider;
use yii\data\DataProviderInterface;
use yii\data\SqlDataProvider;
use yii\db\Expression;

use function xlerr\desensitise\encrypt;

/**
 * AssetSearch represents the model behind the search form about `finance\models\Asset`.
 */
class AssetSearch extends Asset
{
    public $startTimeOfCreatedAt;
    public $endTimeOfCreatedAt;
    public $startTimeOfActualGrantAt;
    public $endTimeOfActualGrantAt;
    public $mobile;
    public $orderNo;
    public $name;
    public $cardNum;
    public $simpleSearchCardType = RiskControlHttpComponent::TYPE_BANK_ACCOUNT;

    public $withdraw_record_order_no;
    public $withdraw_record_channel;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['startTimeOfCreatedAt', 'endTimeOfCreatedAt'], 'string'],
            [['asset_item_no', 'mobile', 'orderNo'], 'trim'],
            [
                [
                    'asset_status',
                    'simpleSearchCardType',
                    'asset_loan_channel',
                    'asset_from_system_name',
                    'startTimeOfActualGrantAt',
                    'endTimeOfActualGrantAt',
                    'orderNo',
                    'name',
                    'asset_period_count',
                    'asset_sub_type',
                    'cardNum',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return \yii\data\DataProviderInterface
     * @throws InvalidConfigException
     */
    public function search($params): DataProviderInterface
    {
        $this->load($params);

        if (!$this->validate()) {
            return new ArrayDataProvider([
                'allModels' => [],
            ]);
        }

        $itemNoList = [];
        if ($this->mobile) {
            $itemNoList = AssetBorrower::find()
                ->where([
                    'asset_borrower_mobile' => encrypt($this->mobile, Desensitise::TYPE_PHONE_NUMBER)->hash,
                ])
                ->select('asset_borrower_item_no')
                ->column();
        }

        if (empty($this->asset_item_no . $this->mobile)) {
            $defaultDate = Carbon::now()->toDateString();
            if (!$this->startTimeOfCreatedAt) {
                $this->startTimeOfCreatedAt = $defaultDate;
            }
            if (!$this->endTimeOfCreatedAt) {
                $this->endTimeOfCreatedAt = $defaultDate;
            }
        }
        if (!empty($this->orderNo)) {
            /**
             * @var WithdrawRecord $record
             */
            $record = WithdrawRecord::find()->where([
                'withdraw_record_channel_key' => $this->orderNo,
            ])->one();
            if (!$record || !$record->withdraw_record_channel) {
                return new ArrayDataProvider([
                    'allModels' => [],
                ]);
            }
            //在此求交集
            $itemNoWithOrderNo =
                (array)rtrim($record->withdrawOrder->withdraw_order_asset_item_no ?? '', WithdrawOrder::SPLICE_STR);
            $itemNoList        = $this->mobile ? array_intersect($itemNoList, $itemNoWithOrderNo) : $itemNoWithOrderNo;
        }
        // grid filtering conditions
        $query = Asset::find()
            ->andFilterWhere([
                'asset_item_no'      => $this->asset_item_no,
                'asset_status'       => $this->asset_status,
                'asset_period_count' => $this->asset_period_count,
                'asset_sub_type'     => $this->asset_sub_type,
                'asset_loan_channel' => $this->asset_loan_channel,
            ])
            ->andFilterWhere([
                'asset_item_no' => $itemNoList,
            ])
            ->andWhere(new Expression(sprintf('%d', empty($this->mobile) || !empty($itemNoList))))
            ->andFilterWhere([
                'and',
                ['like', 'asset_from_system_name', $this->asset_from_system_name],
                ['>=', 'asset_create_at', $this->startTimeOfCreatedAt],
                [
                    '<',
                    'asset_create_at',
                    $this->endTimeOfCreatedAt ? Carbon::parse($this->endTimeOfCreatedAt)->addDay()->toDateString()
                        : null,
                ],
                ['>=', 'asset_actual_grant_at', $this->startTimeOfActualGrantAt],
                [
                    '<',
                    'asset_actual_grant_at',
                    $this->endTimeOfActualGrantAt ? Carbon::parse($this->endTimeOfActualGrantAt)->addDay()
                        ->toDateString()
                        : null,
                ],
            ])
            ->indexBy('asset_item_no');

        $command = $query->createCommand();


        $dataProvider = new SqlDataProvider([
            'sql'    => $command->getSql(),
            'params' => $command->params,
            'db'     => self::getDb(),
            'sort'   => [
                'attributes'   => ['asset_create_at'],
                'defaultOrder' => ['asset_create_at' => SORT_DESC],
            ],
        ]);
        if (!empty($params['download'])) {
            $dataProvider->setPagination(false);
        }

        return $dataProvider;
    }

    /**
     * @param array $orders
     * @param array $assets
     *
     * @return array
     */
    protected function makeReturn(array $orders, array $assets): array
    {
        foreach ($assets as $itemNo => &$value) {
            $itemNo                          .= WithdrawOrder::SPLICE_STR;
            $value->withdraw_record_order_no = $orders[$itemNo]['withdrawRecord']['withdraw_record_channel_key'] ?? '';
            $value->withdraw_record_channel  = $orders[$itemNo]['withdrawRecord']['withdraw_record_channel'] ?? '';
        }

        return $assets;
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'mobile'  => '手机号',
            'orderNo' => '放款订单号',
            'cardNum' => '卡号',
            'name'    => '姓名,备注:如果多个名字建议用空格隔开',
        ]);
    }

    /**
     * @param $params
     *
     * @return ArrayDataProvider
     * @throws InvalidConfigException
     */
    public function simpleSearch($params): DataProviderInterface
    {
        $dataProvider = new ArrayDataProvider([
            'modelClass' => self::class,
            'allModels'  => [],
            'sort'       => [
                'attributes'   => ['create_at'],
                'defaultOrder' => ['create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if ((empty($this->name) && empty($this->cardNum) && empty($this->mobile)) || !$this->validate()) {
            return $dataProvider;
        }

        $filter = [
            RiskControlHttpComponent::TYPE_NAME  => $this->name,
            $this->simpleSearchCardType          => $this->cardNum,
            RiskControlHttpComponent::TYPE_PHONE => $this->mobile,
        ];

        $userUuids = [];

        $client = RiskControlHttpComponent::instance();
        try {
            foreach ($filter as $type => $value) {
                if (!empty($value)) {
                    $res = $client->getUserUUIDByPlainText($value, $type);
                    if ($res) {
                        array_push($userUuids, ...$res);
                    }
                }
            }
        } catch (RiskControlException $e) {
            Yii::$app->getSession()->setFlash('error', $e->getMessage());
        }

        $itemNoList = AssetBorrower::find()
            ->andWhere(['asset_borrower_uuid' => $userUuids])
            ->select(['asset_borrower_item_no'])
            ->column();
        if (empty($itemNoList)) {
            return $dataProvider;
        }

        $query = Asset::find()
            ->where(['asset_item_no' => $itemNoList])
            ->indexBy('asset_item_no');

        $dataProvider->allModels = $query->all();

        return $dataProvider;
    }
}
