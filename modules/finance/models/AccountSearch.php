<?php

namespace finance\models;

use yii\data\ActiveDataProvider;

/**
 * AccountSearch represents the model behind the search form about `finance\models\Account`.
 */
class AccountSearch extends Account
{
    public $keyword;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['keyword', 'type'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Account::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'type' => $this->type,
        ]);

        $query->andFilterWhere([
            'or',
            ['like', 'identity', $this->keyword],
            ['like', 'name', $this->keyword],
            ['like', 'number', $this->keyword],
            ['like', 'owner', $this->keyword],
        ]);

        return $dataProvider;
    }
}
