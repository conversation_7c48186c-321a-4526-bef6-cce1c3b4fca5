<?php

namespace finance\models;

use Carbon\Carbon;
use common\models\User;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "indicator_explanation".
 *
 * @property int $id 主键ID
 * @property string $page_path 页面路径
 * @property string $indicator_name 指标名称
 * @property string $explanation 解释说明
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property int $created_by 创建人ID
 * @property int $updated_by 更新人ID
 * @property User $createUser 创建人
 * @property User $updateUser 更新人
 */
class IndicatorExplanation extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'indicator_explanation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['page_path', 'indicator_name', 'explanation'], 'required'],
            [['explanation'], 'default', 'value' => ''],
            [['updated_by'], 'default', 'value' => 0],
            [['created_at', 'updated_at'], 'safe'],
            [['created_by', 'updated_by'], 'integer'],
            [['page_path', 'indicator_name'], 'string', 'max' => 256],
            [['explanation'], 'string', 'max' => 512],
        ];
    }


    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => 'updated_by',
                'value' => fn($event) => Yii::$app->user->id,
            ],
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => Carbon::now()->toDateTimeString(),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键ID',
            'page_path' => '页面路径',
            'indicator_name' => '指标名称',
            'explanation' => '解释说明',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'created_by' => '创建人ID',
            'updated_by' => '更新人ID',
        ];
    }

    public function getCreateUser(): \yii\db\ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    public function getUpdateUser(): \yii\db\ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    /**
     * 根据菜单地址和指标名称去匹配
     * @param string $path
     * @param string $indicatorName
     * @return false|int|string|null
     */
    public static function getTooltip(string $path, string $indicatorName)
    {
        return self::find()->where([
            'page_path' => $path,
            'indicator_name' => $indicatorName
        ])->select('explanation')->scalar();
    }
}
