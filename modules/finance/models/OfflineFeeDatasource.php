<?php

namespace finance\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\Connection;

/**
 * This is the model class for table "offline_fee_datasource".
 *
 * @property int $id 主键
 * @property string $fee_type 费用类型
 * @property string $fee_type_desc 费用类型描述
 * @property string $sub_fee_type 子费用类型，计算区分
 * @property float $rate 比例/单价; 约定比例为3.5% 值为3.5，单价1元，值为100
 * @property string $calculation_type
 *     计算类型，每笔固定比例fixed_rate，每笔固定金额fixed_amount，等额本息avg_capital,等额本息最后一期按天计息avg_capital_last_period_according_day,等额本息差额avg_capital_difference
 * @property string $calculation_desc 计算公式描述
 * @property string $payment_mode 缴费方式,period:期缴,total趸缴
 * @property string $data_source  数据源类型，gbiz,rbiz,psvr,biz
 * @property string|null $data_source_sql 数据源sql
 * @property string $comment 备注
 * @property string $create_user 添加人
 * @property string $create_at 创建时间
 * @property string $update_at 更新时间
 * @property string $statistics_dimension 统计维度:day日,month月
 */
class OfflineFeeDatasource extends \yii\db\ActiveRecord
{
    public const CALCULATION_TYPE_LIST = [
        'fixed_rate' => '每笔固定比例',
        'fixed_amount' => '每笔固定金额',
        'avg_capital' => '等额本息',
        'avg_capital_last_period_according_day' => '等额本息最后一期按天计息',
        'avg_capital_difference' => '等额本息差额',
    ];

    public const PAYMENT_MODE_LIST = [
        'period' => '期缴',
        'total' => '趸交',
    ];

    public const STATISTICS_DIMENSION_LIST = [
        'day' => '日',
        'month' => '月',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'dcs_offline_fee_datasource';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                [
                    'fee_type',
                    'sub_fee_type',
                    'rate',
                    'calculation_type',
                    'calculation_desc',
                    'payment_mode',
                    'data_source',
                ],
                'required',
            ],
            [['rate'], 'number'],
            [['data_source_sql'], 'string'],
            [['create_at', 'update_at'], 'safe'],
            [['fee_type', 'fee_type_desc', 'sub_fee_type', 'calculation_type'], 'string', 'max' => 64],
            [['calculation_desc'], 'string', 'max' => 255],
            [['payment_mode', 'data_source'], 'string', 'max' => 16],
            [['comment', 'create_user'], 'string', 'max' => 100],
            [['statistics_dimension'], 'string', 'max' => 8],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => '主键',
            'fee_type' => '费用类型',
            'fee_type_desc' => '费用类型描述',
            'sub_fee_type' => '子费用类型，计算区分',
            'rate' => '比例/单价; 约定比例为3.5% 值为3.5，单价1元，值为100',
            'calculation_type' => '计算类型',
            'calculation_desc' => '计算公式描述',
            'payment_mode' => '缴费方式',
            'data_source' => ' 数据源类型',
            'data_source_sql' => '数据源sql',
            'comment' => '备注',
            'create_user' => '添加人',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'statistics_dimension' => '统计维度',
        ];
    }
}
