<?php

namespace finance\models\oaApprovalDto;

use common\helpers\ArrayHelper;
use yii\base\UserException;
use yii\db\ActiveRecord;

class OaApprovalDto extends ActiveRecord
{
    public const WITHDRAW_TYPE_INSTAPAY = '系统代付';
    public const WITHDRAW_TYPE_ACCOUNT_WITHDRAW = '同名账户提现';
    public const WITHDRAW_TYPE_SWIFT_WITHDRAW = '跨境提现';
    public const WITHDRAW_TYPE_PESONET = '手工代付';

    public const FLOW_KEY_OVERSEA_INCOME_WITHDRAW = 'oversea_income_withdraw'; //通道资金提现
    public const FLOW_KEY_PAY_FOR_OPERATION = 'pay_for_operation'; //通道支付运营款
    public const FLOW_KEY_OVERSEA_OTHER_EXPENSES = 'oversea_other_expenses'; //海外其他业务支出

    public static $flowKeyMap = [
        self::FLOW_KEY_OVERSEA_INCOME_WITHDRAW => '通道资金提现',
        self::FLOW_KEY_PAY_FOR_OPERATION       => '通道支付运营款',
        self::FLOW_KEY_OVERSEA_OTHER_EXPENSES  => '海外其他业务支出',
    ];

    public $tenantId; //租户ID;
    public $entryId; //流程ID
    public $title; //标题
    public $createdAt; //创建时间
    public $updatedAt; //更新时间
    public $flowKey; //流程KEY
    public $statusCode; //状态码

    public $billStartDate; //账单开始日期
    public $billEndDate; //账单结束日期
    public $capitalFlow; //资金去向
    public $expenseCategory;//付款类型

    public $region;//区域
    public $memo; //备注
    public $channel;
    public $withdrawType; //目前菲律宾在使用

    /** @var OaPaymentInfo[] */
    public $paymentList; //付款信息

    public $raw; //原始数据

    public function rules()
    {
        return [
            [
                [
                    'tenantId',
                    'entryId',
                    'title',
                    'createdAt',
                    'updatedAt',
                    'flowKey',
                    'statusCode',
                    'billStartDate',
                    'billEndDate',
                    'region',
                    'paymentList',
                    'raw',
                ],
                'required',
            ],
            [
                [
                    'withdrawType',
                ],
                'required',
                'when' => function ($model) {
                    if ($model->region == '菲律宾') {
                        return $model->flowKey == self::FLOW_KEY_PAY_FOR_OPERATION
                            || $model->flowKey == self::FLOW_KEY_OVERSEA_INCOME_WITHDRAW;
                    }
                    return false;
                }
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public function validate($attributeNames = null, $clearErrors = true)
    {
        parent::validate($attributeNames, $clearErrors);
        if (!is_array($this->paymentList)) {
            $this->addError('paymentList', '付款信息不能为空');
        }
        foreach ($this->paymentList as $paymentInfo) {
            if (!$paymentInfo->validate()) {
                $this->addError('paymentList', implode(';', $paymentInfo->getErrorSummary(true)));
            }
        }
        return !$this->hasErrors();
    }

    /**
     * 解析OA表单到DTO对象
     * @return void
     */
    public function parser($data)
    {
        $this->raw = $data;
        $camelData = ArrayHelper::snakeToCamel($data);
        parent::load($camelData, '');

        $this->billStartDate = $this->createdAt;
        $this->billEndDate = $this->createdAt;

        switch ($this->flowKey) {
            case self::FLOW_KEY_OVERSEA_INCOME_WITHDRAW:
                $this->parserOverseaIncomeWithdraw();
                break;
            case self::FLOW_KEY_PAY_FOR_OPERATION:
                $this->parserPayForOperation();
                break;
            case self::FLOW_KEY_OVERSEA_OTHER_EXPENSES:
                $this->parserOverseaOtherExpenses();
        }
    }

    /**
     * 解析OA表单-通道资金提现
     * @return void
     */
    private function parserOverseaIncomeWithdraw()
    {
        $this->capitalFlow  = $this->raw['form_data']['capital_flow']['value'] ?? null;
        $this->region       = $this->raw['form_data']['region']['value'] ?? null;
        $this->memo         = $this->raw['form_data']['memo']['value'] ?? null;
        $this->channel      = $this->raw['form_data']['channel']['value'] ?? null;
        $this->withdrawType = $this->raw['form_data']['withdraw_type']['value'] ?? null;

        $subFormList = $this->raw['form_data']['子表单']['list'] ?? [];
        foreach ($subFormList as $item) {
            $paymentInfo                   = new OaPaymentInfo();
            $paymentInfo->payee            = $item['payee']['value'] ?? null;
            $paymentInfo->currency         = $item['currency']['value'] ?? null;
            $paymentInfo->withdrawAmount   = $item['withdraw_amount']['value'] ?? null;
            $paymentInfo->bankCardUserName = $item['bank_card_user_name']['value'] ?? null;
            $paymentInfo->bankCard         = $item['bank_card']['value'] ?? null;
            $paymentInfo->withdrawMemo     = $item['withdraw_memo']['value'] ?? null;
            $paymentInfo->bank             = $item['bank']['value'] ?? null;
            $paymentInfo->mobile           = $item['mobile']['value'] ?? null;
            $this->paymentList[] = $paymentInfo;
        }
    }

    /**
     * 解析OA表单-通道支付运营款
     * @return void
     * @throws UserException
     */
    private function parserPayForOperation()
    {
        if (empty($this->raw['form_data']['FormData']['value'])) {
            throw new UserException('OA表单pay_for_operation，缺少form_data.FormData.value');
        }
        $forData               = json_decode($this->raw['form_data']['FormData']['value'], true);
        $this->expenseCategory = $forData['expense_info'][0]['expense_category_name'] ?? null; // todo 跟着语言联动
        $this->region          = $forData['expense_info'][0]['business_region'] ?? null;
        $this->memo            = $forData['cause'] ?? null;
        $this->withdrawType    = $forData['withdraw_method'] ?? null;
        $this->billStartDate   = $forData['expense_info'][0]['expense_period'][0] ?? null;
        $this->billEndDate     = $forData['expense_info'][0]['expense_period'][1] ?? null;

        $paymentInfo                   = new OaPaymentInfo();
        $paymentInfo->payer            = $forData['our_subject'] ?? null;
        $paymentInfo->payee            = $forData['bank_card_account_name'] ?? null;
        $paymentInfo->currency         = $forData['currency'] ?? null;
        $paymentInfo->withdrawAmount   = $forData['amount'] ?? null;
        $paymentInfo->bankCardUserName = $forData['bank_card_account_name'] ?? null;
        $paymentInfo->bankCard         = $forData['bank_card_number'] ?? null;
        $paymentInfo->withdrawMemo     = $forData['cause'] ?? null;
        $paymentInfo->bank             = $forData['bank_input'] ?? null;
        $paymentInfo->mobile           = $forData['mobile_phone'] ?? null;

        $this->paymentList[] = $paymentInfo;
    }

    /**
     * 解析OA表单-海外其他业务支出
     * @return void
     */
    private function parserOverseaOtherExpenses()
    {
        $this->capitalFlow  = $this->raw['form_data']['capital_flow']['value'] ?? null;
        $this->region       = $this->raw['form_data']['region']['value'] ?? null;
        $this->memo         = $this->raw['form_data']['memo']['value'] ?? null;

        $subFormList = $this->raw['form_data']['子表单']['list'] ?? [];
        foreach ($subFormList as $item) {
            $paymentInfo                   = new OaPaymentInfo();
            $paymentInfo->payee            = $item['payee']['value'] ?? null;
            $paymentInfo->currency         = $item['currency']['value'] ?? null;
            $paymentInfo->withdrawAmount   = $item['withdraw_amount']['value'] ?? null;
            $paymentInfo->bankCardUserName = $item['bank_card_user_name']['value'] ?? null;
            $paymentInfo->bankCard         = $item['bank_card']['value'] ?? null;
            $paymentInfo->withdrawMemo     = $item['withdraw_memo']['value'] ?? null;
            $paymentInfo->bank             = $item['bank']['value'] ?? null;
            $paymentInfo->mobile           = $item['mobile']['value'] ?? null;
            $this->paymentList[] = $paymentInfo;
        }
    }
}
