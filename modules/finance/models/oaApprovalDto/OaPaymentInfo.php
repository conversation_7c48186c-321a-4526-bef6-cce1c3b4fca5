<?php

namespace finance\models\oaApprovalDto;

use yii\base\Model;

class OaPaymentInfo extends Model
{
    public $payer; //付款人/付款方
    public $payee; //收款人/收款方
    public $currency; //币种
    public $withdrawAmount;//付款金额
    public $bankCardUserName; //账户名称
    public $bankCard; //银行账号
    public $withdrawMemo; //提现说明
    public $bank; //银行名称
    public $mobile; //银行预留手机号

    public function rules()
    {
        return [
            [
                [
                    'payee',
                    'currency',
                    'withdrawAmount',
                    'bankCardUserName',
                    'bankCard',
                    'bank',
                    'mobile'
                ],
                'required',
            ],
        ];
    }
}
