<?php

namespace finance\models;

use Carbon\Carbon;
use common\models\User;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "biz_type".
 *
 * @property int $biz_type_id 自增主键
 * @property string $biz_type_fund_flow 资金流向：in 流入、out 流出
 * @property string $biz_type_category 交易类型一级分类: 放款、还款、充值、提现.......
 * @property string $biz_type_subtype 交易类型二级分类：.....
 * @property string|null $biz_type_memo 备注
 * @property string $biz_type_create_user 创建用户
 * @property string $biz_type_create_at 创建时间
 * @property string $biz_type_update_at 更新时间
 * @property string $biz_type_scope 更新时间
 * @property User $createUser 更新时间
 */
class BizType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'biz_type';
    }

    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'biz_type_create_user',
                'updatedByAttribute' => null,
                'value' => fn($event) => Yii::$app->user->id,
            ],
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'biz_type_create_at',
                'updatedAtAttribute' => 'biz_type_update_at',
                'value' => Carbon::now()->toDateTimeString(),
            ],
        ];
    }

    public const FUND_FLOW_IN = 'in';
    public const FUND_FLOW_OUT = 'out';


    public const FUND_FLOWS = [
        self::FUND_FLOW_IN => '入金',
        self::FUND_FLOW_OUT => '出金',
    ];

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['biz_type_fund_flow', 'biz_type_category', 'biz_type_subtype', 'biz_type_memo'], 'required'],
            [['biz_type_create_at', 'biz_type_update_at', 'biz_type_create_user', 'biz_type_scope'], 'safe'],
            [['biz_type_fund_flow', 'biz_type_category', 'biz_type_create_user'], 'string', 'max' => 32],
            [['biz_type_subtype', 'biz_type_memo'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'biz_type_id' => '自增主键',
            'biz_type_fund_flow' => '资金流向',
            'biz_type_category' => '一级交易类型',
            'biz_type_subtype' => '子类型',
            'biz_type_memo' => '备注',
            'biz_type_create_user' => '创建用户',
            'biz_type_create_at' => '创建时间',
            'biz_type_update_at' => '更新时间',
            'biz_type_scope' => '作用域',
        ];
    }

    /**
     * @param string|array $flow
     * @param string $category
     * @param string $subType
     *
     * @return array
     */
    public static function getBizTypeList(
        $flow = self::FUND_FLOW_IN,
        string $category = '',
        string $subType = ''
    ): array {
        return self::find()->select('biz_type_memo')
            ->where(['biz_type_fund_flow' => $flow])
            ->andFilterWhere(['biz_type_category' => $category])
            ->andFilterWhere(['biz_type_subtype' => $subType])
            ->indexBy('biz_type_subtype')
            ->column();
    }


    public static function getCategoryList(): array
    {
        return BizType::find()
            ->select(['biz_type_category'])
            ->groupBy('biz_type_category')
            ->indexBy('biz_type_category')
            ->column();
    }


    public function getCreateUser(): \yii\db\ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'biz_type_create_user']);
    }
}
