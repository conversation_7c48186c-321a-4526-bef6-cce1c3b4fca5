<?php

namespace finance\models;

use backend\behaviors\FormatterBehavior;
use Generator;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%withdraw}}".
 *
 * @property int $withdraw_id                  主键
 * @property int $withdraw_oa_id               OA审核ID
 * @property int $withdraw_procedure_id        收付款订单流程编号
 * @property int $withdraw_amount              金额，单位为分
 * @property int $withdraw_status              提现状态 0:未处理，1:处理中 2:成功 3:失败
 * @property string $withdraw_type                类型 本金结算(principal_settlement)，
 *                                                     利息结算 (Interest_settlement)，
 *                                                     提取收入(withdraw_income) ，
 *                                                     服务费补充保证金(service_supplement_deposit)，
 *                                                     资金调拨 (fund_allot),其他扣除 (other_deduct)
 * @property string $withdraw_channel             通道
 * @property string $withdraw_receive_account     收款名称
 * @property string $withdraw_virtual_account     出款虚户
 * @property string $withdraw_receive_number      收款号码
 * @property string $withdraw_receive_uuid        收款uuid
 * @property string $withdraw_receive_userid      收款用户id
 * @property string $withdraw_merchant_key        交易key  （随机生成）
 * @property string $withdraw_trade_no            交易流水号（随机生成）
 * @property string $withdraw_reason              提现原因
 * @property string $withdraw_create_user         创建人
 * @property string $withdraw_create_user_email   创建人邮箱
 * @property string $withdraw_created_at          创建时间
 * @property string $withdraw_oa_created_at       OA审核创建时间
 * @property string $withdraw_payment_created_at  支付请求时间
 * @property string $withdraw_payment_info        支付请求信息
 * @property string $withdraw_updated_at          更新时间
 * @property string $currency                     币种
 * @property string $withdraw_finished_at         交易完成时间
 * @property string $withdraw_biz_type            业务类型
 */
class Withdraw extends ActiveRecord
{
    public const STATUS_APPROVAL_CREATING = -1;
    public const STATUS_NEW = 0;
    public const STATUS_PROCESSING = 1;
    public const STATUS_SUCCESS = 2;
    public const STATUS_FAILURE = 3;
    public const STATUS_PAYMENT_FAILED = 4;
    public const STATUS_PAYMENT_ING = 5;
    public const STATUS_VOID = 6;

    public const STATUS = [
        self::STATUS_APPROVAL_CREATING => '审核单创建中',
        self::STATUS_PROCESSING => '审核中',         // OA审批中
        self::STATUS_NEW => '待处理',           // 初始状态
        self::STATUS_PAYMENT_ING => '代付处理中',         // 请求代付接口成功，等待回调
        self::STATUS_SUCCESS => '成功',            // oa审批通过并且请求代付接口成功
        self::STATUS_PAYMENT_FAILED => '失败',            // 请求代付接口失败
        self::STATUS_FAILURE => '审核未通过',        // OA审批不通过
        self::STATUS_VOID => '废弃',
    ];

    public const BIZ_TYPE_DEFAULT = ''; // 适用于放款业务
    public const BIZ_TYPE_WITHDRAW = 'withdraw'; // 提现
    public const BIZ_TYPE_SALARY = 'salary'; // 发工资
    public const BIZ_TYPE_GREY = 'grey'; // 灰度

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'dcs_withdraw_order';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('rbizDb');
    }

    public static function receiveList(): array
    {
        $config = KeyValue::take('withdraw_receive_config');
        $accounts = array_column($config, 'receive_account');

        return array_combine($accounts, $accounts);
    }

    /**
     * @param array|Withdraw $withdraw
     * @param int $totalAmount
     *
     * @return Generator
     * @throws UserException
     * @throws KVException
     */
    public static function amountSplit($withdraw, int $totalAmount): Generator
    {
        if ($totalAmount <= 0) {
            throw new UserException('提现金额必须大于0');
        }

        $config = KeyValue::take('withdraw_split_config');

        if (!is_array($withdraw)) {
            $withdraw = $withdraw->toArray();
        }
        $withdrawChannel = $withdraw['withdraw_channel'] ?? '';

        $accords = (array)($config['channel_config'][$withdrawChannel]['accords'] ?? []);
        if (!empty($accords)) {
            $accord = array_find($accords, function ($accord) use ($withdraw) {
                $rule = $accord['rule'] ?? null;
                if (empty($rule)) {
                    return false;
                }

                return empty(array_diff_assoc($rule, $withdraw));
            });

            if ($accord) {
                $maxAmount = (int)($accord['max_amount'] ?? 0);
            }
        }

        // 单笔最大提现金额
        $maxAmount ??= (int)($config['channel_config'][$withdrawChannel]['max_amount'] ?? $config['max_amount'] ?? 0);

        if ($maxAmount <= 0) {
            throw new UserException(sprintf('提现通道[%s]单笔最大提现金额必须大于0', $withdrawChannel));
        }

        while ($totalAmount > $maxAmount) {
            yield $maxAmount;
            $totalAmount -= $maxAmount;
        }

        yield $totalAmount;
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                [
                    'withdraw_amount',
                    'withdraw_status',
                    'withdraw_type',
                    'withdraw_channel',
                    'withdraw_receive_account',
                    'withdraw_receive_number',
                    'withdraw_create_user',
                    'withdraw_reason',
                ],
                'required',
            ],
            ['withdraw_virtual_account', 'default', 'value' => ''],
            [
                ['withdraw_amount'],
                'integer',
                'min' => 1,
                'tooSmall' => vsprintf('金额不能低于%s%s', [
                    Yii::$app->getFormatter()->asFormatAmount(1, true),
                    FormatterBehavior::currencyUnit(),
                ]),
            ],
            [['withdraw_procedure_id'], 'integer', 'min' => 1],
            [
                [
                    'withdraw_created_at',
                    'withdraw_updated_at',
                    'withdraw_oa_id',
                    'withdraw_merchant_key',
                    'withdraw_receive_uuid',
                    'withdraw_receive_userid',
                    'withdraw_virtual_account',
                    'withdraw_oa_created_at',
                    'withdraw_payment_created_at',
                    'withdraw_payment_info',
                    'trade_no',
                    'withdraw_create_user_email',
                    'withdraw_biz_type',
                ],
                'safe',
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'withdraw_id' => 'ID',
            'withdraw_channel' => '提现通道',
            'withdraw_receive_number' => '收款号码',
            'withdraw_merchant_key' => '订单号',
            'withdraw_trade_no' => '交易号',
            'withdraw_virtual_account' => '出款虚户',
            'withdraw_receive_account' => '收款名称',
            'withdraw_receive_uuid' => '收款卡UUID',
            'withdraw_receive_userid' => '收款用户ID',
            'withdraw_oa_id' => 'OAID',
            'withdraw_procedure_id' => 'PID',
            'withdraw_type' => '提现类型',
            'withdraw_status' => '提现状态',
            'withdraw_amount' => sprintf('提现金额(%s)', FormatterBehavior::currencyUnit()),
            'withdraw_reason' => '提现原因',
            'withdraw_created_at' => '创建时间',
            'withdraw_updated_at' => '更新时间',
            'withdraw_create_user' => '操作人',
            'withdraw_finished_at' => '完成时间',
            'withdraw_biz_type' => '业务类型',
            'withdraw_oa_created_at' => '请求OA时间',
            'withdraw_payment_created_at' => '请求支付时间',
            'withdraw_payment_info' => '请求支付信息',
        ];
    }
}
