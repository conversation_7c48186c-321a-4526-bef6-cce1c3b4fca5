<?php

namespace finance\models;

use backend\behaviors\FormatterBehavior;
use Carbon\Carbon;
use common\models\User;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use payment\models\Account as PaymentAccount;
use payment\models\AccountWithdrawalCard;
use Random\RandomException;
use RuntimeException;
use xlerr\desensitise\EncryptException;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;
use yii\helpers\Json;

use function xlerr\desensitise\batchDecrypt;

/**
 * This is the model class for table "{{%withdraw}}".
 *
 * @property int $withdraw_id                  主键
 * @property int $withdraw_procedure_id        制单流程ID
 * @property int $withdraw_oa_id               OA审核ID
 * @property int $withdraw_amount              金额，单位为分
 * @property int $withdraw_status              提现状态 0:未处理，1:处理中 2:成功 3:失败
 * @property string $withdraw_type                类型 本金结算(principal_settlement)，
 *                                                     利息结算 (Interest_settlement)，
 *                                                     提取收入(withdraw_income) ，
 *                                                     服务费补充保证金(service_supplement_deposit)，
 *                                                     资金调拨 (fund_allot),其他扣除 (other_deduct)
 * @property string $withdraw_transfer_type       转账类型
 * @property string $withdraw_channel             通道
 * @property string $withdraw_virtual_account     出款虚户
 * @property string $withdraw_receive_account     收款名称
 * @property string $withdraw_receive_account_address 收款账户地址
 * @property string $withdraw_receive_account_number      收款号码
 * @property string $withdraw_receive_bank_code   银行编码
 * @property string $withdraw_merchant_key        交易key  （随机生成）
 * @property string $withdraw_trade_no            交易流水号（随机生成）
 * @property string $withdraw_reason              提现原因
 * @property string $withdraw_create_user         创建人
 * @property string $withdraw_create_user_email   创建人邮箱
 * @property string $withdraw_created_at          创建时间
 * @property string $withdraw_oa_created_at       OA审核创建时间
 * @property string $withdraw_payment_created_at  支付请求时间
 * @property string $withdraw_payment_info        支付请求信息
 * @property string $withdraw_updated_at          更新时间
 * @property string $withdraw_finished_at         交易完成时间
 * @property string $withdraw_biz_type            业务类型
 * @property int $withdraw_split            拆分付款
 * @property-read User $creator
 */
class Withdraw extends ActiveRecord
{
    public const TRANSFER_TYPE_ACCOUNT_INTER_TRANSFER = 'account_inter_transfer';
    public const TRANSFER_TYPE_ACCOUNT_ACROSS_TRANSFER = 'account_across_transfer';
    public const TRANSFER_TYPE_TRANSFER_TO_CARD = 'transfer_to_card';
    public const TRANSFER_TYPE_WITHDRAWAL_TO_CARD = 'withdrawal_to_card';

    public const TRANSFER_TYPE_LIST = [
        self::TRANSFER_TYPE_ACCOUNT_INTER_TRANSFER => '同租户账户间转账',
        self::TRANSFER_TYPE_ACCOUNT_ACROSS_TRANSFER => '跨租户账户间转账',
        self::TRANSFER_TYPE_TRANSFER_TO_CARD => '转账到实卡',
        self::TRANSFER_TYPE_WITHDRAWAL_TO_CARD => '提现到绑定卡',
    ];

    public const STATUS_WAIT_AUTH = -1;
    public const STATUS_NEW = 0;
    public const STATUS_PROCESSING = 1;
    public const STATUS_SUCCESS = 2;
    public const STATUS_FAILURE = 3;
    public const STATUS_PAYMENT_FAILED = 4;
    public const STATUS_PAYMENT_ING = 5;
    public const STATUS_VOID = 6;

    public const OA_AGREE_STATUS = 9;  //OA审核通过
    public const OA_REFUSE_STATUS = -3;//OA审核拒绝

    public const STATUS = [
        self::STATUS_WAIT_AUTH => '待授权',
        self::STATUS_PROCESSING => '审核中',         // OA审批中
        self::STATUS_NEW => '待处理',           // 初始状态
        self::STATUS_PAYMENT_ING => '代付处理中',         // 请求代付接口成功，等待回调
        self::STATUS_SUCCESS => '成功',            // oa审批通过并且请求代付接口成功
        self::STATUS_PAYMENT_FAILED => '失败',            // 请求代付接口失败
        self::STATUS_FAILURE => '审核拒绝',        // OA审批不通过
        self::STATUS_VOID => '废弃',
    ];

    public const BIZ_TYPE_DEFAULT = ''; // 适用于放款业务
    public const BIZ_TYPE_WITHDRAW = 'withdraw'; // 提现
    public const BIZ_TYPE_SALARY = 'salary'; // 发工资
    public const BIZ_TYPE_GREY = 'grey'; // 灰度

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%withdraw}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db');
    }


    public static function receiveList()
    {
        $config = KeyValue::take('withdraw_receive_config');

        $accounts = [];
        foreach ($config as $item) {
            $memo = empty($item['memo']) ? '' : sprintf('(%s)', $item['memo']);
            $accounts[$item['receive_account']] = $item['receive_account'] . $memo;
        }

        return $accounts;
    }


    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['withdraw_split'], 'default', 'value' => 1],
            [
                [
                    'withdraw_merchant_key',
                    'withdraw_trade_no',

                    'withdraw_transfer_type',
                    'withdraw_channel',
                    'withdraw_amount',
                    'withdraw_biz_type',
                    'withdraw_reason',
                    'withdraw_receive_account_number',
                ],
                'required',
            ],
            [
                [
                    'withdraw_receive_account',
                    'withdraw_receive_bank_code',
                ],
                'required',
                'when' => function () {
                    return $this->withdraw_transfer_type === self::TRANSFER_TYPE_TRANSFER_TO_CARD;
                },
            ],
            [['withdraw_receive_account_address'], 'safe'],
            [
                ['withdraw_receive_account', 'withdraw_receive_bank_code', 'withdraw_type', 'withdraw_virtual_account'],
                'default',
                'value' => '',
            ],
            [
                ['withdraw_amount'],
                'integer',
                'min' => 100,
                'tooSmall' => vsprintf('金额不能低于%s%s', [
                    Yii::$app->getFormatter()->asFormatAmount(100, true),
                    FormatterBehavior::currencyUnit(),
                ]),
            ],
            [['withdraw_receive_bank_code'], 'string', 'max' => 16],
            [
                ['withdraw_reason'],
                'match',
                'pattern' => '/[\x{4e00}-\x{9fa5}]/u',
                'not' => true,
                'message' => '不能包含汉字',
            ],
            [
                [
                    'withdraw_created_at',
                    'withdraw_updated_at',
                    'withdraw_procedure_id',
                    'withdraw_oa_id',
                    'withdraw_virtual_account',
                    'withdraw_oa_created_at',
                    'withdraw_payment_created_at',
                    'withdraw_payment_info',
                    'withdraw_create_user',
                    'withdraw_create_user_email',
                ],
                'safe',
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_id' => 'ID',
            'withdraw_channel' => '出款账户',
            'withdraw_transfer_type' => '交易类型',
            'withdraw_trade_no' => '交易流水号',
            'withdraw_virtual_account' => '出款虚户',

            'withdraw_receive_account' => '账户名称',
            'withdraw_receive_account_address' => '账户地址',
            'withdraw_receive_account_number' => '收款卡号',
            'withdraw_receive_bank_code' => '银行编码',
            'withdraw_procedure_id' => '制单流程ID',
            'withdraw_oa_id' => 'OA审核单ID',
            'withdraw_type' => '提现类型',
            'withdraw_status' => '状态',
            'withdraw_amount' => '金额',
            'withdraw_reason' => '原因',
            'withdraw_created_at' => '时间',
            'withdraw_create_user' => '操作人',
            'withdraw_finished_at' => '完成时间',
            'withdraw_biz_type' => '业务类型',
            'withdraw_split' => '拆分付款',
        ];
    }

    public function getCreator(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'withdraw_create_user']);
    }

    public static function generateRandomNo(): string
    {
        return vsprintf('%s%08d', [
            Carbon::now()->format('YmdHisu'),
            random_int(0, ********),
        ]);
    }

    /**
     * @param int $totalAmount 提现总金额
     * @param string $channel 提现通道
     *
     * @return int[] 按限额拆分后的金额列表
     * @throws KVException
     * @throws RandomException
     */
    public static function splitAmountByChannel(int $totalAmount, string $channel): array
    {
        if ($totalAmount <= 0) {
            throw new RuntimeException('提现金额必须大于0');
        }

        $config = KeyValue::take('withdraw_split_config');

        // 单笔金额范围
        $min = (int)($config['channel_config'][$channel]['min_amount'] ?? $config['min_amount'] ?? 200000);
        $max = (int)($config['channel_config'][$channel]['max_amount'] ?? $config['max_amount'] ?? 0);
        if ($max < $min) {
            throw new RuntimeException(vsprintf('拆分金额范围配置错误, 单笔最小金额不能大于最大金额: [%s, %s]', [
                Yii::$app->formatter->format($min, 'amount'),
                Yii::$app->formatter->format($max, 'amount'),
            ]));
        }

        // 随机范围 [1/10000, 20/10000]
        $randomMin = floor($max * .0001);
        $randomMax = floor($max * .001);

        $amounts = [];
        $balance = $totalAmount;
        while ($balance > $max) {
            $curAmount = $max - random_int($randomMin, $randomMax) * 100;
            $balance -= $curAmount;
            $amounts[] = $curAmount;
        }

        foreach ($amounts as $i => $amount) {
            if ($balance >= $min) {
                break;
            }

            $allocatedAmount = min($min - $balance, $amount - $min);

            $amounts[$i] -= $allocatedAmount;
            $balance += $allocatedAmount;
        }

        $amounts[] = $balance;

        if ($balance < $min) {
            throw new RuntimeException(vsprintf('拆分后金额: %s，不满足范围[%s, %s]限制。', [
                Json::encode($amounts),
                Yii::$app->formatter->format($min, 'amount'),
                Yii::$app->formatter->format($max, 'amount'),
            ]));
        }

        return $amounts;
    }

    /**
     * @return array
     */
    public static function withdrawChannelList(): array
    {
        return PaymentAccount::find()
            ->where([
                'account_status' => PaymentAccount::STATUS_VALID,
                'account_type' => 'rdl',
            ])
            ->select('account_no')
            ->indexBy('account_no')
            ->column();
    }

    public static function globalAccountList(): array
    {
        return PaymentAccount::find()
            ->where([
                'account_status' => PaymentAccount::STATUS_VALID,
            ])
            ->select([
                'account_card_no' => 'ext_account_no',
                'account_bank' => 'account_no',
                'name' => 'account_name',
                'type' => new Expression("'global_account'"),
            ])
            ->indexBy('account_bank')
            ->asArray()
            ->all();
    }

    public static function globalEntityAccountList(string $accountNo = null): array
    {
        $accounts = AccountWithdrawalCard::find()
            ->where(['status' => AccountWithdrawalCard::STATUS_VALID])
            ->andFilterWhere(['account_no' => $accountNo])
            ->andWhere(['!=', 'withdrawal_card_number', ''])
            ->select([
                'account_name' => 'withdrawal_card_holder_name',
                'account_address' => 'withdrawal_card_holder_address',
                'account_bank' => 'withdrawal_card_code',
                'account_card_no' => 'withdrawal_card_number',
                'account_no',
                'type' => new Expression("'global_entity_account'"),
            ])
            ->indexBy(fn($account) => sprintf('%s:%s', $account['account_no'], $account['account_card_no']))
            ->asArray()
            ->all();

        $cacheKey = sha1(Json::encode($accounts));
        $cacheAccounts = Yii::$app->cache->get($cacheKey);
        if (empty($cacheAccounts)) {
            $hashList = [];
            foreach ($accounts as $account) {
                $hashList[] = $account['account_card_no'];
                $hashList[] = $account['account_name'];
                if (!empty($account['account_address'])) {
                    $hashList[] = $account['account_address'];
                }
            }

            $plaintexts = batchDecrypt($hashList, true, EncryptException::throwFunc());

            $cacheAccounts = array_map(static function ($account) use ($plaintexts) {
                $account['account_card_no'] = $plaintexts[$account['account_card_no']] ?? $account['account_card_no'];
                $account['account_name'] = $plaintexts[$account['account_name']] ?? $account['account_name'];
                if (!empty($account['account_address'])) {
                    $account['account_address'] = $plaintexts[$account['account_address']] ?? $account['account_address'];
                }

                $account['name'] = vsprintf('%s(%s) %s', [
                    $account['account_name'],
                    $account['account_card_no'],
                    $account['account_bank'],
                ]);

                return $account;
            }, $accounts);

            Yii::$app->cache->set($cacheKey, $cacheAccounts, 86400);
        }

        return $cacheAccounts;
    }
}
