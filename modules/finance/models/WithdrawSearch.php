<?php

namespace finance\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

class WithdrawSearch extends Withdraw
{
    public $startDate;
    public $endDate;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [
                [
                    'withdraw_status',
                    'withdraw_type',
                    'withdraw_channel',
                    'withdraw_receive_account',
                    'withdraw_receive_account_number',
                    'withdraw_trade_no',
                    'startDate',
                    'endDate',
                    'pendingTaskId',
                    'withdraw_oa_id',
                    'withdraw_procedure_id',
                    'withdraw_biz_type',
                ],
                'safe',
            ],
        ];
    }

    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['withdraw_id', 'withdraw_created_at'],
                'defaultOrder' => ['withdraw_created_at' => SORT_DESC],
            ],
        ]);
        $this->load($params);
        if (!$this->validate()) {
            return $dataProvider;
        }

        $query
            ->andFilterWhere([
                'withdraw_type' => $this->withdraw_type,
                'withdraw_status' => $this->withdraw_status,
                'withdraw_channel' => $this->withdraw_channel,
                'withdraw_receive_account_number' => $this->withdraw_receive_account_number,
                'withdraw_trade_no' => $this->withdraw_trade_no,
                'withdraw_oa_id' => $this->withdraw_oa_id,
                'withdraw_procedure_id' => $this->withdraw_procedure_id,
                'withdraw_biz_type' => $this->withdraw_biz_type,
            ])
            ->andFilterWhere(['>=', 'withdraw_created_at', $this->startDate])
            ->andFilterWhere(['<', 'withdraw_created_at', Carbon::parse($this->endDate)->addDay()->toDateString()])
            ->andFilterWhere(['like', 'withdraw_receive_account', $this->withdraw_receive_account]);

        return $dataProvider;
    }
}
