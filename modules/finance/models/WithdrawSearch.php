<?php

namespace finance\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

/**
 * @property PaymentWithdrawalPendingTasks $pendingTasks
 */
class WithdrawSearch extends Withdraw
{
    public $startDate;
    public $endDate;
    public $pendingTaskId;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [
                [
                    'withdraw_status',
                    'withdraw_type',
                    'withdraw_channel',
                    'withdraw_receive_account',
                    'withdraw_receive_number',
                    'withdraw_merchant_key',
                    'withdraw_trade_no',
                    'startDate',
                    'endDate',
                    'pendingTaskId',
                    'withdraw_oa_id',
                    'withdraw_procedure_id',
                ],
                'safe',
            ],
        ];
    }

    public function search($params): ActiveDataProvider
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['withdraw_id', 'withdraw_created_at'],
                'defaultOrder' => ['withdraw_created_at' => SORT_DESC],
            ],
        ]);
        $this->load($params);
        if (!$this->validate()) {
            return $dataProvider;
        }
        if ($this->pendingTaskId) {
            $withdrawIds = BusinessAssociationTable::find()
                ->where([
                    'main_id' => $this->pendingTaskId,
                    'business_type' => PaymentWithdrawalPendingTasks::BUSINESS_TYPE_OA_WITHDRAW,
                ])
                ->select('side_id')
                ->distinct()
                ->column();
            $query->andWhere([
                'withdraw_id' => $withdrawIds,
            ]);
        }
        $query
            ->with(['pendingTasks'])
            ->andFilterWhere([
                'withdraw_type' => $this->withdraw_type,
                'withdraw_status' => $this->withdraw_status,
                'withdraw_channel' => $this->withdraw_channel,
                'withdraw_receive_number' => $this->withdraw_receive_number,
                'withdraw_merchant_key' => $this->withdraw_merchant_key,
                'withdraw_trade_no' => $this->withdraw_trade_no,
                'withdraw_oa_id' => $this->withdraw_oa_id,
                'withdraw_procedure_id' => $this->withdraw_procedure_id,
            ])
            ->andFilterWhere(['>=', 'withdraw_created_at', $this->startDate])
            ->andFilterWhere(['<', 'withdraw_created_at', Carbon::parse($this->endDate)->addDay()->toDateString()])
            ->andFilterWhere(['like', 'withdraw_receive_account', $this->withdraw_receive_account]);

        return $dataProvider;
    }

    public function attributeLabels(): array
    {
        return array_merge(parent::attributeLabels(), ['pendingOaId' => 'OA流程单号']);
    }

    public function getPendingTasks(): ActiveQuery
    {
        return $this->hasOne(PaymentWithdrawalPendingTasks::class, ['id' => 'main_id'])
            ->via('businessAssociationTable', function (ActiveQuery $query) {
                $query->onCondition(['business_type' => PaymentWithdrawalPendingTasks::BUSINESS_TYPE_OA_WITHDRAW]);
            });
    }

    /**
     * @return ActiveQuery
     */
    public function getBusinessAssociationTable(): ActiveQuery
    {
        return $this->hasOne(BusinessAssociationTable::class, ['side_id' => 'withdraw_id']);
    }
}
