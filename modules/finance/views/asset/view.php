<?php

use backend\grid\MoneyDataColumn;
use finance\models\AccountRepay;
use finance\models\Asset;
use finance\models\AssetTran;
use finance\models\OperateAssetGrantAtLog;
use kvmanager\models\KeyValue;
use payment\models\WithdrawReceipt;
use repay\models\AssetDelay;
use repay\models\AssetExtend;
use repay\models\WithholdDetail;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\db\Expression;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model Asset */
/* @var $relationModel Asset */
/* @var $withdrawInfo array */
/* @var $table array */

$this->title = '资产详情-' . $model->asset_item_no;
$this->params['breadcrumbs'][] = ['label' => Yii::t('finance', 'Assets'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
$assetExtend = AssetExtend::find()->where([
    'asset_extend_asset_item_no' => $model->asset_item_no
])->indexBy('asset_extend_type')->column();
?>
<p>
    <?= Html::a(Yii::t('common', 'Go Back'), ['index'], ['class' => 'btn btn-default']) ?>
    <?php
    if ((Yii::$app->getUser()->can('UpdateAssetGrantAt')) && !OperateAssetGrantAtLog::find()->where(
            ['asset_item_no' => $model->asset_item_no]
        )->exists()) {
        echo Html::a(
            Yii::t('common', 'Change asset grant at'),
            ['change-asset-grant-at', 'id' => $model->asset_id],
            ['class' => 'btn btn-primary', 'id' => 'changeAssetGrantAt']
        );
    }
    ?>
</p>

<div class="row">
    <div class="col-md-4">
        <div class="box box-info">
            <div class="box-header with-border">
                <div class="box-title"><?= Yii::t('common', '基本信息') ?></div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body no-padding">
                <?= DetailView::widget([
                    'model' => $model,
                    'options' => [
                        'class' => 'table table-striped',
                    ],
                    'attributes' => [
                        'asset_item_no',
                        'asset_type',
                        'asset_sub_type',
                        'asset_period_type',
                        [
                            'attribute' => 'asset_period_count',
                            'label' => '周期',
                            'value' => function (Asset $asset) {
                                return $asset->asset_period_count . '期';
                            },
                        ],
                        [
                            'attribute' => 'asset_product_category',
                            'label' => '每期天数',
                            'format' => ['periodCategory'],
                            'value' => function (Asset $asset) {
                                return [
                                    $asset->asset_period_count,
                                    $asset->asset_product_category,
                                ];
                            },
                        ],
                        'asset_cmdb_product_number',
                        'asset_from_system',
                        [
                            'attribute' => 'asset_status',
                            'format' => ['in', Asset::statusList()],
                        ],
                        'asset_loan_channel',
                        'asset_interest_rate',
                        'asset_from_system_name',
                        'asset_due_bill_no',
                        'asset_from_app',
                        'asset_repayment_app',
                        [
                            'label' => '资产关联订单类型',
                            'value' => static function () use ($assetExtend) {
                                return $assetExtend['ref_order_type'] ?? '';
                            },
                        ],
                        [
                            'label' => '资产关联订单号',
                            'value' => static function () use ($assetExtend) {
                                return $assetExtend['ref_order_no'] ?? '';
                            },
                        ],
                        [
                            'label' => '放款订单号',
                            'value' => static function () use ($withdrawInfo) {
                                return $withdrawInfo['withdraw_record_channel_key'] ?? '';
                            },
                        ],
                        [
                            'label' => '通道流水号',
                            'value' => static function () use ($model) {
                                return WithdrawReceipt::find()->where([
                                    'withdraw_receipt_merchant_key' => $model->asset_item_no . 'w',
                                    'withdraw_receipt_status' => 2,
                                ])->select('withdraw_receipt_channel_inner_key')->scalar();
                            },
                        ],
                        [
                            'label' => '通道',
                            'value' => static function () use ($withdrawInfo) {
                                return $withdrawInfo['withdraw_record_channel'] ?? '';
                            },
                        ],
                    ],
                ]) ?>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="box box-warning">
            <div class="box-header with-border">
                <div class="box-title"><?= Yii::t('common', '费用概览') ?></div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body no-padding">
                <?= DetailView::widget([
                    'model' => $model,
                    'options' => [
                        'class' => 'table table-striped',
                    ],
                    'attributes' => [
                        [
                            'attribute' => 'asset_credit_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_principal_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_interest_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_fee_amount',
                            'format' => 'formatAmount',
                        ],
//                        [
//                            'attribute' => 'asset_tax_amount',
//                            'format' => 'formatAmount',
//                        ],
                        [
                            'attribute' => 'asset_granted_principal_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_withholding_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_total_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_decrease_principal_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_decrease_interest_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_decrease_fee_amount',
                            'format' => 'formatAmount',
                        ],
//                        [
//                            'attribute' => 'asset_decrease_tax_amount',
//                            'format' => 'formatAmount',
//                        ],
                        [
                            'attribute' => 'asset_late_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_decrease_late_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_repaid_principal_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_repaid_interest_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_repaid_fee_amount',
                            'format' => 'formatAmount',
                        ],
//                        [
//                            'attribute' => 'asset_repaid_tax_amount',
//                            'format' => 'formatAmount',
//                        ],
                        [
                            'attribute' => 'asset_repaid_late_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_repaid_amount',
                            'format' => 'formatAmount',
                        ],
                        [
                            'attribute' => 'asset_balance_amount',
                            'value' => static function (Asset $model) {
                                return Yii::$app->getFormatter()->asFormatAmount(
                                    $model->asset_total_amount - ($model->asset_repaid_amount + $model->asset_decrease_principal_amount + $model->asset_decrease_interest_amount + $model->asset_decrease_fee_amount + $model->asset_decrease_late_amount)
                                );
                            },
                        ],
                    ],
                ]) ?>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="box box-success">
            <div class="box-header with-border">
                <div class="box-title"><?= Yii::t('common', '时间轴') ?></div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body no-padding">
                <?= DetailView::widget([
                    'model' => $model,
                    'options' => [
                        'class' => 'table table-striped',
                    ],
                    'attributes' => [
                        'asset_grant_at:VDateTime',
                        'asset_effect_at:VDateTime',
                        'asset_actual_grant_at:VDateTime',
                        'asset_due_at:VDateTime',
                        [
                            'label' => '结清时间',
                            'value' => function (Asset $model) {
                                if ($model->asset_status != Asset::STATUS_PAYOFF) {
                                    return '-';
                                }

                                return $model->getActualPayoffAt();
                            },
                        ],
                    ],
                ]) ?>
            </div>
        </div>
        <div class="box box-success">
            <div class="box-header with-border">
                <div class="box-title"><?= Yii::t('common', '用户信息') ?></div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body no-padding">
                <?=
                DetailView::widget([
                    'model' => $model,
                    'options' => [
                        'class' => 'table table-striped',
                    ],
                    'attributes' => $table,
                ]) ?>
            </div>
        </div>
    </div>
</div>

<?php
$layout = <<<HTML
<div class="box-header with-border" id="repayPlans">
    <div class="box-title">还款计划</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;

echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getRepayPlans(),
        'pagination' => false,
        'sort' => [
            'attributes' => ['asset_tran_period'],
            'defaultOrder' => ['asset_tran_period' => SORT_ASC],
        ],
    ]),
    'columns' => [
        'asset_tran_period',
        [
            'attribute' => 'asset_tran_type',
            'format' => ['in', AssetTran::typeList()],
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'asset_tran_amount',
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'asset_tran_decrease_amount',
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'asset_tran_repaid_amount',
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'asset_tran_balance_amount',
            'value' => static function ($model) {
                /**@var  AssetTran $model */
                return $model->asset_tran_total_amount - ($model->asset_tran_repaid_amount + $model->asset_tran_decrease_amount);
            },
        ],
        [
            'class' => MoneyDataColumn::class,
            'attribute' => 'asset_tran_total_amount',
        ],
        [
            'attribute' => 'asset_tran_status',
            'format' => ['in', AssetTran::statusList()],
        ],
        'asset_tran_due_at:VDateTime',
        'asset_tran_finish_at:VDateTime',
    ],
]);
if ($relationModel) {
    $relationHyperlinks = Html::a($relationModel->asset_item_no, ['view', 'id' => $relationModel->asset_id]);
    $layout = <<<HTML
<div class="box-header with-border" id="repayPlans">
    <div class="box-title">关联资产还款计划{$relationHyperlinks}</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;

    echo GridView::widget([
        'layout' => $layout,
        'dataProvider' => new ActiveDataProvider([
            'query' => $relationModel->getRepayPlans(),
            'pagination' => false,
            'sort' => [
                'attributes' => ['asset_tran_period'],
                'defaultOrder' => ['asset_tran_period' => SORT_ASC],
            ],
        ]),
        'columns' => [
            'asset_tran_period',
            [
                'attribute' => 'asset_tran_type',
                'format' => ['in', AssetTran::typeList()],
            ],
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'asset_tran_amount',
            ],
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'asset_tran_decrease_amount',
            ],
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'asset_tran_repaid_amount',
            ],
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'asset_tran_balance_amount',
                'value' => static function ($model) {
                    /**@var  AssetTran $model */
                    return $model->asset_tran_total_amount - ($model->asset_tran_repaid_amount + $model->asset_tran_decrease_amount);
                },
            ],
            [
                'class' => MoneyDataColumn::class,
                'attribute' => 'asset_tran_total_amount',
            ],
            [
                'attribute' => 'asset_tran_status',
                'format' => ['in', AssetTran::statusList()],
            ],
            'asset_tran_due_at:VDateTime',
            'asset_tran_finish_at:VDateTime',
        ],
    ]);
}

$layout = <<<HTML
<div class="box-header with-border" id="repayDetails">
    <div class="box-title">还款明细</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;

echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getRepayLog(),
        'pagination' => false,
        'sort' => [
            'attributes' => ['account_repay_log_order_period'],
            'defaultOrder' => ['account_repay_log_order_period' => SORT_ASC],
        ],
    ]),
    'columns' => [
        'account_repay_log_order_period',
        [
            'attribute' => 'account_repay_log_operate_type',
            'format' => ['in', AccountRepay::operationTypeList()],
        ],
        [
            'attribute' => 'account_repay_log_amount',
            'class' => MoneyDataColumn::class,
        ],
        'account_repay_log_order_no',
        [
            'attribute' => 'account_repay_log_tran_type',
            'format' => ['in', AccountRepay::tranTypeList()],
        ],
        'account_repay_log_comment',
    ],
]);

$layout = <<<HTML
<div class="box-header with-border" id="repayDetails">
    <div class="box-title">展期信息</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;
echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getAssetDelays(),
        'pagination' => false,
        'sort' => [
            'attributes' => ['asset_delay_id'],
            'defaultOrder' => ['asset_delay_id' => SORT_ASC],
        ],
    ]),
    'columns' => [
        [
            'attribute' => 'asset_delay_withhold_serial_no',
        ],
        [
            'attribute' => 'asset_delay_item_no',
        ],
        [
            'attribute' => 'asset_delay_period',
        ],
        [
            'attribute' => 'asset_delay_days',
        ],
        [
            'attribute' => 'asset_delay_apply_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'asset_delay_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'asset_delay_start_at',
            'format' => 'VDate',
        ],
        [
            'attribute' => 'asset_delay_end_at',
            'format' => 'VDate',
        ],
        [
            'attribute' => 'asset_delay_status',
        ],
        [
            'attribute' => 'asset_delay_pay_at',
            'format' => 'VDateTime',
        ],
    ],
]);

$layout = <<<HTML
<div class="box-header with-border">
    <div class="box-title">代扣记录</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;
echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => new ArrayDataProvider([
        'allModels' => WithholdDetail::getWithholdList($model->asset_item_no),
    ]),
    'columns' => [
        [
            'label' => '序列号',
            'attribute' => 'serial_no',
            'format' => function ($serialNo) {
                return Html::a($serialNo, [
                    'repay-record/detail',
                    'serialNo' => $serialNo,
                ], [
                    'target' => '_blank',
                ]);
            },
        ],
        [
            'label' => '金额',
            'attribute' => 'amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '通道',
            'attribute' => 'channel',
        ],
        [
            'label' => '通道编码',
            'attribute' => 'channel_key',
        ],
        [
            'label' => '支付产品',
            'attribute' => 'payment_type',
        ],
        [
            'label' => '支付方式',
            'attribute' => 'payment_option',
        ],
        [
            'label' => '状态',
            'attribute' => 'status',
        ],
        [
            'label' => '付款码',
            'attribute' => 'extend_info',
            'format' => static function ($data) {
                try {
                    $config = KeyValue::take('asset_view_payment_code');
                } catch (Throwable $e) {
                    $config = [];
                }
                $data = (array)json_decode($data, true);
                $data = $data['paysvrRes'] ?? '';
                $data = (array)json_decode($data, true);
                $type = $data['type'] ?? '';
                $data = (array)json_decode($data['content'] ?? '', true);
                $attr = $config[$type] ?? '';
                if ($attr) {
                    return $data[$attr] ?? '';
                }

                return '';
            },
        ],
        [
            'label' => '子状态',
            'attribute' => 'sub_status',
            'format' => [
                'in',
                [
                    'normal' => '正常',
                    'payment_cancel' => '协议支付取消',
                    'agreement' => '协议支付',
                    'user_cancel' => '用户取消',
                    'asset_delay' => '用户展期',
                ],
            ],
        ],
        [
            'label' => '展期期限',
            'value' => static function ($row) {
                if (($row['sub_status'] ?? null) !== 'asset_delay') {
                    return '-';
                }

                return AssetDelay::find()->where([
                    'asset_delay_withhold_serial_no' => $row['serial_no'] ?? '',
                ])->select([
                    new Expression('CONCAT(DATE(asset_delay_start_at), \' - \', DATE(asset_delay_end_at))'),
                ])->scalar();
            },
        ],
        [
            'label' => '完成时间',
            'attribute' => 'finish_at',
        ],
        //        'user_name:DDecrypt:姓名',
        'user_phone:DDecrypt:手机号',
    ],
]);
echo $this->render('_asset-loan-record', [
    'model' => $model,
    'title' => '放款记录',
]);

if (!YII_ENV_PROD || Yii::$app->user->can('IndividualInfoManage')) { ?>
    <div class="box box-primary">
        <div class="loading-mask" id="loading-mask">数据加载中...</div>
        <div class="box-header with-border" id="enclosureDetails">
            <div class="box-title">资产附件</div>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="box-body table-responsive no-padding">
            <table class="table table-hover table-striped">
                <thead>
                <tr>
                    <th data-idx="0">附件类型</th>
                    <th data-idx="1">附件状态</th>
                    <th data-idx="2">操作</th>
                </tr>
                </thead>
                <tbody id="enclosureBody">
                <tr>
                    <td colspan="6">
                        <div class="empty">没有找到数据。</div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <?php
}
$url = Url::to(['contract-sign', 'itemNo' => $model->asset_item_no]);

$js = <<<JS
const mask = $('#loading-mask'),
      enclosureBody = $('#enclosureBody');

if(mask.length){
    mask.addClass('is-active');
    //延迟加载执行，避免同步执行请求。导致页面一直在加载。
    setTimeout(function (){
        $.ajax({
            type: "POST",
            url: "$url",
            timeout: 30000, // 设置超时时间为5秒
            error: function(jqXHR, textStatus, errorThrown) {
               layer.msg("网络错误或请求超时，请检查网络或稍后再试。");
           },
           success: function (response) {
              let {code,data,message} = response 
                   if(code !== 0) {
                      layer.alert(message,{
                            title: '获取合同附件发生错误,请刷新页面重试',
                            icon: 2,
                            shade: 0.3,
                            scrollbar: false,
                            btnAlign: 'c',
                            closeBtn: false,
                            resize: false,
                            btn: ['关闭', '关闭且重新加载页面'],
                            btn1: function(index, layero, that){
                               layer.close(index);
                           },
                           btn2: function(index, layero, that){
                               layer.close(index);
                               window.location.reload();
                           }
                     });
                 } else  {
                      // 处理返回的数据
                     enclosureBody.empty()
                      // 遍历返回的数据
                     $.each(data, function(index, item) {
                          let rowContent = "<tr><td>" + item.contract_name + "</td><td>有效</td><td><a href=" + item.contract_url + " target='_blank'>下载</a></td></tr>";
                         // 将行添加到表格中
                     $("#enclosureBody").append(rowContent);
              });
       }
        mask.removeClass('is-active');
     }
    });
    },1000)
}
$('#changeAssetGrantAt').on('click', function () {
    layer.open({
        type: 2,
        title: '更新资产放款时间',
        shadeClose: false,
        area: ['50%', '90%'],
        content: $(this).attr('href')
    })
    return false;
});
JS;

$this->registerJs($js); ?>
<style>
    .loading-mask {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all .3s ease;
    }

    .loading-mask.is-active {
        opacity: 1;
        visibility: visible;
    }
</style>
