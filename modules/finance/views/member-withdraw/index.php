<?php

use backend\grid\ActionColumn;
use backend\grid\MoneyDataColumn;
use payment\models\MemberCard;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $searchModel MemberCard */
/* @var $dataProvider ArrayDataProvider */

$this->title = Yii::t('payment', '提现卡管理');
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{view} {update}',
            'visibleButtons' => [
                'update' => function (MemberCard $memberCard) {
                    return $memberCard->status === 0;
                },
            ],
        ],

        [
            'attribute' => 'user_name',
            'format' => ['DDecrypt', true],
        ],
        [
            'attribute' => 'account_type',
            'format' => ['in', MemberCard::accountTypeList(), '-'],
        ],
        [
            'attribute' => 'account_no',
            'format' => ['DDecrypt', true],
        ],
        [
            'attribute' => 'large_account',
            'format' => ['in', ['否', '是'], '-'],
        ],
        [
            'attribute' => 'max_withdraw_amount',
            'class' => MoneyDataColumn::class,
        ],
        'user_uuid',
        'card_uuid',
        [
            'attribute' => 'status',
            'format' => ['in', MemberCard::STATUS_LIST, '-'],
        ],
        [
            'attribute' => 'member_type',
            'format' => ['in', MemberCard::MEMBER_TYPE_LIST, '-'],
        ],
        'bank_code',
        [
            'attribute' => 'email',
            'format' => ['DDecrypt', true],
        ],
        [
            'attribute' => 'id_type',
            'format' => ['in', MemberCard::idTypeList(), '-'],
        ],
        [
            'attribute' => 'id_num',
            'format' => ['DDecrypt', true],
        ],
        [
            'attribute' => 'address',
            'format' => ['DDecrypt', true],
        ],
        [
            'attribute' => 'receiver_type',
            'format' => ['in', MemberCard::RECEIVER_TYPE_LIST, '-'],
        ],
    ],
]);
