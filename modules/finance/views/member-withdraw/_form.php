<?php

use backend\widgets\MoneyInput;
use kartik\widgets\DepDrop;
use payment\models\MemberCard;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model MemberCard */

$memberTypeList = MemberCard::MEMBER_TYPE_LIST;
if (Yii::$app->getRequest()->get('payment')) {
    unset($memberTypeList[MemberCard::MEMBER_TYPE_WITHDRAW]);
} else {
    unset($memberTypeList[MemberCard::MEMBER_TYPE_SALARY], $memberTypeList[MemberCard::MEMBER_TYPE_GREY]);
}

?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= Html::activeHiddenInput($model, 'user_uuid') ?>
    <?= Html::activeHiddenInput($model, 'card_uuid') ?>
    <?= Html::activeHiddenInput($model, 'member_type') ?>

    <?= $form->field($model, 'user_name') ?>

    <?= $form->field($model, 'account_type')->widget(Select2::class, [
        'data' => MemberCard::accountTypeList(),
        'pluginOptions' => [],
        'options' => [
            'id' => 'account_type-input',
            'prompt' => $model->getAttributeLabel('account_type'),
        ],
    ]) ?>


    <?= $form->field($model, 'bank_code')->widget(DepDrop::class, [
        'type' => DepDrop::TYPE_SELECT2,
        'select2Options' => [
            'theme' => 'default',
            'hideSearch' => false,
        ],
        'options' => [
            'id' => 'group-input',
            'placeholder' => '请选择...',
        ],
        'pluginOptions' => [
            'depends' => ['account_type-input'],
            'initDepends' => ['account_type-input'],
            'initialize' => true,
            'params' => [],
            'placeholder' => '请选择...',
            'url' => Url::to(['bank-code-list', 'default' => $model->bank_code]),
        ],
    ]) ?>


    <?= $form->field($model, 'account_no') ?>

    <?= $form->field($model, 'large_account')->widget(Select2::class, [
        'data' => ['否', '是'],
        'hideSearch' => true,
        'options' => [
            'prompt' => $model->getAttributeLabel('large_account'),
        ],
    ]) ?>

    <?= $form->field($model, 'max_withdraw_amount')->widget(MoneyInput::class) ?>

    <?= $form->field($model, 'mobile') ?>

    <?= $form->field($model, 'email') ?>

    <?= $form->field($model, 'id_num') ?>

    <?= $form->field($model, 'id_type')->widget(Select2::class, [
        'data' => MemberCard::idTypeList(),
        'pluginOptions' => [],
        'hideSearch' => false,
        'options' => [
            'prompt' => $model->getAttributeLabel('id_type'),
        ],
    ]) ?>

    <?= $form->field($model, 'address') ?>

    <?= $form->field($model, 'receiver_type')->widget(Select2::class, [
        'data' => MemberCard::RECEIVER_TYPE_LIST,
        'pluginOptions' => [],
        'options' => [
            'prompt' => $model->getAttributeLabel('receiver_type'),
        ],
    ]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
