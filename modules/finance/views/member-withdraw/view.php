<?php

use backend\grid\MoneyDataColumn;
use payment\models\MemberCard;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model MemberCard */

$this->title = $model->user_uuid;
$this->params['breadcrumbs'][] = ['label' => Yii::t('payment', '提现卡管理'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title"><?= Yii::t('payment', 'Detail') ?></div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options' => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                [
                    'attribute' => 'user_name',
                    'format' => ['DDecrypt', true],
                ],
                [
                    'attribute' => 'account_type',
                    'format' => ['in', MemberCard::accountTypeList()],
                ],
                [
                    'attribute' => 'account_no',
                    'format' => ['DDecrypt', true],
                ],
                [
                    'attribute' => 'large_account',
                    'format' => ['in', ['否', '是'], '-'],
                ],
                [
                    'attribute' => 'max_withdraw_amount',
                    'format' => ['f2y',  true],
                ],
                'user_uuid',
                'card_uuid',
                'bank_code',
                [
                    'attribute' => 'mobile',
                    'format' => ['DDecrypt', true],
                ],
                [
                    'attribute' => 'email',
                    'format' => ['DDecrypt', true],
                ],
                [
                    'attribute' => 'status',
                    'format' => ['in', MemberCard::STATUS_LIST],
                ],
                [
                    'attribute' => 'id_type',
                    'format' => ['in', MemberCard::idTypeList(), '-'],
                ],
                [
                    'attribute' => 'id_num',
                    'format' => ['DDecrypt', true],
                ],
                [
                    'attribute' => 'address',
                    'format' => ['DDecrypt', true],
                ],
                [
                    'attribute' => 'receiver_type',
                    'format' => ['in', MemberCard::RECEIVER_TYPE_LIST, '-'],
                ],
            ],
        ]) ?>
    </div>
</div>
