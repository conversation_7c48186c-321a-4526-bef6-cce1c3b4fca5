<?php

use finance\models\PaymentWithdrawalPendingTasks;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

use function waterank\audit\config;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel PaymentWithdrawalPendingTasks */

$this->title = '支付通道出金待办';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{view} {submit} {replenishment}',
            'buttons' => [
                'view' => static function ($url, PaymentWithdrawalPendingTasks $model) {
                    return ActionColumn::newButton('查看提现进度', [
                        'withdraw/index',
                        'pendingTaskId' => $model->id,
                    ], [
                        'class' => 'btn-primary',
                        'target' => '_blank',
                    ]);
                },
                'submit' => static function ($url, PaymentWithdrawalPendingTasks $model) {
                    return ActionColumn::newButton('提交', [
                        'withdraw/new-from-oa',
                        'pendingTasksId' => $model->id,
                        'pendingOaId' => $model->oa_id,
                        'view' => PaymentWithdrawalPendingTasks::isSubmitToCreate() ? 'create-oa' : 'new-oa',
                    ], [
                        'class' => 'btn-twitter',
                        'target' => '_blank',
                    ]);
                },
                'replenishment' => static function ($url, PaymentWithdrawalPendingTasks $model) {
                    return ActionColumn::newButton('补录提现', [
                        '/payment/channel-offline-trade/withdraw',
                        'pendingTaskId' => $model->id,
                        'oaId' => $model->oa_id,
                        'type' => $model->fund_direction,
                    ], [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
            ],
            'visibleButtons' => [
                'replenishment' => static function (PaymentWithdrawalPendingTasks $model) {
                    return $model->successAmount < $model->payment_amount;
                },
            ],
        ],
        [
            'attribute' => 'tenant_id',
            'format' => ['in', config('paymentPendingTasks.tenantName', [])],
        ],
        'oa_id',
        'oa_create_at',
        [
            'label' => '付款金额',
            'class' => MoneyDataColumn::class,
            'attribute' => 'payment_amount',
        ],
        [
            'label' => '付款成功金额',
            'class' => MoneyDataColumn::class,
            'attribute' => 'successAmount',
        ],
        'currency',
        //        'payment_from',
        'payee',
        'payee_account_number',
        'payee_account_name',
        'payee_bank',
        'fund_direction',
        [
            'attribute' => 'status',
            'format' => ['in', PaymentWithdrawalPendingTasks::STATUS_TEXT],
            'value' => static function (PaymentWithdrawalPendingTasks $model) {
                if ($model->successAmount >= $model->payment_amount) {
                    $status = PaymentWithdrawalPendingTasks::STATUS_SUCCESS;
                } else {
                    $status = PaymentWithdrawalPendingTasks::STATUS_ING;
                }

                return $status;
            },
        ],
        [
            'label' => 'OA出金审核单号',
            'format' => 'raw',
            'value' => static function (PaymentWithdrawalPendingTasks $model) {
                $oaIdList = $model->getWithdraw()
                    ->select('withdraw_oa_id')
                    ->distinct()
                    ->column();

                return implode(',', array_filter($oaIdList));
            },
        ],
    ],
]);
