<?php

use datasource\assets\VueAsset;
use Diff\Assets\DiffAsset;
use finance\approval\BizPartnerApproval;
use finance\models\BizPartner;
use finance\models\BizPartnerSearch;
use waterank\audit\models\ApprovalEntries;
use yii\web\View;

/**
 * @var View $this
 * @var BizPartnerApproval $approval
 * @var ApprovalEntries $entries
 * @var string $actions
 */

DiffAsset::register($this);
VueAsset::register($this);

/**
 * @param $attribute
 * @param $format
 *
 * @return string
 */
$renderAttribute = static function ($attribute, $format = null) use ($approval) {
    $template = <<<HTML
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">%s</label>
                    <span class="form-control bg-gray-light" style="%s">%s</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">%s(旧值)</label>
                    <span class="form-control bg-gray-light">%s</span>
                </div>
            </div>
        </div>
HTML;

    $label = (new BizPartnerSearch())->getAttributeLabel($attribute);

    $value = $approval->getData('new.' . $attribute);
    $oldValue = $approval->getData('old.' . $attribute);
    if ($format) {
        $value = Yii::$app->getFormatter()->format($value, $format);
        $oldValue = Yii::$app->getFormatter()->format($oldValue, $format);
    }

    return vsprintf($template, [
        $label,
        $value == $oldValue ? '' : 'background-color: #e6ffec',
        $value,
        $label,
        $oldValue,
    ]);
};

?>
<div class="box box-<?= ['create' => 'success', 'update' => 'warning', 'delete' => 'danger'][$approval->getData(
    'action'
)] ?>">
    <div class="box-header with-border">
        <div class="box-title">机构规则-<?= [
                'create' => '创建',
                'update' => '修改',
                'delete' => '删除',
            ][$approval->getData('action')] ?>申请
        </div>
    </div>
    <div class="box-body">
        <?= $renderAttribute('channel') ?>
        <?= $renderAttribute('admin_channel') ?>
        <?= $renderAttribute('channel_status', ['in', BizPartner::STATUS_LIST]) ?>
        <?= $renderAttribute('company_type', ['in', BizPartner::COMPANY_TYPE_LIST]) ?>
        <?= $renderAttribute('grant_start_at') ?>
        <?= $renderAttribute('desc') ?>
    </div>
    <div class="box-footer"><?= $actions ?></div>
</div>
