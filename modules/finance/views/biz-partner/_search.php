<?php

use finance\models\BizPartnerSearch;
use finance\models\BizPartner;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View $this
 * @var BizPartnerSearch $model
 */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-search"></i> 搜索</h3>
    </div>

    <div class="box-body">
        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'style' => 'min-width: 180px',
            ],
        ])->widget(Select2::class, [
            'data' => capitalChannelList(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '请选择资金方',
            ],
        ]) ?>

        <?= $form->field($model, 'channel_status', [
            'options' => [
                'style' => 'min-width: 180px',
            ],
        ])->widget(Select2::class, [
            'data' => BizPartner::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
                'multiple' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_status'),
            ],
        ]) ?>

        <?= $form->field($model, 'company_type', [
            'options' => [
                'style' => 'min-width: 180px',
            ],
        ])->widget(Select2::class, [
            'data' => BizPartner::COMPANY_TYPE_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('company_type'),
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('添加', ['create'], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end(); ?>
    </div>
</div>
