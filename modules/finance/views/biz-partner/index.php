<?php

use finance\models\BizPartner;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\helpers\Url;
use yii\web\View;
use function xlerr\adminlte\userFullName;

/**
 * @var View $this
 * @var ActiveDataProvider $dataProvider
 * @var ArrayDataProvider $auditDataProvider
 * @var BizPartner $searchModel
 */

$this->title = '机构规则管理';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

Yii::$app->getFormatter()->nullDisplay = '-';

if ($auditDataProvider->getTotalCount()) {
    echo GridView::widget([
        'dataProvider' => $auditDataProvider,
        'options' => [
            'class' => 'box box-warning',
        ],
        'layout' => '{items}',
        'rowOptions' => function (BizPartner $model) {
            return [
                'data' => [
                    'new' => $model->attributes,
                    'old' => $model->oldAttributes,
                ],
                'style' => 'background-color: ' . [
                        'create' => '#dff0d8',
                        'update' => '#fcf8e3',
                        'delete' => '#f2dede'
                    ][$model->audit->approval->getData('action')],
            ];
        },
        'columns' => [
            [
                'header' => '待审核数据',
                'class' => ActionColumn::class,
                'template' => '{preview} {audit} {undo}',
                'urlCreator' => function ($action, BizPartner $model) {
                    return Url::to([
                        $action,
                        'channel' => $model->channel
                    ]);
                },
                'buttons' => [
                    'preview' => function ($url) {
                        return DialogActionColumn::newButton('查看', $url, [
                            'class' => 'btn-info layer-dialog',
                        ]);
                    },
                    'audit' => static function ($url) {
                        return DialogActionColumn::newButton('审核', $url, [
                            'class' => 'btn-facebook layer-dialog',
                        ]);
                    },
                    'undo' => function ($url) {
                        return ActionColumn::newButton('撤销', $url, [
                            'class' => 'btn-danger',
                            'data' => [
                                'confirm' => '您确定要撤销该审核吗',
                                'method' => 'post',
                            ],
                        ]);
                    },
                ],
                'visibleButtons' => [
                    'audit' => function (BizPartner $model) {
                        return $model->audit->approvalEntries->auditable();
                    },
                    'undo' => function (BizPartner $model) {
                        return $model->audit->audit_creator_id === Yii::$app->getUser()->getId();
                    }
                ],
            ],
            'channel',
            'admin_channel',
            [
                'attribute' => 'channel_status',
                'format' => ['in', BizPartner::STATUS_LIST],
            ],
            'grant_start_at',
            [
                'attribute' => 'company_type',
                'format' => ['in', BizPartner::COMPANY_TYPE_LIST],
            ],
            'desc',
            [
                'label' => '操作人',
                'value' => function (BizPartner $model) {
                    return userFullName($model->audit->creator);
                }
            ],
            [
                'label' => '审核人',
                'value' => function (BizPartner $model) {
                    return userFullName($model->audit->approvalEntries->auditor);
                }
            ]
        ],
    ]);
}

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update} {delete} {audit_his}',
            'buttons' => [
                'audit_his' => static function ($url, BizPartner $model) {
                    return ActionColumn::newButton('审核历史', [
                        '/audit/approval-entries/index',
                        'audit_type' => 'BizPartnerApproval',
                        'start_date' => '2024-01-01',
//                        'business_key' => $model->channel,
                    ], [
                        'class' => 'btn-twitter layer-dialog',
                    ]);
                },
            ],
        ],
        'id',
        'channel',
        'admin_channel',
        [
            'attribute' => 'channel_status',
            'format' => ['in', BizPartner::STATUS_LIST],
        ],
        [
            'attribute' => 'grant_start_at',
        ],
        [
            'attribute' => 'company_type',
            'format' => ['in', BizPartner::COMPANY_TYPE_LIST],
        ],
        [
            'label' => '创建人',
            'attribute' => 'createUser',
            'format' => static fn($user) => $user ? userFullName($user) : '-',
        ],
        [
            'label' => '修改人',
            'attribute' => 'updateUser',
            'format' => static fn($user) => $user ? userFullName($user) : '-',
        ],
        'create_at',
        'update_at'
    ],
]);
