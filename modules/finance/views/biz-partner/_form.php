<?php


use finance\models\BizPartner;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View $this
 * @var BizPartner $model
 */

$form = ActiveForm::begin();
?>

<div class="box-body">
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'channel')->textInput([
                'readonly' => !$model->isNewRecord,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'admin_channel')->widget(Select2::class, [
                'data' => capitalChannelList(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => $model->getAttributeLabel('admin_channel'),
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'company_type')->widget(Select2::class, [
                'data' => BizPartner::COMPANY_TYPE_LIST,
                'options' => [
                    'prompt' => $model->getAttributeLabel('company_type'),
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                ],
            ]) ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'channel_status')->widget(Select2::class, [
                'data' => BizPartner::STATUS_LIST,
                'hideSearch' => true,
                'options' => [
                    'prompt' => $model->getAttributeLabel('channel_status'),
                ],
            ]) ?>
        </div>
        <div class="col-md-3">
            <?= $form->field($model, 'grant_start_at') ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <?= $form->field($model, 'desc')->widget(CodeEditor::class, [
                'clientOptions' => [
                    'mode' => CodeEditor::MODE_Text,
                    'minLines' => 3,
                    'maxLines' => 20,
                ],
            ]) ?>
        </div>
    </div>
</div>
<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
