<?php

use backend\behaviors\FormatterBehavior;
use backend\widgets\MoneyInput;
use finance\models\AccountTurnoverSupplement;
use finance\models\Asset;
use finance\models\Withdraw;
use kvmanager\models\KeyValue;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model  Withdraw */
/* @var $actionName */

$formatter = Yii::$app->getFormatter();

$withdrawSplitConfig = KeyValue::take('withdraw_split_config');
$withdrawSplitConfig['max_amount'] = $formatter->format($withdrawSplitConfig['max_amount'] ?? 0, 'formatAmount');
$withdrawSplitConfig['channel_config'] = (array)($withdrawSplitConfig['channel_config'] ?? []);
foreach ($withdrawSplitConfig['channel_config'] as &$config) {
    $config['max_amount'] = $formatter->format($config['max_amount'] ?? 0, 'formatAmount');
}

$form = ActiveForm::begin();
?>

<div class="box-body">


    <?= $form->field($model, 'withdraw_channel')->widget(Select2::class, [
        'data' => Asset::getWithdrawList(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'withdraw_type')->textInput(['readOnly' => true]) ?>

    <?= $form->field($model, 'withdraw_receive_account', [
        'addon' => [
            'append' => [
                'content' => Html::a('收款信息管理', ['account-manage'], [
                    'class' => 'btn btn-success btn-flat layer-dialog',
                ]),
                'asButton' => true,
            ],
        ],
    ])->widget(Select2::class, [
        'data' => Withdraw::receiveList(),
        'id' => 'withdraw_receive_account',
        'hideSearch' => false,
    ]) ?>
    <?= $form->field($model, 'withdraw_receive_number')->textInput([
        'value' => $model->withdraw_receive_account,
        'readOnly' => true,
    ]) ?>
    <?= $form->field($model, 'withdraw_receive_uuid')->textInput([
        'value' => $model->withdraw_receive_uuid,
        'readOnly' => true,
    ]) ?>
    <?= $form->field($model, 'withdraw_receive_userid')->textInput([
        'value' => $model->withdraw_receive_uuid,
        'readOnly' => true,
    ]) ?>

    <?= $form->field($model, 'withdraw_amount')->widget(MoneyInput::class) ?>
    <?= Html::tag(
        'p',
        vsprintf('注: 提现金额><span id="max_amount">%s</span>%s, 会拆分多笔任务提现', [
            $withdrawSplitConfig['max_amount'],
            FormatterBehavior::currencyUnit(),
        ]),
        [
            'class' => 'text-danger',
        ]
    ) ?>

    <?= $form->field($model, 'withdraw_reason')->textInput([
        'maxlength' => true,
    ]) ?>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php
ActiveForm::end(); ?>
<script>
    <?php $this->beginBlock('js') ?>
    const accountDom = $('#<?= Html::getInputId($model, 'withdraw_receive_account') ?>');
    const channelDom = $('#<?= Html::getInputId($model, 'withdraw_channel') ?>');
    const virtualDom = $('#<?= Html::getInputId($model, 'withdraw_virtual_account') ?>');
    const numberDom = $('#<?= Html::getInputId($model, 'withdraw_receive_number') ?>');
    const uuidDom = $('#<?= Html::getInputId($model, 'withdraw_receive_uuid') ?>');
    const useridDom = $('#<?= Html::getInputId($model, 'withdraw_receive_userid') ?>');
    const maxAmountBox = $('#max_amount'),
        amountSplitConfig = <?= json_encode($withdrawSplitConfig) ?>;
    accountDom.on('change', function () {
        let account = accountDom.val();
        //当发生change事件 收款号码 收款卡UUID 收款用户ID设置为空
        numberDom.val('')
        uuidDom.val('')
        useridDom.val('');
        $.post('<?= Url::to(['get-number'])?>', {
            account: account,
        }, function (res) {
            if (res.code !== 0) {
                numberDom.val(res.number);
                uuidDom.val(res.uuid);
                useridDom.val(res.userid);
            }
        });
    });
    channelDom.on('change', function () {
        let channel = channelDom.val();
        maxAmountBox.text(amountSplitConfig.channel_config[channel]?.max_amount ?? amountSplitConfig.max_amount ?? 0);
        $.post('<?= Url::to(['get-virtual-account'])?>', {
            channel: channel,
        }, function (res) {
            if (res.code !== 0) {
                virtualDom.val(res.name);
            }
        });
    });
    accountDom.trigger("change");
    channelDom.trigger("change");

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
