<?php

use finance\models\AccountTurnoverSupplement;
use finance\models\Asset;
use finance\models\Withdraw;
use finance\models\WithdrawSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;


/* @var $this View */
/* @var $model WithdrawSearch */
/* @var $form ActiveForm */
?>
<div class="box search">
    <div class="box-header with-border">
        <div class="box-title">搜索</div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= Html::activeHiddenInput($model, 'pendingTaskId') ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建结束时间',
            ],
        ]) ?>
        <?= $form->field($model, 'withdraw_status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data' => Withdraw::STATUS,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('withdraw_status'),
            ],
        ]) ?>
        <?= $form->field($model, 'withdraw_type', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 150px',
            ],
        ])->widget(Select2::class, [
            'data' => AccountTurnoverSupplement::withdrawTypes(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('withdraw_type'),
            ],
        ]) ?>
        <?= $form->field($model, 'withdraw_channel', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 340px',
            ],
        ])->widget(Select2::class, [
            'data' => Asset::getWithdrawList(),
            'hideSearch' => false,

            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('withdraw_channel'),
            ],
        ]) ?>

        <?= $form->field($model, 'withdraw_receive_account') ?>
        <?= $form->field($model, 'withdraw_receive_number') ?>
        <?= $form->field($model, 'withdraw_merchant_key') ?>
        <?= $form->field($model, 'withdraw_trade_no') ?>

        <?= $form->field($model, 'withdraw_oa_id') ?>
        <?= $form->field($model, 'withdraw_procedure_id') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']); ?>
        <?php ActiveForm::end() ?>
    </div>
</div>