<?php

use backend\grid\MoneyDataColumn;
use finance\models\BizType;
use finance\models\Withdraw;
use finance\models\WithdrawSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithdrawSearch */

$this->title = '提现记录';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

$canWithdrawRetry = Yii::$app->user->can('withdraw_retry');

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{view} {retry} {void}',
            'buttons' => [
                'retry' => static function ($url) {
                    return DialogActionColumn::newButton('重试', $url, [
                        'class' => 'btn-warning',
                        'data' => [
                            'confirm' => '确定重试吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
                'void' => static function ($url) {
                    return DialogActionColumn::newButton('废弃', $url, [
                        'class' => 'btn-danger',
                        'data' => [
                            'confirm' => '确定废弃吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'retry' => static function (Withdraw $model) use ($canWithdrawRetry) {
                    return $model->withdraw_status === Withdraw::STATUS_PAYMENT_FAILED && $canWithdrawRetry;
                },
                'void' => static function (Withdraw $model) use ($canWithdrawRetry) {
                    return $model->withdraw_status === Withdraw::STATUS_PAYMENT_FAILED && $canWithdrawRetry;
                },
            ],
        ],
        'withdraw_id',
        [
            'label' => 'OA流程单号',
            'value' => static function (WithdrawSearch $model) {
                return (string)($model->pendingTasks->oa_id ?? '-');
            },
        ],
        [
            'attribute' => 'withdraw_type',
        ],
        [
            'attribute' => 'withdraw_biz_type',
            'format' => ['in', BizType::getBizTypeList([BizType::FUND_FLOW_IN, BizType::FUND_FLOW_OUT])],
        ],
        [
            'attribute' => 'withdraw_amount',
            'class' => MoneyDataColumn::class,
        ],
        'withdraw_channel',
        'withdraw_receive_account',
        'withdraw_receive_number',
        'withdraw_merchant_key',
        'withdraw_trade_no',
        [
            'label' => 'OAID/PID',
            'value' => static function (WithdrawSearch $model) {
                if ($model->withdraw_oa_id) {
                    return 'OAID: ' . $model->withdraw_oa_id;
                }

                if ($model->withdraw_procedure_id) {
                    return 'PID: ' . $model->withdraw_procedure_id;
                }

                return '-';
            },
        ],
        'withdraw_created_at',
        'withdraw_finished_at:VDateTime',
        'withdraw_create_user',
        [
            'attribute' => 'withdraw_status',
            'format' => 'html',
            'value' => static function (Withdraw $model) {
                $error = '';
                if ($model->withdraw_status === Withdraw::STATUS_PAYMENT_FAILED) {
                    try {
                        $error = ArrayHelper::getValue(
                            Json::decode($model->withdraw_payment_info),
                            'error',
                            $model->withdraw_payment_info
                        );
                    } catch (Throwable $e) {
                        $error = $model->withdraw_payment_info;
                    }

                    $error = Html::tag('span', sprintf('(%s)', $error), [
                        'class' => 'text-danger',
                    ]);
                }

                return Withdraw::STATUS[$model->withdraw_status] . $error;
            },
        ],
    ],
]);
