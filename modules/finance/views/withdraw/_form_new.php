<?php

use backend\behaviors\FormatterBehavior;
use backend\widgets\MoneyInput;
use finance\models\AccountTurnoverSupplement;
use finance\models\Asset;
use finance\models\Withdraw;
use kvmanager\models\KeyValue;
use payment\models\MemberCard;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model  Withdraw */
/* @var $actionName */

$formatter = Yii::$app->getFormatter();

$withdrawSplitConfig = KeyValue::take('withdraw_split_config');
$withdrawSplitConfig['max_amount'] = $formatter->format($withdrawSplitConfig['max_amount'] ?? 0, 'formatAmount');
$withdrawSplitConfig['channel_config'] = (array)($withdrawSplitConfig['channel_config'] ?? []);
foreach ($withdrawSplitConfig['channel_config'] as &$config) {
    $config['max_amount'] = $formatter->format($config['max_amount'] ?? 0, 'formatAmount');
}

$memberCards = [];
$memberCardDropdown = [];
foreach (MemberCard::query() as $memberCard) {
    if ($memberCard->member_type === MemberCard::MEMBER_TYPE_WITHDRAW) {
        $userName = $memberCard->decrypt('user_name', true);
        $accountNo = $memberCard->decrypt('account_no', true);

        $key = $memberCard->user_uuid . ':' . $userName;

        $memberCards[$key] = [
            'user_uuid' => $memberCard->user_uuid,
            'card_uuid' => $memberCard->card_uuid,
            'account_no' => $accountNo,
        ];

        $memberCardDropdown[$key] = $key;
    }
}

$model->withdraw_biz_type = Withdraw::BIZ_TYPE_WITHDRAW;

$form = ActiveForm::begin();
?>

<div class="box-body">
    <?= Html::activeHiddenInput($model, 'withdraw_biz_type') ?>

    <?= $form->field($model, 'withdraw_channel')->widget(Select2::class, [
        'data' => Asset::getWithdrawList(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'withdraw_type')->textInput(['readOnly' => true]) ?>

    <?= $form->field($model, 'withdraw_receive_account', [
        'addon' => [
            'append' => [
                'content' => Html::a('提现卡管理', ['member-withdraw/index'], [
                    'class' => 'btn btn-success btn-flat',
                    'target' => '_blank',
                ]),
                'asButton' => true,
            ],
        ],
    ])->widget(Select2::class, [
        'data' => $memberCardDropdown,
        'id' => 'withdraw_receive_account',
        'hideSearch' => false,
    ]) ?>
    <?= $form->field($model, 'withdraw_receive_number')->textInput([
        'value' => $model->withdraw_receive_account,
        'readOnly' => true,
    ]) ?>
    <?= $form->field($model, 'withdraw_receive_uuid')->textInput([
        'value' => $model->withdraw_receive_uuid,
        'readOnly' => true,
    ]) ?>
    <?= $form->field($model, 'withdraw_receive_userid')->textInput([
        'value' => $model->withdraw_receive_uuid,
        'readOnly' => true,
    ]) ?>

    <?= $form->field($model, 'withdraw_amount')->widget(MoneyInput::class) ?>
    <?= Html::tag(
        'p',
        vsprintf('注: 提现金额><span id="max_amount">%s</span>%s, 会拆分多笔任务提现', [
            $withdrawSplitConfig['max_amount'],
            FormatterBehavior::currencyUnit(),
        ]),
        [
            'class' => 'text-danger',
        ]
    ) ?>

    <?= $form->field($model, 'withdraw_reason')->textInput([
        'maxlength' => true,
    ]) ?>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>

<script>
    <?php $this->beginBlock('js') ?>
    const accountDom = $('#<?= Html::getInputId($model, 'withdraw_receive_account') ?>'),
        channelDom = $('#<?= Html::getInputId($model, 'withdraw_channel') ?>'),
        virtualDom = $('#<?= Html::getInputId($model, 'withdraw_virtual_account') ?>'),
        numberDom = $('#<?= Html::getInputId($model, 'withdraw_receive_number') ?>'),
        uuidDom = $('#<?= Html::getInputId($model, 'withdraw_receive_uuid') ?>'),
        useridDom = $('#<?= Html::getInputId($model, 'withdraw_receive_userid') ?>'),
        memberCards = <?= Json::encode($memberCards) ?>,
        maxAmountBox = $('#max_amount'),
        amountSplitConfig = <?= json_encode($withdrawSplitConfig) ?>;
    accountDom.on('change', function () {
        const member = memberCards[$(this).val()];
        numberDom.val(member.account_no);
        uuidDom.val(member.card_uuid);
        useridDom.val(member.user_uuid);
    });
    channelDom.on('change', function () {
        let channel = channelDom.val();
        maxAmountBox.text(amountSplitConfig.channel_config[channel]?.max_amount ?? amountSplitConfig.max_amount ?? 0);
        $.post('<?= Url::to(['get-virtual-account'])?>', {
            channel: channel,
        }, function (res) {
            if (res.code !== 0) {
                virtualDom.val(res.name);
            }
        });
    });
    accountDom.trigger("change");
    channelDom.trigger("change");

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
