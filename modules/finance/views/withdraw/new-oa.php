<?php

use finance\models\Withdraw;
use yii\web\View;

/* @var $this View */
/* @var $model Withdraw */
/* @var array $electronicInvoicePreviewConfig */
/* @var array $electronicInvoicePreview */
/* @var array $attachmentsPreviewConfig */
/* @var array $attachmentsPreview */
/* @var array $noLimitWithdrawChannel */

$this->title = '付款申请';

$this->params['breadcrumbs'][] = ['label' => '付款记录列表', 'url' => ['index']];
$this->params['breadcrumbs'][] = '创建';

?>

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title"><?= $this->title ?></h3>
    </div>

    <?= $this->render('_form_new_oa', [
        'model' => $model,
        'noLimitWithdrawChannel' => $noLimitWithdrawChannel,
        'pendingTask' => $pendingTask ?? null,
    ]) ?>

</div>
