<?php

use xlerr\common\widgets\ActiveForm;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ListView;

/* @var $this View */
/* @var $accounts array */

$this->title = '收款信息管理';

$form = ActiveForm::begin();
?>
    <div class="box">
        <div class="box-body no-padding">
            <table class="table">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>收款账户</th>
                    <th>收款卡号</th>
                    <th>收款UUID</th>
                    <th>收款用户ID</th>
                </tr>
                </thead>
                <tbody>
                <?= ListView::widget([
                    'dataProvider' => new ArrayDataProvider([
                        'allModels'  => $accounts,
                        'sort'       => false,
                        'pagination' => false,
                    ]),
                    'layout'       => '{items}',
                    'itemView'     => function ($accountInfo, $key, $index) {
                        $account = Html::textInput('account[]', $accountInfo['receive_account'], [
                            'class' => 'form-control',
                        ]);
                        $number  = Html::textInput('number[]', $accountInfo['receive_number'], [
                            'class' => 'form-control',
                        ]);
                        $uuid    = Html::textInput('uuid[]', $accountInfo['receive_uuid'], [
                            'class' => 'form-control',
                        ]);
                        $userid  = Html::textInput('userid[]', $accountInfo['receive_userid'], [
                            'class' => 'form-control',
                        ]);

                        return <<<HTML
<tr>
    <td style="line-height: 34px">{$index}</td>
    <td>{$account}</td>
    <td>{$number}</td>
    <td>{$uuid}</td>
    <td>{$userid}</td>
</tr>
HTML;
                    },
                ]) ?>
                <tr>
                    <td style="line-height: 34px">#</td>
                    <td><input type="text" class="form-control" name="account[]" value=""></td>
                    <td><input type="text" class="form-control" name="number[]" value=""></td>
                    <td><input type="text" class="form-control" name="uuid[]" value=""></td>
                    <td><input type="text" class="form-control" name="userid[]" value=""></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="box-footer">
            <?= Html::submitButton('保存', [
                'class' => 'btn btn-primary',
            ]) ?>
        </div>
    </div>
<?php
ActiveForm::end() ?>