<?php

use backend\behaviors\FormatterBehavior;
use backend\widgets\MoneyInput;
use finance\models\Asset;
use finance\models\BizType;
use finance\models\OaWithdraw;
use finance\models\PaymentWithdrawalPendingTasks;
use kvmanager\models\KeyValue;
use payment\models\MemberCard;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/* @var $this View */
/* @var $model OaWithdraw */
/* @var $pendingTask PaymentWithdrawalPendingTasks */
/* @var $actionName */
/* @var $noLimitWithdrawChannel array */

$formatter = Yii::$app->getFormatter();

$withdrawSplitConfig = KeyValue::take('withdraw_split_config');
$withdrawSplitConfig['max_amount'] = $formatter->format($withdrawSplitConfig['max_amount'] ?? 0, 'formatAmount');
$withdrawSplitConfig['channel_config'] = (array)($withdrawSplitConfig['channel_config'] ?? []);
foreach ($withdrawSplitConfig['channel_config'] as &$config) {
    $config['max_amount'] = $formatter->format($config['max_amount'] ?? 0, 'formatAmount');
}

$memberCards = [];
$memberCardDropdown = [];
foreach (MemberCard::query() as $memberCard) {
    if ($memberCard->member_type === MemberCard::MEMBER_TYPE_WITHDRAW) {
        $userName = $memberCard->decrypt('user_name', true);
        $accountNo = $memberCard->decrypt('account_no', true);

        $key = $memberCard->user_uuid . ':' . $userName;

        $memberCards[$key] = [
            'user_uuid' => $memberCard->user_uuid,
            'card_uuid' => $memberCard->card_uuid,
            'account_no' => $accountNo,
        ];

        $memberCardDropdown[$key] = $key;
    }
}

//$model->withdraw_biz_type = Withdraw::BIZ_TYPE_WITHDRAW;

$form = ActiveForm::begin();
?>

<div class="box-body">

    <?= $form->field($model, 'pendingOaId')->textInput(['readOnly' => true])->label('OA绑定批次号') ?>

    <?= $form->field($model, 'withdraw_channel')->widget(Select2::class, [
        'data' => Asset::getWithdrawList(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'withdraw_type')->textInput(['readOnly' => true]) ?>

    <?= $form->field($model, 'withdraw_biz_type')->widget(Select2::class, [
        'data' => BizType::getBizTypeList([BizType::FUND_FLOW_OUT], '提现'),
        'hideSearch' => true,
    ]) ?>

    <br/>
    <br/>

    <div id="card-warning"></div>

    <div id="receiveBox">
        <div class="form-group" id="change_receive_account">
            <label for="" class="form-label">提现卡列表</label>

            <div class="input-group">
                <?= Select2::widget([
                    'value' => '',
                    'id' => 'receive_list',
                    'name' => 'receive_list',
                    'data' => $memberCardDropdown,
                    'options' => [
                        'prompt' => '请选择收款卡信息',
                    ],
                ]) ?>
                <span class="input-group-btn">
                <?= Html::a('提现卡管理', ['member-withdraw/index'], [
                    'class' => 'btn btn-success btn-flat',
                    'target' => '_blank',
                ]) ?>
            </span>
            </div>
        </div>

        <?= $form->field($model, 'withdraw_receive_account')->textInput([
            'readOnly' => true,
        ]) ?>

        <?= $form->field($model, 'withdraw_receive_number')->textInput([
            'readOnly' => true,
        ]) ?>
        <?= $form->field($model, 'withdraw_receive_uuid')->textInput([
            'readOnly' => true,
        ]) ?>
        <?= $form->field($model, 'withdraw_receive_userid')->textInput([
            'readOnly' => true,
        ]) ?>
    </div>

    <br/>
    <br/>

    <?php
    $attachments = $model->applyAttachments((int)$model->pendingOaId);
    if (null !== $attachments) {
        echo $form->field($model, 'attachmentUrl')->radioList($attachments, [
            'item' => static function ($index, $file, $name, $checked) {
                return vsprintf('<div>%s %s</div>', [
                    Html::radio($name, $checked, [
                        'value' => $file['id'],
                        'label' => $file['filename'],
                    ]),
                    Html::a('预览', [
                        'preview-oa-attachment',
                        'id' => $file['id'],
                    ], [
                        'target' => '_blank',
                        'target_browser' => true,
                    ]),
                ]);
            },
        ]);
    }
    ?>

    <?= $form->field($model, 'withdraw_amount')->widget(MoneyInput::class, [
        'options' => [
            'value' => $model->withdraw_amount > 0 ? $model->withdraw_amount : $pendingTask->payment_amount,
        ],
    ]) ?>

    <?= Html::tag(
        'p',
        vsprintf('注: 提现金额><span id="max_amount">%s</span>%s, 会拆分多笔任务提现', [
            $withdrawSplitConfig['max_amount'],
            FormatterBehavior::currencyUnit(),
        ]),
        [
            'class' => 'text-danger',
        ]
    ) ?>

    <?= $form->field($model, 'withdraw_reason')->textInput([
        'maxlength' => true,
    ]) ?>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>

<script>
    <?php $this->beginBlock('js') ?>
    const accountDom = $('#<?= Html::getInputId($model, 'withdraw_receive_account') ?>'),
        channelDom = $('#<?= Html::getInputId($model, 'withdraw_channel') ?>'),
        virtualDom = $('#<?= Html::getInputId($model, 'withdraw_virtual_account') ?>'),
        numberDom = $('#<?= Html::getInputId($model, 'withdraw_receive_number') ?>'),
        uuidDom = $('#<?= Html::getInputId($model, 'withdraw_receive_uuid') ?>'),
        useridDom = $('#<?= Html::getInputId($model, 'withdraw_receive_userid') ?>'),
        memberCards = <?= Json::encode($memberCards) ?>,
        maxAmountBox = $('#max_amount'),
        changeReceiveAccount = $('#change_receive_account'),
        noLimitWithdrawChannel = <?= Json::htmlEncode($noLimitWithdrawChannel) ?>,
        amountSplitConfig = <?= Json::htmlEncode($withdrawSplitConfig) ?>,
        setCardInfo = () => {
            let exists = false
            for (const [receiveAccount, {account_no}] of Object.entries(memberCards)) {
                if (account_no === '<?= $pendingTask->payee_account_number ?>') {
                    $('#receive_list').val(receiveAccount).change()
                    exists = true
                    break
                }
            }
            if (!exists) {
                $('#card-warning').html($(`<h4 class="text-red">收款卡白名单中没有: <?= $pendingTask->payee_account_number ?>, 去<a href="/finance/member-withdraw/create?accountNo=<?= $pendingTask->payee_account_number ?>">添加</a></h4>`))
                $('#receiveBox').hide()
            }
        };
    $('#receive_list').on('change', function () {
        const val = $(this).val()
        const member = memberCards[val];
        accountDom.val(val);
        numberDom.val(member.account_no);
        uuidDom.val(member.card_uuid);
        useridDom.val(member.user_uuid);
    });
    channelDom.on('change', function () {
        let channel = channelDom.val();
        if ('<?= $pendingTask->fund_direction ?>' === '提现-U钱包') {
            changeReceiveAccount.show()
        } else {
            if (noLimitWithdrawChannel.includes(channel)) {
                changeReceiveAccount.show()
            } else {
                changeReceiveAccount.hide()
            }
            setCardInfo()
        }

        maxAmountBox.text(amountSplitConfig.channel_config[channel]?.max_amount ?? amountSplitConfig.max_amount ?? 0);
    });
    channelDom.trigger("change");

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
