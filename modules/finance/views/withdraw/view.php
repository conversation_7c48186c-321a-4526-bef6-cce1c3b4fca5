<?php

use finance\models\BizType;
use finance\models\Withdraw;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model Withdraw */
$this->title = '提现详情';
$this->params['breadcrumbs'][] = ['label' => '提现记录', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="box">
    <div class="box-header with-border">
        <h3 class="box-title">详情</h3>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'withdraw_id',
                'withdraw_type',
                [
                    'attribute' => 'withdraw_biz_type',
                    'format' => ['in', BizType::getBizTypeList([BizType::FUND_FLOW_IN, BizType::FUND_FLOW_IN])],
                ],
                [
                    'attribute' => 'withdraw_amount',
                    'format' => ['formatAmount'],
                ],
                'withdraw_channel',
                'withdraw_receive_account',
                'withdraw_receive_number',
                'withdraw_receive_uuid',
                'withdraw_receive_userid',

                'withdraw_merchant_key',
                'withdraw_trade_no',

                'withdraw_oa_id',
                'withdraw_procedure_id',

                'withdraw_updated_at',
                'withdraw_created_at',
                'withdraw_oa_created_at:VDateTime',
                'withdraw_payment_created_at:VDateTime',
                'withdraw_finished_at:VDateTime',

                [
                    'attribute' => 'withdraw_create_user',
                    'value' => vsprintf('%s(%s)', [
                        $model->withdraw_create_user,
                        $model->withdraw_create_user_email,
                    ]),
                ],

                'withdraw_reason',
                [
                    'attribute' => 'withdraw_status',
                    'format' => ['in', Withdraw::STATUS],
                ],
                [
                    'attribute' => 'withdraw_payment_info',
                ],
            ],
        ]) ?>
    </div>
</div>
