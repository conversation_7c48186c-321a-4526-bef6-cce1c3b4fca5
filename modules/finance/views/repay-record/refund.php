<?php

use backend\widgets\MoneyInput;
use finance\models\Asset;
use repay\models\operates\RepayRefund;
use repay\models\Withhold;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;

/** @var Withhold $model */
/** @var RepayRefund $refund */
/** @var int $maxAmount */

$this->title = '退款';

$form = ActiveForm::begin([
    //    'action' => '',
    'method' => 'post',
]);

echo Html::activeHiddenInput($refund, 'action');
?>
<div class="box box-primary">
    <div class="box-body">
        <?= $form->field($refund, 'refundWithholdSerialNo')->textInput([
            'value'    => $model->withhold_serial_no,
            'readonly' => true,
        ]) ?>

        <?= $form->field($refund, 'uuid')->textInput([
            'value'    => $model->withhold_card_num,
            'readonly' => true,
        ]) ?>

        <?= $form->field($refund, 'channel')->widget(Select2::class, [
            'data' => Asset::getWithdrawList(),
        ]) ?>

        <?= $form->field($refund, 'amount')->widget(MoneyInput::class) ?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('确定', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>
</div>
<?php ActiveForm::end() ?>
