<?php

use finance\models\IndicatorExplanation;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;
use function xlerr\adminlte\userFullName;

/**
 * @var View $this
 * @var ActiveDataProvider $dataProvider
 * @var IndicatorExplanation $searchModel
 */

$this->title = '指标配置说明';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

Yii::$app->getFormatter()->nullDisplay = '-';

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update}',
        ],
        [
            'attribute' => 'id',
            'label' => 'ID'
        ],
        'page_path',
        'table_name',
        'indicator_name',
        'explanation',
        [
            'label' => '创建人',
            'attribute' => 'createUser',
            'format' => static fn($user) => $user ? userFullName($user) : '-',
        ],
        [
            'label' => '修改人',
            'attribute' => 'updateUser',
            'format' => static fn($user) => $user ? userFullName($user) : '-',
        ],
        'created_at',
        'updated_at'
    ],
]);
