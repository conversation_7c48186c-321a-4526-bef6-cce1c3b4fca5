<?php

use Carbon\Carbon;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use xlerr\desensitise\DesensitiseWidget;
use yii\base\DynamicModel;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ActiveForm;

/* @var $this View */
/* @var $form ActiveForm */
/* @var $model DynamicModel */
/* @var $dataProvider ArrayDataProvider */
/* @var $columns array */

$this->title = '放款未到账明细';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <h3 class="box-title">资产查询</h3>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'post',
        ]); ?>

        <?= $form->field($model, 'itemNo')->widget(CodeEditor::class, [
            'name' => 'itemNo',
            'clientOptions' => [
                'fontSize' => 14,
                'mode' => CodeEditor::MODE_Text,
            ],
        ]) ?>

        <?= Html::a('查询', ['index'], ['class' => 'btn btn-primary search-btn']) ?>
        <?php ActiveForm::end(); ?>

    </div>
</div>

<!--查询结果-->
<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '资产编号',
            'attribute' => 'asset_item_no'
        ],
        [
            'label' => '收款账户类型',
            'attribute' => 'withdraw_receipt_transfer_option'
        ],
        [
            'label' => '放款通道',
            'attribute' => 'withdraw_receipt_channel_name'
        ],
        [
            'label' => '资金方',
            'attribute' => 'asset_loan_channel'
        ],
        [
            'label' => '收款账号',
            'attribute' => 'withdraw_receipt_card_num',
            'format' => static function ($val) {
                if (!empty($val)) {
                    return DesensitiseWidget::decrypt($val,true);
                }

                return '-';
            },
        ],
        [
            'label' => '收款账户',
            'attribute' => 'withdraw_receipt_transfer_mode'
        ],
        [
            'label' => '交易流水号',
            'attribute' => 'withdraw_receipt_channel_inner_key'
        ],
        [
            'label' => '放款本金',
            'attribute' => 'asset_granted_principal_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '放款日期',
            'attribute' => 'asset_actual_grant_at',
            'format' => static function ($val) {
                return Carbon::parse($val)->format('Y-m-d');
            }
        ],
        [
            'label' => '放款时间',
            'attribute' => 'asset_actual_grant_at',
        ],
        [
            'label' => '用户手机号',
            'attribute' => 'phone_encrypt',
            'format' => static function ($val) {
                if (!empty($val)) {
                    return DesensitiseWidget::decrypt($val,true);
                }

                return '-';
            },
        ],

        [
            'label' => '用户姓名',
            'attribute' => 'name_encrypt',
            'format' => static function ($val) {
                if (!empty($val)) {
                    return DesensitiseWidget::decrypt($val,true);
                }

                return '-';
            },
        ],
        [
            'label' => '用户证件号',
            'attribute' => 'withdraw_receipt_id_num',
            'format' => static function ($val) {
                if (!empty($val)) {
                    return DesensitiseWidget::decrypt($val,true);
                }

                return '-';
            },
        ],
    ]
]); ?>

<script>
    <?php $this->beginBlock('encryptBtn') ?>
    $('.search-btn').click(function () {
        let self = $(this);
        let form = self.parents('form');

        form.attr('action', self.attr('href'));

        let target = self.attr('target');
        if (target) {
            form.attr('target', target);
        } else {
            form.removeAttr('target');
        }

        form.submit();
        return false;
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['encryptBtn']) ?>
</script>
