<?php

use finance\models\OfflineFeeDatasource;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model OfflineFeeDatasource */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'fee_type')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'fee_type_desc')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'sub_fee_type') ?>

    <?= $form->field($model, 'rate')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'calculation_type')->widget(Select2::class, [
        'data' => OfflineFeeDatasource::CALCULATION_TYPE_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'calculation_desc')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'payment_mode')->widget(Select2::class, [
        'data' => OfflineFeeDatasource::PAYMENT_MODE_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'data_source')->widget(Select2::class, [
        'data' => [
            'gbiz' => 'gbiz',
            'rbiz' => 'rbiz',
            'psvr' => 'psvr',
            'biz' => 'biz',
            'capital' => 'capital',
        ],
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'data_source_sql')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_MySQL,
        ],
    ]) ?>


    <?= $form->field($model, 'statistics_dimension')->widget(Select2::class, [
        'data' => OfflineFeeDatasource::STATISTICS_DIMENSION_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'comment')->textarea() ?>

</div>

<div class="box-footer">
    <?= Html::submitButton(
        '保存',
        ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']
    ) ?>
</div>

<?php ActiveForm::end(); ?>
