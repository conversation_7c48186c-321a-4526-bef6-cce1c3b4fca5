<?php

use finance\models\OaApprovalOrder;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model \finance\models\Account */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">


    <?= $form->field($model, 'status')->widget(Select2::class, [
        'data' => [
            OaApprovalOrder::STATUS_PROCESSING => '继续处理',
            OaApprovalOrder::STATUS_TERMINATED => '结束任务'
        ],
        'hideSearch'    => true,
        'pluginOptions' => [
            'allowClear' => true,
        ],
        'options'       => [
            'prompt' => $model->getAttributeLabel('处理结果'),
        ],
    ])->label('处理结果') ?>

    <?= $form->field($model, 'memo')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_Text,
            'minLines' => 5,
            'maxLines' => 20,
        ],
    ])->label('备注') ?>

</div>

<div class="box-footer">
    <?= Html::submitButton(Yii::t('finance', '修改'), ['class' => 'btn btn-primary'])?>
</div>

<?php ActiveForm::end(); ?>
