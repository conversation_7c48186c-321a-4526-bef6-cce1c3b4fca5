<?php

use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\OaApprovalOrder;
use kartik\form\ActiveForm as ActiveFormAlias;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model \finance\models\OaApprovalOrder */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title"><?= Yii::t('OA审批订单', 'Search') ?></h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action'        => ['index'],
            'method'        => 'get',
            'type'          => ActiveFormAlias::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'oa_tenant_id') ?>
        <?= $form->field($model, 'oa_flow_id')?>
        <?= $form->field($model, 'oa_flow_key', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 150px',
            ]])->widget(Select2::class, [
            'data'          => OaApprovalDto::$flowKeyMap,
            'hideSearch'    => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options'       => [
                'prompt' => $model->getAttributeLabel('流程类型'),
            ],
        ]) ?>
        <?= $form->field($model, 'status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 120px',
            ]])->widget(Select2::class, [
            'data'          => OaApprovalOrder::$statusMap,
            'hideSearch'    => true,
            'pluginOptions' => [
                'allowClear' => true,
                'multiple' => true,
            ],
            'options'       => [
                'prompt' => $model->getAttributeLabel('状态'),
            ],
        ]) ?>

        <div class="form-group">
            <?= Html::submitButton(Yii::t('finance', 'Search'), ['class' => 'btn btn-primary']) ?>
            <?= Html::a(Yii::t('finance', 'Reset'), ['index'], ['class' => 'btn btn-default']) ?>
        </div>

        <?php ActiveForm::end(); ?>

    </div>
</div>