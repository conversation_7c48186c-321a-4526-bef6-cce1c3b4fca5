<?php

use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\OaApprovalOrder;
use kartik\helpers\Html;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\StringHelper;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel \finance\models\AccountSearch */

$this->title                   = Yii::t('finance', 'OA审批订单');
$this->params['breadcrumbs'][] = $this->title;
?>

<?= $this->render('_search', ['model' => $searchModel]); ?>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class' => ActionColumn::class,
            'template' => '{update-status} {cpop-settlement-order}',
            'buttons' => [
                'update-status' => static function ($url, OaApprovalOrder $model) {
                    return ActionColumn::newButton('人工介入', [$url], [
                        'class' => 'btn-primary layer-dialog',
                    ]);
                },
                'cpop-settlement-order' => static function ($url, OaApprovalOrder $model) {
                    return ActionColumn::newButton(
                        '对资收付款订单',
                        [
                            '/cpop-settlement/order/index',
                            'status' => '',
                            'orderNo' => sprintf(
                                'oa-approval-order-%s-%s-',
                                $model->oa_tenant_id,
                                $model->oa_flow_id
                            )
                        ],
                        [
                            'class' => 'btn-primary layer-dialog',
                        ]
                    );
                },
            ],
            'visibleButtons' => [
                'update-status' => static function (OaApprovalOrder $model) {
                    if ($model->status === OaApprovalOrder::STATUS_HOLD) {
                        return true;
                    }
                    if (
                        $model->status === OaApprovalOrder::STATUS_PROCESSING
                        && Yii::$app->user->can('oa_approval_order_terminated')
                    ) {
                        return true;
                    }
                    return false;
                },
            ],
        ],
        'id',
        'oa_tenant_id',
        'oa_flow_id',
        [
            'label' => '流程类型',
            'attribute' => 'oa_flow_key',
            'format'    => ['in', OaApprovalDto::$flowKeyMap],
        ],
        [
            'label' => '状态',
            'attribute' => 'status',
            'format'    => ['in', OaApprovalOrder::$statusMap],
        ],
        [
            'label' => 'BIZ处理结果',
            'attribute' => 'response_data',
            'format' => static function ($val) {
                $val = (string)$val;
                if ($data = json_decode($val)) {
                    return Html::tag('span', StringHelper::truncate($val, 30), [
                        'title' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                    ]);
                }

                return $val;
            },
        ],
        'memo',
        'finish_at',
        'create_at',
        'update_at',
    ],
]);
?>
