<?php


use finance\models\BizType;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\web\View;

/**
 * @var View $this
 * @var BizType $model
 */

$form = ActiveForm::begin();
?>

<div class="box-body">
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'biz_type_fund_flow')->widget(Select2::class, [
                'data' => BizType::FUND_FLOWS,
                'options' => [
                    'prompt' => '请选择资金流向',
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'biz_type_category', [
                'options' => [
                    'style' => 'min-width: 180px',
                ],
            ])->widget(Select2::class, [
                'data' => BizType::getCategoryList(),
                'hideSearch' => false,
                'pluginOptions' => [
                    'allowClear' => true,
                    'multiple' => false,
                    'tags' => true,
                    'createTag' => new JsExpression("
            function (params) {
                return {
                    id: params.term,
                    text: params.term
                };
            }
        "),
                ],
                'options' => [
                    'prompt' => $model->getAttributeLabel('biz_type_category'),
                ],
            ]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <?= $form->field($model, 'biz_type_subtype')->textInput(['readOnly' => !$model->isNewRecord]) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'biz_type_memo') ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'biz_type_scope') ?>
        </div>
    </div>
</div>
<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
