<?php

use finance\models\BizType;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;
use function xlerr\adminlte\userFullName;

/**
 * @var View $this
 * @var ActiveDataProvider $dataProvider
 * @var BizType $searchModel
 */

$this->title = '出入金业务场景管理';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

Yii::$app->getFormatter()->nullDisplay = '-';

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update}',
            'visibleButtons' => [
                'update' => function (BizType $model) {
                    return $model->biz_type_scope !== 'system';
                },
            ],
        ],
        [
            'attribute' => 'biz_type_id',
            'label' => 'ID'
        ],
        'biz_type_fund_flow',
        'biz_type_category',
        'biz_type_subtype',
        'biz_type_memo',
        'biz_type_scope',
        [
            'label' => '创建人',
            'attribute' => 'createUser',
            'format' => static fn($user) => $user ? userFullName($user) : '-',
        ],
        'biz_type_create_at',
        'biz_type_update_at'
    ],
]);
