<?php

use common\models\BizReport;
use finance\models\BizReportSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model BizReportSearch */

$channels = BizReport::find()
    ->where(['type' => 'v2401_pc_acct_flowinout_amount_daily'])
    ->select('channel')
    ->distinct()
    ->column();
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php
        $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'style' => 'min-width: 240px',
            ],
        ])->widget(Select2::class, [
            'data' => array_combine($channels, $channels),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel'),
            ],
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class) ?>
        <?= $form->field($model, 'endDate')->widget(DatePicker::class) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['flow-in-out'], ['class' => 'btn btn-default']) ?>

        <?php
        ActiveForm::end() ?>

    </div>
</div>