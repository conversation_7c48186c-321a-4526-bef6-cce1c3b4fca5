<?php

namespace finance\services\oaWithdrawService;

use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\oaApprovalDto\OaPaymentInfo;
use payment\models\MemberCard;
use yii\base\UserException;

/**
 * Class OaWithdrawServiceForPak
 * @package finance\services\oaWithdrawService
 *
 * <AUTHOR> <<EMAIL>>
 * @date   2025/4/15 11:07
 */
class OaWithdrawServiceForPak extends OaWithdrawServiceBase
{
    public const BANK_JAZZCASH = 'JazzCash';
    public const BANK_EASYPAISA = 'EasyPaisa';

    /**
     * @inheritDoc
     */
    public function addWhitelistCard(OaPaymentInfo $data, $serialNumber)
    {
        $model = new MemberCard();
        if (empty($data->mobile)) {
            throw new UserException("{$data->bankCard}：手机为空");
        }
        //避免一个OA单添加多张卡时生成的user_id相同
        $model->validate();
        $model->user_uuid = $model->user_uuid . $serialNumber;
        $model->card_uuid = $model->card_uuid . $serialNumber;
        $model->member_type         = MemberCard::MEMBER_TYPE_WITHDRAW;
        $model->account_type        = $this->getAccountType($data);
        $model->account_no          = $data->bankCard;
        $model->large_account       = '0';
        $model->max_withdraw_amount = 0;
        $model->user_name           = $data->bankCardUserName;
        $model->bank_code           = $this->getBankCode($data);
        $model->mobile              = $data->mobile;
        if (!$model->save()) {
            throw new UserException("添加银行卡:{$model->account_no}失败");
        }
    }

    /**
     * @param OaPaymentInfo $data
     * @return string
     */
    public function getAccountType(OaPaymentInfo $data): string
    {
        if (
            strtolower($data->bank) === strtolower(self::BANK_JAZZCASH)
            || strtolower($data->bank) === strtolower(self::BANK_EASYPAISA)
        ) {
            return MemberCard::BANK_CODE_WALLET;
        }

        return MemberCard::BANK_CODE_ACCOUNT;
    }

    /**
     * @param OaPaymentInfo $data
     * @return string
     * @throws UserException
     */
    public function getBankCode(OaPaymentInfo $data): string
    {
        if (
            strtolower($data->bank) === strtolower(self::BANK_JAZZCASH)
            || strtolower($data->bank) === strtolower(self::BANK_EASYPAISA)
        ) {
            return $data->bank;
        }

        return $this->getBankCodeByBankName($data->bank);
    }

    /**
     * @inheritDoc
     */
    public function getPaymentChannel(): string
    {
        return 'easypaisa_goldlion_withdraw'; //todo 当前只有一个通道产品所以先写死;
    }

    /**
     * @inheritDoc
     */
    public function isHold(): bool
    {
        if ($this->oaApprovalDto->flowKey !== OaApprovalDto::FLOW_KEY_OVERSEA_OTHER_EXPENSES) {
            return false;
        }
        //如果发现卡号被其他人使用，不要直接发起代付，将代付任务挂起，人工介入。
        $memberCards = $this->getMemberCardList();
        foreach ($memberCards as $memberCard) {
            foreach ($this->oaApprovalDto->paymentList as $paymentInfo) {
                if (
                    $paymentInfo->bankCard === $memberCard->account_no
                    && strtolower(trim($paymentInfo->bankCardUserName)) !== strtolower(trim($memberCard->user_name))
                ) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getProcedures(OaPaymentInfo $paymentInfo): array
    {
        $procedures = $this->getPaymentAutoWithdrawProcedures($paymentInfo);
        return [$procedures];
    }
}
