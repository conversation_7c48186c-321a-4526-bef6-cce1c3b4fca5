<?php

namespace finance\services\oaWithdrawService;

use finance\models\BusinessAssociationTable;
use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\oaApprovalDto\OaPaymentInfo;
use finance\worker\AddChannelOfflineTrade;
use payment\models\ChannelOfflineTrade;
use xlerr\common\exceptions\SaveException;
use Xlerr\SettlementFlow\Models\Rule;
use Yii;
use yii\base\UserException;
use yii\web\NotFoundHttpException;

/**
 * Class OaWithdrawServiceForPhl
 * @package finance\services\oaWithdrawService
 *
 * <AUTHOR> <<EMAIL>>
 * @date   2025/4/15 11:07
 */
class OaWithdrawServiceForPhl extends OaWithdrawServiceBase
{
    /**
     * @return void
     * @throws \Throwable
     */
    public function addChannelOfflineTradeJob(): void
    {
        $allowFlowKey = [
            'pay_for_operation',
            'oversea_income_withdraw'
        ];
        if (
            $this->oaApprovalDto->withdrawType === OaApprovalDto::WITHDRAW_TYPE_INSTAPAY
            || !in_array($this->oaApprovalDto->flowKey, $allowFlowKey, true)
        ) {
            return;
        }

        AddChannelOfflineTrade::make([
            'oaApprovalOrderId' => $this->oaApprovalOrder->id
        ], [
            'task_from_system' => 'OA',
        ]);
    }

    /**
     * @return void
     * @throws SaveException
     * @throws UserException
     * @throws \Throwable
     * @throws \yii\base\Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\db\Exception
     */
    public function addChannelOfflineTrade()
    {
        $allowFlowKey = [
            OaApprovalDto::FLOW_KEY_PAY_FOR_OPERATION,
            OaApprovalDto::FLOW_KEY_OVERSEA_INCOME_WITHDRAW,
        ];
        if (
            $this->oaApprovalDto->withdrawType === OaApprovalDto::WITHDRAW_TYPE_INSTAPAY
            || !in_array($this->oaApprovalDto->flowKey, $allowFlowKey, true)
        ) {
            return;
        }

        //检查是否已经消费，如果消费了不在处理；
        $exists = BusinessAssociationTable::find()->where([
            'business_type' => BusinessAssociationTable::BUSINESS_TYPE_OA_OFFLINE_TRADE,
            'main_id'       => $this->oaApprovalOrder->id
        ])->exists();
        if ($exists) {
            return;
        }
        $channel = $this->getPaymentChannel();
        $businessTransaction = BusinessAssociationTable::getDb()->beginTransaction();
        $transaction = ChannelOfflineTrade::getDb()->beginTransaction();
        $orderNo = Yii::$app->security->generateRandomString();
        $serialNumber = 1;
        $rule = $this->matchSettlementRule();
        if ($rule->status !== Rule::STATUS_ACTIVE) {
            throw new NotFoundHttpException('只有启用状态才能制单');
        }
        try {
            foreach ($this->oaApprovalDto->paymentList as $paymentInfo) {
                $amount = Yii::$app->getFormatter()->format($paymentInfo->withdrawAmount, ['amount']);
                $comment = "{$this->oaApprovalDto->tenantId}_{$this->oaApprovalDto->entryId}_{$this->oaApprovalDto->title}";

                $offlineTrade = new ChannelOfflineTrade();
                $offlineTrade->channel_offline_trade_oa_id = $this->oaApprovalDto->entryId;
                $offlineTrade->channel_offline_trade_order_no = sprintf('%s_%s_%s', 'null', $orderNo, $serialNumber);
                $offlineTrade->channel_offline_trade_capital_order_no = sprintf('%s_%s', $this->oaApprovalDto->tenantId, $this->oaApprovalDto->entryId);
                $offlineTrade->channel_offline_trade_channel_name = $channel;
                $offlineTrade->channel_offline_trade_busi_type = $rule->fee_type;
                $offlineTrade->channel_offline_trade_pay_type = $this->oaApprovalDto->withdrawType ?? '';
                $offlineTrade->channel_offline_trade_amount = $amount;
                $offlineTrade->channel_offline_trade_order_from_type = 'manual';
                $offlineTrade->in_account_name = $paymentInfo->bankCardUserName;
                $offlineTrade->in_account_number = $paymentInfo->bankCard;
                $offlineTrade->out_account_name = $this->getPaymentChannel();
                $offlineTrade->out_account_number = $this->getPaymentChannel();
                $offlineTrade->currency = $paymentInfo->currency;
                $offlineTrade->channel_offline_trade_comment = $comment;
                $offlineTrade->channel_offline_trade_finish_at = '1000-01-01 00:00:00';
                $offlineTrade->channel_offline_trade_create_user = 0;
                $serialNumber++;
                $offlineTrade->saveOrException();

                BusinessAssociationTable::addBusinessAssociationTable(
                    BusinessAssociationTable::BUSINESS_TYPE_OA_OFFLINE_TRADE,
                    $this->oaApprovalOrder->id,
                    $offlineTrade->channel_offline_trade_id,
                );
            }
            $businessTransaction->commit();
            $transaction->commit();
        } catch (\Throwable $e) {
            $businessTransaction->rollBack();
            $transaction->rollBack();
            Yii::error($e->getMessage());
            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function getPaymentChannel(): string
    {
        return 'payso_copperstone_withdraw'; //todo 实际需要根据通道和主来匹配，当前只有一个通道产品所以先写死;
    }

    /**
     * 是否需要自动提现
     * @return true
     */
    public function isPaymentAutoWithdraw(): bool
    {
        if ($this->oaApprovalDto->flowKey === OaApprovalDto::FLOW_KEY_OVERSEA_OTHER_EXPENSES) {
            return true;
        }
        $allowFlowKey = [
            OaApprovalDto::FLOW_KEY_PAY_FOR_OPERATION,
            OaApprovalDto::FLOW_KEY_OVERSEA_INCOME_WITHDRAW,
        ];

        return $this->oaApprovalDto->withdrawType === OaApprovalDto::WITHDRAW_TYPE_INSTAPAY
            && in_array($this->oaApprovalDto->flowKey, $allowFlowKey, true);
    }

    /**
     * @inheritDoc
     */
    public function getProcedures(OaPaymentInfo $paymentInfo): array
    {
        if ($this->isPaymentAutoWithdraw()) {
            $procedures = $this->getPaymentAutoWithdrawProcedures($paymentInfo);
            return [$procedures];
        }

        return parent::getProcedures($paymentInfo);
    }
}
