<?php

namespace finance\services\oaWithdrawService;

use finance\models\OaApprovalOrder;
use finance\models\PaymentWithdrawalPendingTasks;
use xlerr\common\exceptions\SaveException;
use Yii;
use yii\db\Exception;
use yii\db\Transaction;

/**
 * Class OaWithdrawServiceForDefault
 * @package finance\services\oaWithdrawService
 *
 * <AUTHOR> <<EMAIL>>
 * @date   2025/3/31 17:08
 */
class OaWithdrawServiceForDefault extends OaWithdrawServiceBase
{
    /**
     * @return array|mixed
     * @throws SaveException
     * @throws Exception
     */
    public function callbackHandle()
    {
        if ($data = $this->check()) {
            $message = "处理成功_" . $data['message'] ?? '';
            $this->updateOaApprovalOrderStatus(OaApprovalOrder::STATUS_SUCCESS, $message);
            return $data;
        }
        /** @var Transaction $transaction */
        $transaction = PaymentWithdrawalPendingTasks::getDb()->beginTransaction();
        try {
            $this->addPendingTask();
            $this->updateOaApprovalOrderStatus(OaApprovalOrder::STATUS_SUCCESS, '处理成功');
            $transaction->commit();
            return [
                'code' => 0,
                'message' => '处理成功',
                'data' => null,
            ];
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function checkExists(): bool
    {
        return PaymentWithdrawalPendingTasks::find()
            ->where([
                'tenant_id' => $this->oaApprovalDto->tenantId,
                'oa_id'     => $this->oaApprovalDto->entryId,
            ])
            ->exists();
    }
}
