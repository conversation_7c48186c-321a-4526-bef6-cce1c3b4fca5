<?php

namespace finance\services\oaWithdrawService;

use Carbon\Carbon;
use common\helpers\ArrayHelper;
use finance\models\BusinessAssociationTable;
use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\oaApprovalDto\OaPaymentInfo;
use finance\models\OaApprovalOrder;
use finance\models\PaymentWithdrawalPendingTasks;
use finance\procedures\PaymentAutoWithdraw;
use finance\procedures\TransferRecord;
use finance\worker\OaApprovalOrderSubmit;
use payment\models\Bank;
use payment\models\MemberCard;
use waterank\audit\models\Audit;
use xlerr\common\exceptions\SaveException;
use Xlerr\SettlementFlow\Models\Account;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use Xlerr\SettlementFlow\Models\RuleProcedure;
use Xlerr\SettlementFlow\Tasks\OrderProcedureExecutor;
use Yii;
use yii\base\BaseObject;
use yii\base\UserException;
use yii\db\Exception;
use yii\db\Transaction;
use yii\helpers\Json;
use yii\web\NotFoundHttpException;
use function waterank\audit\config;

abstract class OaWithdrawServiceBase extends BaseObject implements OaWithdrawServiceInterface
{
    /** @var OaApprovalOrder */
    public $oaApprovalOrder;

    /** @var OaApprovalDto */
    public $oaApprovalDto;

    /** @var MemberCard[] */
    private $memberCardList;

    /**
     * 检查状态
     * @return bool
     */
    public function checkStatusCode(): bool
    {
        return $this->oaApprovalDto->statusCode === Audit::OA_AGREE_STATUS;
    }

    /**
     * 检查国家是否匹配
     * @return bool
     */
    public function checkCountry(): bool
    {
        return $this->oaApprovalDto->region === config('paymentPendingTasks.current_country');
    }

    /**
     * 检查资金去向
     * @return bool
     */
    public function checkCapitalFlow(): bool
    {
        $matchRules = PaymentWithdrawalPendingTasks::matchRules();
        $key = sprintf('%s:%s', $this->oaApprovalDto->tenantId, $this->oaApprovalDto->flowKey);
        $capitalFlow = $matchRules[$key] ?? null;

        return !($capitalFlow && !in_array($this->oaApprovalDto->capitalFlow, $capitalFlow, true));
    }

    /**
     * 检查是否已经存在
     * @return bool
     */
    public function checkExists(): bool
    {
        //兼容历史订单
        $businessExists = BusinessAssociationTable::find()->where([
            'business_type' => BusinessAssociationTable::BUSINESS_TYPE_OA_ORDER,
            'main_id'       => $this->oaApprovalOrder->id,
        ])->exists();
        if ($businessExists) {
            return true;
        }
        $orderNoPrefix = $this->getOrderNoPrefix();
        return Order::find()->where(['like', 'order_no', $orderNoPrefix . '%', false])->exists();
    }

    /**
     * 判断是否可以继续处理，如果不能继续处理返回处理成功结束该任务
     * @return array
     */
    public function check(): array
    {
        if (!$this->checkStatusCode()) {
            return [
                'code' => 0,
                'message' => '此审批单状态未审核通过!',
                'data' => null,
            ];
        }
        if (!$this->checkCountry()) {
            return [
                'code' => 0,
                'message' => '此审批单不属于当前国家',
                'data' => null,
            ];
        }
        if (!$this->checkCapitalFlow()) {
            return [
                'code' => 0,
                'message' => '此审批单资金去向不匹配',
                'data' => null,
            ];
        }
        if ($this->checkExists()) {
            return [
                'code' => 0,
                'message' => '此审批单已处理',
                'data' => null,
            ];
        }
        return [];
    }

    /**
     * @inheritDoc
     * @throws \Exception
     */
    public function addOrder(): array
    {
        $orderList = [];
        $serialNo = 1;
        foreach ($this->oaApprovalDto->paymentList as $paymentInfo) {
            $rule = $this->matchSettlementRule();
            if ($rule->status !== Rule::STATUS_ACTIVE) {
                throw new NotFoundHttpException('只有启用状态才能制单');
            }
            $orderNO = $this->getOrderNoPrefix() . $serialNo;
            $order = $rule->makeApply(RuleProcedure::MODE_PAYMENT, $orderNO);
            $procedures = $this->getProcedures($paymentInfo);
            $order->setProcedures($procedures);
            $order->currency = $paymentInfo->currency;
            $order->bill_amount = Yii::$app->getFormatter()->format($paymentInfo->withdrawAmount, ['amount']);
            $order->payment_amount = Yii::$app->getFormatter()->format($paymentInfo->withdrawAmount, ['amount']);
            $order->bill_start_date = Carbon::parse($this->oaApprovalDto->billStartDate)->toDateString();
            $order->bill_end_date = Carbon::parse($this->oaApprovalDto->billEndDate)->toDateString();
            $comment = $this->oaApprovalDto->memo ?: $this->oaApprovalDto->title;
            $order->comment = mb_substr(vsprintf('%s_%s_%s', [
                $this->oaApprovalDto->tenantId,
                $this->oaApprovalDto->entryId,
                $comment,
            ]), 0, 250); // DB限制最大长度255
            $order->amt_snapshot = Json::encode([
                'bill' => $order->payment_amount,
                'adj' => 0,
                'total' => $order->payment_amount,
            ]);
            $order->created_by = 0;
            if (!$order->validate()) {
                throw new UserException(implode(";", $order->getErrorSummary(true)));
            }
            if (!$order->save()) {
                $error = implode(";", $order->getErrorSummary(true));
                throw new UserException('保存Order失败：' . $error);
            }
            $orderList[] = $order;
            $serialNo++;
        }
        return $orderList;
    }

    /**
     * 获取首付款订单前缀
     * @return string
     */
    public function getOrderNoPrefix(): string
    {
        return sprintf(
            'oa-approval-order-%s-%s-',
            $this->oaApprovalOrder->oa_tenant_id,
            $this->oaApprovalOrder->oa_flow_id
        );
    }

    /**
     * @param array $orderList
     * @return void
     * @throws UserException
     * @throws \Throwable
     */
    public function orderSubmit(array $orderList): void
    {
        foreach ($orderList as $order) {
            $affected = (int)Order::updateAll([
                'status' => Order::STATUS_PROCESSING,
            ], [
                'and',
                ['=', 'id', $order->id],
                ['=', 'status', Order::STATUS_NEW],
            ]);
            if ($affected !== 1) {
                throw new UserException('请检查状态和申请人信息是否正确');
            }

            OrderProcedure::updateAll(
                [
                'serial_no' => $this->oaApprovalDto->entryId,
                ],
                [
                    'and',
                    ['=', 'order_id', $order->id],
                    ['!=', 'type', 'payment_auto_withdraw'],
                ]
            );

            OrderProcedureExecutor::make(['id' => $order->id]);
        }
    }

    public function getMemberCardList($refresh = false): array
    {
        if (is_null($this->memberCardList) || $refresh) {
            $memberCards = [];
            foreach (MemberCard::query() as $memberCard) {
                if ($memberCard->member_type === MemberCard::MEMBER_TYPE_WITHDRAW) {
                    $memberCard->user_name = $memberCard->decrypt('user_name', true);
                    $memberCard->account_no = $memberCard->decrypt('account_no', true);
                    $memberCards[] = $memberCard;
                }
            }
            $this->memberCardList = $memberCards;
        }
        return $this->memberCardList;
    }

    /**
     * @inheritDoc
     */
    public function isWhitelistCard($accountNo): bool
    {
        $memberCard = $this->getMemberCardByAccountNo($accountNo);
        return $memberCard !== null;
    }

    /**
     * 获取卡信息
     * @param $accountNo
     * @return MemberCard|null
     */
    public function getMemberCardByAccountNo($accountNo): ?MemberCard
    {
        $memberCards = $this->getMemberCardList();
        if (empty($memberCards)) {
            return null;
        }
        foreach ($memberCards as $memberCard) {
            if ($memberCard->account_no === $accountNo) {
                return $memberCard;
            }
        }
        return null;
    }

    /**
     * 批量添加白名单卡
     * @return void
     * @throws UserException
     */
    public function batchAddWhitelistCard(): void
    {
        $serialNumber = 1;
        foreach ($this->oaApprovalDto->paymentList as $paymentInfo) {
            if (!$this->isWhitelistCard($paymentInfo->bankCard)) {
                $this->addWhitelistCard($paymentInfo, $serialNumber);
                $this->getMemberCardList(true);
                $serialNumber++;
            }
        }
    }

    /**
     * 绑卡
     * @param OaPaymentInfo $data
     * @param $serialNumber
     * @throws UserException
     */
    public function addWhitelistCard(OaPaymentInfo $data, $serialNumber)
    {
        $model = new MemberCard();
        if (empty($data->mobile)) {
            throw new UserException("{$data->bankCard}：手机为空");
        }
        //避免一个OA单添加多张卡时生成的user_id相同
        $model->validate();
        $model->user_uuid = $model->user_uuid . $serialNumber;
        $model->card_uuid = $model->card_uuid . $serialNumber;
        $model->member_type = MemberCard::MEMBER_TYPE_WITHDRAW;
        $model->account_type = MemberCard::BANK_CODE_ACCOUNT;
        $model->account_no = $data->bankCard;
        $model->large_account = '0';
        $model->max_withdraw_amount = 0;
        $model->user_name = $data->bankCardUserName . '_new';
        $model->bank_code = $this->getBankCodeByBankName($data->bank);
        $model->mobile = $data->mobile;
        if (!$model->save()) {
            throw new UserException("添加银行卡:{$model->account_no}失败");
        }
    }

    /**
     * @param string $bankName
     * @return string
     * @throws UserException
     */
    public function getBankCodeByBankName(string $bankName): string
    {
        $bank = Bank::find()->where(['bank_name' => $bankName])->one();
        if (empty($bank)) {
            throw new UserException("找不到：{$bankName}");
        }
        if (empty($bank->bank_code)) {
            throw new UserException("{$bankName}配置的bank_code为空");
        }
        return $bank->bank_code;
    }


    /**
     * @inheritDoc
     */
    public function matchSettlementRule(): Rule
    {
        $config = $this->getRuleConfig();
        if ($this->oaApprovalDto->flowKey === 'pay_for_operation') {
            $capitalFlow = $this->oaApprovalDto->expenseCategory;
        } else {
            $capitalFlow = $this->oaApprovalDto->capitalFlow;
        }

        foreach ($config as $ruleConfig) {
            if (empty($ruleConfig['ruleId'])) {
                continue;
            }
            if (
                !empty($ruleConfig['withdrawChannel'])
                && !in_array($this->oaApprovalDto->channel, $ruleConfig['withdrawChannel'], true)
            ) {
                continue;
            }
            if (
                !empty($ruleConfig['withdrawType'])
                && !in_array($this->oaApprovalDto->withdrawType, $ruleConfig['withdrawType'], true)
            ) {
                continue;
            }
            if (
                !empty($ruleConfig['capitalFlow'])
                && !in_array($capitalFlow, $ruleConfig['capitalFlow'], true)
            ) {
                continue;
            }
            $rule = Rule::findOne($ruleConfig['ruleId']);
            if (!$rule) {
                throw new UserException("RuleId:{$ruleConfig['ruleId']}，不存在");
            }
            return $rule;
        }
        throw new UserException('未匹配到Rule');
    }

    /**
     * @return array|mixed
     * @throws UserException
     */
    public function getRuleConfig()
    {
        $matchRules = (array)config('paymentPendingTasks.matchRules');
        $matchRules = ArrayHelper::index($matchRules, function ($item) {
            return sprintf('%s_%s', $item['tenantId'] ?? '', $item['flowKey'] ?? '');
        });
        $key = sprintf('%s_%s', $this->oaApprovalDto->tenantId, $this->oaApprovalDto->flowKey);
        if (empty($matchRules[$key]['ruleConfig'])) {
            throw new UserException("没有配置{$key}的RuleConfig");
        }
        return $matchRules[$key]['ruleConfig'];
    }

    /**
     * @inheritDoc
     */
    public function callbackHandle()
    {
        if (!$this->oaApprovalDto->validate()) {
            $error = implode(';', $this->oaApprovalDto->getErrorSummary(true));
            throw new UserException('参数校验失败' . $error);
        }
        if ($data = $this->check()) {
            $message = "处理成功_" . $data['message'] ?? '';
            $this->updateOaApprovalOrderStatus(OaApprovalOrder::STATUS_SUCCESS, $message);
            return $data;
        }
        /** @var Transaction $transaction */
        $transaction = Order::getDb()->beginTransaction();
        /** @var Transaction $businessTransaction */
        $businessTransaction = BusinessAssociationTable::getDb()->beginTransaction();
        try {
            if ($this->isPaymentAutoWithdraw()) {
                $this->batchAddWhitelistCard();
            }

            $orderList = $this->addOrder();
            $this->addRelationship($this->oaApprovalOrder->id, $orderList);
            $this->updateOaApprovalOrderStatus(OaApprovalOrder::STATUS_SUCCESS, '处理成功');
            $this->addChannelOfflineTradeJob();
            OaApprovalOrderSubmit::make([
                'oaApprovalOrderId' => $this->oaApprovalOrder->id,
                'orderIdList' => ArrayHelper::getColumn($orderList, 'id'),
            ]);

            $businessTransaction->commit();
            $transaction->commit();

            return [
                'code' => 0,
                'message' => '处理成功',
                'data' => null,
            ];
        } catch (\Throwable $e) {
            Yii::error($e->getMessage());
            $businessTransaction->rollBack();
            $transaction->rollBack();
            throw $e;
        }
    }

    public function addChannelOfflineTradeJob(): void
    {
    }

    public function addChannelOfflineTrade()
    {
    }

    /**
     * @param $mainId
     * @param Order[] $orderList
     * @return void
     * @throws SaveException
     */
    public function addRelationship($mainId, $orderList)
    {
        foreach ($orderList as $order) {
            BusinessAssociationTable::addBusinessAssociationTable(
                BusinessAssociationTable::BUSINESS_TYPE_OA_ORDER,
                $mainId,
                $order->id
            );
        }
    }

    /**
     * 获取账户
     * @return array
     */
    public function getAccountAll(): array
    {
        if (function_exists('\cpop_settlement_accounts')) {
            $accounts = \cpop_settlement_accounts();
        } else {
            $accounts = Account::innerAll();
        }
        return $accounts;
    }

    /**
     * 是否需要自动提现
     * @return true
     */
    public function isPaymentAutoWithdraw(): bool
    {
        return true;
    }

    /**
     * @throws Exception
     * @throws SaveException
     */
    public function addPendingTask(): void
    {
        $transaction = PaymentWithdrawalPendingTasks::getDb()->beginTransaction();
        try {
            foreach ($this->oaApprovalDto->paymentList as $paymentInfo) {
                $model = new PaymentWithdrawalPendingTasks();
                $model->tenant_id            = $this->oaApprovalDto->tenantId;
                $model->oa_id                = $this->oaApprovalDto->entryId;
                $model->oa_create_at         = $this->oaApprovalDto->createdAt;
                $model->status               = PaymentWithdrawalPendingTasks::STATUS_TODO;
                $model->fund_direction       = $this->oaApprovalDto->capitalFlow ?? $this->oaApprovalDto->expenseCategory ?? '';
                $model->payment_from         = $paymentInfo->payer ?? '';
                $model->payment_amount       = Yii::$app->getFormatter()->format($paymentInfo->withdrawAmount, ['amount']);
                $model->currency             = $paymentInfo->currency;
                $model->payee_account_name   = $paymentInfo->bankCardUserName ?? '';
                $model->payee_account_number = $paymentInfo->bankCard ?? '';
                $model->payee                = $paymentInfo->payee ?? '';
                $model->payee_bank           = $paymentInfo->bank ?? '';
                $model->saveOrException();
                $transaction->commit();
            }
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function isHold(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getProcedures(OaPaymentInfo $paymentInfo): array
    {
        $procedures = $this->getTransferRecordProcedures();
        return [$procedures];
    }

    /**
     * 转账(仅记录数据)的procedures
     * @return array
     */
    public function getTransferRecordProcedures(): array
    {
        $data = [];
        if ($this->oaApprovalDto->withdrawType) {
            $data['withdrawType'] = $this->oaApprovalDto->withdrawType;
        }
        return [
            'type' => TransferRecord::key(),
            'mode' => 'payment',
            'config' => [
                'data' => $data
            ],
        ];
    }

    /**
     * 支付系统自动代付 procedures
     * @param OaPaymentInfo $paymentInfo
     * @return array
     * @throws UserException
     */
    public function getPaymentAutoWithdrawProcedures(OaPaymentInfo $paymentInfo): array
    {
        $account = $this->getAccountAll();
        $fromAccount = $this->getPaymentChannel();
        if (empty($account[$fromAccount])) {
            throw new UserException("未找到{$fromAccount}账号");
        }
        $memberCard = $this->getMemberCardByAccountNo($paymentInfo->bankCard);
        if ($memberCard === null) {
            throw new UserException("制单时未找到卡：{$paymentInfo->bankCard}");
        }
        $toAccount = $memberCard->getAccountKey();
        if (empty($account[$toAccount])) {
            throw new UserException("toAccount未设置");
        }
        $commentValue = $this->oaApprovalDto->capitalFlow ?: $this->oaApprovalDto->expenseCategory ?: '打款备注';
        $data = [
            'fromAccountType'  => 'withdraw_channel',
            'toAccountType'    => 'member_card',
            'fromAccount'      => $fromAccount,
            'fromAccountValue' => $account[$fromAccount],
            'toAccount'        => $toAccount,
            'toAccountValue'   => $account[$toAccount],
            'comment'          => '{{comment}}',
            'commentValue'     => $commentValue,
        ];
        if ($this->oaApprovalDto->withdrawType) {
            $data['withdrawType'] = $this->oaApprovalDto->withdrawType;
        }
        return [
            'type'   => PaymentAutoWithdraw::key(),
            'mode'   => 'payment',
            'config' => [
                'data' => $data
            ]
        ];
    }

    /**
     * @inheritDoc
     */
    public function getPaymentChannel(): string
    {
        return '';
    }

    protected function updateOaApprovalOrderStatus($status, $msg): void
    {
        $this->oaApprovalOrder->status        = $status;
        $this->oaApprovalOrder->finish_at     = Carbon::now();
        $this->oaApprovalOrder->response_data = $msg;
        $this->oaApprovalOrder->saveOrException();
    }
}
