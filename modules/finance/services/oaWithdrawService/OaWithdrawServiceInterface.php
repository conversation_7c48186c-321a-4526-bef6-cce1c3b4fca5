<?php

namespace finance\services\oaWithdrawService;

use finance\models\oaApprovalDto\OaPaymentInfo;
use Xlerr\SettlementFlow\Models\Rule;
use yii\base\UserException;
use yii\db\Exception;
use yii\web\NotFoundHttpException;

interface OaWithdrawServiceInterface
{
    /**
     * 匹配对资收付款规则
     * @return Rule
     * @throws UserException
     */
    public function matchSettlementRule(): Rule;

    /**
     * @return mixed
     * @throws UserException
     */
    public function callbackHandle();

    /**
     * 是否为白名单卡
     * @param $accountNo
     * @return bool
     */
    public function isWhitelistCard($accountNo): bool;

    /**
     * 添加白名单卡
     * @param OaPaymentInfo $data
     * @param $serialNumber
     * @throws UserException
     */
    public function addWhitelistCard(OaPaymentInfo $data, $serialNumber);

    /**
     * 是否需要人工介入
     * @return mixed
     */
    public function isHold(): bool;

    /**
     * 获取付款渠道
     * @return string
     */
    public function getPaymentChannel(): string;

    /**
     * 制单
     * @return array
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws UserException
     */
    public function addOrder(): array;

    /**
     * 获取制单的procedures
     * @param OaPaymentInfo $paymentInfo
     * @return array[]
     * @throws UserException
 */
    public function getProcedures(OaPaymentInfo $paymentInfo): array;

    public function addChannelOfflineTradeJob(): void;

    public function addChannelOfflineTrade();

    /**
     * 提交对资收付款订单
     * @param array $orderList
     * @return void
     * @throws UserException
     * @throws \Throwable
     */
    public function orderSubmit(array $orderList): void;
}
