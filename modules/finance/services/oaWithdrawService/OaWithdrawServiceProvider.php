<?php

namespace finance\services\oaWithdrawService;

use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;

/**
 * Class OaWithdrawServiceProvider
 * @package finance\services\oaWithdrawService
 *
 * <AUTHOR> <<EMAIL>>
 * @date   2025/3/31 17:09
 */
class OaWithdrawServiceProvider
{
    /**
     * @param string $processRule
     * @param array $config
     * @return OaWithdrawServiceInterface
     * @throws InvalidConfigException
     * @throws UserException
     */
    public static function getService(
        string $processRule = '',
        array $config = []
    ): OaWithdrawServiceInterface {
        if (empty($processRule)) {
            $processRule = 'default';
        }

        $classMap = [
            '泰国'       => OaWithdrawServiceForDefault::class,
            '墨西哥'     => OaWithdrawServiceForDefault::class,
            '菲律宾'     => OaWithdrawServiceForPhl::class,
            '巴基斯坦'   => OaWithdrawServiceForPak::class,
            '印度尼西亚' => OaWithdrawServiceForDefault::class,
            'default'   => OaWithdrawServiceForDefault::class,
        ];
        if (!isset($classMap[$processRule])) {
            throw new UserException("规则：{$processRule}，不存在");
        }

        $config['class'] = $classMap[$processRule];

        /** @var OaWithdrawServiceInterface $service */
        $service = Yii::createObject($config);
        return $service;
    }
}
