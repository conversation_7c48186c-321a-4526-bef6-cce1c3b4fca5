<?php

namespace finance\services;

use finance\models\oaApprovalDto\OaApprovalDto;
use waterank\audit\provider\AuditProvider;
use yii\base\BaseObject;
use yii\base\UserException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use function waterank\audit\authClient;
use function waterank\audit\config;

/**
 * Class OaService
 * @package finance\services
 *
 * <AUTHOR> <<EMAIL>>
 * @date   2025/6/30 10:59
 */
class OaService extends BaseObject
{
    /**
     * @param $tenantId
     * @param $entryId
     * @return OaApprovalDto
     * @throws UserException
     */
    /**
     * @param $tenantId
     * @param $entryId
     * @return OaApprovalDto
     * @throws UserException
     */
    public static function getOaApprovalDto($tenantId, $entryId): OaApprovalDto
    {
        $oaApprovalData = self::getOaApprovalData($tenantId, $entryId);
        $responseData = ArrayHelper::index($oaApprovalData['data'], 'entry_id');
        $responseData[$entryId]['tenantId'] = $tenantId;
        $oaApprovalDto = new OaApprovalDto();
        $oaApprovalDto->parser($responseData[$entryId]);
        return $oaApprovalDto;
    }

    /**
     * @param $tenantId
     * @param $entryId
     * @return mixed|null
     * @throws UserException
     */
    public static function getOaApprovalData($tenantId, $entryId)
    {
        $tenantMapping = (array)config('tenant_mapping');
        $tenantConfig = $tenantMapping[$tenantId] ?? null;
        if (empty($tenantConfig)) {
            throw new UserException('OA租户与配置关联异常:' . $tenantId);
        }
        /** @var AuditProvider $service */
        $service = authClient($tenantConfig);

        [, $responseRaw] = $service->approvalDetailQueryAndCheck($entryId, 0, false);
        return Json::decode($responseRaw);
    }
}
