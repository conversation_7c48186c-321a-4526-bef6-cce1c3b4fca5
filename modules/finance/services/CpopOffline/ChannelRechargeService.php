<?php

namespace finance\services\CpopOffline;

use finance\worker\CpopOfflineOrder;
use kvmanager\models\KeyValue;
use RuntimeException;
use system\components\CapitalHttpComponent;
use Xlerr\CpopOffline\Services\CpopOfflineService;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * Class RechargeServiceForDefault
 *
 * @package finance\services\rechargeService
 * <AUTHOR> <<EMAIL>>
 * @date   2025/3/31 17:08
 */
class ChannelRechargeService extends CpopOfflineService
{
    public const FEE_TYPE = 'channel_recharge';

    public $region;
    public $channel;
    public $fee_type;
    public $tenant_uuid;
    public $tenant_id;
    public $entry_id;
    public $employee;
    public $company_name;
    public $receiver;
    public $received_card_num;
    public $currency;
    public $amount;
    public $tds_amount;
    public $finished_at;
    public $expense_start;
    public $expense_end;
    /**
     * @var array
     */
    public $trades;

    public function tenants(): array
    {
        return KeyValue::take('oa_tenant_config');
    }

    public function rules(): array
    {
        return [
            [
                [
                    'region',
                    'tenant_uuid',
                    'tenant_id',
                    'entry_id',
                    'employee',
                    'company_name',
                    'receiver',
                    'received_card_num',
                    'currency',
                    'amount',
                    'tds_amount',
                    'finished_at',
                    'expense_start',
                    'expense_end',
                ],
                'required',
            ],
            [['currency'], 'filter', 'filter' => 'strtolower'],
            [['trades'], 'safe'],
            [['fee_type'], 'default', 'value' => self::FEE_TYPE],
            [['channel'], 'filter', 'filter' => [$this, 'matchChannel']],
        ];
    }

    public function fetchData(string $region, string $startDate, string $endDate): array
    {
        $client = CapitalHttpComponent::instance();
        if (!$client->partnerFeeFlow($region, $startDate, $endDate)) {
            throw new RuntimeException('请求接口失败: ' . $client->getRawResponse()->getBody());
        }

        return (array)ArrayHelper::getValue($client->getData(), 'list', []);
    }

    public function process(): void
    {
        CpopOfflineOrder::make([
            'tenant_id' => $this->tenant_id,
            'entry_id' => $this->entry_id,
            'employee' => $this->employee,
            'currency' => $this->currency,
            'channel' => $this->channel,
            'fee_type' => $this->fee_type,
            'company_name' => $this->company_name,
            'receiver' => $this->receiver,
            'received_card_num' => $this->received_card_num,
            'amount' => $this->amount,
            'expense_start' => $this->expense_start,
            'expense_end' => $this->expense_end,
            'finished_at' => $this->finished_at,
            'trades' => $this->trades,
        ]);
    }

    public function matchChannel(): string
    {
        $matchRules = ArrayHelper::getValue(KeyValue::take('cpop_offline_match_rule'), 'channel_recharge');

        foreach ($matchRules as $rules) {
            $channel = ArrayHelper::remove($rules, 'channel');
            foreach ($rules as $attr => $val) {
                if (property_exists($this, $attr) && strtolower((string)($this->$attr)) === strtolower((string)$val)) {
                    return $channel;
                }
            }
        }

        throw new RuntimeException('未匹配到渠道: ' . Json::encode($this));
    }
}
