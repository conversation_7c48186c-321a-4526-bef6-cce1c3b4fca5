<?php

namespace finance\services\CpopOffline;

use kvmanager\models\KeyValue;
use RuntimeException;
use yii\db\Expression;
use yii\helpers\Json;

/**
 * Class RechargeServiceForMex
 *
 * @package finance\services\rechargeService
 * <AUTHOR> <<EMAIL>>
 * @date   2025/3/31 17:08
 */
class ChannelRechargeServiceForMex extends ChannelRechargeService
{
    public function matchChannel(): string
    {
        $channel = KeyValue::find()
            ->select('key')
            ->where([
                'namespace' => 'paysvr',
                'group' => 'KV',
            ])
            ->andWhere(['like', 'value', '{%', false])
            ->andWhere(new Expression('JSON_VALUE(value, \'$.merchant_card_num\') = :cardNum'), [
                'cardNum' => (string)$this->received_card_num,
            ])
            ->column();

        if (empty($channel)) {
            throw new RuntimeException('匹配失败: ' . Json::encode($this));
        }

        if (count($channel) !== 1) {
            throw new RuntimeException('匹配到多个通道: ' . Json::encode($this));
        }

        return current($channel);
    }
}
