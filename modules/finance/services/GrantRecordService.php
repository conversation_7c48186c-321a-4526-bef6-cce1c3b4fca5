<?php

namespace finance\services;

use Carbon\Carbon;
use payment\models\Card;
use payment\models\Withdraw;
use payment\models\WithdrawReceipt;
use xlerr\desensitise\Desensitise;
use yii\base\InvalidConfigException;
use yii\base\Model;
use yii\data\SqlDataProvider;

use function xlerr\desensitise\encrypt;

class GrantRecordService extends Model
{
    public $asset_item_no;
    public $user_phone;
    public $status;
    public $startDate;
    public $endDate;

    public static function statusList()
    {
        return [
            0 => '新建',
            1 => '处理中',
            2 => '成功',
            3 => '失败',
            4 => '冻结',
        ];
    }

    public function formName()
    {
        return '';
    }

    public function rules()
    {
        return [
            [
                [
                    'startDate',
                    'endDate',
                    'asset_item_no',
                    'user_phone',
                    'status',
                ],
                'string',
            ],
        ];
    }

    public function attributeLabels()
    {
        return [
            'asset_item_no' => '资产编号',
            'user_phone'    => '用户电话',
            'status'        => '放款状态',
        ];
    }

    /**
     * @param array $params
     *
     * @return SqlDataProvider
     * @throws InvalidConfigException
     */
    public function search($params)
    {
        $this->load($params);
        $this->validate();

        if (empty($this->asset_item_no . $this->user_phone)) {
            if (!$this->startDate) {
                $this->startDate = Carbon::now()->subDays(6)->toDateString();
            }
            if (!$this->endDate) {
                $this->endDate = Carbon::now()->toDateString();
            }
        }

        $userPhone = null;
        if ($this->user_phone) {
            $userPhone = encrypt($this->user_phone, Desensitise::TYPE_PHONE_NUMBER)->hash;
        }

        $subQuery = WithdrawReceipt::find()
            ->innerJoin(Withdraw::tableName(), 'withdraw_receipt_merchant_key = withdraw_merchant_key')
            ->innerJoin(Card::tableName(), 'withdraw_receipt_card_num = card_num')
            ->select(['MAX(withdraw_receipt_id)'])
            ->where([
                'withdraw_merchant_name' => 'gbiz',
            ])
            ->andFilterWhere([
                'withdraw_merchant_key' => $this->asset_item_no,
                'card_mobile'           => $userPhone,
                'withdraw_status'       => $this->status,
            ])
            ->andFilterWhere([
                'and',
                ['>=', 'withdraw_created_at', $this->startDate],
                [
                    '<',
                    'withdraw_created_at',
                    $this->endDate ? Carbon::parse($this->endDate)->addDay()->toDateString() : null,
                ],
            ])
            ->groupBy('withdraw_merchant_key');

        $query = WithdrawReceipt::find()
            ->innerJoin(Withdraw::tableName(), 'withdraw_receipt_merchant_key = withdraw_merchant_key')
            ->innerJoin(Card::tableName(), 'withdraw_receipt_card_num = card_num')
            ->select([
                'status'            => 'withdraw_status',
                'channel'           => 'withdraw_receipt_channel_name',
                'asset_item_no'     => 'withdraw_merchant_key',
                'channel_serial_no' => 'withdraw_receipt_channel_key',
                'serial_no'         => 'withdraw_receipt_channel_inner_key',
                'user_name'         => 'card_username',
                'user_phone'        => 'card_mobile',
                //                'card_num'          => 'withdraw_receiver_no',
                'failure_message'   => 'withdraw_receipt_channel_resp_message',
                'create_at'         => 'withdraw_created_at',
                'finish_at'         => 'withdraw_finished_at',
                'amount'            => 'withdraw_amount',
            ])
            ->where([
                'withdraw_receipt_id' => $subQuery,
            ]);

        $sql = $query->createCommand()->rawSql;

        return new SqlDataProvider([
            'sql'  => $sql,
            'db'   => WithdrawReceipt::getDb(),
            'sort' => [
                'attributes'   => ['create_at'],
                'defaultOrder' => ['create_at' => SORT_DESC],
            ],
        ]);
    }
}
