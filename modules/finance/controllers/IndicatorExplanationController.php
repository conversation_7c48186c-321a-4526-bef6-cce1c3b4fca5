<?php

namespace finance\controllers;


use finance\models\BizType;
use finance\models\BizTypeSearch;
use finance\models\IndicatorExplanation;
use finance\models\IndicatorExplanationSearch;
use Throwable;
use yii\db\StaleObjectException;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Request;
use yii\web\Response;

/**
 * @property Request $request
 */
class IndicatorExplanationController extends Controller
{
    public function actionIndex(): string
    {
        $searchModel = new IndicatorExplanationSearch();
        $dataProvider = $searchModel->search($this->request->getQueryParams());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @return string
     */
    public function actionCreate(): string
    {
        $model = new IndicatorExplanation();

        if ($this->request->getIsPost() && $model->load($this->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException|\yii\db\Exception
     */
    public function actionUpdate($id): string
    {
        $model = $this->findModel($id);

        if ($this->request->getIsPost() && $model->load($this->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionDelete($id): Response
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * @param $id
     *
     * @return IndicatorExplanation
     * @throws NotFoundHttpException
     */
    protected function findModel($id): IndicatorExplanation
    {
        $model = IndicatorExplanation::findOne(['id' => $id]);
        if ($model === null) {
            throw new NotFoundHttpException('出入金配置不存在!');
        }

        return $model;
    }
}
