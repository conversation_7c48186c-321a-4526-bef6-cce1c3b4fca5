<?php

namespace finance\controllers;

use finance\models\OaApprovalOrder;
use finance\models\OaApprovalOrderSearch;
use finance\worker\OaApprovalWithWithdraw;
use kartik\helpers\Html;
use xlerr\lock\Lock;
use Yii;
use yii\base\UserException;
use yii\db\Transaction;
use yii\web\Controller;

class OaApprovalOrderController extends Controller
{
    /**
     * Lists all Account models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new OaApprovalOrderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionUpdateStatus($id)
    {
        $session = Yii::$app->session;
        $model = new OaApprovalOrder();

        if ($model->load(Yii::$app->request->post())) {
            /** @var Transaction $transaction */
            $transaction = OaApprovalOrder::getDb()->beginTransaction();
            try {
                $lockKey = sprintf('UpdateStatus:%s', $id);
                $lock    = new Lock(Yii::$app->get('redis'));
                $token   = $lock->lock($lockKey, 3600);
                if (!$token) {
                    throw new UserException('获取锁失败');
                }

                $status = (int)$model->status;
                $oaApprovalOrder = OaApprovalOrder::findOne($id);
                if (!$oaApprovalOrder) {
                    throw new UserException('OA审批订单不存在');
                }
                if (
                    $oaApprovalOrder->status !== OaApprovalOrder::STATUS_HOLD
                    && $oaApprovalOrder->status !== OaApprovalOrder::STATUS_PROCESSING
                ) {
                    throw new UserException('当前状态不能编辑');
                }
                if (
                    $oaApprovalOrder->status === OaApprovalOrder::STATUS_PROCESSING
                    && !Yii::$app->user->can('oa_approval_order_terminated')
                ) {
                    throw new UserException('处理中的任务你没有权限修改为终止状态');
                }

                if (
                    $status !== OaApprovalOrder::STATUS_TERMINATED
                    && $status !== OaApprovalOrder::STATUS_PROCESSING
                ) {
                    throw new UserException('处置结果错误');
                }
                $oaApprovalOrder->status = $model->status;
                $oaApprovalOrder->memo   = $model->memo;
                $oaApprovalOrder->saveOrException();
                if ($status === OaApprovalOrder::STATUS_PROCESSING) {
                    OaApprovalWithWithdraw::make([
                        'oaApprovalOrderId'  => $oaApprovalOrder->id
                    ], [
                        'task_from_system' => 'OA',
                    ]);
                }
                $transaction->commit();
                return Html::script('window.top.reloadCurrentTab()');
            } catch (UserException $e) {
                $session->addFlash('error', $e->getMessage());
                $transaction->rollBack();
            } finally {
                $lock->unlock($lockKey, $token);
            }
        }

        return $this->render('update-status', [
            'model' => $model,
        ]);
    }
}