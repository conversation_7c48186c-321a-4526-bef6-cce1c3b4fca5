<?php

namespace finance\controllers;

use backend\behaviors\FormatterBehavior;
use common\models\User;
use finance\approval\RepayRefundApproval;
use finance\services\RepayRecordService;
use repay\components\RepayHttpComponent;
use repay\models\operates\RepayRefund;
use repay\models\RefundRequest;
use repay\models\Withhold;
use repay\models\WithholdHis;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class RepayRecordController extends Controller
{
    public function verbs()
    {
        return [
            'settle-debt' => ['POST'],
        ];
    }

    public function actionSettleDebt($assetItemNo, $serialNo, $channelKey)
    {
        $session = Yii::$app->getSession();

        /** @var User $user */
        $user = Yii::$app->user->getIdentity();

        $http = RepayHttpComponent::instance();
        if (!$http->settleDebt($assetItemNo, $channelKey, $user)) {
            $session->addFlash('error', '操作失败: ' . $http->getError());
        } else {
            $session->addFlash('success', '操作成功！');
        }

        return $this->redirect(['detail', 'serialNo' => $serialNo]);
    }

    public function actionIndex()
    {
        $searchModel = new RepayRecordService();
        $dataProvider = $searchModel->search($this->request->get());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @param string $serialNo
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionDetail(string $serialNo): string
    {
        $model = Withhold::findOne(['withhold_serial_no' => $serialNo]) ??
            WithholdHis::findOne(['withhold_serial_no' => $serialNo]);
        if (!$model) {
            throw new NotFoundHttpException('page not found');
        }

        /** @var array<int, array<string, mixed>> $refundRecords 退款记录 */
        $refundRecords = $model->getRefundRequests()->select([
            'refund_request_withhold_serial_no',
            'refund_request_channel',
            'refund_request_status',
            'refund_request_amount',
            'refund_request_finish_at',
            'refund_request_withhold_channel_key',
            'refund_request_serial_no',
        ])->asArray()->all();

        $refundDataProvider = new ArrayDataProvider([
            'allModels' => $refundRecords,
            'sort' => false,
            'pagination' => false,
        ]);

        // 退款金额
        $refundAmount = array_sum(array_column(array_filter($refundRecords, static function (array $refund): bool {
            return $refund['refund_request_status'] !== RefundRequest::STATUS_WITHDRAW_FAIL;
        }), 'refund_request_amount'));

        // 展示信息记录`account_repay`表
        $delayRecords = $model->getAssetDelay()
            ->where(['asset_delay_status' => 'success'])
            ->select([
                'asset_item_no' => 'asset_delay_item_no',
                'delay_amount' => 'sum(asset_delay_apply_amount)',
            ])
            ->groupBy(['asset_item_no'])
            ->asArray()
            ->all();

        // 展期金额
        $delayAmount = array_sum(array_column($delayRecords, 'delay_amount'));

        $delayDataProvider = new ArrayDataProvider([
            'allModels' => $delayRecords,
            'sort' => false,
            'pagination' => false,
        ]);

        // 还款记录`withhold_order`表
        // $repayRecords = $model->getWithholdOrder()->select([
        //     'repay_amount'  => 'withhold_order_withhold_amount',
        //     'asset_item_no' => 'withhold_order_reference_no',
        // ])->asArray()->all();

        // 还款记录`account_repay`表
        $repayRecords = $model->getAccountRepays()
            ->where(['account_repay_order_type' => 'asset'])
            ->select([
                'asset_item_no' => 'account_repay_order_no',
                'repay_amount' => 'sum(account_repay_amount)',
            ])
            ->groupBy(['asset_item_no'])
            ->asArray()
            ->all();

        // 还款金额
        $repayAmount = array_sum(array_column($repayRecords, 'repay_amount'));

//        // 储备虚户
//        $repayRecords[] = [
//            'repay_amount' => $model->withhold_amount - $refundAmount - $repayAmount - $delayAmount,
//            'asset_item_no' => null, // 资产编号为空就是储备虚户
//        ];

        $repayDataProvider = new ArrayDataProvider([
            'allModels' => $repayRecords,
            'sort' => false,
            'pagination' => false,
        ]);

        return $this->render('detail', [
            'model' => $model,
            'refundDataProvider' => $refundDataProvider,
            'delayDataProvider' => $delayDataProvider,
            'repayDataProvider' => $repayDataProvider,
        ]);
    }

    /**
     * @param int $id
     * @param int $maxAmount 最多可退金额
     * @param string $action
     * @param string $error
     *
     * @return string
     * @throws InvalidConfigException
     * @throws NotFoundHttpException
     * @throws UserException
     * @throws Throwable
     */
    public function actionRefund($id, $maxAmount, $action = 'withdraw', $error = '')
    {
        $model = Withhold::findOne($id) ?? WithholdHis::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException('page not found');
        }

        $refund = new RepayRefund();

        $refund->action = $action;
        $refund->amount = $maxAmount;

        if ($this->request->isPost) {
            $refund->load($this->request->post());
            if ($refund->amount > $maxAmount) {
                $refund->addError('amount', vsprintf('最多可退款:%s%s', [
                    Yii::$app->formatter->format($maxAmount, 'formatAmount'),
                    FormatterBehavior::currencyUnit(),
                ]));
            } elseif ($refund->validate()) {
                $approval = new RepayRefundApproval($refund->toArray(), [
                    'operator' => Yii::$app->getUser()->getIdentity()->getId(),
                ]);
                $approval->audit();
                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        if ($error) {
            Yii::$app->session->setFlash('error', $error);
        }

        return $this->render('refund', [
            'model' => $model,
            'refund' => $refund,
            'maxAmount' => (int)$maxAmount,
        ]);
    }

    /**
     * @param int $id
     *
     * @return Response|string
     * @throws NotFoundHttpException
     * @throws UserException
     * @throws InvalidConfigException
     * @throws Throwable
     */
    public function actionOnlineRefund(int $id)
    {
        if (!$this->request->isPost) {
            throw new UserException('非post请求');
        }

        $withhold = Withhold::findOne($id) ?? WithholdHis::findOne($id);
        if (!$withhold) {
            throw new NotFoundHttpException('page not found');
        }

        $refund = new RepayRefund([
            'action' => RepayRefund::ACTION_ONLINE,
            'amount' => $withhold->withhold_amount,
            'channel' => $withhold->withhold_channel,
            'uuid' => $withhold->withhold_card_num,
            'refundWithholdSerialNo' => $withhold->withhold_serial_no,
        ]);

        $session = Yii::$app->getSession();
        if (!$refund->validate()) {
            $session->addFlash('error', current($refund->firstErrors));

            return $this->redirect($this->request->referrer);
        }

        $authUrl = (new RepayRefundApproval($refund->toArray()))->audit();

        return vsprintf('<script>window.top.open(\'%s\');window.location.href = \'%s\';</script>', [
            $authUrl,
            $this->request->referrer,
        ]);
    }

    /**
     * @return string
     */
    public function actionAuditClose(): string
    {
        return '<script>window.close();</script>';
    }
}
