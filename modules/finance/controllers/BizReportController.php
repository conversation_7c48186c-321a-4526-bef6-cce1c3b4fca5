<?php

namespace finance\controllers;

use finance\models\BizReportSearch;
use yii\web\Controller;

class BizReportController extends Controller
{
    /**
     * @return string
     */
    public function actionFlowInOut(): string
    {
        $searchModel = new BizReportSearch();

        [$dataProvider, $columns] = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'columns' => $columns,
        ]);
    }
}