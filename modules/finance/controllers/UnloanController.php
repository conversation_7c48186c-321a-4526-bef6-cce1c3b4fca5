<?php

namespace finance\controllers;

use common\helpers\ArrayHelper;
use finance\components\risk\UserInfo;
use finance\components\RiskControlHttpComponent;
use finance\models\Asset;
use payment\models\WithdrawReceipt;
use Throwable;
use Yii;
use yii\base\DynamicModel;
use yii\data\ArrayDataProvider;
use yii\web\Controller;

class UnloanController extends Controller
{
    public function actionIndex(): string
    {
        $model = new class () extends DynamicModel {
            public $itemNo;

            public function rules()
            {
                return [
                    [['itemNo'], 'required'],
                ];
            }

            public function getUserInfo(int $userUuid, ?int $cardUuid = null): UserInfo
            {
                try {
                    $client = RiskControlHttpComponent::instance();
                    if ($userInfo =
                        $client->getUserInfo(
                            $userUuid,
                            $cardUuid
                        )
                    ) {
                        return $userInfo;
                    }
                } catch (Throwable $exception) {
                    Yii::$app->getSession()->addFlash('error', $exception->getMessage());
                }

                return new UserInfo();
            }

            /**
             * @return array
             */
            public function getItemNoList(): array
            {
                $data = (array)preg_split('/\r*\n|,|;/', (string)$this->itemNo, -1, PREG_SPLIT_NO_EMPTY);

                return array_filter(array_map('trim', $data));
            }

            public function formName(): string
            {
                return '';
            }

            public function attributeLabels(): array
            {
                return [
                    'itemNo' => '资产编号',
                    'withdraw_receipt_transfer_option' => '收款账户类型',
                    'withdraw_receipt_channel_name' => '放款通道',
                    'asset_loan_channel' => '资金方',
                    'withdraw_receipt_card_num' => '收款账号',
                    'withdraw_receipt_transfer_mode' => '收款账户',
                    'withdraw_receipt_channel_inner_key' => '交易流水号',
                    'asset_granted_principal_amount' => '放款本金',
                    'asset_actual_grant_at' => '放款日期',
                    'phone_encrypt' => '用户手机号',
                    'name_encrypt' => '用户姓名',
                    'withdraw_receipt_id_num' => '用户证件号'
                ];
            }
        };

        $dataProvider = new ArrayDataProvider([
            'allModels' => [],
            'sort' => false,
            'pagination' => false,
        ]);

        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            $list = $model->getItemNoList();
            $assets = Asset::find()->where(['asset_item_no' => $list])->select([
                'asset_granted_principal_amount',
                'asset_actual_grant_at',
                'asset_loan_channel',
                'asset_item_no'
            ])->asArray()->all();
            $assets = ArrayHelper::index($assets, 'asset_item_no');
            $withdraw = WithdrawReceipt::find()->where([
                'withdraw_receipt_merchant_key' => array_map(static function ($val) {
                    return $val . 'w';
                }, $list), 'withdraw_receipt_status' => 2])
                ->select([
                    'withdraw_receipt_transfer_option',
                    'withdraw_receipt_channel_name',
                    'withdraw_receipt_card_num',
                    'withdraw_receipt_id_num',
                    'withdraw_receipt_transfer_mode',
                    'withdraw_receipt_user_uuid',
                    'withdraw_receipt_card_uuid',
                    'withdraw_receipt_channel_inner_key',
                    'withdraw_receipt_merchant_key'
                ])->asArray()->all();
            foreach ($withdraw as &$item) {
                $itemNo = rtrim($item['withdraw_receipt_merchant_key'], 'w');
                $userInfo = $model->getUserInfo((int)$item['withdraw_receipt_user_uuid'], (int)$item['withdraw_receipt_card_uuid']);

                $item['phone_encrypt'] = $userInfo->individual_phone_encrypt;
                $item['name_encrypt'] = $userInfo->individual_full_name_encrypt;
                $item['asset_loan_channel'] = $assets[$itemNo]['asset_loan_channel'] ?? '-';
                $item['asset_granted_principal_amount'] = $assets[$itemNo]['asset_granted_principal_amount'] ?? '-';
                $item['asset_actual_grant_at'] = $assets[$itemNo]['asset_actual_grant_at'] ?? '-';
                $item['asset_item_no'] = $itemNo;
            }
            unset($item);
            $selectItemNo = array_column($withdraw, 'asset_item_no');
            $diff = array_diff($list, $selectItemNo);
            if ($diff) {
                $session = Yii::$app->getSession();
                $session->setFlash('warning', sprintf(' [%s] 的资产编号未查询到对应的信息!', implode(', ', array_values($diff))));
            }
            $dataProvider->setModels($withdraw);
        }

        return $this->render('index', [
            'model' => $model,
            'dataProvider' => $dataProvider,
        ]);
    }
}