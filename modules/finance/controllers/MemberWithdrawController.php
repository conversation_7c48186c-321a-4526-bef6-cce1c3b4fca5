<?php

namespace finance\controllers;

use kartik\depdrop\DepDropAction;
use payment\models\Bank;
use payment\models\MemberCard;
use payment\models\MemberCardSearch;
use payment\models\PaymentMode;
use Yii;
use yii\db\Expression;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

class MemberWithdrawController extends Controller
{
    /**
     * Lists all Channel models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new MemberCardSearch();
        $dataProvider = $searchModel->search([
                'member_type' => MemberCard::MEMBER_TYPE_WITHDRAW,
            ] + Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new Channel model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate($accountNo = '')
    {
        $model = new MemberCard();

        $model->account_no = $accountNo;
        $model->member_type = MemberCard::MEMBER_TYPE_WITHDRAW;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Channel model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        $fieldsToDecrypt = ['account_no', 'user_name', 'mobile', 'email', 'id_num', 'address'];

        foreach ($fieldsToDecrypt as $field) {
            $model->$field = $model->decrypt($field, true);
        }

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    public function actionView($id)
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Finds the Channel model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return MemberCard the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = MemberCard::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }


    public function actions(): array
    {
        return [
            'bank-code-list' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($type) {
                    $result = $this->selectDataByType($type);
                    $data = [];
                    foreach ($result as $code => $name) {
                        $data[] = [
                            'id' => $code,
                            'name' => $name,
                        ];
                    }
                    return $data;
                },
                'selectedCallback' => function ($type) {
                    $result = $this->selectDataByType($type);
                    $defaultSelect = Yii::$app->request->get('default');
                    if (array_key_exists($defaultSelect, $result)) {
                        return $defaultSelect;
                    }
                    return current($result);
                }
            ]
        ];
    }

    private function selectDataByType($type): array
    {
        if (in_array($type, ['account', 'clabe'])) {
            return Bank::find()->select(new Expression('CONCAT(`bank_code`, ":", `bank_name`)'))->indexBy('bank_code')->column();
        }

        if ($type === 'wallet') {
            return PaymentMode::find()->where(['payment_mode_option' => 'wallet'])
                ->select(new Expression('CONCAT(`payment_mode_code`, ":", `payment_mode_name`)'))->indexBy('payment_mode_code')->column();
        }

        return [];
    }
}
