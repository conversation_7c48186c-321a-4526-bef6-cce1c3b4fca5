<?php

namespace finance\controllers;

use backend\behaviors\FormatterBehavior;
use common\models\User;
use finance\approval\OaWithdrawApproval;
use finance\approval\WithdrawApproval;
use finance\models\OaWithdraw;
use finance\models\PaymentWithdrawalPendingTasks;
use finance\models\Withdraw;
use finance\models\WithdrawSearch;
use finance\worker\AutoWithdraw;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Throwable;
use Yii;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Transaction;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Request;
use yii\web\Response;


/**
 * WithdrawController implements the CRUD actions for Withdraw model.
 *
 * @property-read Request $request
 */
class WithdrawController extends Controller
{
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'retry' => ['POST'],
                    'void' => ['POST'],
                ],
            ],
        ];
    }

    public function actionIndex(): string
    {
        $searchModel = new WithdrawSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new WithdrawalManual model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * @param int $id
     *
     * @return Response
     * @throws UserException
     * @throws IdentityProviderException
     */
    public function actionPreviewOaAttachment(int $id): Response
    {
        $url = OaWithdraw::parsePreviewUrl($id);
        if (!$url) {
            throw new UserException('附件地址为空');
        }

        return $this->redirect($url);
    }

    /**
     * @return string|Response
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function actionNew()
    {
        $model = new Withdraw();
        if ($this->request->isPost) {
            /** @var User $user */
            $user = Yii::$app->user->identity;
            $paramsKey = $model->formName();
            $params = $this->request->post($paramsKey);
            $params = array_merge($params, [
                'withdraw_type_text' => $params['withdraw_type'] ?? '',
                'withdraw_status' => Withdraw::STATUS_APPROVAL_CREATING,
                'withdraw_create_user' => $user->username,
                'withdraw_create_user_email' => $user->email,
                'withdraw_amount_human' => Yii::$app->formatter->asFormatAmount($params['withdraw_amount'] ?? 0),
            ]);
            if ($model->load($params, '') && $model->validate()) {
                $approval = new WithdrawApproval($params, [
                    'operator' => $user->id,
                ]);
                $approval->audit();

                return $this->redirect(['index']);
            }
        }

        return $this->render('new', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $pendingTasksId
     * @param int $pendingOaId
     * @param string $view
     *
     * @return string|Response
     * @throws InvalidConfigException
     * @throws KVException
     * @throws Throwable
     * @throws UserException
     */
    public function actionNewFromOa(int $pendingTasksId, int $pendingOaId, string $view)
    {
        $model = new OaWithdraw();

        /** @var PaymentWithdrawalPendingTasks $pendingTask */
        $pendingTask = PaymentWithdrawalPendingTasks::findOne(['id' => $pendingTasksId]);

        // 计算实际的提现金额
        $withdrawSuccessAmount = $pendingTask->successAmount;

        $withdrawIngAmount = (int)$pendingTask->getWithdraw()
            ->andWhere([
                'withdraw_status' => [
                    Withdraw::STATUS_APPROVAL_CREATING,
                    Withdraw::STATUS_PROCESSING,
                    Withdraw::STATUS_NEW,
                    Withdraw::STATUS_PAYMENT_ING,
                ],
            ])
            ->sum('withdraw_amount');

        /** @var FormatterBehavior $formatter */
        $formatter = Yii::$app->getFormatter();

        $currencyUnit = FormatterBehavior::currencyUnit();

        $session = Yii::$app->getSession();

        $withdrawChannelConfig = KeyValue::take('withdraw_channel_config');
        $defaultChannelConfig = ArrayHelper::remove($withdrawChannelConfig, 'default', [
            'scale' => 1,
            'float' => 0,
        ]);

        if ($this->request->isPost) {
            /** @var User $user */
            $user = Yii::$app->user->identity;
            $params = $this->request->post($model->formName());
            $params = array_merge($params, [
                'withdraw_type_text' => $params['withdraw_type'] ?? '',
                'withdraw_status' => Withdraw::STATUS_APPROVAL_CREATING,
                'withdraw_create_user' => $user->username,
                'withdraw_create_user_email' => $user->email,
                'withdraw_amount_human' => $formatter->asFormatAmount($params['withdraw_amount'] ?? 0),
            ]);

            $currentChannelConfig = $withdrawChannelConfig[$params['withdraw_channel']] ?? $defaultChannelConfig;

            $paymentTotalAmount = $pendingTask->payment_amount * ($currentChannelConfig['scale'] ?? 1) + ($currentChannelConfig['float'] ?? 0);

            $needWithdrawAmount = max($paymentTotalAmount - $withdrawSuccessAmount - $withdrawIngAmount, 0);

            $model->load($params, '');

            if ($model->withdraw_amount > $needWithdrawAmount) {
                $session->addFlash(
                    'error',
                    vsprintf(
                        '允许提现总金额:[%s%s], 已成功提现:[%s%s], 处理中金额: [%s%s], 本次最大提现金额不能超过[%s%s]',
                        [
                            $formatter->asFormatAmount($paymentTotalAmount),
                            $currencyUnit,
                            $formatter->asFormatAmount($withdrawSuccessAmount),
                            $currencyUnit,
                            $formatter->asFormatAmount($withdrawIngAmount),
                            $currencyUnit,
                            $formatter->asFormatAmount($needWithdrawAmount),
                            $currencyUnit,
                        ]
                    )
                );
            } elseif ($model->validate()) {
                $params['attachmentUrl'] = $model->attachmentUrl;
                $approval = new OaWithdrawApproval($params, [
                    'pendingTasksId' => $pendingTasksId,
                    'operator' => $user->id,
                ]);

                $approval->audit();

                return $this->redirect(['index']);
            }
        } else {
            $model->withdraw_type = $pendingTask->fund_direction;
            $model->pendingOaId = $pendingOaId;
            $model->pendingTasksId = $pendingTasksId;

            $session->addFlash(
                'warning',
                vsprintf('收款账户: %s, 已成功提现金额: %s%s, 处理中金额: %s%s', [
                    $pendingTask->payee_account_number,
                    $formatter->asFormatAmount($withdrawSuccessAmount),
                    $currencyUnit,
                    $formatter->asFormatAmount($withdrawIngAmount),
                    $currencyUnit,
                ])
            );
        }

        return $this->render($view, [
            'model' => $model,
            'pendingTask' => $pendingTask,
            'noLimitWithdrawChannel' => array_keys($withdrawChannelConfig),
        ]);
    }

    /**
     * @return string|Response
     * @throws Exception
     * @throws InvalidConfigException
     * @throws UserException
     * @throws Throwable
     */
    public function actionCreate()
    {
        $model = new Withdraw();
        if ($this->request->isPost) {
            /** @var User $user */
            $user = Yii::$app->user->identity;
            $paramsKey = $model->formName();
            $params = $this->request->post($paramsKey);
            $params = array_merge($params, [
                'withdraw_type_text' => $params['withdraw_type'] ?? '',
                'withdraw_status' => Withdraw::STATUS_APPROVAL_CREATING,
                'withdraw_create_user' => $user->username,
                'withdraw_create_user_email' => $user->email,
                'withdraw_amount_human' => Yii::$app->formatter->asFormatAmount($params['withdraw_amount'] ?? 0),
            ]);
            if ($model->load($params, '') && $model->validate()) {
                $approval = new WithdrawApproval($params, [
                    'operator' => $user->id,
                ]);

                $approval->audit();

                return $this->redirect(['index']);
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    public function actionAccountManage()
    {
        if ($this->request->isPost) {
            $accountList = $this->request->post('account');
            $number = $this->request->post('number');
            $uuid = $this->request->post('uuid');
            $userid = $this->request->post('userid');
            $data = [];
            foreach ($accountList as $k => $account) {
                $item = [
                    'receive_account' => trim($account),
                    'receive_number' => trim($number[$k] ?? ''),
                    'receive_uuid' => trim($uuid[$k] ?? ''),
                    'receive_userid' => trim($userid[$k] ?? ''),
                ];
                if (empty(array_filter($item))) {
                    continue;
                }

                $data[] = $item;
            }

            $result = (int)KeyValue::updateAll([
                'value' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            ], [
                'namespace' => 'biz',
                'group' => 'KV',
                'key' => 'withdraw_receive_config',
            ]);
            if ($result === 1) {
                $model = KeyValue::findOne([
                    'namespace' => 'biz',
                    'group' => 'KV',
                    'key' => 'withdraw_receive_config',
                ]);

                if ($model) {
                    $model->cleanCache();
                }
                Yii::$app->getSession()->addFlash('success', '保存成功');
            }

            return $this->redirect(['account-manage']);
        }

        $accounts = KeyValue::take('withdraw_receive_config');

        return $this->render('account-manage', [
            'accounts' => $accounts,
        ]);
    }

    public function actionGetNumber(): Response
    {
        $account = Yii::$app->request->post('account');
        $config = KeyValue::take("withdraw_receive_config");
        $number = '-';
        $uuid = '-';
        $userId = '-';
        foreach ($config as $item) {
            if ($item['receive_account'] == $account) {
                $number = $item['receive_number'] ?? '-';
                $uuid = $item['receive_uuid'] ?? '-';
                $userId = $item['receive_userid'] ?? '';
            }
        }

        return $this->asJson([
            'code' => 1,
            'number' => $number,
            'uuid' => $uuid,
            'userid' => $userId,
        ]);
    }

    public function actionGetVirtualAccount(): Response
    {
        $channel = Yii::$app->request->post('channel');
        $config = KeyValue::take("withdraw_channel_account_map");
        $accountInfo = $config[$channel] ?? [];

        return $this->asJson([
            'code' => 1,
            'name' => $accountInfo['account_name'] ?? '-',
        ]);
    }

    /**
     * 提现失败后重试
     *
     * @param int $id
     *
     * @return Response
     * @throws InvalidConfigException
     */
    public function actionRetry(int $id): Response
    {
        $session = Yii::$app->session;
        if (!Yii::$app->user->can('withdraw_retry')) {
            $session->setFlash('error', '缺少失败后重试操作的权限!');
        } else {
            /** @var Transaction $transaction */
            $transaction = Withdraw::getDb()->beginTransaction();
            try {
                $condition = [
                    'withdraw_id' => $id,
                    'withdraw_status' => Withdraw::STATUS_PAYMENT_FAILED,
                ];
                $data = Withdraw::find()
                    ->select([
                        'withdraw_merchant_key',
                        'withdraw_trade_no',
                    ])
                    ->where($condition)
                    ->asArray()
                    ->one();
                $data = preg_replace_callback('/(^.*?)(MR(\d+))?$/', static function ($mcs) {
                    return sprintf('%sMR%d', $mcs[1], (int)($mcs[3] ?? 0) + 1);
                }, $data);
                $data['withdraw_status'] = Withdraw::STATUS_NEW;

                $affected = (int)Withdraw::updateAll($data, $condition);
                if ($affected !== 1) {
                    throw new Exception('重置订单号失败');
                }
                AutoWithdraw::make([
                    'dataId' => $id,
                    'action' => 'retry',
                ]);
                $transaction->commit();
            } catch (Throwable $e) {
                $transaction->rollBack();
                $session->setFlash('error', '操作失败, 请重试');
            }
        }

        return $this->redirect($this->request->referrer);
    }

    public function actionVoid(int $id): Response
    {
        $affected = (int)Withdraw::updateAll([
            'withdraw_status' => Withdraw::STATUS_VOID,
        ], [
            'withdraw_id' => $id,
            'withdraw_status' => Withdraw::STATUS_PAYMENT_FAILED,
        ]);

        if ($affected !== 1) {
            Yii::$app->getSession()->setFlash('error', '操作失败, 请重试');
        } else {
            Yii::$app->getSession()->setFlash('success', '操作成功');
        }

        return $this->redirect($this->request->referrer);
    }

    /**
     * @param int $id
     *
     * @return Withdraw
     * @throws NotFoundHttpException
     */
    protected function findModel(int $id): Withdraw
    {
        if (($model = Withdraw::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
