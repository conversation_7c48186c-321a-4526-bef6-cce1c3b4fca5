<?php

namespace finance\controllers;

use common\models\User;
use finance\approval\WithdrawApproval;
use finance\models\Withdraw;
use finance\models\WithdrawSearch;
use finance\worker\ManualWithdrawal;
use Throwable;
use Xlerr\SettlementFlow\Models\Account;
use Yii;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Transaction;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * WithdrawController implements the CRUD actions for Withdraw model.
 */
class WithdrawController extends Controller
{
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'retry' => ['POST'],
                    'void' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * @param string $channel
     * @param string $transferType
     *
     * @return Response
     */
    public function actionReceiveAccount(string $channel = '', string $transferType = ''): Response
    {
        if (empty($channel) || empty($transferType)) {
            $cards = [];
        } elseif ($transferType === Withdraw::TRANSFER_TYPE_WITHDRAWAL_TO_CARD) {
            $cards = Withdraw::globalEntityAccountList($channel);
        } else {
            $cards = array_filter(
                Account::innerAll(),
                static fn($card) => $card['type'] === Account::TYPE_GLOBAL_CARD_ACCOUNT
            );
        }

        return $this->asJson($cards);
    }

    public function actionIndex(): string
    {
        $searchModel = new WithdrawSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new WithdrawalManual model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * @return string
     * @throws Exception
     * @throws InvalidConfigException
     * @throws UserException
     * @throws Throwable
     */
    public function actionCreate(): string
    {
        $model = new Withdraw();

        if ($this->request->isPost) {
            /** @var User $user */
            $user = Yii::$app->user->identity;

            $params = $this->request->post($model->formName());
            $params = array_merge($params, [
                'withdraw_create_user' => $user->id,
                'withdraw_create_user_email' => $user->email,
                'withdraw_status' => Withdraw::STATUS_NEW,
            ]);

            $model->load($params, '');

            if ($model->save()) {
                $approval = new WithdrawApproval(array_merge($params, [
                    'withdraw_channel_text' => $model->withdraw_channel,
                    'withdraw_amount_human' => Yii::$app->formatter->asFormatAmount($model->withdraw_amount),
                    'withdraw_type_text' => $model->withdraw_type,
                ]), [
                    'operator' => $user->id,
                ]);

                $approval->audit();

                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        if (empty($model->withdraw_merchant_key)) {
            $model->withdraw_merchant_key = Withdraw::generateRandomNo();
        }
        if (empty($model->withdraw_trade_no)) {
            $model->withdraw_trade_no = Withdraw::generateRandomNo();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $id
     *
     * @return Response
     */
    public function actionRetry(int $id): Response
    {
        $session = Yii::$app->session;
        if (!Yii::$app->user->can('withdraw_retry')) {
            $session->setFlash('error', '缺少失败后重试操作的权限!');
        } else {
            /** @var Transaction $transaction */
            $transaction = Withdraw::getDb()->beginTransaction();
            try {
                $affected = (int)Withdraw::updateAll([
                    'withdraw_merchant_key' => Withdraw::generateRandomNo(),
                    'withdraw_trade_no' => Withdraw::generateRandomNo(),
                    'withdraw_status' => Withdraw::STATUS_NEW,
                ], [
                    'withdraw_id' => $id,
                    'withdraw_status' => Withdraw::STATUS_PAYMENT_FAILED,
                ]);

                if ($affected !== 1) {
                    throw new Exception('重置订单号失败');
                }

                ManualWithdrawal::make([
                    'dataId' => $id,
                ]);

                $transaction->commit();
            } catch (Throwable $e) {
                $transaction->rollBack();
                $session->setFlash('error', $e->getMessage());
            }
        }

        return $this->redirect($this->request->referrer);
    }

    public function actionVoid(int $id): Response
    {
        $affected = (int)Withdraw::updateAll([
            'withdraw_status' => Withdraw::STATUS_VOID,
        ], [
            'withdraw_id' => $id,
            'withdraw_status' => Withdraw::STATUS_PAYMENT_FAILED,
        ]);

        if ($affected !== 1) {
            Yii::$app->getSession()->setFlash('error', '操作失败, 请重试');
        } else {
            Yii::$app->getSession()->setFlash('success', '操作成功');
        }

        return $this->redirect($this->request->referrer);
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findModel($id): Withdraw
    {
        if (($model = Withdraw::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
