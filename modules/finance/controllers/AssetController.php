<?php

namespace finance\controllers;

use backend\grid\MoneyDataColumn;
use Carbon\Carbon;
use finance\components\risk\BankCard;
use finance\components\risk\UserInfo;
use finance\components\RiskControlHttpComponent;
use finance\models\Asset;
use finance\models\AssetSearch;
use finance\models\ChangeAssetForm;
use grant\components\GrantHttpComponent;
use grant\models\AssetBorrower;
use grant\models\ManualAsset;
use grant\models\WithdrawOrder;
use kvmanager\models\KeyValue;
use repay\models\AssetExtend;
use Throwable;
use xlerr\common\helpers\CsvHelper;
use xlerr\desensitise\DesensitiseWidget;
use Yii;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\RangeNotSatisfiableHttpException;
use yii\web\Response;

use function xlerr\desensitise\decrypt;

/**
 * AssetController implements the CRUD actions for Asset model.
 */
class AssetController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Asset models.
     *
     * @return mixed
     * @throws InvalidConfigException
     * @throws RangeNotSatisfiableHttpException
     */
    public function actionIndex()
    {
        $searchModel = new AssetSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        $itemNoList = array_column($dataProvider->models, 'asset_item_no');
        $withdrawInfo = WithdrawOrder::withdrawInfo($itemNoList);
        $borrowerInfo = AssetBorrower::borrowerInfo($itemNoList);
        if ($this->request->get('download')) {
            $stream = CsvHelper::build($dataProvider->getModels(), $this->columns($withdrawInfo, $borrowerInfo));

            return $this->response->sendStreamAsFile(
                $stream,
                vsprintf('%s_%s.csv', [
                    '资产列表',
                    Carbon::now()->toDateString(),
                ]),
                [
                    'mimeType' => 'application/csv',
                ]
            );
        }

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'withdrawInfo' => $withdrawInfo,
            'borrowerInfo' => $borrowerInfo,
        ]);
    }

    /**
     * @param array $withdrawInfo
     * @param array $borrowerInfo
     *
     * @return array
     */
    protected function columns($withdrawInfo, $borrowerInfo): array
    {
        return [
            [
                'attribute' => 'asset_item_no',
                'label' => '资产编号',
            ],
            [
                'attribute' => 'asset_item_no',
                'label' => '资产关联订单号',
                'format' => function ($itemNo) {
                    return AssetExtend::find()->where([
                        'asset_extend_asset_item_no' => $itemNo,
                        'asset_extend_type' => 'ref_order_no',
                    ])->select('asset_extend_val')->scalar() ?? '-';
                },
            ],
            [
                'attribute' => 'asset_item_no',
                'label' => '放款通道',
                'format' => function ($itemNo) use ($withdrawInfo) {
                    return $withdrawInfo[$itemNo . WithdrawOrder::SPLICE_STR]['withdraw_record_channel'] ?? '-';
                },
            ],
            [
                'attribute' => 'asset_item_no',
                'label' => '放款订单号',
                'format' => function ($itemNo) use ($withdrawInfo) {
                    return $withdrawInfo[$itemNo . WithdrawOrder::SPLICE_STR]['withdraw_record_channel_key'] ?? '-';
                },
            ],
            [
                'attribute' => 'asset_item_no',
                'label' => '手机号',
                'format' => function ($itemNo) use ($borrowerInfo) {
                    if (!empty($borrowerInfo[$itemNo]['asset_borrower_mobile'])) {
                        return decrypt($borrowerInfo[$itemNo]['asset_borrower_mobile']);
                    }

                    return '-';
                },
            ],
            [
                'attribute' => 'period_count',
                'label' => '周期',
                'value' => function ($asset) {
                    return $asset['period_count'] . '期';
                },
            ],
            [
                'attribute' => 'product_category',
                'label' => '每期天数',
                'value' => function ($asset) {
                    return $asset['product_category'] . '天';
                },
            ],
            [
                'attribute' => 'status',
                'label' => '资产状态',
                'format' => ['in', Asset::statusList()],
            ],
            [
                'attribute' => 'loan_channel',
                'label' => '资金方',
            ],
            [
                'attribute' => 'type',
                'label' => '资金类型',
            ],
            [
                'label' => '借款本金',
                'attribute' => 'principal_amount',
                'class' => MoneyDataColumn::class,
            ],
            [
                'label' => '实际放款金额',
                'attribute' => 'granted_principal_amount',
                'class' => MoneyDataColumn::class,
            ],
            [
                'label' => '来源包',
                'attribute' => 'from_app',
            ],
            [
                'label' => '预计放款时间',
                'attribute' => 'grant_at',
                'format' => 'VDateTime',
            ],
            [
                'label' => '实际放款时间',
                'attribute' => 'actual_grant_at',
                'format' => 'VDateTime',
            ],
            [
                'label' => '创建时间',
                'attribute' => 'create_at',
                'format' => 'VDateTime',
            ],
        ];
    }


    /**
     * Displays a single Asset model.
     *
     * @param null $id
     * @param null $itemNo
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionView($id = null, $itemNo = null)
    {
        if (isset($id)) {
            $model = $this->findModel($id);
        } else {
            $model = Asset::findOne(['asset_item_no' => $itemNo]);
            if (!$model) {
                throw new NotFoundHttpException('The requested page does not exist.');
            }
        }


        $relationModel = AssetExtend::find()->where([
            'asset_extend_type' => 'ref_order_no',
            'asset_extend_asset_item_no' => $model->asset_item_no,
        ])->select('asset_extend_val')->scalar();

        if ($relationModel) {
            $relationModel = Asset::find()->where(['asset_item_no' => $relationModel])->one();
        }

        $userInfo = $this->getUserInfo($model);
        $bankCard = $this->getBankCard($model);

        return $this->render('view', [
            'model' => $model,
            'withdrawInfo' => current(
                array_merge([
                    $model->asset_item_no . WithdrawOrder::SPLICE_STR => [
                        'withdraw_record_channel' => '',
                        'withdraw_record_channel_key' => '',
                        'withdraw_order_asset_item_no' => '',
                    ],
                ], WithdrawOrder::withdrawInfo([$model->asset_item_no], true))
            ),
            'userInfo' => $userInfo,
            'table' => $this->showTableForUser($userInfo, $bankCard),
            'relationModel' => $relationModel ?? null,
        ]);
    }

    /**
     * Finds the Asset model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return Asset the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id): Asset
    {
        if (($model = Asset::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }


    /**
     * @param Asset $asset
     *
     * @return BankCard
     */
    public function getBankCard(Asset $asset): BankCard
    {
        try {
            $user = $asset->borrower;
            $client = RiskControlHttpComponent::instance();
            if ($bankCard = $client->getBankCardByCardUUID($user->asset_borrower_card_uuid)) {
                return $bankCard;
            }
        } catch (Throwable $exception) {
            Yii::$app->getSession()->addFlash('error', $exception->getMessage());
        }

        return new BankCard();
    }

    /**
     * @param Asset $asset
     *
     * @return UserInfo
     */
    public function getUserInfo(Asset $asset): UserInfo
    {
        $user = $asset->borrower;
        try {
            $client = RiskControlHttpComponent::instance();
            if ($userInfo =
                $client->getUserInfo(
                    (int)$user->asset_borrower_uuid,
                    $user->asset_borrower_withdraw_type === AssetBorrower::WITHDRAW_TYPE_OFFLINE ? null
                        : (int)$user->asset_borrower_card_uuid
                )
            ) {
                // 通过资产资金方和实际放款日期控制是否为手动放款
                $config = KeyValue::take('grant_manual_by_channel_and_date');
                $date = $config[$asset->asset_loan_channel] ?? null;
                if ($date && Carbon::parse($asset->asset_actual_grant_at) < Carbon::parse($date)->addDay()) {
                    // 获取手动放款的资产的收款人卡号, 卡号为密文
                    $userInfo->bank_account = $this->queryGrantManualBankInfo($asset->asset_item_no);
                } else {
                    $userInfo->bank_account =
                        $client->getBankCardByCardUUID((int)$user->asset_borrower_card_uuid)->no_encrypt;
                }

                /*
                else {
                    //TODO 获取自动放款的资产的收款人卡号, 卡号为密文 暂时不走查支付
                  $userInfo->bank_account = $this->queryPaymentBankInfo($asset->item_no);
                }*/

                return $userInfo;
            }
        } catch (Throwable $exception) {
            Yii::$app->getSession()->addFlash('error', $exception->getMessage());
        }

        return new UserInfo();
    }

    protected function queryGrantManualBankInfo(string $assetItemNo)
    {
        return ManualAsset::find()->where([
            'manual_asset_item_no' => $assetItemNo,
        ])->select('manual_asset_receiver_card_no')->scalar() ?? '';
    }

    /**
     * 展示用户信息和银行卡的table
     *
     * @param UserInfo $userInfo
     * @param BankCard $bankCard
     *
     * @return array[]
     */
    protected function showTableForUser(UserInfo $userInfo, BankCard $bankCard): array
    {
        return [
            [
                'label' => '姓名',
                'format' => 'raw',
                'value' => static function () use ($userInfo) {
                    $username = $userInfo->individual_full_name_encrypt;
                    if (!is_array($username)) {
                        $username = [$username];
                    }
                    $hash = '';
                    foreach ($username as $nameEncrypt) {
                        $hash .= DesensitiseWidget::decrypt($nameEncrypt, true) . ' ';
                    }

                    return $hash;
                },
            ],
            [
                'label' => '手机号',
                'format' => static function () use ($userInfo) {
                    return Yii::$app->getFormatter()->format($userInfo->individual_phone_encrypt, ['DDecrypt', true]);
                },
                'value' => null,
            ],
            [
                'label' => '银行卡号',
                'format' => static function () use ($userInfo) {
                    $result = '';
                    foreach ((array)$userInfo->bank_account as $cardNum) {
                        $result .= Yii::$app->getFormatter()->format($cardNum, ['DDecrypt', true]);
                    }

                    return $result;
                },
                'value' => null,
            ],
            [
                'label' => '所属银行/钱包',
                'value' => fn() => $bankCard->name ?? '-',
            ],
            [
                'label' => 'userUUID',
                'value' => fn() => $userInfo->userUuid,
            ],
            [
                'label' => 'cardUUID',
                'value' => fn() => $userInfo->cardUuid ?? '-',
            ],
            [
                'label' => '放款方式名称',
                'value' => fn() => $bankCard->method_name ?? '-',
            ],
        ];
    }

    /**
     * @return string
     * @throws InvalidConfigException
     */
    public function actionSimpleSearch(): string
    {
        $searchModel = new AssetSearch();
        $dataProvider = $searchModel->simpleSearch(Yii::$app->request->queryParams);

        $withdrawInfo = WithdrawOrder::withdrawInfo(array_column($dataProvider->models, 'asset_item_no'));

        return $this->render('simple', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'withdrawInfo' => $withdrawInfo,
        ]);
    }

    public function actionChangeAssetGrantAt($id)
    {
        $asset = $this->findModel($id);
        $model = new ChangeAssetForm();
        if (Yii::$app->request->isGet) {
            $withdrawInfo = current(WithdrawOrder::withdrawInfo([$asset->asset_item_no]));
            if (empty($withdrawInfo['withdraw_record_channel_key']) || empty($withdrawInfo['withdraw_record_channel'])) {
                Yii::$app->getSession()->setFlash('error', '代付信息为空!');
            }
            $model->assetItemNo = $asset->asset_item_no;
            $model->originGrantAt = $asset->asset_actual_grant_at;
            $model->channelKey = $withdrawInfo['withdraw_record_channel_key'] ?? '';
            $model->channelName = $withdrawInfo['withdraw_record_channel'] ?? '';
        }
        if ($model->load(Yii::$app->request->post()) && $model->submit()) {
            Yii::$app->getSession()->setFlash('success', '修改成功!');
        }

        return $this->render('change-asset-grant-at', [
            'model' => $model,
        ]);
    }


    /**
     * Action method for contract signing.
     *
     * @param $itemNo
     *
     * @return Response The response data.
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function actionContractSign($itemNo): Response
    {
        $client = GrantHttpComponent::instance();

        $client->contractSign([
            'item_no' => $itemNo,
        ]);

        return $this->asJson([
            'code' => 0,
            'data' => array_filter((array)$client->getData()),
            'test' => (string)$client->getRawResponse()->getBody(),
            'message' => '成功',
        ]);
    }
}
