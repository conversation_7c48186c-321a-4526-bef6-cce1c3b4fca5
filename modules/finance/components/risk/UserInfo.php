<?php

namespace finance\components\risk;

use xlerr\desensitise\Desensitise;
use yii\base\Arrayable;
use yii\base\ArrayableTrait;
use yii\base\BaseObject;
use function xlerr\desensitise\batchDecrypt;
use function xlerr\desensitise\encrypt;

class UserInfo extends BaseObject implements Arrayable
{
    use ArrayableTrait;

    /**
     * @var string
     */
    public $individual_full_name_encrypt = '';
    /**
     * @var string
     */
    public $individual_phone_encrypt = '';
    /**
     * @var array|string
     */
    public $bank_account = [];

    public $bank_code;
    public $account_name;

    /** @var string|int $cardUuid */
    public $cardUuid = '';

    /** @var string|int $userUuid */
    public $userUuid = '';

    public function fields(): array
    {
        return [
            'full_name' => fn() => $this->fullNameEncrypt(),
            'phone'     => 'individual_phone_encrypt',
        ];
    }

    /**
     * @return string
     */
    public function fullNameEncrypt(): string
    {
        $fullName = $this->individual_full_name_encrypt;
        if (is_array($fullName)) {
            if (count($fullName) > 1) {
                $fullNamePlain = batchDecrypt($fullName, true);
                foreach ($fullName as &$name) {
                    $name = $fullNamePlain[$name];
                }
                $fullName = implode(' ', $fullName);

                return encrypt($fullName, Desensitise::TYPE_NAME)->hash;
            }

            return current($fullName);
        }

        return $fullName;
    }
}
