<?php

namespace finance\components\risk;

use finance\components\risk\exception\RiskControlException;
use finance\components\RiskControlHttpComponent;
use GuzzleHttp\RequestOptions;

class IndonesiaRiskControl extends RiskControlHttpComponent
{
    /**
     * @param int $userUuid
     * @param int|null $cardUuid
     * @return UserInfo
     * @throws RiskControlException
     */
    public function getUserInfo(int $userUuid, ?int $cardUuid = null): UserInfo
    {
        $result = $this->post($this->getUserInfoUri, [
            RequestOptions::JSON => [
                'user_uuid' => $userUuid,
                'card_uuid' => $cardUuid,
            ],
        ]);

        if (!$result) {
            throw new RiskControlException($this->getError());
        }

        $data = (array)$this->getData();

        return new UserInfo([
            'individual_full_name_encrypt' => $data['name_encrypt'] ?? '',
            'individual_phone_encrypt' => $data['phone_encrypt'] ?? '',
            'bank_account' => $data['bank_card_account_encrypt'] ?? '',
            'bank_code' => $data['bank_card_bank_code'] ?? '',
            'account_name' => $data['name_encrypt'] ?? '',
            'userUuid' => $userUuid,
            'cardUuid' => $cardUuid,
        ]);
    }

    public function getUserUUIDsByEncrypt($encrypt, string $type = self::TYPE_PHONE): array
    {
        $this->post($this->getUserUUIDsByEncryptUri, [
            RequestOptions::JSON => [
                'encrypt' => [$encrypt],
                'type' => $type,
            ],
        ]);

        return (array)$this->getData();
    }

    /**
     * @param int $value
     * @return BankCard
     * @throws RiskControlException
     */
    public function getBankCardByCardUUID(int $value): BankCard
    {

        /** @noinspection DuplicatedCode */
        $result = $this->post($this->getBankCardByCardUUIDUri, [
            RequestOptions::JSON => [
                'card_uuid' => $value,
            ],
        ]);

        if (!$result) {
            throw new RiskControlException('接口错误: ' . $this->getError());
        }

        $content = (array)$this->getData();
        return new BankCard([
            'no_encrypt' => $content['bank_card_account_number_encrypt'] ?? '',
            'name' => $content['bank_card_bank_name'] ?? '',
            'method' => $content['bank_card_method'] ?? '',
            'method_name' => $content['bank_card_method_name'] ?? '',
        ]);
    }
}
