<?php

namespace finance\worker;

use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\OaApprovalOrder;
use finance\services\OaService;
use finance\services\oaWithdrawService\OaWithdrawServiceProvider;
use Throwable;
use waterank\audit\models\Audit;
use xlerr\lock\Lock;
use Xlerr\SettlementFlow\Models\Order;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\base\Arrayable;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Transaction;

class OaApprovalOrderSubmit extends TaskHandler
{
    public $oaApprovalOrderId;

    /** @var OaApprovalOrder */
    public $oaApprovalOrder;

    public $orderIdList;

    /** @var Order[] */
    public $orderList;

    public function rules(): array
    {
        return [
            [['oaApprovalOrderId', 'orderIdList'], 'required'],
        ];
    }

    /**
     * @param  array|Arrayable  $data
     * @param  array  $options
     *
     * @return string
     */
    protected static function generateKey($data, $options = []): string
    {
        return 'OaApprovalOrderSubmit_' . $data['oaApprovalOrderId'];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function process()
    {
        if (($data = $this->prepare()) === false) {
            throw new UserException('基础校验失败');
        }

        $lockKey = sprintf('OaApprovalOrderSubmit:%s', $this->oaApprovalOrderId);
        $lock    = new Lock(Yii::$app->get('redis'));
        $token   = $lock->lock($lockKey, 3600);
        if (!$token) {
            throw new UserException('获取锁失败');
        }

        try {
            $config = [
                'oaApprovalOrder' => $this->oaApprovalOrder,
                'oaApprovalDto' => $data
            ];
            $service = OaWithdrawServiceProvider::getService($data->region, $config);

            /** @var Transaction $transaction */
            $transaction = Order::getDb()->beginTransaction();
            try {
                $service->orderSubmit($this->orderList);
                $transaction->commit();
            } catch (\Exception $e) {
                Yii::error($e->getMessage());
                $transaction->rollBack();
                throw $e;
            }
            return TaskResult::success();
        } catch (Throwable $e) {
            Yii::error($e->getMessage());
            throw $e;
        } finally {
            $lock->unlock($lockKey, $token);
        }
    }

    /**
     * 判断数据是否准备好
     * @return false|OaApprovalDto
     * @throws UserException
     */
    public function prepare()
    {
        if (!$this->oaApprovalOrderId || !$this->orderIdList) {
            return false;
        }
        $this->oaApprovalOrder = OaApprovalOrder::findOne($this->oaApprovalOrderId);
        if (!$this->oaApprovalOrder) {
            return false;
        }

        $this->orderList = Order::find()->where([
            'id' => $this->orderIdList
        ])->all();
        if (empty($this->orderIdList) || count($this->orderIdList) !== count($this->orderList)) {
            return false;
        }
        foreach ($this->orderList as $order) {
            if ($order->status !== Order::STATUS_NEW) {
                return false;
            }
        }
        $oaApprovalDto = OaService::getOaApprovalDto(
            $this->oaApprovalOrder->oa_tenant_id,
            $this->oaApprovalOrder->oa_flow_id
        );
        if ($oaApprovalDto->statusCode !== Audit::OA_AGREE_STATUS) {
            return false;
        }
        return $oaApprovalDto;
    }
}
