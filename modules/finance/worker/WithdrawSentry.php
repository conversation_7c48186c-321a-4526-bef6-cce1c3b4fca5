<?php

namespace finance\worker;

use Carbon\Carbon;
use finance\models\Withdraw;
use kvmanager\models\KeyValue;
use Throwable;
use waterank\audit\models\Audit;
use waterank\audit\task\BusinessEndTask;
use xlerr\common\helpers\TVSender;
use xlerr\task\TaskHandler;
use Yii;
use yii\base\UserException;
use yii\db\StaleObjectException;
use yii\helpers\ArrayHelper;

use function xlerr\adminlte\userFullName;

class WithdrawSentry extends TaskHandler
{
    /**
     * @var int[] id
     */
    public $withdrawIds;

    /**
     * @var int
     */
    public $auditId;

    public $retryCount;

    public function rules()
    {
        return [
            [['withdrawIds', 'auditId'], 'required'],
            [['retryCount'], 'default', 'value' => 0],
        ];
    }

    /**
     * @return bool
     * @throws UserException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function process()
    {
        $audit = Audit::findOne($this->auditId);
        if (!$audit) {
            throw new UserException('未关联到审核数据');
        }

        $withdrawQuery = Withdraw::find()
            ->where([
                'withdraw_id' => $this->withdrawIds,
                'withdraw_oa_id' => $audit->audit_oa_id,
            ]);

        $withdrawStatus = (clone $withdrawQuery)
            ->select('withdraw_status')
            ->indexBy('withdraw_merchant_key')
            ->column();

        if (count($withdrawStatus) !== count($this->withdrawIds)) {
            throw new UserException('提现拆单数据不完整');
        }
        $done = true;
        foreach ($withdrawStatus as $status) {
            if (!in_array($status, [Withdraw::STATUS_SUCCESS, Withdraw::STATUS_PAYMENT_FAILED])) {
                $done = false;
                break;
            }
        }

        if (!$done) {
            // 有未完成的重新创建一个哨兵任务，待下次检测
            self::make([
                'auditId' => $this->auditId,
                'withdrawIds' => $this->withdrawIds,
                'retryCount' => $this->retryCount + 1,
            ], [
                'task_next_run_date' => Carbon::parse('5 minute')->toDateTimeString(), // 5分钟后执行
            ]);
        } else {
            $botIds = (array)KeyValue::take('withdraw_notify_config');
            /** @var Withdraw $withdraw */
            $withdraw = $withdrawQuery->one();
            foreach ($botIds as $botId) {
                TVSender::send(
                    $botId,
                    vsprintf(
                        "提现任务已处理完成. %d笔成功, %d笔失败!\n国家: %s\nOA单号: %s\n金额: %s\n通道: %s\n收款卡号: %s\n操作人: %s",
                        [
                            count(array_filter($withdrawStatus, static fn(int $s) => $s === Withdraw::STATUS_SUCCESS)),
                            count(
                                array_filter(
                                    $withdrawStatus,
                                    static fn(int $s) => $s === Withdraw::STATUS_PAYMENT_FAILED
                                )
                            ),
                            SIDE_LOGO,
                            ArrayHelper::getValue($withdraw, ['pendingTasks', 'oa_id'], '-'),
                            Yii::$app->formatter->format($withdrawQuery->sum('withdraw_amount'), ['formatAmount']),
                            $withdraw->withdraw_channel,
                            $withdraw->withdraw_receive_number,
                            userFullName($audit->creator),
                        ]
                    )
                );
            }

            BusinessEndTask::make([
                'audit_id' => $audit->audit_id,
                'status' => Audit::BUSINESS_END,
                'status_detail' => $withdrawStatus,
                'finish_time' => $withdrawQuery->max('withdraw_finished_at'),
            ]);
        }

        return true;
    }
}
