<?php

namespace finance\worker;

use Carbon\Carbon;
use common\models\User;
use repay\components\RepayHttpComponent;
use repay\models\operates\RepayRefund;
use Throwable;
use waterank\audit\models\Audit;
use waterank\audit\task\BusinessEndTask;
use xlerr\desensitise\Desensitise;
use xlerr\task\TaskHandler;
use yii\base\UserException;
use function xlerr\desensitise\encrypt;

class RepayRefundLaunch extends TaskHandler
{
    /**
     * @var int id
     */
    public $auditId;

    /**
     * @var array
     */
    public $params;

    public function rules()
    {
        return [
            [['params', 'auditId'], 'required'],
        ];
    }

    /**
     * @return bool
     * @throws UserException
     * @throws Throwable
     */
    public function process(): bool
    {
        $audit = Audit::findOne($this->auditId);
        if (!$audit) {
            throw new UserException('未关联到审核数据');
        }

        $repayRefund = new RepayRefund();
        $repayRefund->load($this->params);

        $repayRefund->operator = User::findOne($audit->audit_creator_id);
        if (!$repayRefund->operator) {
            throw new UserException('用户不存在');
        }

        $repayRefund->bankAccount = encrypt(
            $repayRefund->bankAccount,
            Desensitise::TYPE_BANK_CARD_NUMBER
        )->hash ?? null;
        $repayRefund->accountName = encrypt($repayRefund->accountName, Desensitise::TYPE_NAME)->hash ?? null;

        $repayClient = RepayHttpComponent::instance();
        if (!$repayClient->refund($repayRefund)) {
            throw new UserException('请求还款报错: ' . $repayClient->getError());
        }

        // 审核业务结束回调
        BusinessEndTask::make([
            'audit_id' => $audit->audit_id,
            'status' => Audit::BUSINESS_END,
            'status_detail' => ['success'],
            'memo' => '退款请求已提交到还款系统',
            'finish_time' => Carbon::now()->toDateTimeString(),
        ]);

        return true;
    }
}
