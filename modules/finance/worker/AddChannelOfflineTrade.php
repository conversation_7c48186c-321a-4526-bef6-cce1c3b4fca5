<?php

namespace finance\worker;

use finance\models\oaApprovalDto\OaApprovalDto;
use finance\models\OaApprovalOrder;
use finance\services\OaService;
use finance\services\oaWithdrawService\OaWithdrawServiceProvider;
use Throwable;
use waterank\audit\models\Audit;
use xlerr\lock\Lock;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\base\Arrayable;
use yii\base\InvalidConfigException;
use yii\base\UserException;

class AddChannelOfflineTrade extends TaskHandler
{
    public $oaApprovalOrderId;

    /** @var OaApprovalOrder */
    public $oaApprovalOrder;

    public function rules(): array
    {
        return [
            [['oaApprovalOrderId'], 'required'],
        ];
    }

    /**
     * @param  array|Arrayable  $data
     * @param  array  $options
     *
     * @return string
     */
    protected static function generateKey($data, $options = []): string
    {
        return 'AddChannelOfflineTrade_' . $data['oaApprovalOrderId'];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function process()
    {
        if (($data = $this->prepare()) === false) {
            throw new UserException('基础校验失败');
        }

        if (!$data->validate()) {
            $error = implode(';', $data->getFirstErrors());
            throw new UserException('参数校验失败' . $error);
        }

        $lockKey = sprintf('AddChannelOfflineTrade:%s', $this->oaApprovalOrderId);
        $lock    = new Lock(Yii::$app->get('redis'));
        $token   = $lock->lock($lockKey, 3600);
        if (!$token) {
            throw new UserException('获取锁失败');
        }

        try {
            $config = [
                'oaApprovalOrder' => $this->oaApprovalOrder,
                'oaApprovalDto' => $data
            ];
            $service = OaWithdrawServiceProvider::getService($data->region, $config);
            $service->addChannelOfflineTrade();
            return TaskResult::success();
        } catch (Throwable $e) {
            Yii::error($e->getMessage());
            throw $e;
        } finally {
            $lock->unlock($lockKey, $token);
        }
    }

    /**
     * 判断数据是否准备好
     * @return false|OaApprovalDto
     * @throws UserException
     */
    public function prepare()
    {
        $this->oaApprovalOrder = OaApprovalOrder::findOne($this->oaApprovalOrderId);
        if (!$this->oaApprovalOrder) {
            return false;
        }
        if ($this->oaApprovalOrder->oa_status !== Audit::OA_AGREE_STATUS) {
            return false;
        }
        $oaApprovalDto = OaService::getOaApprovalDto(
            $this->oaApprovalOrder->oa_tenant_id,
            $this->oaApprovalOrder->oa_flow_id
        );
        if ($oaApprovalDto->statusCode !== Audit::OA_AGREE_STATUS) {
            return false;
        }
        return $oaApprovalDto;
    }
}
