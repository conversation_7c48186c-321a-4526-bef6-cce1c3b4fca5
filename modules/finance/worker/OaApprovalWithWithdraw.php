<?php

namespace finance\worker;

use backend\behaviors\FormatterBehavior;
use finance\models\PaymentWithdrawalPendingTasks;
use Throwable;
use waterank\audit\models\Audit;
use waterank\audit\provider\AuditProvider;
use xlerr\lock\Lock;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\base\Arrayable;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

use function waterank\audit\authClient;
use function waterank\audit\config;

class OaApprovalWithWithdraw extends TaskHandler
{
    /** @var int */
    public $tenantId;

    /** @var array{entry_id: int, title: string, flow_key: string, status_code: int, status_desc: string, update_time: string, compoundKey: array<int, string>} */
    public $eventData;

    public function rules(): array
    {
        return [
            [['tenantId', 'eventData'], 'required'],
        ];
    }

    /**
     * @param  array|Arrayable  $data
     * @param  array  $options
     *
     * @return string
     */
    protected static function generateKey($data, $options = []): string
    {
        return $data['tenantId'].'_'.$data['eventData']['entry_id'];
    }

    /**
     * @return array|TaskResult
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function process()
    {
        $entryId = $this->eventData['entry_id'];
        $statusCode = $this->eventData['status_code'];
        $flowKey = $this->eventData['flow_key'];
        if ($statusCode !== Audit::OA_AGREE_STATUS) {
            return [
                'code' => 0,
                'message' => '此审批单状态未审核通过!',
                'data' => null,
            ];
        }

        $lockKey = sprintf('OaApprovalWithWithdraw:%s:%s', $this->tenantId, $entryId);
        $lock = new Lock(Yii::$app->get('redis'));
        $token = $lock->lock($lockKey, 3600);
        if (!$token) {
            throw new UserException('获取锁失败');
        }

        try {
            return $this->handle($this->tenantId, $flowKey, $entryId, $statusCode);
        } finally {
            $lock->unlock($lockKey, $token);
        }
    }

    /**
     * @param  int  $tenantId
     *
     * @return AuditProvider
     * @throws UserException
     */
    protected function auditProvider(int $tenantId): AuditProvider
    {
        $tenantMapping = (array)config('tenant_mapping');
        $tenant = $tenantMapping[$tenantId] ?? null;
        if (empty($tenant)) {
            throw new UserException('OA租户与配置关联异常:'.$tenantId);
        }

        /** @var AuditProvider $provider */
        $provider = authClient($tenant);

        return $provider;
    }

    /**
     * @return array|TaskResult
     * @throws InvalidConfigException
     * @throws UserException
     * @throws Throwable
     */
    public function handle(int $tenantId, string $flowKey, int $entryId, int $statusCode)
    {
        $exists = PaymentWithdrawalPendingTasks::find()->where([
            'tenant_id' => $tenantId,
            'oa_id' => $entryId,
        ])->exists();
        if ($exists) {
            return [
                'code' => 0,
                'message' => '此审批单已同步至支付通道出金待办表!',
                'data' => null,
            ];
        }

        [, $responseRaw] = $this->auditProvider($tenantId)->approvalDetailQueryAndCheck($entryId, $statusCode);

        echo $responseRaw.PHP_EOL;

        /**
         * {"code":0,"message":"成功","data":[{"entry_id":241,"title":"海外收入提现2023-06-21","created_at":"2023-06-21 17:56:57","updated_at":"2023-06-21 17:57:02","flow_key":"oversea_income_withdraw","flow_version":68,"flow_name":"海外收入提现","status_code":9,"status_desc":"通过","employee_number":"KN1OA0000183","employee_name":"朱开鹏","department_name":"管理层","contract_department_name":"","form_data":{"file":{"value":[],"label":"Annex"},"memo":{"value":"111","label":"Remark"},"deals":{"value":true,"label":"Whether To Get Department"},"payer":{"value":"Amber Star Co.,LTD.","label":"Contributor"},"title":{"value":"测试海外收入提现","label":"Title"},"region":{"value":"泰国","valueDisplay":"Thailand","label":"Region"},"workflowKey":{"value":"applications_for_payment","label":"Process Type"},"apply_number":{"value":"KN1OA0000183","valueDisplay":"朱开鹏","label":"Applicant"},"capital_flow":{"value":"提现-个人","valueDisplay":"Withdrawals-Individual","label":"Where The Money Goes"},"company_name":{"value":"乾升利信息技术（上海）有限公司","label":"Affiliated Company"},"approval_applicant":{"value":"KN1OA0000183","label":"代申请标识"},"Withdraw_Information":{"value":null,"label":"Withdraw Information"},"main_department_name":{"value":"管理层","label":"Department."},"Applicant_Information":{"value":null,"label":"Applicant Information"},"子表单":{"label":"Withdrawal Details","list":[{"payee":{"label":"Recipients","value":"Amber Star"},"currency":{"label":"Currency","value":"USD","valueDisplay":"美元"},"bank_card_user_name":{"label":"Name of Account","value":"1111"},"bank_card":{"label":"Bank Account Number","value":"11111"},"bank_address":{"label":"Bank Address","value":"11111"},"withdraw_amount":{"label":"Withdrawal Amount","value":111},"withdraw_memo":{"label":"Withdrawal Instructions","value":"111"},"bank":{"label":"Name of Bank","value":"1111"},"swift_code":{"label":"SWIFT Code","value":"1111"},"clabe":{"label":"CLABE Number","value":"11"}}]}},"procs_data":[{"procs_id":569,"name":"","type_name":"开始节点","status_code":9,"status_desc":"通过","is_current":false,"updated_at":"2023-06-21 17:56:57","finish_users":[{"employee_id":6999,"employee_number":"KN1OA0000183","employee_name":"朱开鹏","status_code":9,"status_desc":"已通过","remark":"","updated_at":"2023-06-21 17:56:57"}],"wait_users":[]},{"procs_id":570,"name":"审批","type_name":"审批节点","status_code":9,"status_desc":"通过","is_current":false,"updated_at":"2023-06-21 17:57:02","finish_users":[{"employee_id":6999,"employee_number":"KN1OA0000183","employee_name":"朱开鹏","status_code":9,"status_desc":"通过","remark":"审批人去重，直接通过","attachment":[],"updated_at":"2023-06-21 17:57:02"}],"wait_users":[]},{"procs_id":571,"name":"","type_name":"结束节点","status_code":9,"status_desc":"通过","is_current":false,"updated_at":"2023-06-21 17:57:02","finish_users":[],"wait_users":[]}]}]}
         */
        $response = Json::decode($responseRaw);

        /** @var array<int, array{entry_id: int, created_at: string, form_data: array}> $responseData */
        $responseData = ArrayHelper::index($response['data'], 'entry_id');

        /** @var array{
         *      title: string,
         *      region: array{value: string, valueDisplay: string, label: string},
         *      payer: array{value: string},
         *      capital_flow: array{value: string, valueDisplay: string, label: string},
         *      payer: array{value: string},
         *      子表单: array{list: array<int, array>}
         * } $form
         */
        $form = $responseData[$entryId]['form_data'];

        $createdAt = $responseData[$entryId]['created_at'];

        if (($form['region']['value'] ?? '') !== config('paymentPendingTasks.current_country')) {
            return [
                'code' => 0,
                'message' => '此审批单不属于当前国家',
                'data' => null,
            ];
        }


        $matchRules = PaymentWithdrawalPendingTasks::matchRules();

        $capitalFlow = $matchRules[sprintf('%s:%s', $tenantId, $flowKey)];

        if (!empty($capitalFlow) && !in_array($form['capital_flow']['value'] ?? '', $capitalFlow, true)) {
            return [
                'code' => 0,
                'message' => '此审批单资金去向不匹配',
                'data' => null,
            ];
        }

        /** @var array{label: string, value: string} $subFormItem */

        /**
         * @var array<int, array{
         *     withdraw_amount: array{value: string},
         *     currency: array{value: string},
         *     bank_card_user_name: array{value: string},
         *     bank_card: array{value: string},
         *     payee: array{value: string},
         *     bank: array{value: string},
         * }> $subFormList
         */
        $subFormList = $form['子表单']['list'];
        foreach ($subFormList as $subForm) {
            $model = new PaymentWithdrawalPendingTasks();

            $model->tenant_id = $tenantId;
            $model->oa_id = $entryId;
            $model->oa_create_at = $createdAt;
            $model->status = PaymentWithdrawalPendingTasks::STATUS_TODO;
            $model->fund_direction = $form['capital_flow']['value'] ?? '';

            $model->payment_from = $form['payer']['value'] ?? '';

            $model->payment_amount = Yii::$app->getFormatter()
                ->format($subForm['withdraw_amount']['value'] ?? 0, ['amount']);
            $model->currency = $subForm['currency']['value'] ?? FormatterBehavior::currencyPrecision();
            $model->payee_account_name = $subForm['bank_card_user_name']['value'] ?? '';
            $model->payee_account_number = $subForm['bank_card']['value'] ?? '';
            $model->payee = $subForm['payee']['value'] ?? '';
            $model->payee_bank = $subForm['bank']['value'] ?? '';

            if (!$model->insert()) {
                throw new UserException('同步海外提现记录失败:'.Json::encode($model->getErrors()));
            }
        }

        return TaskResult::success();
    }
}
