<?php

namespace finance\worker;

use Carbon\Carbon;
use RuntimeException;
use system\components\PaymentHttpComponent;
use Throwable;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use xlerr\task\models\Task;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;
use yii\helpers\Json;

class ProcedurePaymentTransfer extends TaskHandler
{
    /**
     * 流程id
     *
     * @var int
     */
    public int $procedureId = 0;

    public function rules(): array
    {
        return [
            [['procedureId'], 'required'],
            [['procedureId'], 'integer', 'min' => 1],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     * @throws Throwable
     */
    public function process(): TaskResult
    {
        $procedure = OrderProcedure::findOne([
            'id' => $this->procedureId,
        ]);
        if (!$procedure) {
            return TaskResult::failure('未知流程: ' . $this->procedureId);
        }

        if ($procedure->status !== OrderProcedure::STATUS_PROCESSING) {
            throw new UserException('流程状态错误: ' . $procedure->status);
        }

        if (empty($procedure->serial_no)) {
            throw new UserException('订单号不能为空');
        }

        ProcedurePaymentQuery::make([
            'procedureId' => $procedure->id,
        ], [
            'task_priority' => Task::PRIORITY_1,
            'task_next_run_date' => Carbon::parse('3 minutes')->toDateTimeString(),
        ]);

        $orderNo = $procedure->serial_no;

        $context = Json::decode($procedure->context);
        $tradeNo = $context['trade_no'];

        $amount = $procedure->order->payment_amount;
        $memo = $procedure->data('commentValue');

        $client = PaymentHttpComponent::instance();
        $client->transfer(
            $procedure->order->fee_type,
            $orderNo,
            $tradeNo,
            $procedure->data('fromAccountValue.channel'),
            $procedure->data('toAccountValue.user_uuid'),
            $procedure->data('toAccountValue.card_uuid'),
            $amount,
            $memo
        );

        $rawResponse = (string)$client->getRawResponse()->getBody();
        $response = Json::decode($rawResponse);
        if (!isset($response['data']['status'])) {
            throw new RuntimeException('API ERROR: ' . $rawResponse);
        }

        $context['submit'] = [
            'request' => [
                'merchant_key' => $orderNo,
                'trade_no' => $tradeNo,
                'channel_name' => $procedure->data('fromAccountValue.channel'),
                'user_uuid' => $procedure->data('toAccountValue.user_uuid'),
                'card_uuid' => $procedure->data('toAccountValue.card_uuid'),
                'amount' => $amount,
                'memo' => $memo,
            ],
            'response' => $response,
        ];

        $affected = (int)OrderProcedure::updateAll([
            'context' => Json::encode($context),
        ], [
            'id' => $this->procedureId,
            'status' => OrderProcedure::STATUS_PROCESSING,
        ]);

        if ($affected !== 1) {
            throw new RuntimeException('更新请求数据失败');
        }

        return TaskResult::success($response);
    }
}
