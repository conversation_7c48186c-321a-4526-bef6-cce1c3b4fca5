<?php

namespace finance\worker;

use finance\models\OaApprovalOrder;
use finance\services\oaWithdrawService\OaWithdrawServiceProvider;
use Throwable;
use xlerr\lock\Lock;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\base\Arrayable;
use yii\base\InvalidConfigException;
use yii\base\UserException;

class OaApproval extends TaskHandler
{
    /** @var int */
    public $oaApprovalOrderId;

    public function rules(): array
    {
        return [
            [['oaApprovalOrderId'], 'required'],
        ];
    }

    /**
     * @param  array|Arrayable  $data
     * @param  array  $options
     *
     * @return string
     */
    protected static function generateKey($data, $options = []): string
    {
        return 'OaApproval_' . $data['oaApprovalOrderId'];
    }

    /**
     * @return array|TaskResult
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function process()
    {
        $lockKey = sprintf('OaApproval:%s', $this->oaApprovalOrderId);
        $lock    = new Lock(Yii::$app->get('redis'));
        $token   = $lock->lock($lockKey, 3600);
        if (!$token) {
            throw new UserException('获取锁失败');
        }
        try {
            $oaApprovalOrder = OaApprovalOrder::findOne($this->oaApprovalOrderId);
            if (!$oaApprovalOrder) {
                return TaskResult::failure("OA审批订单ID不存在，ID:{$this->oaApprovalOrderId}");
            }
            if ($oaApprovalOrder->status !== OaApprovalOrder::STATUS_INIT) {
                $oaApprovalOrder = sprintf('OA审批订单当前状态是%s,只有状态为0才能处理', $oaApprovalOrder->status);
                return TaskResult::failure($oaApprovalOrder);
            }
            $oaApprovalDto = $oaApprovalOrder->getOaApprovalDto();
            $config = [
                'oaApprovalOrder' => $oaApprovalOrder,
                'oaApprovalDto' => $oaApprovalDto
            ];
            $service = OaWithdrawServiceProvider::getService($oaApprovalDto->region, $config);
            if ($service->isHold()) {
                $oaApprovalOrder->status = OaApprovalOrder::STATUS_HOLD;
                $oaApprovalOrder->saveOrException();
            } else {
                $oaApprovalOrder->status = OaApprovalOrder::STATUS_PROCESSING;
                $oaApprovalOrder->saveOrException();
                OaApprovalWithWithdraw::make([
                    'oaApprovalOrderId'  => $oaApprovalOrder->id,
                ], [
                    'task_from_system' => 'OA',
                ]);
            }
            return TaskResult::success();
        } catch (Throwable $e) {
            Yii::error($e->getMessage());
            throw $e;
        } finally {
            $lock->unlock($lockKey, $token);
        }
    }
}
