<?php

namespace finance\worker;

use Carbon\Carbon;
use finance\models\Withdraw;
use system\components\PaymentHttpComponent;
use Throwable;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Exception;
use yii\db\Transaction;
use yii\helpers\Json;

/**
 * 代付结果是通过接口回调同步的
 */
class AutoWithdraw extends TaskHandler
{
    /**
     * @var int id
     */
    public $dataId;
    public $attachmentUrl;
    public $action = 'new';

    public function rules(): array
    {
        return [
            [['dataId'], 'required'],
            [['dataId'], 'integer'],
            [['attachmentUrl'], 'string'],
            [['action'], 'safe'],
        ];
    }

    /**
     * @return TaskResult
     * @throws Exception
     * @throws InvalidConfigException
     * @throws UserException
     * @throws Throwable
     * @throws \yii\base\Exception
     */
    public function process(): TaskResult
    {
        /** @var Transaction $transaction */
        $transaction = Withdraw::getDb()->beginTransaction();
        try {
            $result = (int)Withdraw::updateAll([
                'withdraw_status' => Withdraw::STATUS_PAYMENT_ING,
                'withdraw_payment_created_at' => Carbon::now()->toDateTimeString(),
            ], [
                'withdraw_id' => $this->dataId,
                'withdraw_status' => Withdraw::STATUS_NEW,
            ]);
            if ($result !== 1) {
                throw new UserException('修改代付状态失败');
            }

            $client = PaymentHttpComponent::instance();
            $requestStatus = $client->autoWithdraw($this->find($this->dataId), (string)$this->attachmentUrl);
            $data = [
                'withdraw_status' => $requestStatus ? Withdraw::STATUS_PAYMENT_ING : Withdraw::STATUS_PAYMENT_FAILED,
                'withdraw_payment_info' => Json::encode([
                    'requestFlag' => $requestStatus,
                    'response' => $client->getResponse(),
                    'error' => $client->getError(),
                ]),
            ];

            $result = (int)Withdraw::updateAll($data, [
                'withdraw_id' => $this->dataId,
                'withdraw_status' => Withdraw::STATUS_PAYMENT_ING,
            ]);
            if ($result !== 1) {
                throw new UserException('修改代付请求信息失败');
            }

            if ($data['withdraw_status'] === Withdraw::STATUS_PAYMENT_ING) {
                AutoWithdrawQuery::make([
                    'dataId' => $this->dataId,
                ], [
                    'task_next_run_date' => Carbon::parse('+3 minutes')->toDateTimeString(),
                ]);
            }

            $transaction->commit();
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        return TaskResult::success();
    }

    /**
     * @param int $id
     *
     * @return Withdraw
     * @throws Exception
     */
    protected function find(int $id): Withdraw
    {
        $withdraw = Withdraw::findOne(['withdraw_id' => $id]);
        if (!$withdraw) {
            throw new Exception('数据不存在!');
        }

        return $withdraw;
    }
}
