<?php

namespace finance\worker;

use payment\models\ChannelOfflineTrade;
use RuntimeException;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\helpers\Json;

class CpopOfflineTrade extends TaskHandler
{
    public $entry_id;
    public $currency;
    public $company_name;
    public $receiver;
    public $received_card_num;

    public $channel;
    public $fee_type;
    public $trades;

    public function rules(): array
    {
        return [
            [
                [
                    'entry_id',
                    'currency',
                    'company_name',
                    'receiver',
                    'received_card_num',
                    'channel',
                    'fee_type',
                    'trades',
                ],
                'required',
            ],
        ];
    }

    public function process(): TaskResult
    {
        $tradeNos = array_column($this->trades, 'trade_no');

        $existTradeNos = ChannelOfflineTrade::find()
            ->where(['channel_offline_trade_capital_order_no' => $tradeNos])
            ->select('channel_offline_trade_capital_order_no')
            ->column();

        ChannelOfflineTrade::getDb()->transaction(function () use ($existTradeNos) {
            foreach ($this->trades as $trade) {
                if (empty($trade['trade_no']) || in_array($trade['trade_no'], $existTradeNos, true)) {
                    continue;
                }

                $offlineTrade = new ChannelOfflineTrade();
                $offlineTrade->channel_offline_trade_oa_id = $this->entry_id;
                $offlineTrade->channel_offline_trade_order_no = $trade['trade_no'];
                $offlineTrade->channel_offline_trade_capital_order_no = $trade['trade_no'];
                $offlineTrade->channel_offline_trade_channel_name = $this->channel;
                $offlineTrade->channel_offline_trade_busi_type = $this->fee_type;
                $offlineTrade->channel_offline_trade_amount = $trade['amount'];
                $offlineTrade->channel_offline_trade_order_from_type = 'manual';
                $offlineTrade->in_account_name = $this->receiver;
                $offlineTrade->in_account_number = $this->received_card_num;
                $offlineTrade->out_account_name = $this->company_name;
                $offlineTrade->out_account_number = $this->company_name;
                $offlineTrade->currency = $this->currency;
                $offlineTrade->channel_offline_trade_comment = '自动同步';
                $offlineTrade->channel_offline_trade_finish_at = $trade['finished_at'];
                $offlineTrade->channel_offline_trade_create_user = 0;
                if (!$offlineTrade->save()) {
                    if ($offlineTrade->hasErrors()) {
                        throw new RuntimeException('记录线下交易信息失败: ' . Json::encode($offlineTrade->getErrors()));
                    }
                    throw new RuntimeException('记录线下交易信息失败');
                }
            }
        });

        return TaskResult::success();
    }
}
