<?php

namespace finance\worker;

use Carbon\Carbon;
use common\models\User;
use finance\procedures\OfflineTradeRecord;
use RuntimeException;
use Throwable;
use xlerr\lock\Lock;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\Rule;
use Xlerr\SettlementFlow\Models\RuleProcedure;
use Xlerr\SettlementFlow\Tasks\OrderProcedureExecutor;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\helpers\Json;

class CpopOfflineOrder extends TaskHandler
{
    public $tenant_id;
    public $entry_id;
    public $employee;
    public $company_name;
    public $receiver;
    public $received_card_num;
    public $currency;
    public $amount;
    public $expense_start;
    public $expense_end;
    public $finished_at;
    public $channel;
    public $fee_type;
    public $apply_at;

    /**
     * 不能删除
     * $this->task->task_request_data中需要使用
     */
    public $trades;

    public function rules(): array
    {
        return [
            [
                [
                    'tenant_id',
                    'entry_id',
                    'employee',
                    'company_name',
                    'receiver',
                    'received_card_num',
                    'currency',
                    'amount',
                    'expense_start',
                    'expense_end',
                    'finished_at',
                    'channel',
                    'fee_type',
                ],
                'required',
            ],
            [['trades'], 'safe'],
            [['apply_at'], 'safe'],
        ];
    }

    public function process(): TaskResult
    {
        $redis = Yii::$app->get('redis');
        $lockKey = vsprintf('%s:%d', [__CLASS__, $this->entry_id]);
        $lock = new Lock($redis);
        $result = $lock->criticalSection($lockKey, fn() => $this->run(), 10, Lock::SECOND);
        if (!$result) {
            throw new RuntimeException('获取锁失败, 共尝试10次');
        }

        return $result;
    }

    public function run(): TaskResult
    {
        $orderNo = vsprintf('%s-%s-%s', [
            $this->fee_type,
            $this->tenant_id,
            $this->entry_id,
        ]);

        $order = Order::findOne([
            'order_no' => $orderNo,
        ]);

        // 如果已经存在，表示已经同步过该数据
        if ($order) {
            if ($order->status !== Order::STATUS_FAILURE) {
                return TaskResult::success([
                    'exists' => true,
                    'orderId' => $order->id,
                    'status' => $order->status,
                ]);
            }

            // 如果已经有作废的，则将作废订单的order_no后面追加时间
            $affected = (int)Order::updateAll([
                'order_no' => $orderNo . '-' . Carbon::now()->format('YmdHis'),
            ], [
                'order_no' => $orderNo,
                'status' => Order::STATUS_FAILURE,
            ]);
            if ($affected !== 1) {
                throw new RuntimeException('修改作废订单失败: ' . $orderNo);
            }
        }

        $rule = Rule::findOne([
            'channel' => $this->channel,
            'inc_type' => Rule::INC_TYPE_EXPENSES,
            'fee_type' => $this->fee_type,
        ]);
        if (!$rule) {
            throw new RuntimeException('未匹配到规则: ' . $this->task->task_request_data);
        }

        $order = $rule->makeApply(RuleProcedure::MODE_PAYMENT, $orderNo);

        $order->currency = $this->currency;
        $order->bill_amount = $this->amount;
        $order->payment_amount = $this->amount;
        $order->amt_snapshot = Json::encode([
            'bill' => $this->amount,
            'adj' => 0,
            'total' => $this->amount,
        ]);
        $order->bill_start_date = Carbon::parse($this->expense_start)->firstOfMonth()->toDateString();
        $order->bill_end_date = Carbon::parse($this->expense_end)->lastOfMonth()->toDateString();
        $order->created_by = (int)User::find()
            ->where(['username' => $this->employee])
            ->select('id')
            ->scalar();
        $order->finish_at = $this->finished_at;
        $order->status = Order::STATUS_PROCESSING;

        $order->setProcedures([
            OfflineTradeRecord::buildProcedureParams([
                'tenant_id' => $this->tenant_id,
                'entry_id' => $this->entry_id,
                'currency' => $this->currency,
                'company_name' => $this->company_name,
                'receiver' => $this->receiver,
                'received_card_num' => $this->received_card_num,
                'channel' => $this->channel,
                'fee_type' => $this->fee_type,
                'trades' => $this->trades,
                'apply_at' => $this->apply_at,
            ]),
        ]);

        try {
            if ($order->save()) {
                OrderProcedureExecutor::make([
                    'id' => $order->id,
                ]);

                return TaskResult::success([
                    'orderId' => $order->id,
                ]);
            }
        } catch (Throwable $e) {
            $error = $e;
        }

        if ($order->hasErrors()) {
            throw new RuntimeException('保存订单失败:' . Json::encode($order->errors));
        }

        throw new RuntimeException('保存订单失败: ' . (isset($error) && $error ? $error->getMessage() : ''));
    }
}
