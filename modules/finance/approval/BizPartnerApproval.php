<?php

namespace finance\approval;

use common\models\User;
use Exception;
use finance\models\BizPartner;
use Throwable;
use waterank\audit\InternalApproval;
use waterank\audit\models\ApprovalEntries;
use waterank\audit\models\Audit;
use waterank\audit\provider\AvoidProvider;
use waterank\audit\task\BusinessEndTask;
use Yii;
use yii\base\UserException;
use yii\db\StaleObjectException;
use yii\db\Transaction;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\NotFoundHttpException;
use yii\web\View;
use function waterank\audit\authClient;

class BizPartnerApproval extends InternalApproval
{
    public function flowKey(): string
    {
        return 'BizPartnerApproval';
    }

    public static function genAuditPreviewUrl($channel): ?string
    {
        $self = new self();
        $model = current($self->queryNotEndOfBusiness([
            'channel' => $channel
        ]));

        if (empty($model)) {
            Yii::$app->getSession()->setFlash('warning', '数据已处理');

            return Yii::$app->getRequest()->getReferrer();
        }

        /** @var AvoidProvider $avoidProvider */
        $avoidProvider = authClient('avoid_audit');

        return Url::to([$avoidProvider->previewUrl, 'id' => $model->audit->approvalEntries->id]);
    }

    /**
     */
    public function submittedCallback(array $response): array
    {
        return [
            'status' => $this->getData('action')
        ];
    }

    /**
     * @param Audit $audit
     * @param $status
     * @return void
     * @throws UserException
     * @throws Exception
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        $model = $this->callbackAction($audit, $status);

        if ($audit->audit_status === Audit::STATUS_SUCCESS) {
            BusinessEndTask::process([
                'audit_id' => $audit->audit_id,
                'status' => Audit::BUSINESS_END,
                'status_detail' => [
                    $model->id => 'success',
                ],
                'memo' => '审核通过',
                'finish_time' => $model->update_at,
            ]);
        }
    }

    /**
     * @param Audit $audit
     * @param $status
     * @return ?BizPartner
     */
    private function callbackAction(Audit $audit, $status): ?BizPartner
    {
        if ($audit->audit_status !== Audit::STATUS_SUCCESS) {
            return null;
        }
        /** @var BizPartner $model */
        return call_user_func([$this, $this->getData('action')]);
    }

    public function listTitle(): string
    {
        return vsprintf('[<b>%s</b>] 资金方:<code>%s</code> 资金方性质:<code>%s</code>', [
            ['create' => '新增', 'update' => '修改', 'delete' => '删除'][$this->getData('action')],
            $this->getData('new.channel'),
            $this->getData('new.company_type'),
        ]);
    }

    /**
     * @param View $view
     * @param ApprovalEntries $approvalEntries
     * @return string
     * @throws Exception
     */
    public function preview(View $view, ApprovalEntries $approvalEntries): string
    {
        return $view->render('@finance/views/biz-partner/approval/preview', [
            'approval' => $this,
            'entries' => $approvalEntries,
            'actions' => $this->actionButtons($approvalEntries),
        ]);
    }

    /**
     * @return BizPartner
     * @throws UserException
     * @throws Throwable
     */
    protected function create(): BizPartner
    {
        $model = new BizPartner();

        /** @var Transaction $transaction */
        $transaction = BizPartner::getDb()->beginTransaction();
        try {
            if ($model->load($this->getData(), 'new') && $model->save()) {
                $transaction->commit();
                $model->refresh();
                return $model;
            }

            if ($model->hasErrors()) {
                throw new UserException(Json::encode($model->errors));
            }

            throw new UserException('未知错误');
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @param $id
     *
     * @return BizPartner
     * @throws NotFoundHttpException
     */
    protected function findModel($id): BizPartner
    {
        $model = BizPartner::findOne($id);
        if ($model) {
            return $model;
        }

        throw new NotFoundHttpException('not found data source: ' . $id);
    }

    /**
     * @return BizPartner
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws UserException
     * @throws \yii\db\Exception
     * @throws StaleObjectException
     */
    protected function update(): BizPartner
    {
        $model = $this->findModel($this->getData('new.id'));
        /** @var Transaction $transaction */
        $transaction = BizPartner::getDb()->beginTransaction();
        try {
            if ($model->load($this->getData(), 'new') && $model->update()) {
                $transaction->commit();
                $model->refresh();
                return $model;
            }

            if ($model->hasErrors()) {
                throw new UserException(Json::encode($model->errors));
            }

            throw new UserException('未知错误');
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @return BizPartner
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     * @throws UserException
     * @throws \yii\db\Exception
     */
    public function delete(): BizPartner
    {
        $model = $this->findModel($this->getData('new.id'));
        /** @var Transaction $transaction */
        $transaction = BizPartner::getDb()->beginTransaction();
        try {
            if ($model->load($this->getData(), 'new') && $model->delete()) {
                $transaction->commit();
                $model->refresh();
                return $model;
            }

            if ($model->hasErrors()) {
                throw new UserException(Json::encode($model->errors));
            }

            throw new UserException('未知错误');
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    public function businessKey(): string
    {
        return $this->getData('new.channel');
    }

    public static function auditors(array $userIds = null): array
    {
        $userIds ??= Yii::$app->getAuthManager()->getUserIdsByRole('BizPartnerApproval');

        return User::list('id', [
            'id' => array_merge([-1], $userIds), // 强制增加`-1`, 防止查询条件为空被忽略. 条件为空时, 会查询出所有用户.
        ]);
    }

    public function queryNotEndOfBusiness(array $condition = []): array
    {
        $query = Audit::find()
            ->where(['audit_type' => $this->type()])
            ->andWhere(['>', 'audit_oa_id', 0])
            ->andWhere(['!=', 'business_status', Audit::BUSINESS_END]);

        $models = [];
        /** @var Audit $audit */
        foreach ($query->each() as $audit) {
            /** @var self $approval */
            $approval = $audit->getApproval();
            foreach ($condition as $ck => $cv) {
                if ($approval->getData(['new', $ck]) != $cv) {
                    continue 2;
                }
            }
            $model = $approval->generateModel();

            $model->audit = $audit;

            // 生成主键
            $pk = md5(json_encode([$model->channel]));

            $models[$pk] = $model;
        }
        return $models;
    }

    protected function generateModel(): BizPartner
    {
        $modelId = $this->getData('new.id');
        if (!$modelId || !($model = BizPartner::findOne($modelId))) {
            $model = new BizPartner();
        }

        $model->setOldAttributes($this->getData('old'));
        $model->setAttributes($this->getData('new'));

        return $model;
    }
}