<?php

namespace finance\approval;

use finance\models\BusinessAssociationTable;
use finance\models\PaymentWithdrawalPendingTasks;
use finance\models\Withdraw;
use yii\base\UserException;
use yii\db\Exception;

class OaWithdrawApproval extends WithdrawApproval
{
    /**
     * @param array $statusDetails
     *
     * @return void
     * @throws Exception
     * @throws UserException
     */
    protected function initBusiness(array $statusDetails): void
    {
        parent::initBusiness($statusDetails);

        $ids = (array)Withdraw::find()
            ->where(['withdraw_merchant_key' => array_keys($statusDetails)])
            ->select('withdraw_id')
            ->column();

        $pendingTasksId = $this->getOptions('pendingTasksId');

        $batchData = [];
        foreach ($ids as $id) {
            $batchData[] = [$pendingTasksId, PaymentWithdrawalPendingTasks::BUSINESS_TYPE_OA_WITHDRAW, $id];
        }

        $affectedRowCount = (int)BusinessAssociationTable::getDb()->createCommand()
            ->batchInsert(BusinessAssociationTable::tableName(), ['main_id', 'business_type', 'side_id'], $batchData)
            ->execute();

        if ($affectedRowCount !== count($ids)) {
            throw new UserException('支付通道出金待办表绑定withdraw信息失败!');
        }
    }
}
