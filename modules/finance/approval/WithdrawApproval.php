<?php

namespace finance\approval;

use Carbon\Carbon;
use finance\models\Withdraw;
use finance\worker\AutoWithdraw;
use finance\worker\WithdrawSentry;
use RuntimeException;
use Throwable;
use waterank\audit\ClientApproval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\Transaction;
use yii\helpers\Html;
use yii\helpers\Json;

class WithdrawApproval extends ClientApproval
{
    use FlowKeyTrait;

    public function type(): string
    {
        return 'withdraw';
    }

    public function statusDetailHuman(array $statusDetail): string
    {
        $withdraws = Withdraw::find()
            ->where([
                'withdraw_merchant_key' => array_keys($statusDetail),
            ])
            ->select('withdraw_id')
            ->indexBy('withdraw_merchant_key')
            ->column();

        $data = '';
        foreach ($statusDetail as $key => $status) {
            $data .= vsprintf("%s: %s\n", [
                isset($withdraws[$key]) ? Html::a($key, ['/finance/withdraw/view', 'id' => $withdraws[$key]], [
                    'target' => '_blank',
                ]) : $key,
                Withdraw::STATUS[$status] ?? $status,
            ]);
        }

        return $data;
    }

    public function audit(?string $key = null, string $authClientId = 'audit')
    {
        $data = $this->getData();
        $amountList = Withdraw::amountSplit($data, (int)($data['withdraw_amount'] ?? 0));

        $security = Yii::$app->security;
        $merchantKey = 'WITHDRAW' . $security->generateRandomString(10);

        /** @var Transaction $transaction */
        $transaction = Withdraw::getDb()->beginTransaction();
        try {
            $statusDetails = [];
            foreach ($amountList as $i => $amount) {
                $withdraw = new Withdraw();
                $withdraw->load($data, '');
                $withdraw->withdraw_merchant_key = sprintf('%s_%s', $merchantKey, $i);
                $withdraw->withdraw_trade_no = 'WITHDRAW' . $security->generateRandomString(16);
                $withdraw->withdraw_amount = $amount;
                $withdraw->withdraw_status = Withdraw::STATUS_APPROVAL_CREATING;
                $withdraw->withdraw_oa_id = 0;
                if (!$withdraw->insert()) {
                    throw new UserException('创建提现失败:' . Json::encode($withdraw->errors));
                }

                $statusDetails[$withdraw->withdraw_merchant_key] = $withdraw->withdraw_status;
            }

            $approvalResult = parent::audit();

            $this->initBusiness($statusDetails);

            $transaction->commit();

            return $approvalResult;
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @param array $statusDetails
     *
     * @return void
     * @throws UserException
     */
    protected function initBusiness(array $statusDetails): void
    {
        $affectedRowCount = (int)Audit::updateAll([
            'business_status_detail' => Json::encode($statusDetails),
        ], [
            'audit_id' => $this->getAudit()->audit_id,
        ]);
        if ($affectedRowCount !== 1) {
            // 业务数据和audit如果不是同一事务, 会导致业务数据垃圾
            throw new UserException('修改审核表业务状态明细失败');
        }
    }

    /**
     * @param Audit $audit
     * @param $status
     *
     * @return void
     * @throws Throwable
     * @throws UserException
     * @throws InvalidConfigException
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        $merchantKeys = array_keys($audit->businessStatusDetails());

        if ($audit->audit_status !== Audit::STATUS_SUCCESS) {
            Withdraw::updateAll([
                'withdraw_status' => Withdraw::STATUS_FAILURE,
            ], [
                'withdraw_merchant_key' => $merchantKeys,
            ]);

            return;
        }

        Withdraw::getDb()->transaction(function () use ($merchantKeys, $audit) {
            // 将代付状态改为`待处理`
            $affectedWithdrawCount = Withdraw::updateAll([
                'withdraw_status' => Withdraw::STATUS_NEW,
            ], [
                'withdraw_merchant_key' => $merchantKeys,
                'withdraw_status' => Withdraw::STATUS_PROCESSING,
            ]);
            if ($affectedWithdrawCount !== count($merchantKeys)) {
                throw new UserException('修改代付状态失败');
            }

            $withdrawIds = Withdraw::find()->where([
                'withdraw_merchant_key' => $merchantKeys,
                'withdraw_status' => Withdraw::STATUS_NEW,
            ])->select('withdraw_id')->column();

            // 发起代付任务
            foreach ($withdrawIds as $withdrawId) {
                AutoWithdraw::make([
                    'dataId' => $withdrawId,
                    'attachmentUrl' => $this->getData('attachmentUrl'),
                ]);
            }

            // 创建代付监控任务, 所有代付都执行成功后，会将信息更新到audit表中
            WithdrawSentry::make([
                'auditId' => $audit->audit_id,
                'withdrawIds' => $withdrawIds,
            ], [
                'task_next_run_date' => Carbon::parse('5 minute')->toDateTimeString(),
            ]);
        });
    }

    /**
     * @param array $response
     *
     * @return array
     * @throws Throwable
     */
    public function submittedCallback(array $response): array
    {
        return Withdraw::getDb()->transaction(function () use ($response) {
            $audit = $this->getAudit();
            $merchantKeys = array_keys($audit->businessStatusDetails());
            $affected = (int)Withdraw::updateAll([
                'withdraw_status' => Withdraw::STATUS_PROCESSING,
                'withdraw_oa_id' => $response['data']['entry_id'],
            ], [
                'withdraw_merchant_key' => $merchantKeys,
                'withdraw_status' => Withdraw::STATUS_APPROVAL_CREATING,
            ]);

            if ($affected !== count($merchantKeys)) {
                throw new RuntimeException('修改提现订单状态为审核中失败');
            }

            $statusDetails = [];
            foreach ($merchantKeys as $merchantKey) {
                $statusDetails[$merchantKey] = Withdraw::STATUS_PROCESSING;
            }

            return $statusDetails;
        });
    }
}
