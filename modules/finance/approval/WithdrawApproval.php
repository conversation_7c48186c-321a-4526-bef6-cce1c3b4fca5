<?php

namespace finance\approval;

use finance\models\Withdraw;
use finance\worker\AutoWithdrawLaunch;
use RuntimeException;
use waterank\audit\ClientApproval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use yii\helpers\Html;

class WithdrawApproval extends ClientApproval
{
    use FlowKeyTrait;

    public function type(): string
    {
        return 'withdraw';
    }

    public function serialNumber(): ?string
    {
        return $this->getData('withdraw_merchant_key');
    }

    public function submittedCallback(array $response): array
    {
        $merchantKey = $this->getData('withdraw_merchant_key');
        $affected = Withdraw::updateAll([
            'withdraw_status' => Withdraw::STATUS_PROCESSING,
            'withdraw_oa_id' => $response['data']['entry_id'],
        ], [
            'withdraw_status' => Withdraw::STATUS_NEW,
            'withdraw_merchant_key' => $merchantKey,
            'withdraw_oa_id' => '0',
        ]);

        if ($affected !== 1) {
            throw new RuntimeException('修改提现订单状态失败');
        }

        return [
            $merchantKey => Withdraw::STATUS_PROCESSING,
        ];
    }

    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        $merchantKey = $this->getData('withdraw_merchant_key');

        if ($audit->audit_status !== Audit::STATUS_SUCCESS) {
            Withdraw::updateAll([
                'withdraw_status' => Withdraw::STATUS_FAILURE,
            ], [
                'withdraw_status' => Withdraw::STATUS_PROCESSING,
                'withdraw_merchant_key' => $merchantKey,
            ]);

            return;
        }

        AutoWithdrawLaunch::make([
            'merchantKey' => $merchantKey,
        ]);
    }

    public function statusDetailHuman(array $statusDetail): string
    {
        $withdraws = Withdraw::find()
            ->where([
                'withdraw_merchant_key' => array_keys($statusDetail),
            ])
            ->select('withdraw_id')
            ->indexBy('withdraw_merchant_key')
            ->column();

        $data = '';
        foreach ($statusDetail as $key => $status) {
            $data .= vsprintf("%s: %s\n", [
                isset($withdraws[$key]) ? Html::a($key, ['/finance/withdraw/view', 'id' => $withdraws[$key]], [
                    'target' => '_blank',
                ]) : $key,
                Withdraw::STATUS[$status] ?? $status,
            ]);
        }

        return $data;
    }
}
