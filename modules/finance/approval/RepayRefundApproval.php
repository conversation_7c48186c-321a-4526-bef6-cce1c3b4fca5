<?php

namespace finance\approval;

use finance\worker\RepayRefundLaunch;
use RuntimeException;
use waterank\audit\ClientApproval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use waterank\audit\task\AuditHandler;
use xlerr\task\models\Task;
use xlerr\task\TaskHandler;
use yii\base\UserException;
use yii\web\Response;

class RepayRefundApproval extends ClientApproval
{
    use FlowKeyTrait;

    public function type(): string
    {
        return 'repayRefund';
    }

    /**
     * @param Audit $audit
     * @param $status
     * @return void
     * @throws UserException
     * @throws \Throwable
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        if ($audit->audit_status === Audit::STATUS_SUCCESS) {
            // 发起退款任务
            RepayRefundLaunch::make([
                'auditId' => $audit->audit_id,
                'params' => $this->getData(),
            ]);
        }
    }


    public function submittedCallback(array $response): array
    {
        $no = $this->getData('refundWithholdSerialNo');
        return [
            $no => Audit::STATUS_PROCESSING
        ];
    }

    /**
     * @param string|null $key
     * @param string $authClientId
     * @return mixed|string|Task|Response|null
     * @throws UserException
     * @throws \Throwable
     */
    public function audit(?string $key = null, string $authClientId = 'audit')
    {
        $key ??= $this->generateAuditKey();

        $this->setKey($key);
        $this->setAuthClientId($authClientId);

        $operator = $this->getOperator();
        if (!$operator) {
            throw new RuntimeException('操作人错误');
        }

        $audit = new Audit([
            'audit_key' => $this->getKey(),
            'audit_type' => $this->type(),
            'audit_creator_id' => $operator,
            'audit_oa_id' => 0,
            'audit_creator_name' => '',
            'audit_creator_email' => '',
            'audit_status' => Audit::STATUS_NEW,
            'business_key' => $this->businessKey(),
            'business_status' => Audit::BUSINESS_NEW,
            'business_status_detail' => '[]',
            'approval' => $this,
        ]);

        $task = null;

        $result = Audit::getDb()->transaction(function () use ($audit, &$task) {
            if (!$audit->insert()) {
                throw new RuntimeException('创建审核记录失败');
            }

            $task = AuditHandler::make(['key' => $audit->audit_key]);
        });

        if ($this->syncSubmit && $task && $task->refresh()) {
            TaskHandler::execute($task);
        }

        return $result;
    }

    /**
     * @return string
     */
    public function businessKey(): string
    {
        return $this->getData('refundWithholdSerialNo', '');
    }
}
