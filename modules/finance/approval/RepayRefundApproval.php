<?php

namespace finance\approval;

use finance\worker\RepayRefundLaunch;
use waterank\audit\Approval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use waterank\audit\MultiWindowLinkageTrait;
use waterank\audit\task\BusinessBeginTask;
use yii\base\UserException;

class RepayRefundApproval extends Approval
{
    use FlowKeyTrait, MultiWindowLinkageTrait;

    public function type(): string
    {
        return 'repayRefund';
    }

    public function createAuditModel(): Audit
    {
        $audit = parent::createAuditModel();

        $audit->business_key = $this->getData(['business_key', 1]);
        if (empty($audit->business_key)) {
            throw new UserException('缺少业务key');
        }

        return $audit;
    }

    public function successMsg(): string
    {
        return '退款申请提交成功, 请勿重复提交!';
    }

    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        if ($audit->audit_status !== Audit::STATUS_SUCCESS) {
            return;
        }

        BusinessBeginTask::make([
            'audit_id'      => $audit->audit_id,
            'status_detail' => ['processing'],
        ]);

        // 发起退款任务
        RepayRefundLaunch::make([
            'auditId' => $audit->audit_id,
            'params'  => $this->getData(),
        ]);
    }
}
