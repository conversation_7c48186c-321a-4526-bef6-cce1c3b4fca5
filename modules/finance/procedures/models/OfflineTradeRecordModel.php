<?php

namespace finance\procedures\models;

use yii\base\Model;

class OfflineTradeRecordModel extends Model
{
    public $tenant_id;
    public $entry_id;
    public $currency;
    public $company_name;
    public $receiver;
    public $received_card_num;

    public $channel;
    public $fee_type;
    public $trades;

    public function rules(): array
    {
        return [
            [
                [
                    'tenant_id',
                    'entry_id',
                    'currency',
                    'company_name',
                    'receiver',
                    'received_card_num',
                    'channel',
                    'fee_type',
                    'trades',
                ],
                'required',
                'on' => ['order'],
            ],
        ];
    }
}
