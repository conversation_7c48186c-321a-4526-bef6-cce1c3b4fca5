<?php

namespace finance\procedures\approvals;

use RuntimeException;
use Throwable;
use waterank\audit\ClientApproval;
use waterank\audit\FlowKeyTrait;
use waterank\audit\models\Audit;
use waterank\audit\task\BusinessEndTask;
use Xlerr\ApplicationPayment\Interfaces\OaAuditInterface;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Yii;
use yii\base\Exception;
use yii\base\UserException;
use yii\helpers\Json;

abstract class BaseApproval extends ClientApproval
{
    use FlowKeyTrait;

    public function serialNumber(): ?string
    {
        return $this->getData('serial_number');
    }

    /**
     * @return string
     * @throws Exception
     */
    public function generateAuditKey(): string
    {
        $security = Yii::$app->getSecurity();

        return strtoupper(md5($security->generateRandomKey()));
    }

    /**
     * @param array $response
     *
     * @return array
     */
    public function submittedCallback(array $response): array
    {
        $id = $this->getData('procedure_id');

        OrderProcedure::updateAll([
            'serial_no' => $response['data']['entry_id'],
            'context' => Json::encode([
                'submit' => [
                    'request' => $this->getData(),
                    'response' => $response,
                ]
            ])
        ], [
            'id' => $id,
            'status' => OrderProcedure::STATUS_PROCESSING,
        ]);

        return [
            $id => OrderProcedure::STATUS_PROCESSING,
        ];
    }

    /**
     * @param Audit $audit
     * @param       $status
     *
     * @return void
     * @throws Throwable
     * @throws UserException
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        $id = $this->getData('procedure_id');

        $procedureStatus = OrderProcedure::STATUS_FAILURE;

        if ($audit->audit_status === Audit::STATUS_SUCCESS) {
            BusinessEndTask::process([
                'audit_id' => $audit->audit_id,
                'status' => Audit::BUSINESS_END,
                'status_detail' => [
                    $id => OrderProcedure::STATUS_SUCCESS,
                ],
                'memo' => '审核通过',
            ]);

            $procedureStatus = OrderProcedure::STATUS_SUCCESS;
        }

        $procedure = OrderProcedure::findOne([
            'id' => $id,
        ]);

        if (!$procedure) {
            throw new RuntimeException('流程不存在');
        }

        $handler = $procedure->handler;
        if (!$handler instanceof OaAuditInterface) {
            throw new RuntimeException('流程处理器错误, 必须继承至:' . OaAuditInterface::class);
        }

        $handler->oaCallback($procedure, $procedureStatus, [
            'oa_status' => $status,
        ]);
    }
}
