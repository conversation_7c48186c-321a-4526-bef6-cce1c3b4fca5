<?php

namespace finance\procedures\tasks;

use Carbon\Carbon;
use finance\models\Withdraw;
use RuntimeException;
use Throwable;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use xlerr\task\models\Task;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\base\UserException;

class PaymentAutoWithdrawSentry extends TaskHandler
{
    /**
     * 流程id
     *
     * @var int
     */
    public int $procedureId = 0;

    public int $retry = 0;

    public function rules(): array
    {
        return [
            [['procedureId'], 'required'],
            [['procedureId'], 'integer', 'min' => 1],
            [['retry'], 'integer'],
        ];
    }

    /**
     * @return TaskResult
     * @throws UserException
     * @throws Throwable
     */
    public function process(): TaskResult
    {
        $procedure = OrderProcedure::findOne([
            'id' => $this->procedureId,
        ]);
        if (!$procedure) {
            return TaskResult::failure('未知流程: ' . $this->procedureId);
        }

        if ($procedure->status !== OrderProcedure::STATUS_PROCESSING) {
            throw new UserException('流程状态错误: ' . $procedure->status);
        }

        if ($procedure->serial_no === '') {
            throw new UserException('订单号错误');
        }

        $hasNotSuccess = Withdraw::find()
            ->where(['withdraw_procedure_id' => $procedure->serial_no])
            ->andWhere(['<>', 'withdraw_status', Withdraw::STATUS_SUCCESS])
            ->exists();

        if ($hasNotSuccess) {
            // 有未完成的重新创建一个任务，待下次检测
            self::make([
                'procedureId' => $procedure->id,
                'retry' => $this->retry + 1,
            ], [
                'task_priority' => Task::PRIORITY_1,
                'task_next_run_date' => Carbon::parse('3 minutes')->toDateTimeString(),
            ]);
        } else {
            $affected = (int)OrderProcedure::updateAll([
                'status' => OrderProcedure::STATUS_SUCCESS,
            ], [
                'id' => $procedure->id,
                'status' => OrderProcedure::STATUS_PROCESSING,
            ]);
            if ($affected !== 1) {
                throw new RuntimeException('修改流程状态失败');
            }
        }

        return TaskResult::success();
    }
}
