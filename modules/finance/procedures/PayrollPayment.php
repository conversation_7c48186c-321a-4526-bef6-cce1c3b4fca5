<?php

namespace finance\procedures;

use finance\procedures\approvals\PayrollPaymentApproval;
use kvmanager\KVException;
use Throwable;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use Xlerr\SettlementFlow\Procedures\AbstractProcedure;
use Yii;
use yii\base\UserException;

class PayrollPayment extends AbstractProcedure
{
    public static function key(): string
    {
        return 'payroll_payment';
    }

    public function name(): string
    {
        return '发薪OA审批';
    }

    public function component(): string
    {
        return 'no-configuration';
    }

    /**
     * @param Order $order
     * @param OrderProcedure $procedure
     *
     * @return void
     * @throws Throwable
     * @throws KVException
     * @throws UserException
     */
    public function run(Order $order, OrderProcedure $procedure): void
    {
        $procedures = [];
        /** @var OrderProcedure $pd */
        foreach ($order->procedures as $pd) {
            if ($pd['type'] === PaymentAutoWithdraw::key()) {
                $procedures[] = [
                    'withdraw_channel' => $pd['config']['data']['fromAccountValue']['channel'], //提现通道
                    'receive_account_no' => $pd['config']['data']['toAccountValue']['account_no'], //收款卡号
                    'receive_card_uuid' => $pd['config']['data']['toAccountValue']['card_uuid'], //Card UUID
                    'receive_user_uuid' => $pd['config']['data']['toAccountValue']['user_uuid'], //User UUID
                    'memo' => $pd['config']['data']['commentValue'], //打款备注
                ];
            }
        }

        $amountTypeName = Rule::feeTypeName($order->fee_type);
        $approval = new PayrollPaymentApproval([
            // 不同国家需要生成不同的序列号
            'serial_number' => md5(vsprintf('%s:%s:%d:%d', [
                Yii::$app->params['logo'] ?? 'global',
                __CLASS__,
                $order->id,
                $procedure->id,
            ])),
            'region' => str_replace('信贷平台', '', Yii::$app->params['logo'] ?? '未知'),
            'procedure_id' => $procedure->id,
            'order_id' => $order->id,
            'inc_type' => $order->inc_type,
            'fee_type' => $order->fee_type,
            'fee_type_name' => $amountTypeName,
            'bill_start_date' => $order->bill_start_date,
            'bill_end_date' => $order->bill_end_date,
            'amount_human' => formatAmount($order->payment_amount),
            'amount' => $order->payment_amount,
            'comment' => $order->comment,
            'procedures' => $procedures,
        ], [
            'operator' => $order->created_by,
        ]);

        $approval->audit();
    }
}
