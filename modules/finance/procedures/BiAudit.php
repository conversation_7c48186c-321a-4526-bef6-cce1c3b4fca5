<?php

namespace finance\procedures;

use finance\procedures\approvals\DepositForBIApproval;
use kvmanager\KVException;
use Throwable;
use Xlerr\ApplicationPayment\Interfaces\OaAuditInterface;
use Xlerr\ApplicationPayment\Traits\OaAuditCallbackTrait;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use Xlerr\SettlementFlow\Procedures\AbstractProcedure;
use Yii;
use yii\base\UserException;

class BiAudit extends AbstractProcedure implements OaAuditInterface
{
    use OaAuditCallbackTrait;

    public static function key(): string
    {
        return 'bi_audit';
    }

    public function name(): string
    {
        return 'BIZ-OA审核';
    }

    public function component(): string
    {
        return 'no-configuration';
    }

    /**
     * @param Order $order
     * @param OrderProcedure $procedure
     *
     * @return void
     * @throws Throwable
     * @throws KVException
     * @throws UserException
     */
    public function run(Order $order, OrderProcedure $procedure): void
    {
        $formatter = Yii::$app->formatter;
        $amountTypeName = Rule::feeTypeName($order->fee_type);

        $country = Yii::$app->params['country'];

        $approval = new DepositForBIApproval([
            'serial_number' => sprintf('%s_cpop_order_procedure_%d', $country, $procedure->id),
            'biz_region' => $country,
            'procedure_id' => $procedure->id,
            'form_id' => $order->id,
            'funder_name' => $order->channel,
            'funder_code' => $order->channel,
            'amount_type' => $order->fee_type,
            'amount_type_name' => $amountTypeName,
            'amount_human' => $formatter->format($order->payment_amount, ['formatAmount']),
            'amount' => $order->payment_amount,
            'remark' => vsprintf("%s_%s\n%s", [
                $amountTypeName,
                $order->channel,
                $order->bankFlow->remark ?? '',
            ]),
        ], [
            'operator' => $order->created_by,
        ]);

        $approval->audit();
    }
}
