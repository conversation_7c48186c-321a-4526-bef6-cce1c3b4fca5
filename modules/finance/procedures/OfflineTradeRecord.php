<?php

namespace finance\procedures;

use finance\procedures\models\OfflineTradeRecordModel;
use payment\models\ChannelOfflineTrade;
use RuntimeException;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Procedures\AbstractProcedure;
use yii\helpers\Json;

class OfflineTradeRecord extends AbstractProcedure
{
    public string $modelClass = OfflineTradeRecordModel::class;

    public static function key(): string
    {
        return 'offline_trade_record';
    }

    public function type(): string
    {
        return self::TYPE_CONSOLE;
    }

    public function component()
    {
        return 'no-configuration';
    }

    public function name(): string
    {
        return '线下交易补录';
    }

    public function run(Order $order, OrderProcedure $procedure): void
    {
        /** @var OfflineTradeRecordModel $model */
        $model = $this->createModel($procedure);
        if ($model->hasErrors()) {
            throw new RuntimeException('参数错误: ' . Json::encode($model->errors));
        }

        $serialNo = sprintf('%d-%d', $model->tenant_id, $model->entry_id);

        $affected = OrderProcedure::updateAll([
            'status' => OrderProcedure::STATUS_SUCCESS,
            'serial_no' => $serialNo,
        ], [
            'serial_no' => '',
            'status' => OrderProcedure::STATUS_PROCESSING,
            'id' => $procedure->id,
        ]);

        if ($affected !== 1) {
            throw new RuntimeException('修改流程状态失败');
        }

        $trades = $model->trades;
        if (!empty($trades)) {
            $trades = array_map(static fn(array $trade) => [
                'channel_offline_trade_oa_tenant_id' => $model->tenant_id,
                'channel_offline_trade_oa_id' => $model->entry_id,
                'channel_offline_trade_order_no' => $trade['trade_no'],
                'channel_offline_trade_capital_order_no' => $trade['trade_no'],
                'channel_offline_trade_channel_name' => $model->channel,
                'channel_offline_trade_busi_type' => $model->fee_type,
                'channel_offline_trade_amount' => $trade['amount'],
                'channel_offline_trade_order_from_type' => 'manual',
                'in_account_number' => $model->received_card_num,
                'in_account_name' => $model->receiver,
                'out_account_number' => $model->company_name,
                'out_account_name' => $model->company_name,
                'currency' => $model->currency,
                'channel_offline_trade_comment' => 'auto sync',
                'channel_offline_trade_finish_at' => $trade['finished_at'],
                'channel_offline_trade_create_user' => $order->created_by,
            ], $trades);

            $affected = (int)ChannelOfflineTrade::getDb()
                ->createCommand()
                ->batchInsert(ChannelOfflineTrade::tableName(), array_keys(current($trades)), $trades)
                ->execute();
            if ($affected !== count($trades)) {
                throw new RuntimeException('写入线下补录表失败');
            }
        }
    }
}
