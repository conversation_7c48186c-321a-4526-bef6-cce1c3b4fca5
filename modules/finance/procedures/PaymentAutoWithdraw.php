<?php

namespace finance\procedures;

use Carbon\Carbon;
use finance\models\BusinessAssociationTable;
use finance\models\OaApprovalOrder;
use finance\models\Withdraw;
use finance\procedures\models\PaymentAutoWithdrawModel;
use finance\procedures\tasks\PaymentAutoWithdrawSentry;
use finance\worker\AutoWithdraw;
use kvmanager\KVException;
use RuntimeException;
use Throwable;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use Xlerr\SettlementFlow\Models\Rule;
use Xlerr\SettlementFlow\Procedures\AbstractTransferProcedure;
use xlerr\task\models\Task;
use Yii;
use yii\base\UserException;
use yii\helpers\Json;

class PaymentAutoWithdraw extends AbstractTransferProcedure
{
    public string $modelClass = PaymentAutoWithdrawModel::class;

    public static function key(): string
    {
        return 'payment_auto_withdraw';
    }

    public function name(): string
    {
        return '支付系统自动代付';
    }

    /**
     * @param Order $order
     * @param OrderProcedure $procedure
     *
     * @return void
     * @throws Throwable
     * @throws KVException
     * @throws UserException
     */
    public function run(Order $order, OrderProcedure $procedure): void
    {
        $affected = OrderProcedure::updateAll([
            'serial_no' => $procedure->id,
        ], [
            'status' => OrderProcedure::STATUS_PROCESSING,
            'serial_no' => '',
            'id' => $procedure->id,
        ]);
        if ($affected !== 1) {
            throw new RuntimeException('生成订单号失败');
        }

        $withdrawChannel = $procedure->data('fromAccountValue.channel');

        $creator = $procedure->order->operator;
        $feeType = $procedure->order->fee_type;

        $withdraw = new Withdraw();
        $withdraw->withdraw_procedure_id = $procedure->id;
        $withdraw->withdraw_oa_created_at = Carbon::now()->toDateTimeString();
        $withdraw->withdraw_status = Withdraw::STATUS_NEW;
        $withdraw->withdraw_type = Rule::feeTypeName($feeType);
        $withdraw->withdraw_biz_type = $feeType;
        $withdraw->withdraw_channel = $withdrawChannel;
        $withdraw->withdraw_receive_account = $procedure->data('toAccountValue.name');
        $withdraw->withdraw_receive_number = $procedure->data('toAccountValue.account_no');
        $withdraw->withdraw_receive_uuid = $procedure->data('toAccountValue.card_uuid');
        $withdraw->withdraw_receive_userid = $procedure->data('toAccountValue.user_uuid');
        $withdraw->withdraw_reason = $procedure->data('commentValue');
        $withdraw->withdraw_create_user = $creator->username;
        $withdraw->withdraw_create_user_email = $creator->email;
        $oaId = $this->getOaID($procedure);
        if ($oaId) {
            $withdraw->withdraw_oa_id = $oaId;
        }

        $amounts = Withdraw::amountSplit($withdraw, $procedure->order->payment_amount);
        foreach ($amounts as $i => $amount) {
            $orderNo = vsprintf('%s%011d%d', [
                $this->genUniqueString('PAW'),
                $procedure->id,
                $i,
            ]);
            $tradeNo = vsprintf('%s%011d%d', [
                $this->genUniqueString('PAWT'),
                $procedure->id,
                $i,
            ]);
            $model = clone $withdraw;
            $model->withdraw_merchant_key = $orderNo;
            $model->withdraw_trade_no = $tradeNo;
            $model->withdraw_amount = $amount;
            if (!$model->insert()) {
                if ($model->hasErrors()) {
                    throw new RuntimeException('创建提现记录失败：' . Json::encode($model->getErrors()));
                }
                throw new RuntimeException('创建提现记录失败, 未知错误请重试');
            }

            AutoWithdraw::make([
                'dataId' => $model->withdraw_id,
            ], [
                'task_priority' => Task::PRIORITY_1,
            ]);
        }

        PaymentAutoWithdrawSentry::make([
            'procedureId' => $procedure->id,
        ], [
            'task_next_run_date' => Carbon::parse('+5 minutes')->toDateTimeString(),
        ]);
    }

    protected function genUniqueString(string $prefix): string
    {
        $now = Carbon::now();

        return trim($prefix)
            . ($now->year - 2016)
            . dechex($now->month)
            . $now->day
            . substr($now->getTimestampMs(), 5)
            . substr(md5(Yii::$app->security->generateRandomKey()), 8, 7);
    }

    /**
     * @param OrderProcedure $procedure
     *
     * @return int|null
     */
    protected function getOaID(OrderProcedure $procedure)
    {
        $orderId = $procedure->order_id;
        /** @var BusinessAssociationTable $relation */
        $relation = BusinessAssociationTable::find()->where([
            'business_type' => BusinessAssociationTable::BUSINESS_TYPE_OA_ORDER,
            'side_id' => $orderId,
        ])->one();
        if ($relation) {
            $oaApprovalOrder = OaApprovalOrder::findOne($relation->main_id);
            return $oaApprovalOrder->oa_flow_id ?? null;
        }

        return null;
    }
}
