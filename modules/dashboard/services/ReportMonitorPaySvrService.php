<?php

namespace dashboard\services;

use Carbon\Carbon;
use dashboard\models\ReportMonitorPaysvr;
use dashboard\traits\ReportSearchTrait;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class ReportMonitorPaySvrService extends ReportMonitorPaysvr
{
    use ReportSearchTrait;

    const GROUP_SUBJECT = 'subject';
    const GROUP_CHANNEL = 'channel';
    const GROUP_PRODUCT = 'product';
    const GROUP_LIST    = [
        self::GROUP_SUBJECT => '主体',
        self::GROUP_CHANNEL => '通道',
        self::GROUP_PRODUCT => '产品',
    ];

    public function formName()
    {
        return '';
    }

    public function rules()
    {
        return array_merge([
            [['channel', 'subject', 'product'], 'safe'],

        ], $this->dateGroupRules());
    }

    /**
     * @param array $params
     * @param array $types
     *
     * @return ActiveQuery
     */
    public function searchReportData($params, $types)
    {
        $this->load($params);
        $this->validate();

        [$startDate, $endDate] = explode(' - ', $this->date_range);
        $selects = array_filter((array) $this->group);

        $query = self::find()
            ->andFilterWhere([
                'and',
                ['>=', 'date', $startDate],
                ['<=', 'date', $endDate],
            ])
            ->andWhere(['type' => $types])
            ->andFilterWhere([
                'channel' => $this->channel,
                'subject' => $this->subject,
                'product' => $this->product,
            ])
            ->select($this->convertDateGroup($this->date_group))
            ->groupBy(['dateF'])
            ->addSelect($selects)
            ->addGroupBy($selects);

        $selects = [];
        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type` = \'%s\', `values`, 0))', $type));
        }
        $query->addSelect($selects);

        return $query;
    }

    /**
     * @param array $selects
     *
     * @return array
     */
    public function buildGridColumns($selects)
    {
        $config      = $this->config();
        $columnLists = [
            'dateF'   => [
                'attribute' => 'dateF',
                'label'     => '日期',
            ],
            'subject' => [
                'attribute' => 'subject',
                'label'     => '主体',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.subject', [])],
            ],
            'channel' => [
                'attribute' => 'channel',
                'label'     => '通道',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.channel', [])],
            ],
            'product' => [
                'attribute' => 'product',
                'label'     => '产品',
                'format'    => ['in', ArrayHelper::getValue($config, 'paySvr.product', [])],
            ],
        ];

        $columns = [];
        foreach ($selects as $column => $exp) {
            if (!is_string($column)) {
                $column = $exp;
            }

            if (isset($columnLists[$column])) {
                $columns[] = $columnLists[$column];
            }
        }

        return $columns;
    }

    /**
     * @return array
     */
    public function config()
    {
        static $options;

        if ($options) {
            return $options;
        }

        $config = self::find()
//            ->where(['>=', 'date', Carbon::now()->subDays(10)->toDateString()])
            ->select([
                'subject',
                'channel',
                'product',
            ])
            ->distinct()
            ->asArray()
            ->all();

        $subject = array_filter(array_unique(array_column($config, 'subject')));
        $channel = array_filter(array_unique(array_column($config, 'channel')));
        $product = array_filter(array_unique(array_column($config, 'product')));

        $options = [
            'paySvr' => [
                'subject' => array_combine($subject, $subject),
                'channel' => array_combine($channel, $channel),
                'product' => array_combine($product, $product),
            ],
        ];

        return $options;
    }
}
