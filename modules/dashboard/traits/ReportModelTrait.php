<?php

namespace dashboard\traits;

use datasource\DataSourceContext;
use yii\base\UserException;
use yii\helpers\ArrayHelper;

trait ReportModelTrait
{

    /**
     * @inheritDoc
     */
    public static function clean($data): void
    {
        static::deleteAll([
            'date' => array_values(array_unique(array_column($data, 'date'))),
            'type' => array_values(array_unique(array_column($data, 'type'))),
        ]);
    }

    /**
     * @param  array  $data
     * @param  array  $config
     * @param  DataSourceContext  $dataSourceContext
     *
     * @return int
     * @throws UserException
     */
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        $mapping = ArrayHelper::remove($config, 'mapping');

        if (empty($mapping)) {
            throw new UserException('映射的键值不能为空!');
        }

        $records = [];
        foreach ($data as $row) {
            $record = [];
            foreach ($config as $fieldName => $valueFieldName) {
                if (strpos($valueFieldName, ':') === 0) {
                    $record[$fieldName] = ltrim($valueFieldName, ':');
                } else {
                    $record[$fieldName] = ArrayHelper::getValue($row, $valueFieldName, '');
                }
            }

            foreach ($mapping as $type => $valueColumnName) {
                $rule  = array_filter(explode(':', $type), 'trim');
                $type  = array_shift($rule);
                $value = ArrayHelper::getValue($row, $valueColumnName);
                if (in_array('allowZero', $rule, true) || $value) {
                    $records[] = array_merge($record, [
                        'type'   => $type,
                        'values' => $value,
                    ]);
                }
            }
        }
        if (!empty($records)) {
            static::clean($records);
            $columns = array_keys(current($records));
            return static::getDb()->createCommand()->batchInsert(self::tableName(), $columns, $records)->execute();
        }

        return 0;
    }
}
