<?php

namespace dashboard\grid;

use xlerr\common\widgets\GridView;

class EnableHtmlLabelGridView extends GridView
{
    protected function initColumns(): void
    {
        parent::initColumns();

        foreach ($this->columns as $column) {
            if (property_exists($column, 'encodeLabel')) {
                // 让 label 支持 HTML
                $column->encodeLabel = false;
            }
        }
    }

    /**
     * tooltip 初始化
     */
    public function run(): void
    {
        $view = $this->getView();

        $view->registerJs("$(function () { \$('[data-toggle=\"tooltip\"]').tooltip(); });");

        parent::run();
    }

}