<?php

namespace dashboard\grid;

use dashboard\traits\TotalDataColumnTrait;

/**
 * 表达式计算的金额合计列
 *
 * @package dashboard\grid
 */
class ExpressionMoneyTotalDataColumn extends ExpressionMoneyDataColumn
{
    use TotalDataColumnTrait;

    /**
     * @var float
     */
    public $footer = 0;

    /**
     * @return void
     */
    public function init()
    {
        parent::init();

        $this->footerOptions = $this->headerOptions;
        $this->footerFormat  = ['formatAmount', true];
    }
}
