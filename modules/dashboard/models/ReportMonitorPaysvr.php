<?php

namespace dashboard\models;

use dashboard\traits\ReportModelTrait;
use datasource\interfaces\ReportMonitorInterface;

/**
 * This is the model class for table "report_monitor_paysvr".
 *
 * @property int $id
 * @property string $date
 * @property string $subject
 * @property string $event_type
 * @property string $type
 * @property string $channel
 * @property string $product
 * @property string $bank
 * @property string $values
 * @property string $create_at
 */
class ReportMonitorPaysvr extends \yii\db\ActiveRecord implements ReportMonitorInterface
{
    use ReportModelTrait;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'report_monitor_paysvr';
    }

    /**
     * @inheritDoc
     */
    public static function clean($data): void
    {
        static::deleteAll([
            'id' => static::find()
                ->where([
                    'date' => array_values(array_unique(array_column($data, 'date'))),
                    'subject' => array_values(array_unique(array_column($data, 'subject'))),
                    'type' => array_values(array_unique(array_column($data, 'type'))),
                    'channel' => array_values(array_unique(array_column($data, 'channel'))),
                    'product' => array_values(array_unique(array_column($data, 'product'))),
                ])
                ->select('id')
                ->column(),
        ]);
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['date', 'subject', 'event_type', 'type', 'channel', 'product', 'bank', 'values'], 'required'],
            [['date', 'create_at'], 'safe'],
            [['subject', 'channel'], 'string', 'max' => 32],
            [['event_type', 'type', 'product', 'bank'], 'string', 'max' => 64],
            [['values'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键ID',
            'date' => '日期',
            'subject' => '主体',
            'event_type' => '主体对应事件类型',
            'channel' => '通道',
            'date_group' => '分组',
            'sort' => '分类',
            'product' => '产品',
            'bank' => '银行',
            'type' => '类型',
            'values' => '值',
            'create_at' => '创建时间',
        ];
    }

}