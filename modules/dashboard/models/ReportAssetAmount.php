<?php

namespace dashboard\models;

use dashboard\traits\ReportModelTrait;
use datasource\interfaces\ReportMonitorInterface;
use Yii;

/**
 * This is the model class for table "{{%report_asset_amount}}".
 *
 * @property int $id               主键
 * @property string $date             日期
 * @property string|array $channel          资金方
 * @property string $period_type      期类型
 * @property int $period_count     总期数
 * @property string $product_category 期天
 * @property string $type             类型
 * @property int $values           统计值
 * @property string $created_at       创建时间
 */
class ReportAssetAmount extends \yii\db\ActiveRecord implements ReportMonitorInterface
{
    use ReportModelTrait;

    public static function clean($data): void
    {
        static::deleteAll([
            'id' => static::find()
                ->where([
                    'channel' => array_values(array_unique(array_column($data, 'channel'))),
                    'date' => array_values(array_unique(array_column($data, 'date'))),
                    'type' => array_values(array_unique(array_column($data, 'type'))),
                ])
                ->select('id')
                ->column(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%report_asset_amount}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['date', 'channel', 'period_type', 'period_count', 'product_category', 'type'], 'required'],
            [['date', 'created_at'], 'safe'],
            [['period_count', 'values'], 'integer'],
            [['channel', 'type'], 'string', 'max' => 64],
            [['period_type', 'product_category'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('dashboard', '主键'),
            'date' => Yii::t('dashboard', '日期'),
            'channel' => Yii::t('dashboard', '资金方'),
            'period_type' => Yii::t('dashboard', '期类型'),
            'period_count' => Yii::t('dashboard', '总期数'),
            'product_category' => Yii::t('dashboard', '期天'),
            'type' => Yii::t('dashboard', '类型'),
            'values' => Yii::t('dashboard', '统计值'),
            'created_at' => Yii::t('dashboard', '创建时间'),
        ];
    }
}
