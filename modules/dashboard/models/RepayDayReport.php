<?php

namespace dashboard\models;

use dashboard\traits\ReportModelTrait;
use datasource\interfaces\ReportMonitorInterface;


/**
 * This is the model class for table "repay_day_report".
 *
 * @property int $id                   主键
 * @property string $channel              资金方, 放款渠道
 * @property string $grant_date           放款日期
 * @property string $repay_date           还款日期
 * @property int $values               金额(分)
 * @property string $create_at            创建时间
 * @property string $update_at            更新时间
 * @property string $type                 费用类型 repay_principal,repay_interest,repay_late_interest,repay_fin_service
 * @property int $dds_id               数据源ID
 */
class RepayDayReport extends \yii\db\ActiveRecord implements ReportMonitorInterface
{
    use ReportModelTrait;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'repay_day_report';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['grant_date', 'repay_date', 'values', 'type'], 'required'],
            [['grant_date', 'repay_date', 'create_at', 'update_at'], 'safe'],
            [['values'], 'integer'],
            [['channel'], 'string', 'max' => 64],
            [['type'], 'string', 'max' => 255],
            [
                ['repay_date', 'grant_date', 'channel', 'type'],
                'unique',
                'targetAttribute' => ['repay_date', 'grant_date', 'channel', 'type'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'channel' => '资金方, 放款渠道',
            'grant_date' => '放款日期',
            'repay_date' => '还款日期',
            'values' => '金额(分)',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'type' => '费用类型 repay_principal,repay_interest,repay_late_interest,repay_fin_service',
            'dds_id' => '数据源ID',
        ];
    }

    /**
     * @inheritDoc
     */
    public static function clean($data): void
    {
        static::deleteAll([
            'id' => static::find()
                ->where([
                    'repay_date' => array_values(array_unique(array_column($data, 'repay_date'))),
                    'dds_id' => array_values(array_unique(array_column($data, 'dds_id'))),
                ])
                ->select('id')
                ->column(),
        ]);
    }
}
