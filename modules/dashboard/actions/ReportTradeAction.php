<?php

namespace dashboard\actions;

use backend\grid\MoneyDataColumn;
use dashboard\services\ReportTradeService;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\Action;
use yii\web\Request;

class ReportTradeAction extends Action
{
    public $channel;

    /**
     * @var Request
     */
    private $request;

    public function init()
    {
        $this->request = Yii::$app->getRequest();
    }

    public function run()
    {
        $searchModel = new ReportTradeService([
            'channel' => $this->channel,
            'type'    => [
                'pay_count',                    // 支付笔数
                'pay_amount',                   // 支付金额
                'channel_amount',               // 支付手续费
                'refund_count',                 // 退款笔数
                'refund_amount',                // 退款金额
            ],
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->controller->render('report_trade', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
            'columns'      => $this->columns(),
        ]);
    }

    /**
     * @return array
     */
    public function columns()
    {
        $mappingConfig = KeyValue::take('trade_type_mapping');

        return [
            [
                'label'     => '日期',
                'attribute' => 'date',
            ],
            [
                'label' => '产品',
                'value' => function ($row) use ($mappingConfig) {
                    return $mappingConfig[$row['channel']] ?? $row['channel'];
                },
            ],
            [
                'attribute' => 'product_category',
                'label'     => '支付通道',
            ],
            [
                'attribute' => 'pay_count',
                'label'     => '支付笔数',
            ],
            [
                'attribute' => 'pay_amount',
                'label'     => '支付金额',
                'class'     => MoneyDataColumn::class,
            ],
            [
                'attribute' => 'channel_amount',
                'label'     => '支付手续费',
                'class'     => MoneyDataColumn::class,
            ],
            [
                'attribute' => 'refund_count',
                'label'     => '退款笔数',
            ],
            [
                'attribute' => 'refund_amount',
                'label'     => '退款金额',
                'class'     => MoneyDataColumn::class,
            ],
        ];
    }
}
