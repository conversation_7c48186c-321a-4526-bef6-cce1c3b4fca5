<?php

namespace dashboard;

use yii\base\Application;
use yii\base\BootstrapInterface;
use yii\web\UrlRule;

class Module extends \yii\base\Module implements BootstrapInterface
{
    public $defaultRoute = 'dashboard/index';

    /**
     * @param Application $app
     */
    public function bootstrap($app)
    {
        $app->getUrlManager()->addRules([
            [
                'class'      => UrlRule::class,
                'route'      => 'dashboard/dashboard/index',
                'pattern'    => 'dashboard',
                'normalizer' => false,
                'suffix'     => false,
            ],
            [
                'class'      => UrlRule::class,
                'route'      => 'dashboard/monitor-statistics/<action>',
                'pattern'    => 'dms/<action:[\w\-]+>',
                'normalizer' => false,
                'suffix'     => false,
            ],
            [
                'class'      => UrlRule::class,
                'route'      => 'dashboard/business-report/index',
                'pattern'    => 'brs/<__c:[\w\-]+>',
                'normalizer' => false,
                'suffix'     => false,
            ],
            [
                'class'      => UrlRule::class,
                'route'      => 'dashboard/report/index',
                'pattern'    => 'report/<__c:[\w\-]+>',
                'normalizer' => false,
                'suffix'     => false,
            ],
            [
                'class'      => UrlRule::class,
                'route'      => 'dashboard/diy-report/index',
                'pattern'    => 'diy-report/<__c:[\w\-]+>',
                'normalizer' => false,
                'suffix'     => false,
            ],
        ], false);
    }
}
