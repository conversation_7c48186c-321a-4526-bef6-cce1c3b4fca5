<?php

use common\helpers\TooltipHelper;
use dashboard\assets\EchartsAsset;
use dashboard\grid\EnableHtmlLabelGridView;
use payment\models\paystats\RPCSPaymentModeRepaySearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\helpers\Json;

/*** @var array $params */
/*** @var RPCSPaymentModeRepaySearch $searchModel */
$this->title = '分渠道还款';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$dataProvider = new ArrayDataProvider([
        'allModels' => $params['table'] ?? [],
        'pagination' => false,
        'sort' => [
                'attributes' => ['repay_numbers'],
                'defaultOrder' => ['repay_numbers' => SORT_DESC],
        ]
]);

// 注册ECharts资源
EchartsAsset::register($this);
?>
<style>
    .chart-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .chart-box {
        width: 48%;
        height: 400px;
        background: white;
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .date-filter-container {
        background-color: #f9f9f9;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #eaeaea;
    }

    .channel-filter-container {
        background-color: #f9f9f9;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #eaeaea;
    }

    .channel-filter-container label {
        display: block;
        margin-bottom: 10px;
        font-weight: 600;
        color: #333;
    }

    .channel-filter-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .channel-checkbox-item {
        display: inline-flex;
        align-items: center;
        background: white;
        padding: 5px 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .channel-checkbox-item:hover {
        border-color: #1890ff;
        background-color: #f0f7ff;
    }

    .channel-checkbox-item input[type="checkbox"],
    .channel-checkbox-item input[type="radio"] {
        margin-right: 5px;
    }

    .data-update-time {
        margin-top: 20px;
        font-size: 12px;
        color: #888;
        text-align: right;
    }
</style>
<!-- 分渠道统计数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">top10渠道数据</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-md-12">
                <div class="search-form">
                    <?php $form = ActiveForm::begin([
                            'action' => ['repay-payment-mode-stats'],
                            'method' => 'get',
                            'type' => ActiveForm::TYPE_INLINE,
                            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>


                    <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                            'options' => [
                                    'placeholder' => '开始时间',
                                    'autocomplete' => 'off',
                            ],
                    ]) ?>

                    <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                            'options' => [
                                    'placeholder' => '结束时间',
                                    'autocomplete' => 'off',
                            ],
                    ]) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                            'class' => 'btn btn-primary',
                            'style' => 'margin-right: 10px;'
                    ]) ?>

                    <?= Html::a(Yii::t('grant', 'Reset'), ['repay-payment-mode-stats'], ['class' => 'btn btn-default']) ?>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>

        <!-- 渠道统计表格 -->
        <div class="row">
            <div class="col-md-12">
                <?= EnableHtmlLabelGridView::widget([
                        'dataProvider' => $dataProvider,
                        'tableOptions' => ['class' => 'table table-bordered table-striped'],
                        'columns' => [
                                [
                                        'attribute' => 'payment_mode',
                                        'label' => TooltipHelper::labelWithTooltip('支付渠道'),
                                        'format' => 'raw',
                                        'value' => function ($model) use ($searchModel) {
                                            return Html::a($model['payment_mode'], ['repay-payment-mode-detail', 'startDate' => $searchModel->startDate, 'endDate' => $searchModel->endDate, 'payment_mode' => $model['payment_mode'] ?? '',], ['target' => '_blank',]);
                                        }
                                ],
                                [
                                        'attribute' => 'repay_numbers',
                                        'label' => TooltipHelper::labelWithTooltip('总计还款支付次数'),
                                ],
                                [
                                        'attribute' => 'repay_numbers_ratio',
                                        'label' => TooltipHelper::labelWithTooltip('支付次数占比'),
                                        'format' => 'raw',
                                        'value' => function ($model) {
                                            return ($model['repay_numbers_ratio'] ?? 0) . '%';
                                        },
                                ],
                                [
                                        'attribute' => 'repay_success_rate',
                                        'label' => TooltipHelper::labelWithTooltip('支付次数成功率'),
                                        'format' => 'raw',
                                        'value' => function ($model) {
                                            return ($model['repay_success_rate'] ?? 0) . '%';
                                        },
                                ],
                                [
                                        'attribute' => 'repay_success_numbers',
                                        'label' => TooltipHelper::labelWithTooltip('还款支付成功次数'),
                                ],
                                [
                                        'attribute' => 'repay_amount',
                                        'label' => TooltipHelper::labelWithTooltip('还款支付总金额(K)'),
                                        'value' => function ($model) {
                                            if (empty($model['repay_amount'])) {
                                                return '-';
                                            }
                                            return convertFenToKiloYuan($model['repay_amount']);
                                        },
                                ],
                                [
                                        'attribute' => 'repay_success_amount_ratio',
                                        'label' => TooltipHelper::labelWithTooltip('成功金额占比'),
                                        'format' => 'raw',
                                        'value' => function ($model) {
                                            return ($model['repay_success_amount_ratio'] ?? 0) . '%';
                                        },
                                ],
                                [
                                        'attribute' => 'repay_fee_amount',
                                        'label' => TooltipHelper::labelWithTooltip('总计手续费金额(K)'),
                                        'value' => function ($model) {
                                            if (empty($model['repay_fee_amount'])) {
                                                return '-';
                                            }
                                            return convertFenToKiloYuan($model['repay_fee_amount']);
                                        },
                                ],
                                [
                                        'attribute' => 'thousand_repay_cost',
                                        'label' => TooltipHelper::labelWithTooltip('千元还款成本'),
                                ],
                        ],
                ]);
                ?>
            </div>
        </div>

        <!-- 渠道筛选器 -->
        <div class="row channel-filter-container" id="channel-filter-container">
            <label>渠道统计图表：</label>
            <div class="channel-filter-wrapper">
            </div>
        </div>

        <!-- 渠道统计图表 -->
        <div class="chart-container">
            <div class="chart-box">
                <h4 class="chart-title">渠道成功率走势图</h4>
                <div id="paymentModeSuccessRate" style="height: 300px;"></div>
            </div>
            <div class="chart-box">
                <h4 class="chart-title">每日渠道还款次数</h4>
                <div id="paymentModePayCount" style="height: 300px;"></div>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-box">
                <h4 class="chart-title">按各通道分布</h4>
                <div id="channelPayCount" style="height: 300px;"></div>
            </div>
            <div class="chart-box">
                <h4 class="chart-title">分通道成功率</h4>
                <div id="channelSuccessRate" style="height: 300px;"></div>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-box">
                <h4 class="chart-title">分交易方式笔数分布</h4>
                <div id="paymentMethodPayCount" style="height: 300px;"></div>
            </div>
            <div class="chart-box">
                <h4 class="chart-title">分交易方式成功率</h4>
                <div id="methodSuccessRate" style="height: 300px;"></div>
            </div>
        </div>

        <div class="data-update-time" style="text-align: left">
            数据更新时间: <?= $params['chart']['maxDate'] ?? date('Y-m-d H:i:s'); ?>
        </div>
    </div>
</div>

<?php
$chartDataJson = Json::encode($params['chart'] ?? []);
?>

<script>
    <?php $this->beginBlock('js') ?>

    // 解析后端数据
    const chartData = JSON.parse('<?= $chartDataJson ?>'), paymentModeNames = chartData.modes,
        channelNames = chartData.channels, methodNames = chartData.methods, colors = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
            '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7074'
        ];

    // 初始化渠道筛选器
    function initChannelFilter() {
        const filterContainer = document.getElementById('channel-filter-container').querySelector('.channel-filter-wrapper');
        const paymentModes = Object.keys(paymentModeNames);

        filterContainer.innerHTML = '';

        // 添加各渠道选项（单选按钮）
        let firstMode = null;
        paymentModes.forEach((mode, index) => {
            if (hasDataForMode(mode)) {
                if (firstMode === null) {
                    firstMode = mode;
                }
                const item = document.createElement('div');
                item.className = 'channel-checkbox-item';
                item.innerHTML = `
                    <input type="radio" id="filter-${mode}" name="channel-filter" class="channel-filter" data-mode="${mode}" ${index === 0 ? 'checked' : ''}>
                    <label for="filter-${mode}">${paymentModeNames[mode] || mode}</label>
                `;
                filterContainer.appendChild(item);
            }
        });

        // 绑定事件
        document.querySelectorAll('.channel-filter').forEach(radio => {
            radio.addEventListener('change', function () {
                updateAllCharts();
            });
        });
    }


    // 检查某个渠道是否有数据
    function hasDataForMode(mode) {
        // 检查成功率数据
        if (chartData.paymentModeSuccessRate &&
            chartData.paymentModeSuccessRate.rates &&
            chartData.paymentModeSuccessRate.rates[mode]) {
            const rateValues = chartData.paymentModeSuccessRate.rates[mode];
            if (rateValues.some(v => v > 0)) return true;
        }

        // 检查支付次数数据
        if (chartData.paymentModePayCount &&
            ((chartData.paymentModePayCount.successPayCount && chartData.paymentModePayCount.successPayCount[mode]) ||
                (chartData.paymentModePayCount.failPayCount && chartData.paymentModePayCount.failPayCount[mode]))) {
            const successValues = chartData.paymentModePayCount.successPayCount[mode] || [];
            const failValues = chartData.paymentModePayCount.failPayCount[mode] || [];
            if (successValues.some(v => v > 0) || failValues.some(v => v > 0)) return true;
        }

        return false;
    }

    // 获取选中的渠道
    function getSelectedModes() {
        const selectedRadio = document.querySelector('.channel-filter:checked');
        return selectedRadio ? [selectedRadio.dataset.mode] : [];
    }

    // 初始化所有图表
    function initCharts() {
        initChannelFilter();
        initPaymentModeSuccessRateChart();
        initPaymentModePayCountChart();
        initChannelPayCountChart();
        initChannelSuccessRateChart();
        initPaymentMethodPayCountChart();
        initMethodSuccessRateChart();

        // 窗口大小变化时重绘图表
        window.addEventListener('resize', function () {
            paymentModeSuccessRateChart.resize();
            paymentModePayCountChart.resize();
            channelPayCountChart.resize();
            channelSuccessRateChart.resize();
            paymentMethodPayCountChart.resize();
            methodSuccessRateChart.resize();
        });
    }

    // 更新所有图表
    function updateAllCharts() {
        updatePaymentModeSuccessRateChart();
        updatePaymentModePayCountChart();
        updateChannelPayCountChart();
        updateChannelSuccessRateChart();
        updatePaymentMethodPayCountChart();
        updateMethodSuccessRateChart();
    }

    // 渠道成功率走势图
    let paymentModeSuccessRateChart;

    function initPaymentModeSuccessRateChart() {
        paymentModeSuccessRateChart = echarts.init(document.getElementById('paymentModeSuccessRate'));
        updatePaymentModeSuccessRateChart();
    }

    function updatePaymentModeSuccessRateChart() {
        if (!chartData.paymentModeSuccessRate) return;

        const selectedModes = getSelectedModes();
        const dates = chartData.paymentModeSuccessRate.dates;
        const series = [];

        selectedModes.forEach((mode, index) => {
            if (chartData.paymentModeSuccessRate.rates[mode]) {
                series.push({
                    name: paymentModeNames[mode] || mode,
                    type: 'line',
                    data: chartData.paymentModeSuccessRate.rates[mode],
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: {
                        color: colors[index % colors.length]
                    },
                    lineStyle: {
                        width: 2
                    }
                });
            }
        });

        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    let result = params[0].axisValue + '<br/>';
                    params.forEach(param => {
                        result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
                    });
                    return result;
                }
            },
            legend: {
                data: selectedModes.map(mode => paymentModeNames[mode] || mode),
                bottom: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '8%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dates,
                axisLabel: {
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '成功率(%)',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: series
        };

        paymentModeSuccessRateChart.setOption(option, true);
    }

    // 每日渠道放款次数图
    let paymentModePayCountChart;

    function initPaymentModePayCountChart() {
        paymentModePayCountChart = echarts.init(document.getElementById('paymentModePayCount'));
        updatePaymentModePayCountChart();
    }

    function updatePaymentModePayCountChart() {
        if (!chartData.paymentModePayCount) return;

        const selectedModes = getSelectedModes();
        const dates = chartData.paymentModePayCount.dates;
        const series = [];

        selectedModes.forEach((mode, index) => {
            // 成功次数
            if (chartData.paymentModePayCount.successPayCount && chartData.paymentModePayCount.successPayCount[mode]) {
                series.push({
                    name: paymentModeNames[mode] + ' 成功',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {focus: 'series'},
                    data: chartData.paymentModePayCount.successPayCount[mode],
                    itemStyle: {
                        color: colors[index % colors.length]
                    }
                });
            }

            // 失败次数
            if (chartData.paymentModePayCount.failPayCount && chartData.paymentModePayCount.failPayCount[mode]) {
                series.push({
                    name: paymentModeNames[mode] + ' 失败',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {focus: 'series'},
                    data: chartData.paymentModePayCount.failPayCount[mode],
                    itemStyle: {
                        color: colors[index % colors.length],
                        opacity: 0.5
                    }
                });
            }
        });

        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {type: 'shadow'},
                formatter: function (params) {
                    let result = params[0].axisValue + '<br/>';
                    let total = 0;
                    params.forEach(param => {
                        result += `${param.marker} ${param.seriesName}: ${param.value.toLocaleString()}<br/>`;
                        total += param.value;
                    });
                    result += `<br/>总计: ${total.toLocaleString()}`;
                    return result;
                }
            },
            legend: {
                data: series.map(s => s.name),
                bottom: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '8%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: dates,
                axisLabel: {
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '还款次数'
            },
            series: series
        };

        paymentModePayCountChart.setOption(option, true);
    }

    // 按各通道分布图
    let channelPayCountChart;

    function initChannelPayCountChart() {
        channelPayCountChart = echarts.init(document.getElementById('channelPayCount'));
        updateChannelPayCountChart();
    }

    function updateChannelPayCountChart() {
        if (!chartData.channelPayCount) return;

        const selectedModes = getSelectedModes();
        const series = [];
        const legendData = [];

        // 准备饼图数据
        const pieData = [];

        selectedModes.forEach(mode => {
            if (chartData.channelPayCount.payCount[mode]) {
                const channelData = chartData.channelPayCount.payCount[mode];

                Object.keys(channelData).forEach(channel => {
                    if (channelData[channel] > 0) {
                        const name = `${paymentModeNames[mode] || mode}-${channelNames[channel] || channel}`;
                        pieData.push({
                            name: name,
                            value: channelData[channel],
                            itemStyle: {
                                color: colors[(legendData.length) % colors.length]
                            }
                        });
                        legendData.push(name);
                    }
                });
            }
        });

        series.push({
            name: '通道分布',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            data: pieData,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            label: {
                formatter: '{b}: {d}%'
            }
        });

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                right: 10,
                top: 20,
                bottom: 20,
                data: legendData
            },
            series: series
        };

        channelPayCountChart.setOption(option, true);
    }

    // 分通道成功率图
    let channelSuccessRateChart;

    function initChannelSuccessRateChart() {
        channelSuccessRateChart = echarts.init(document.getElementById('channelSuccessRate'));
        updateChannelSuccessRateChart();
    }

    function updateChannelSuccessRateChart() {
        if (!chartData.channelSuccessRate) return;

        const selectedModes = getSelectedModes();
        const dates = chartData.channelSuccessRate.dates;
        const series = [];
        const legendData = [];

        selectedModes.forEach(mode => {
            if (chartData.channelSuccessRate.rates[mode]) {
                const channelRates = chartData.channelSuccessRate.rates[mode];

                Object.keys(channelRates).forEach(channel => {
                    // 检查该通道是否有数据
                    const hasData = Object.values(channelRates[channel]).some(rate => rate > 0);

                    if (hasData) {
                        const name = `${paymentModeNames[mode] || mode}-${channelNames[channel] || channel}`;
                        const data = dates.map(date => channelRates[channel][date] || 0);

                        series.push({
                            name: name,
                            type: 'line',
                            data: data,
                            symbol: 'circle',
                            symbolSize: 8,
                            itemStyle: {
                                color: colors[(legendData.length) % colors.length]
                            }
                        });

                        legendData.push(name);
                    }
                });
            }
        });

        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    let result = params[0].axisValue + '<br/>';
                    params.forEach(param => {
                        result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
                    });
                    return result;
                }
            },
            legend: {
                type: 'scroll',
                bottom: 0,
                data: legendData
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '8%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dates,
                axisLabel: {
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '成功率(%)',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: series
        };

        channelSuccessRateChart.setOption(option, true);
    }

    // 分交易方式笔数分布图
    let paymentMethodPayCountChart;

    function initPaymentMethodPayCountChart() {
        paymentMethodPayCountChart = echarts.init(document.getElementById('paymentMethodPayCount'));
        updatePaymentMethodPayCountChart();
    }

    function updatePaymentMethodPayCountChart() {
        if (!chartData.paymentMethodPayCount) return;

        const selectedModes = getSelectedModes();
        const series = [];
        const legendData = [];

        // 准备饼图数据
        const pieData = [];

        selectedModes.forEach(mode => {
            if (chartData.paymentMethodPayCount.payCount[mode]) {
                const methodData = chartData.paymentMethodPayCount.payCount[mode];

                Object.keys(methodData).forEach(method => {
                    if (methodData[method] > 0) {
                        const name = `${paymentModeNames[mode] || mode}-${methodNames[method] || method}`;
                        pieData.push({
                            name: name,
                            value: methodData[method],
                            itemStyle: {
                                color: colors[(legendData.length) % colors.length]
                            }
                        });
                        legendData.push(name);
                    }
                });
            }
        });

        series.push({
            name: '交易方式分布',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            data: pieData,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            label: {
                formatter: '{b}: {d}%'
            }
        });

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                right: 10,
                top: 20,
                bottom: 20,
                data: legendData
            },
            series: series
        };

        paymentMethodPayCountChart.setOption(option, true);
    }

    // 分交易方式成功率图
    let methodSuccessRateChart;

    function initMethodSuccessRateChart() {
        methodSuccessRateChart = echarts.init(document.getElementById('methodSuccessRate'));
        updateMethodSuccessRateChart();
    }

    function updateMethodSuccessRateChart() {
        if (!chartData.methodSuccessRate) return;

        const selectedModes = getSelectedModes();
        const dates = chartData.methodSuccessRate.dates;
        const series = [];
        const legendData = [];

        selectedModes.forEach(mode => {
            if (chartData.methodSuccessRate.rates[mode]) {
                const methodRates = chartData.methodSuccessRate.rates[mode];

                Object.keys(methodRates).forEach(method => {
                    // 检查该交易方式是否有数据
                    const hasData = Object.values(methodRates[method]).some(rate => rate > 0);

                    if (hasData) {
                        const name = `${paymentModeNames[mode] || mode}-${methodNames[method] || method}`;
                        const data = dates.map(date => methodRates[method][date] || 0);

                        series.push({
                            name: name,
                            type: 'line',
                            data: data,
                            symbol: 'circle',
                            symbolSize: 8,
                            itemStyle: {
                                color: colors[(legendData.length) % colors.length]
                            }
                        });

                        legendData.push(name);
                    }
                });
            }
        });

        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    let result = params[0].axisValue + '<br/>';
                    params.forEach(param => {
                        result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
                    });
                    return result;
                }
            },
            legend: {
                type: 'scroll',
                bottom: 0,
                data: legendData
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '8%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dates,
                axisLabel: {
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '成功率(%)',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: series
        };

        methodSuccessRateChart.setOption(option, true);
    }

    initCharts()
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>