<?php

use Carbon\Carbon;
use payment\models\paystats\RPCSChannelRepaySearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/** @var array $params */
/** @var RPCSChannelRepaySearch $searchModel */
$this->title = '分通道还款';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$dataProvider = new ArrayDataProvider([
        'allModels' => $params['table'] ?? [],
        'pagination' => false,
]);
$date = Carbon::now()->toDateTimeString();

/** @var array $params */
?>
<!-- 整体放款数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">分通道支付次数占比趋势</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-md-12">
                <div class="search-form">
                    <?php $form = ActiveForm::begin([
                            'action' => ['repay-channel-all'],
                            'method' => 'get',
                            'type' => ActiveForm::TYPE_INLINE,
                            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>

                    <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                            'options' => [
                                    'placeholder' => '开始时间',
                                    'autocomplete' => 'off',
                            ],
                    ]) ?>

                    <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                            'options' => [
                                    'placeholder' => '结束时间',
                                    'autocomplete' => 'off',
                            ],
                    ]) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                            'class' => 'btn btn-primary',
                            'style' => 'margin-right: 10px;'
                    ]) ?>

                    <?= Html::a(Yii::t('grant', 'Reset'), ['repay-channel-all'], ['class' => 'btn btn-default']) ?>


                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <?= GridView::widget([
                        'dataProvider' => $dataProvider,
                        'tableOptions' => ['class' => 'table table-bordered table-striped'],
                        'columns' => [
                                [
                                        'attribute' => 'sign_company_name',
                                        'label' => '主体',
                                ],
                                [
                                        'attribute' => 'channel_name',
                                        'label' => '通道',
                                        'format' => 'raw',
                                        'value' => function ($model) use ($searchModel) {
                                            return Html::a($model['channel_name'], ['repay-channel-detail', 'startDate' => $searchModel->startDate, 'endDate' => $searchModel->endDate, 'channel_name' => $model['channel_name'] ?? '',], ['target' => '_blank',]);
                                        }
                                ],
                                [
                                        'attribute' => 'repay_numbers',
                                        'label' => '总计还款次数',
                                ],
                                [
                                        'attribute' => 'repay_numbers_ratio',
                                        'label' => '支付次数占比',
                                        'format' => 'raw',
                                        'value' => function ($model) {
                                            return ($model['repay_numbers_ratio'] ?? 0) . '%';
                                        },
                                ],
                                [
                                        'attribute' => 'repay_success_rate',
                                        'label' => '支付次数成功率',
                                        'format' => 'raw',
                                        'value' => function ($model) {
                                            return ($model['repay_success_rate'] ?? 0) . '%';
                                        },
                                ],
                                [
                                        'attribute' => 'repay_success_numbers',
                                        'label' => '还款支付成功次数',
                                ],
                                [
                                        'attribute' => 'repay_amount',
                                        'label' => '支付总金额(K)',
                                        'value' => function ($model) {
                                            if (empty($model['repay_amount'])) {
                                                return '-';
                                            }
                                            return convertFenToKiloYuan($model['repay_amount']);
                                        }
                                ],
                                [
                                        'attribute' => 'repay_success_amount',
                                        'label' => '还款成功总金额(K)',
                                        'value' => function ($model) {
                                            if (empty($model['repay_success_amount'])) {
                                                return '-';
                                            }
                                            return convertFenToKiloYuan($model['repay_success_amount']);
                                        }
                                ],
                                [
                                        'attribute' => 'repay_success_amount_ratio',
                                        'label' => '成功金额占比',
                                        'format' => 'raw',
                                        'value' => function ($model) {
                                            return ($model['repay_success_amount_ratio'] ?? 0) . '%';
                                        },
                                ],
                                [
                                        'attribute' => 'repay_fee_amount',
                                        'label' => '总计手续费金额(K)',
                                        'value' => function ($model) {
                                            if (empty($model['repay_fee_amount'])) {
                                                return '-';
                                            }
                                            return convertFenToKiloYuan($model['repay_fee_amount']);
                                        }
                                ],
                                [
                                        'attribute' => 'thousand_repay_cost',
                                        'label' => '千元还款成本',
                                ]
                        ],
                ]);
                ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">分通道成功率</h4>
                    <div id="channelSuccessRateChart" style="height: 300px;"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">分通道的支付次数</h4>
                    <div id="channelPayCountChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">通道的支付次数和其占比优势</h4>
                    <div id="singleChannelPayCountChart" style="height: 300px;"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">支付成本趋势</h4>
                    <div id="payCostTrendChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="data-update-time">
            数据更新时间: <?= $params['chart']['maxDate'] ?? $date; ?>
        </div>
    </div>
</div>
<?php
$chartDataJson = json_encode($params['chart'] ?? []);

$js = <<<JS

function initCharts(chartData) {
    const charts = {
        channelSuccessRateChart: echarts.init(document.getElementById('channelSuccessRateChart')),
        channelPayCountChart: echarts.init(document.getElementById('channelPayCountChart')),
        singleChannelPayCountChart: echarts.init(document.getElementById('singleChannelPayCountChart')),
        payCostTrendChart: echarts.init(document.getElementById('payCostTrendChart'))
    };

    charts.channelSuccessRateChart.setOption({
        xAxis: { type: 'category', data: chartData.channelSuccessRate.dates },
        yAxis: { type: 'value', name: '成功率(%)' },
        tooltip: {
            trigger: 'axis', 
            axisPointer: { type: 'line' }
         },
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0,
            data: Object.keys(chartData.channelSuccessRate.rates)
        },
        series:Object.keys(chartData.channelSuccessRate.rates).map(channelName => ({
            name: channelName,
            type: 'line',
            data: chartData.channelSuccessRate.rates[channelName],
            smooth: true,
            symbolSize: 8, 
            emphasis: { focus: "series" } 
        }))
    });

    charts.channelPayCountChart.setOption({
        xAxis: { type: 'category', data: chartData.channelPayCount.dates },
        yAxis: { type: 'value', name: '支付次数' },
        tooltip: {
            trigger: 'axis', 
            axisPointer: { type: 'line' }
         },
        series: Object.keys(chartData.channelPayCount.payCount).map(channelName => ({
            name: channelName,
            type: 'bar',
            data: chartData.channelPayCount.payCount[channelName],
            smooth: true,
            symbolSize: 8, 
            emphasis: { focus: "series" } 
        })),
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0,
            data: Object.keys(chartData.channelPayCount.payCount)
        }
    });

    charts.singleChannelPayCountChart.setOption({
        xAxis: { type: 'category', data: chartData.singleChannelPayCount.dates },
        yAxis: { type: 'value', name: '占比(%)', position: 'left' },
         tooltip: {
            trigger: 'axis', 
            axisPointer: { type: 'line' }
         },
         legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0,
            data: Object.keys(chartData.singleChannelPayCount.ratio).map(channelName => channelName + '次数占比')
        },
        series: Object.keys(chartData.singleChannelPayCount.ratio).map(channelName => ({
          name: channelName + '次数占比', 
          type: 'line',
          yAxisIndex: 0,  // 改为0，因为只有一个yAxis
          data: chartData.singleChannelPayCount.ratio[channelName],
          smooth: true,
          symbolSize: 8, 
          emphasis: { focus: "series" } 
      }))
    });

    charts.payCostTrendChart.setOption({
        xAxis: { type: 'category', data: chartData.payCostTrend.dates },
        yAxis: [
            { type: 'value', name: '手续费金额(K)' },
            { type: 'value', name: '成本', position: 'right' }
        ],
        tooltip: {
            trigger: 'axis', 
            axisPointer: { type: 'line' }
         },
         legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0,
            data: [
            ...Object.keys(chartData.payCostTrend.payFeeAmount).map(channelName => channelName + '还款总手续费金额(K)'),  
            ...Object.keys(chartData.payCostTrend.cost).map(channelName => channelName + '千元还款成本')
        ]
        },
         series: [
            ...Object.keys(chartData.payCostTrend.payFeeAmount).map(channelName => ({
            name: channelName + '还款总手续费金额(K)', 
            type: 'bar',
            data: chartData.payCostTrend.payFeeAmount[channelName],
            smooth: true,
            symbolSize: 8, 
            emphasis: { focus: "series" }
        })),
            ...Object.keys(chartData.payCostTrend.cost).map(channelName => ({
            name: channelName + '千元还款成本', 
            type: 'line',
            yAxisIndex: 1, 
            data: chartData.payCostTrend.cost[channelName],
            smooth: true,
            symbolSize: 8, 
            emphasis: { focus: "series" } 
        }))
        ]
    });
    resizeChart(charts)
}
initCharts({$chartDataJson})
JS;
$this->registerJs($js);
?>



