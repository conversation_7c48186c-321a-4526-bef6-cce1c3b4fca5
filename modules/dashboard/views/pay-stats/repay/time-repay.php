<?php

use payment\models\paystats\RPCSTimeRepaySearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use yii\helpers\Html;

/*** @var array $params */
/*** @var RPCSTimeRepaySearch $searchModel */
$this->title = '整体还款-分时';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
?>
<!-- 分时放款数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">整体还款-分时</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-md-12">
                <div class="search-form">
                    <?php $form = ActiveForm::begin([
                        'action' => ['repay-time'],
                        'method' => 'get',
                        'type'          => ActiveForm::TYPE_INLINE,
                        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>

                    <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '开始时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '结束时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                        'class' => 'btn btn-primary',
                        'style' => 'margin-right: 10px;'
                    ]) ?>

                    <?= Html::a(Yii::t('grant', 'Reset'), ['repay-time'], ['class' => 'btn btn-default']) ?>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
        <!-- 分时放款图表 -->
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">分时还款成功笔数走势</h4>
                    <div id="hourlyLoanSuccessChart" style="height: 350px;"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">分时还款成功金额走势</h4>
                    <div id="hourlyLoanAmountChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="data-update-time">
            数据更新时间: <?= $params['maxDate'] ?>
        </div>
    </div>
</div>
<?php

$hourlyLoanDataJson = json_encode($params['hourlyLoanData']);

$js = <<<JS
// 初始化所有图表
$(document).ready(function() {
    let hourlyLoanData = {$hourlyLoanDataJson};
    let charts = {};
    charts.hourlyLoanSuccessChart = initChart('hourlyLoanSuccessChart', hourlyLoanData, 'successData', '笔数');
    charts.hourlyLoanAmountChart = initChart('hourlyLoanAmountChart', hourlyLoanData, 'amountData', '金额');
    resizeChart(charts)
});

function initChart(elementId, data, dataKey, yAxisName) {
    var chartDom = document.getElementById(elementId);
    var myChart = echarts.init(chartDom);

    var series = data.dates.map(date => ({
        name: date,
        type: 'line',
        data: data[dataKey][date],
        symbol: 'circle',
        symbolSize: 8,
        markPoint: { data: [{ type: 'max', name: '最大值' }] }
    }));

    var option = {
        tooltip: { trigger: 'axis' },
        legend: {
            data: data.dates,
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.hours,
            name: '时间段',
            axisLabel: {
                interval: function (index, value) {
                    // 只显示整点的标签，隐藏半点的标签以避免重叠
                    return index % 2 === 0;
                },
                rotate: 45,
                formatter: function (value) {
                    return value;
                }
            }
        },
        yAxis: {
            type: 'value',
            name: yAxisName,
            axisLabel: {
            formatter: function (value) {
                if(yAxisName === '金额') {
                    if (value === null || value === undefined || isNaN(value)) {
                        return '0.00' + 'K';
                    }
                    return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + 'K';
                }
                return value;
            }
          }
        },
        series: series
    };
    console.log(series)
    myChart.setOption(option);
    return myChart;
}
JS;
$this->registerJs($js);
?>

