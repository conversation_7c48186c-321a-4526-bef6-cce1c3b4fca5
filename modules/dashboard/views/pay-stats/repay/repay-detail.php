<?php
/** @var array $params */

/** @var RPCSAllGrantSearch $searchModel */

use payment\models\paystats\RPCSAllGrantSearch;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;

$this->title = '还款明细数据';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$comparisonData = $params['data'];

function formatNumber($value, $decimals = 2): string
{
    if ($value === null) {
        return '-';
    }
    return number_format($value, $decimals);
}

echo GridView::widget([
    'dataProvider' => new ArrayDataProvider([
        'allModels' => $comparisonData ?? [],
        'pagination' => false,
    ]),
    'tableOptions' => ['class' => 'table table-bordered table-striped'],
    'columns' => [
        [
            'attribute' => 'date1',
            'label' => '日期',
        ],
        [
            'attribute' => 'repay_counts_day',
            'label' => '还款笔数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['repay_counts_day'], 0);
            },
        ],
        [
            'attribute' => 'repay_success_counts_rate',
            'label' => '还款笔数成功率',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['repay_success_counts_rate']) . '%';
            },
        ],
        [
            'attribute' => 'repay_numbers',
            'label' => '总计支付次数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['repay_numbers'], 0);
            },
        ],
        [
            'attribute' => 'repay_success_numbers_rate',
            'label' => '还款支付次数成功率',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['repay_success_numbers_rate']) . '%';
            },
        ],
        [
            'attribute' => 'avg_repay_numbers_per_repay',
            'label' => '笔均支付次数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['avg_repay_numbers_per_repay']);
            },
        ],
        [
            'attribute' => 'repay_amount',
            'label' => '还款总金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['repay_amount']);
            },
        ],
        [
            'attribute' => 'repay_success_amount',
            'label' => '还款成功总金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['repay_success_amount']);
            },
        ],
        [
            'attribute' => 'repay_success_amount_ratio',
            'label' => '还款成功金额占比',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['repay_success_amount_ratio']) . '%';
            },
        ],
        [
            'attribute' => 'repay_fee_amount',
            'label' => '总计手续费金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['repay_fee_amount']);
            },
        ],
        [
            'attribute' => 'thousand_repay_cost',
            'label' => '千元还款成本',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['thousand_repay_cost']);
            },
        ],]
]);