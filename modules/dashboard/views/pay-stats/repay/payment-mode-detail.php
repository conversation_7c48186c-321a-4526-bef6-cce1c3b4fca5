<?php

use payment\models\paystats\RPCSPaymentModeRepaySearch;
use payment\models\ReportPaymentChannelStats;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/* @var $data array */
/* @var $searchModel RPCSPaymentModeRepaySearch */

$this->title = '分通道渠道还款明细';

?>
    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                    'action' => [''],
                    'method' => 'get',
                    'id' => 'asset-search-form',
                    'type' => ActiveForm::TYPE_INLINE,
            ]) ?>

            <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                    'id' => 'startDate',
                    'options' => [
                            'placeholder' => '开始时间',
                    ],
            ]) ?>
            <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                    'id' => 'startDate',
                    'options' => [
                            'placeholder' => '结束时间',
                    ],
            ]) ?>


            <?= $form->field($searchModel, 'channel_name', [
                    'options' => [
                            'style' => 'min-width: 200px',
                    ],
            ])->widget(Select2::class, [
                    'data' => ReportPaymentChannelStats::getChannel(),
                    'pluginOptions' => [
                            'allowClear' => true,
                    ],
                    'options' => [
                            'prompt' => '请选择通道',
                    ],
            ]) ?>

            <?= $form->field($searchModel, 'payment_method', [
                    'options' => [
                            'style' => 'min-width: 200px',
                    ],
            ])->widget(Select2::class, [
                    'data' => ReportPaymentChannelStats::getPaymentMethod(),
                    'pluginOptions' => [
                            'allowClear' => true,
                    ],
                    'options' => [
                            'prompt' => '请选择付款方式',
                    ],
            ]) ?>

            <?= $form->field($searchModel, 'payment_mode', [
                    'options' => [
                            'style' => 'min-width: 200px',
                    ],
            ])->widget(Select2::class, [
                    'data' => ReportPaymentChannelStats::getPaymentMode(),
                    'pluginOptions' => [
                            'allowClear' => true,
                    ],
                    'options' => [
                            'prompt' => '请选择渠道',
                    ],
            ]) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
            <?= Html::a('重置搜索条件', ['fail-reason-detail'], [
                    'class' => 'btn btn-default',
            ]) ?>
            <?php
            ActiveForm::end() ?>

        </div>
    </div>

<?= GridView::widget([
        'dataProvider' => new ArrayDataProvider([
                'allModels' => $data,
                'sort' => [
                        'attributes' => ['date1', 'sign_company_name', 'channel_name', 'repay_success_numbers', 'payment_method', 'payment_mode', 'repay_numbers', 'repay_success_rate', 'repay_amount', 'repay_success_amount', 'repay_success_amount_ratio', 'repay_fee_amount', 'thousand_repay_cost', 'repay_fail_amount', 'repay_fail_numbers'],
                        'defaultOrder' => ['date1' => SORT_DESC],
                ],
        ]),
        'columns' => [
                [
                        'label' => '日期',
                        'value' => 'date1',
                        'attribute' => 'date1'
                ],
                [
                        'label' => '交易类型',
                        'value' => function () {
                            return '还款';
                        },
                ],
                [
                        'label' => '主体',
                        'attribute' => 'sign_company_name'
                ],
                [
                        'label' => '通道',
                        'attribute' => 'channel_name'
                ],
                [
                        'attribute' => 'payment_method',
                        'label' => '方式',
                ],
                [
                        'attribute' => 'payment_mode',
                        'label' => '渠道',
                ],
                [
                        'attribute' => 'repay_numbers',
                        'label' => '支付次数',
                ],
                [
                        'attribute' => 'repay_success_rate',
                        'label' => '支付成功占比',
                        'format' => 'raw',
                        'value' => function ($model) {
                            return ($model['repay_success_rate'] ?? 0) . '%';
                        },
                ],
                [
                        'attribute' => 'repay_success_numbers',
                        'label' => '还款支付成功次数',
                ],
                [
                        'attribute' => 'repay_amount',
                        'label' => '支付总金额(K)',
                        'value' => function ($model) {
                            if (empty($model['repay_amount'])) {
                                return '-';
                            }
                            return convertFenToKiloYuan($model['repay_amount']);
                        },
                ],
                [
                        'attribute' => 'repay_success_amount',
                        'label' => '还款成功总金额(K)',
                        'value' => function ($model) {
                            if (empty($model['repay_success_amount'])) {
                                return '-';
                            }
                            return convertFenToKiloYuan($model['repay_success_amount']);
                        },
                ],
                [
                        'attribute' => 'repay_success_amount_ratio',
                        'label' => '成功金额占比',
                        'format' => 'raw',
                        'value' => function ($model) {
                            return ($model['repay_success_amount_ratio'] ?? 0) . '%';
                        },
                ],
                [
                        'attribute' => 'repay_fee_amount',
                        'label' => '总计手续费金额(K)',
                        'value' => function ($model) {
                            if (empty($model['repay_fee_amount'])) {
                                return '-';
                            }
                            return convertFenToKiloYuan($model['repay_fee_amount']);
                        },
                ],
                [
                        'attribute' => 'thousand_repay_cost',
                        'label' => '千元还款成本',
                ],
                [
                        'attribute' => 'repay_fail_amount',
                        'label' => '失败总金额(K)',
                        'value' => function ($v) {
                            if (empty($v['repay_fail_amount'])) {
                                return '-';
                            }
                            return convertFenToKiloYuan($v['repay_fail_amount']);
                        }
                ],
                [
                        'attribute' => 'repay_fail_numbers',
                        'label' => '失败总支付次数',
                ],
        ],
]) ?>