<?php

use Carbon\Carbon;
use payment\models\paystats\RPCSAllGrantSearch;
use payment\models\paystats\RPCSAllRepaySearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/** @var array $params */
/** @var RPCSAllRepaySearch $searchModel */
$this->title = '整体还款';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$comparisonData = $params['comparisonData'];

$dataProvider = new ArrayDataProvider([
    'allModels' => $comparisonData['tables']['data'] ?? [],
    'pagination' => false,
]);
$date = Carbon::now()->toDateTimeString();
function formatComparison($value, $isPercentage = true): string
{
    if ($value === null) {
        return '-';
    }
    $sign = $value >= 0 ? '+' : '';
    return $sign . $value . ($isPercentage ? '%' : '');
}

function formatNumber($value, $decimals = 2): string
{
    if ($value === null) {
        return '-';
    }
    return number_format($value, $decimals);
}

/** @var array $params */
?>
<!-- 整体还款数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">整体还款</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-md-12">
                <div class="search-form">
                    <?php $form = ActiveForm::begin([
                        'action' => ['repay-all'],
                        'method' => 'get',
                        'type'          => ActiveForm::TYPE_INLINE,
                        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>
                    <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '开始时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '结束时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                        'class' => 'btn btn-primary',
                        'style' => 'margin-right: 10px;'
                    ]) ?>

                    <?= Html::a(Yii::t('grant', 'Reset'), ['repay-all'], ['class' => 'btn btn-default']) ?>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => ['class' => 'table table-bordered table-striped'],
                    'layout' => '<div class="box-header with-border">{summary}</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer pull-right"><small class="text-muted">数据更新时间:' . $comparisonData['tables']['maxDate'] ?? $date . '</small></div>',
                    'columns' => [
                        [
                            'attribute' => 'type',
                            'label' => '',
                        ],
                        [
                            'attribute' => 'period',
                            'label' => '时间周期',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    [$startDate, $endDate] = explode(' ~ ', $model['period']);
                                    return Html::a(
                                        $model['period'],
                                        ['repay-detail', 'startDate' => $startDate, 'endDate' => $endDate],
                                        ['target' => '_blank',]
                                    );
                                } else {
                                    return $model['period'];
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_counts_day',
                            'label' => '还款笔数',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['repay_counts_day'], 0);
                                } else {
                                    $value = $model['repay_counts_day'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_success_counts_rate',
                            'label' => '还款笔数成功率',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['repay_success_counts_rate']) . '%';
                                } else {
                                    $value = $model['repay_success_counts_rate'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_numbers',
                            'label' => '总计支付次数',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['repay_numbers'], 0);
                                } else {
                                    $value = $model['repay_numbers'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_success_numbers_rate',
                            'label' => '还款支付次数成功率',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['repay_success_numbers_rate']) . '%';
                                } else {
                                    $value = $model['repay_success_numbers_rate'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'avg_repay_numbers_per_repay',
                            'label' => '笔均支付次数',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['avg_repay_numbers_per_repay']);
                                } else {
                                    $value = $model['avg_repay_numbers_per_repay'];
                                    $class = isset($value) && $value <= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_amount',
                            'label' => '还款总金额(K)',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return convertFenToKiloYuan($model['repay_amount']);
                                } else {
                                    $value = $model['repay_amount'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_success_amount',
                            'label' => '还款成功总金额(K)',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return convertFenToKiloYuan($model['repay_success_amount']);
                                } else {
                                    $value = $model['repay_success_amount'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_success_amount_ratio',
                            'label' => '还款成功金额占比',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['repay_success_amount_ratio']) . '%';
                                } else {
                                    $value = $model['repay_success_amount_ratio'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'repay_fee_amount',
                            'label' => '总计手续费金额(K)',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return convertFenToKiloYuan($model['repay_fee_amount']);
                                } else {
                                    $value = $model['repay_fee_amount'];
                                    $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                        [
                            'attribute' => 'thousand_repay_cost',
                            'label' => '千元还款成本',
                            'format' => 'raw',
                            'value' => function ($model) {
                                if ($model['type'] === '数值') {
                                    return formatNumber($model['thousand_repay_cost']);
                                } else {
                                    $value = $model['thousand_repay_cost'];
                                    $class = isset($value) && $value <= 0 ? 'text-success' : 'text-danger';
                                    return Html::tag('span', formatComparison($value), ['class' => $class]);
                                }
                            },
                        ],
                    ],
                ]);
                ?>
            </div>
        </div>
        <!-- 每日还款和还款图表 -->
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">还款支付笔数和成功率走势</h4>
                    <div id="dailyLoanChart" style="height: 300px;"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">还款支付次数和成功率走势</h4>
                    <div id="dailyRepaymentChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- 进件还款和支付成本图表 -->
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">还款均实付次数</h4>
                    <div id="averagePaymentChart" style="height: 300px;"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h4 class="chart-title">支付成本趋势</h4>
                    <div id="paymentCostChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="data-update-time">
            数据更新时间: <?= $comparisonData['chart']['maxDate'] ?? $date; ?>
        </div>
    </div>
</div>
<?php
$chartDataJson = json_encode($comparisonData['chart'] ?? []);

$js = <<<JS
$(document).ready(function() {
    let chartData = {$chartDataJson};
    let charts = {};
    if (chartData.repayCounts && chartData.repayCounts.dates) {
        charts.repayCounts = ImportCountsChart(chartData.repayCounts);
    }
    
    if (chartData.repayNumbers && chartData.repayNumbers.dates) {
       charts.repayNumbers = PayNumbersChart(chartData.repayNumbers);
    }
    
    if (chartData.avgRepayNumbers && chartData.avgRepayNumbers.dates) {
        charts.avgRepayNumbers = AvgPayNumbersChart(chartData.avgRepayNumbers);
    }
    if (chartData.thousandRepayCost && chartData.thousandRepayCost.dates) {
        charts.thousandRepayCost  = ThousandLoanCostChart(chartData.thousandRepayCost);
    }
    resizeChart(charts)
});

function renderChart(dom,option){
    let chartDom = document.getElementById(dom), chart = echarts.init(chartDom);
    chart.setOption(option)
    return chart;
}


function ImportCountsChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['还款笔数', '成功率']
        },
        xAxis: [
            {
                type: 'category',
                data: data.dates,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    rotate: 45
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '笔数',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '成功率',
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: '{value}%'
                }
            }
        ],
        series: [
            {
                name: '还款笔数',
                type: 'bar',
                data: data.counts,
                barWidth: '40%',
                itemStyle: {
                    color: '#1E90FF'
                }
            },
            {
                name: '成功率',
                type: 'line',
                yAxisIndex: 1,
                data: data.rates,
                lineStyle: {
                    color: '#32CD32'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
    
     return renderChart('dailyLoanChart',option)
}

function PayNumbersChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['还款次数', '成功率']
        },
        xAxis: [
            {
                type: 'category',
                data: data.dates,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    rotate: 45
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '次数',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '成功率',
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: '{value}%'
                }
            }
        ],
        series: [
            {
                name: '还款次数',
                type: 'bar',
                data: data.counts,
                barWidth: '40%',
                itemStyle: {
                    color: '#1E90FF'
                }
            },
            {
                name: '成功率',
                type: 'line',
                yAxisIndex: 1,
                data: data.rates,
                lineStyle: {
                    color: '#32CD32'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
    return renderChart('dailyRepaymentChart',option)
}

function AvgPayNumbersChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['笔均支付次数']
        },
        xAxis: {
            type: 'category',
            data: data.dates,
            axisLabel: {
                rotate: 45,
                formatter: function(value) {
                    return value.substring(5); // 只显示月-日
                }
            }
        },
        yAxis: {
            type: 'value',
            min: 0.0,
            axisLabel: {
                formatter: '{value}'
            }
        },
        series: [
            {
                name: '笔均支付次数',
                type: 'line',
                data: data.avgValues,
                lineStyle: {
                    color: '#800080' // 紫色
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
    return renderChart('averagePaymentChart',option)
}

function ThousandLoanCostChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['还款总手续费金额(K)', '千元还款成本']
        },
        xAxis: {
            type: 'category',
            data: data.dates,
            axisLabel: {
                rotate: 45,
                formatter: function(value) {
                    return value.substring(5);
                }
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '总成本(K)',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '千元成本',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            }
        ],
        series: [
            {
                name: '还款总手续费金额(K)',
                type: 'bar',
                data: data.feeAmounts,
                barWidth: '40%',
                itemStyle: {
                    color: '#FF8C00' 
                }
            },
            {
                name: '千元还款成本',
                type: 'line',
                yAxisIndex: 1,
                data: data.thousandCosts,
                lineStyle: {
                    color: '#4169E1'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
   return renderChart('paymentCostChart',option)
}
JS;
$this->registerJs($js);
?>



