<?php

use payment\models\paystats\RPCSFailReasonRepaySearch;
use payment\models\ReportPaymentChannelStats;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/* @var $data array */
/* @var $searchModel RPCSFailReasonRepaySearch */

$this->title = '还款失败明细';

?>
    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'id' => 'asset-search-form',
                'type' => ActiveForm::TYPE_INLINE,
            ]) ?>

            <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                    'placeholder' => '开始时间',
                ],
            ]) ?>
            <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                    'placeholder' => '结束时间',
                ],
            ]) ?>


            <?= $form->field($searchModel, 'channel_name', [
                'options' => [
                    'style' => 'min-width: 200px',
                ],
            ])->widget(Select2::class, [
                'data' => ReportPaymentChannelStats::getChannel(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '请选择通道',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'payment_method', [
                'options' => [
                    'style' => 'min-width: 200px',
                ],
            ])->widget(Select2::class, [
                'data' => ReportPaymentChannelStats::getPaymentMethod(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '请付款方式',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'sign_company_name', [
                'options' => [
                    'style' => 'min-width: 200px',
                ],
            ])->widget(Select2::class, [
                'data' => ReportPaymentChannelStats::getSignCompany(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '请选择主体',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'payment_mode', [
                'options' => [
                    'style' => 'min-width: 200px',
                ],
            ])->widget(Select2::class, [
                'data' => ReportPaymentChannelStats::getPaymentMode(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '请选择渠道',
                ],
            ]) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
            <?= Html::a('重置搜索条件', ['repay-fail-reason-detail'], [
                'class' => 'btn btn-default',
            ]) ?>
            <?php
            ActiveForm::end() ?>

        </div>
    </div>

<?= GridView::widget([
    'dataProvider' => new ArrayDataProvider([
        'allModels' => $data,
        'sort' => [
            'attributes'   => ['date1','code','msg','failure_reason','sign_company_name','channel_name','payment_method','payment_option','payment_mode','repay_fail_amount','repay_fail_numbers','repay_fail_numbers_ratio'],
            'defaultOrder' => ['date1' => SORT_DESC],
        ],
    ]),
    'columns' => [
        [
            'label' => '日期',
            'attribute' => 'date1',
        ],
        [
            'label' => '交易类型',
            'value' => function () {
                return '还款';
            },
        ],
        [
            'label' => '失败code',
            'attribute' => 'code'
        ],
        [
            'label' => '失败msg',
            'attribute' => 'msg'
        ],
        [
            'attribute' => 'failure_reason',
            'label' => '失败理由',
        ],
        [
            'label' => '主体',
            'attribute' => 'sign_company_name',
        ],
        [
            'attribute' => 'channel_name',
            'label' => '通道',
        ],
        [
            'attribute' => 'payment_method',
            'label' => '支付方式',
        ],
        [
            'attribute' => 'payment_option',
            'label' => '渠道分类',
        ],
        [
            'attribute' => 'payment_mode',
            'label' => '渠道',
        ],
        [
            'attribute' => 'repay_fail_amount',
            'label' => '失败总金额(K)',
            'value' => function ($v) {
                if (empty($v['repay_fail_amount'])) {
                    return '-';
                }
                return convertFenToKiloYuan($v['repay_fail_amount']);
            }
        ],
        [
            'attribute' => 'repay_fail_numbers',
            'label' => '失败总支付次数',
        ],
        [
            'attribute' => 'repay_fail_numbers_ratio',
            'label' => '失败次数占比',
            'value' => function ($v) {
                return ($v['repay_fail_numbers_ratio'] ?? 0) . '%';
            }
        ],
    ],
]) ?>