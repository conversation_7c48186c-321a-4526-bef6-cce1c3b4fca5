<?php

use common\helpers\TooltipHelper;
use dashboard\grid\EnableHtmlLabelGridView;
use payment\models\Fee;
use payment\models\paystats\RPCSChannelRepaySearch;
use payment\models\ReportPaymentChannelStats;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/* @var $data array */
/* @var $searchModel RPCSChannelRepaySearch */
/* @var  $maxDate string */

$this->title = '分通道还款明细';

?>
<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            条件筛选
        </div>
    </div>
    <div class="box-body">
        <?php
        $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'id' => 'asset-search-form',
                'type' => ActiveForm::TYPE_INLINE,
        ]) ?>

        <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                        'placeholder' => '开始时间',
                ],
        ]) ?>
        <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                        'placeholder' => '结束时间',
                ],
        ]) ?>


        <?= $form->field($searchModel, 'channel_name', [
                'options' => [
                        'style' => 'min-width: 200px',
                ],
        ])->widget(Select2::class, [
                'data' => ReportPaymentChannelStats::getChannel(),
                'pluginOptions' => [
                        'allowClear' => true,
                ],
                'options' => [
                        'prompt' => '请选择通道',
                ],
        ]) ?>

        <?= $form->field($searchModel, 'sign_company_name', [
                'options' => [
                        'style' => 'min-width: 200px',
                ],
        ])->widget(Select2::class, [
                'data' => Fee::getSignCompany(),
                'pluginOptions' => [
                        'allowClear' => true,
                ],
                'options' => [
                        'prompt' => '请选择主体',
                ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['channel-detail'], [
                'class' => 'btn btn-default',
        ]) ?>
        <?php
        ActiveForm::end() ?>

    </div>
</div>
<div class="row">
    <?= EnableHtmlLabelGridView::widget([
            'dataProvider' => new ArrayDataProvider([
                    'allModels' => $data,
                    'sort' => [
                            'attributes' => ['date1', 'channel_name', 'sign_company_name', 'repay_numbers', 'repay_numbers_ratio', 'repay_success_rate', 'repay_amount', 'repay_success_amount_ratio', 'repay_fee_amount', 'thousand_repay_cost', 'repay_fail_amount', 'repay_fail_numbers'],
                            'defaultOrder' => ['date1' => SORT_DESC],
                    ]
            ]),
            'layout' => '<div class="box-header with-border">{summary}</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer"><small class="text-muted">数据更新时间:' . $maxDate . '</small></div><div class="box-footer">{pager}</div>',
            'columns' => [
                    [
                            'label' => TooltipHelper::labelWithTooltip('日期'),
                            'value' => 'date1',
                            'attribute' => 'date1',
                    ],
                    [
                            'label' => TooltipHelper::labelWithTooltip('交易类型'),
                            'value' => function () {
                                return '还款';
                            },
                    ],
                    [
                            'label' => TooltipHelper::labelWithTooltip('主体'),
                            'attribute' => 'channel_name'
                    ],
                    [
                            'label' => TooltipHelper::labelWithTooltip('通道'),
                            'attribute' => 'sign_company_name'
                    ],
                    [
                            'attribute' => 'repay_numbers',
                            'label' => TooltipHelper::labelWithTooltip('支付次数'),
                    ],
                    [
                            'attribute' => 'repay_numbers_ratio',
                            'label' => TooltipHelper::labelWithTooltip('支付次数占比'),
                            'format' => 'raw',
                            'value' => function ($model) {
                                return ($model['repay_numbers_ratio'] ?? 0) . '%';
                            },
                    ],
                    [
                            'attribute' => 'repay_success_rate',
                            'label' => TooltipHelper::labelWithTooltip('支付次数成功率'),
                            'format' => 'raw',
                            'value' => function ($model) {
                                return ($model['repay_success_rate'] ?? 0) . '%';
                            },
                    ],
                    [
                            'attribute' => 'repay_success_numbers',
                            'label' => TooltipHelper::labelWithTooltip('还款支付成功次数'),
                    ],
                    [
                            'attribute' => 'repay_amount',
                            'label' => TooltipHelper::labelWithTooltip('支付总金额(K)'),
                            'value' => function ($model) {
                                if (empty($model['repay_amount'])) {
                                    return '-';
                                }

                                return convertFenToKiloYuan($model['repay_amount']);
                            },
                    ],
                    [
                            'attribute' => 'repay_success_amount_ratio',
                            'label' => TooltipHelper::labelWithTooltip('成功金额占比'),
                            'format' => 'raw',
                            'value' => function ($model) {
                                return ($model['repay_success_amount_ratio'] ?? 0) . '%';
                            },
                    ],
                    [
                            'attribute' => 'repay_fee_amount',
                            'label' => TooltipHelper::labelWithTooltip('总计手续费金额(K)'),
                            'value' => function ($model) {
                                if (empty($model['repay_fee_amount'])) {
                                    return '-';
                                }
                                return convertFenToKiloYuan($model['repay_fee_amount']);
                            },
                    ],
                    [
                            'attribute' => 'thousand_repay_cost',
                            'label' => TooltipHelper::labelWithTooltip('千元还款成本'),
                    ],
                    [
                            'attribute' => 'repay_fail_amount',
                            'label' => TooltipHelper::labelWithTooltip('失败总金额(K)'),
                            'value' => function ($model) {
                                if (empty($model['repay_fail_amount'])) {
                                    return '-';
                                }
                                return convertFenToKiloYuan($model['repay_fail_amount']);
                            },
                    ],
                    [
                            'attribute' => 'repay_fail_numbers',
                            'label' => TooltipHelper::labelWithTooltip('失败总次数'),
                    ],
            ],
    ]) ?>
</div>
