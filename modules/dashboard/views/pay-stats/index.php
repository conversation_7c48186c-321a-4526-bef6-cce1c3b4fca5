<?php

use dashboard\assets\DashboardAsset;

/** @var array $params */
/** @var string $viewPath */
/** @var array $hourlyLoanData */
/** @var array $dailyLoanData */
/** @var array $averagePaymentData */
/** @var array $paymentCostData */
/** @var \yii\web\View $this */
DashboardAsset::register($this);

/**
 * 使所有ECharts图表响应式适应窗口大小
 *
 * @param array $chartInstances 图表实例对象数组
 * @return string 返回使图表响应式的JavaScript代码
 */
function makeChartsResponsive($chartInstances) {
    // 生成图表实例数组的JavaScript代码
    $chartInstancesJson = json_encode($chartInstances);

    // 生成响应式处理的JavaScript代码
    $js = <<<JS
    // 窗口大小变化时调整所有图表大小
    window.addEventListener('resize', function() {
        var chartInstances = {$chartInstancesJson};
        for (var i = 0; i < chartInstances.length; i++) {
            var chart = chartInstances[i];
            if (chart && typeof chart === 'object') {
                var chartDom = document.getElementById(chart.id);
                if (chartDom && window[chart.instance]) {
                    window[chart.instance].resize();
                }
            }
        }
    });
JS;

    return $js;
}
?>

<div class="notification-area">
    <div class="alert alert-warning" id="notificationAlert">
        <a href="https://docs.google.com/document/d/1Nk366L6l9r63USEO9eSQcpYw6XvD7zgae4OlFUPyVpE/edit?tab=t.0" target="_blank" style="display:block; text-decoration:none; color:inherit;">
            <div class="notification-content" id="notificationContent">
                <h4><i class="info-icon-small">i</i> 支付监控看板说明</h4>
                <p>点击此区域可以查看详细文档</p>
            </div>
        </a>
    </div>
</div>

<div class="dashboard-container">
    <?php
    echo $this->render($viewPath, [
        'params' => $params
    ]);

    $this->registerJs(<<<JS
function resizeChart(charts) {
    console.log(charts)
  $(window).resize(function() {
        // 调整所有图表大小
        Object.keys(charts).forEach(function(key) {
            if (charts[key]) {
                charts[key].resize();
            }
        });
    });
}
JS
    );
    ?>
</div>

<style>
    /* 通知区域悬停效果 */
    #notificationAlert a:hover {
        cursor: pointer;
    }

    #notificationAlert a:hover .notification-content {
        background-color: #f5e7c9;
        transition: background-color 0.3s ease;
    }

    .info-icon-small {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background-color: #ff6600;
        color: white;
        border-radius: 50%;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        margin-right: 5px;
    }

    .page-description-link {
        position: fixed;
        top: 80px;
        right: 30px;
        z-index: 1000;
    }

    .page-description-link a.info-button {
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        position: relative;
    }

    .info-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: #ff6600;
        color: white;
        border-radius: 50%;
        font-style: normal;
        font-weight: bold;
        font-size: 18px;
        box-shadow: 0 3px 8px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 102, 0, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 102, 0, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 102, 0, 0);
        }
    }

    .info-text {
        position: absolute;
        right: 45px;
        top: 5px;
        background-color: #fff;
        color: #333;
        padding: 8px 15px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .info-button:hover .info-icon {
        background-color: #e55c00;
        transform: scale(1.2);
    }

    .info-button:hover .info-text {
        opacity: 1;
        visibility: visible;
        right: 50px;
    }

    .dashboard-container {
        margin-bottom: 20px;
    }

    .panel-heading {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .panel-title {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
    }

    .period-selector {
        display: flex;
        align-items: center;
    }

    .period-selector span {
        margin-right: 10px;
    }

    .period-selector select {
        width: 150px;
        margin-right: 10px;
    }

    .chart-container {
        margin-top: 20px;
        border: 1px solid #ddd;
        padding: 15px;
        border-radius: 4px;
    }

    .chart-title {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 14px;
        font-weight: bold;
        text-align: center;
    }

    .date-selector {
        margin-bottom: 10px;
    }

    .data-update-time {
        margin-top: 20px;
        text-align: right;
        font-size: 12px;
        color: #666;
        margin-right: 15px;
    }

    .text-success {
        color: #5cb85c;
    }

    .text-danger {
        color: #d9534f;
    }

    .download {
        white-space: nowrap;
    }

    /* 右侧黄色提示区域样式 */
    .alert-warning {
        background-color: #fcf8e3;
        border-color: #faebcc;
        color: #8a6d3b;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .alert-warning h4 {
        margin-top: 0;
        font-weight: bold;
    }

    .alert-warning ul {
        margin-top: 10px;
        padding-left: 20px;
    }

    html,
    body {
        height: 100%;
    }

    .wrap {
        min-height: 100%;
        height: auto;
        margin: 0 auto -60px;
        padding: 0 0 60px;
    }

    .wrap > .container {
        padding: 70px 15px 20px;
    }

    .footer {
        height: 60px;
        background-color: #f5f5f5;
        border-top: 1px solid #ddd;
        padding-top: 20px;
    }

    .jumbotron {
        text-align: center;
        background-color: transparent;
    }

    .jumbotron .btn {
        font-size: 21px;
        padding: 14px 24px;
    }

    .not-set {
        color: #c55;
        font-style: italic;
    }

    /* 表格样式优化 */
    .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: #f9f9f9;
    }

    .table-bordered {
        border: 1px solid #ddd;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > tbody > tr > th,
    .table-bordered > tfoot > tr > th,
    .table-bordered > thead > tr > td,
    .table-bordered > tbody > tr > td,
    .table-bordered > tfoot > tr > td {
        border: 1px solid #ddd;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > thead > tr > td {
        border-bottom-width: 2px;
    }

    /* 图表容器样式 */
    .chart-container {
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
    }

    /* 面板样式优化 */
    .panel {
        margin-bottom: 20px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
    }

    .panel-default > .panel-heading {
        color: #333;
        background-color: #f5f5f5;
        border-color: #ddd;
    }


    /* 按钮样式 */
    .btn-primary {
        color: #fff;
        background-color: #337ab7;
        border-color: #2e6da4;
    }

    .btn-primary:hover {
        color: #fff;
        background-color: #286090;
        border-color: #204d74;
    }

    /* 导航栏样式 */
    .navbar-inverse {
        background-color: #222;
        border-color: #080808;
    }

    .navbar-inverse .navbar-brand {
        color: #9d9d9d;
    }

    .navbar-inverse .navbar-brand:hover,
    .navbar-inverse .navbar-brand:focus {
        color: #fff;
        background-color: transparent;
    }

    .navbar-inverse .navbar-nav > li > a {
        color: #9d9d9d;
    }

    .navbar-inverse .navbar-nav > li > a:hover,
    .navbar-inverse .navbar-nav > li > a:focus {
        color: #fff;
        background-color: transparent;
    }

    .navbar-inverse .navbar-nav > .active > a,
    .navbar-inverse .navbar-nav > .active > a:hover,
    .navbar-inverse .navbar-nav > .active > a:focus {
        color: #fff;
        background-color: #080808;
    }

    /* 下拉选择框样式 */
    .form-control {
        display: block;
        width: 100%;
        height: 34px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857143;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
        transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    }

    .form-control:focus {
        border-color: #66afe9;
        outline: 0;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    }

    .help-tip {
        position: relative;
        display: inline-block;
        margin-left: 2px;
        width: 12px;
        height: 12px;
        background-color: #3c8dbc;
        color: white;
        border-radius: 50%;
        font-size: 8px;
        text-align: center;
        line-height: 12px;
        cursor: help;
        vertical-align: top;
        margin-top: 1px;
    }

    .help-tip:hover {
        background-color: #2c6aa0;
        transform: scale(1.1);
    }

    /* 确保表格头部有足够的空间显示提示框 */
    .table thead th {
        position: relative;
        overflow: visible;
    }

    .help-tip:hover::after {
        content: attr(data-tip);
        position: absolute;
        bottom: 150%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 1.4;
        white-space: nowrap;
        max-width: 300px;
        word-wrap: break-word;
        min-height: 200px;
        z-index: 9999;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        pointer-events: none;
        opacity: 1;
        visibility: visible;
    }

    .help-tip:hover::before {
        content: "";
        position: absolute;
        top: -5px;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #333 transparent transparent transparent;
        z-index: 9999;
        pointer-events: none;
    }

</style>
