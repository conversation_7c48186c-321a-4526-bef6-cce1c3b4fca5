<?php

use payment\models\paystats\RPCSAllGrantSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/** @var array $params */
/** @var RPCSAllGrantSearch $searchModel */
$this->title = '放款失败分布';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];

$dataProvider = new ArrayDataProvider([
    'allModels' => $params['table'] ?? [],
    'pagination' => false,
]);

/** @var array $params */
?>
<!-- 整体放款数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">放款失败分布</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-md-12">
                <div class="search-form">
                    <?php $form = ActiveForm::begin([
                        'action' => ['fail-reason'],
                        'method' => 'get',
                        'type' => ActiveForm::TYPE_INLINE,
                        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>

                    <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '开始时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '结束时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                        'class' => 'btn btn-primary',
                        'style' => 'margin-right: 10px;'
                    ]) ?>

                    <?= Html::a(Yii::t('grant', 'Reset'), ['fail-reason'], ['class' => 'btn btn-default']) ?>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="chart-container">
                    <h4 class="chart-title">按各通道分布</h4>
                    <div id="failChannelChart" style="height: 300px;"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h4 class="chart-title">按code类型分布</h4>
                    <div id="failCodeChart" style="height: 300px;"></div>
                </div>
            </div>
            <!--            <div class="col-md-3">-->
            <!--                <div class="chart-container">-->
            <!--                    <h4 class="chart-title">按支付方式</h4>-->
            <!--                    <div id="payMethodChart" style="height: 300px;"></div>-->
            <!--                </div>-->
            <!--            </div>-->
            <div class="col-md-4">
                <div class="chart-container">
                    <h4 class="chart-title">按具体渠道</h4>
                    <div id="payModeChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="data-update-time">
            数据更新时间: <?= $params['maxDate'] ?>
        </div>


        <div class="row">
            <div class="col-md-12">
                <?= GridView::widget([
                    'dataProvider' => $dataProvider,
                    'tableOptions' => ['class' => 'table table-bordered table-striped'],
                    'layout' => '<div class="box-header with-border">TOP10放款失败原因</div>
<div class="box-body table-responsive no-padding">{items}</div>',
                    'columns' => [
                        [

                            'label' => '失败code',
                            'attribute' => 'code',

                        ],
                        [
                            'label' => '失败msg',
                            'attribute' => 'msg',
                        ],
                        [
                            'label' => '主体',
                            'attribute' => 'sign_company_name',
                        ],
                        [
                            'attribute' => 'channel_name',
                            'label' => '通道',
                        ],
                        [
                            'attribute' => 'payment_method',
                            'label' => '支付方式',
                        ],
                        [
                            'attribute' => 'payment_option',
                            'label' => '渠道分类',
                        ],
                        [
                            'attribute' => 'payment_mode',
                            'label' => '渠道',
                        ],
                        [
                            'attribute' => 'failure_reason',
                            'label' => '失败理由',
                        ],
                        [
                            'attribute' => 'pay_fail_amount',
                            'label' => '失败总金额(K)',
                            'value' => function ($v) {
                                if (empty($v['pay_fail_amount'])) {
                                    return '-';
                                }
                                return convertFenToKiloYuan($v['pay_fail_amount']);
                            }
                        ],
                        [
                            'attribute' => 'pay_fail_numbers',
                            'label' => '失败总支付次数',
                        ],
                        [
                            'attribute' => 'pay_fail_amount_ratio',
                            'label' => '失败金额占比',
                            'value' => function ($v) {
                                return ($v['pay_fail_amount_ratio'] ?? 0) . '%';
                            }
                        ],
                        [
                            'attribute' => 'pay_fail_numbers_ratio',
                            'label' => '失败次数占比',
                            'value' => function ($v) {
                                return ($v['pay_fail_numbers_ratio'] ?? 0) . '%';
                            },
                        ],
                    ],
                ]);
                ?>
            </div>
        </div>
        <div class="data-update-time">
            数据更新时间: <?= $params['maxDate'] ?>
        </div>
    </div>
</div>
<?php
$chartDataJson = json_encode($params['chart'] ?? []);

$js = <<<JS
$(document).ready(function() {
    let chartData = {$chartDataJson};
    
    const charts = {
        failChannelChart:  generatePieChart('failChannelChart', '按各通道分布', chartData.failChannel, 'channel_name'),
        failCodeChart: generatePieChart('failCodeChart', '按code类型分布', chartData.failCode, 'failure_reason'),
        // payMethodChart :generatePieChart('payMethodChart', '按支付方式', chartData.payMethod, 'payment_method'),
        payModeChart: generatePieChart('payModeChart', '按具体渠道', chartData.payMode, 'payment_mode'),
    }
    
    resizeChart(charts)
});


function generatePieChart(domId, title, data, key) {
    if (!data || data.length === 0) return;
    
    let legendData = data.map(item => item[key]);
    let seriesData = data.map(item => ({ name: item[key], value: item.pay_fail_numbers }));
    
    let option = {
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
        },
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0,
            data: legendData
        },
        series: [
            {
                name: '失败次数',
                type: 'pie',
                radius: '50%',
                data: seriesData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    
    let chartDom = document.getElementById(domId);
    let chart = echarts.init(chartDom);
        chart.setOption(option);
    return chart;
}

JS;
$this->registerJs($js);
?>



