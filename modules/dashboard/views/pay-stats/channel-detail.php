<?php

use payment\models\Fee;
use payment\models\paystats\RPCSChannelGrantSearch;
use payment\models\ReportPaymentChannelStats;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/* @var $data array */
/* @var $searchModel RPCSChannelGrantSearch */
/* @var  $maxDate string */

$this->title = '分通道放款明细';

?>
<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            条件筛选
        </div>
    </div>
    <div class="box-body">
        <?php
        $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'id' => 'asset-search-form',
                'type' => ActiveForm::TYPE_INLINE,
        ]) ?>

        <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                        'placeholder' => '开始时间',
                ],
        ]) ?>
        <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                        'placeholder' => '结束时间',
                ],
        ]) ?>


        <?= $form->field($searchModel, 'channel_name', [
                'options' => [
                        'style' => 'min-width: 200px',
                ],
        ])->widget(Select2::class, [
                'data' => ReportPaymentChannelStats::getChannel(),
                'pluginOptions' => [
                        'allowClear' => true,
                ],
                'options' => [
                        'prompt' => '请选择通道',
                ],
        ]) ?>

        <?= $form->field($searchModel, 'sign_company_name', [
                'options' => [
                        'style' => 'min-width: 200px',
                ],
        ])->widget(Select2::class, [
                'data' => Fee::getSignCompany(),
                'pluginOptions' => [
                        'allowClear' => true,
                ],
                'options' => [
                        'prompt' => '请选择主体',
                ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['channel-detail'], [
                'class' => 'btn btn-default',
        ]) ?>
        <?php
        ActiveForm::end() ?>

    </div>
</div>
<div class="row">
    <?= GridView::widget([
            'dataProvider' => new ArrayDataProvider([
                    'allModels' => $data,
                    'sort' => [
                            'attributes' => ['date1', 'channel_name', 'sign_company_name', 'pay_success_numbers','pay_numbers', 'pay_numbers_ratio', 'pay_success_rate', 'pay_amount', 'pay_success_amount_ratio', 'pay_fee_amount', 'thousand_loan_cost', 'pay_fail_amount', 'pay_fail_numbers'],
                            'defaultOrder' => ['date1' => SORT_DESC],
                    ]
            ]),
            'layout' => '<div class="box-header with-border">{summary}</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer"><small class="text-muted">数据更新时间:' . $maxDate . '</small></div><div class="box-footer">{pager}</div>',
            'columns' => [
                    [
                            'label' => '日期',
                            'value' => 'date1',
                            'attribute' => 'date1',
                    ],
                    [
                            'label' => '交易类型',
                            'value' => function () {
                                return '放款';
                            },
                    ],
                    [
                            'label' => '通道',
                            'attribute' => 'channel_name'
                    ],
                    [
                            'label' => '主体',
                            'attribute' => 'sign_company_name'
                    ],
                    [
                            'attribute' => 'pay_numbers',
                            'label' => '支付次数',
                    ],
                    [
                            'attribute' => 'pay_numbers_ratio',
                            'label' => '支付次数占比',
                            'format' => 'raw',
                            'value' => function ($model) {
                                return ($model['pay_numbers_ratio'] ?? 0) . '%';
                            },
                    ],
                    [
                            'attribute' => 'pay_success_rate',
                            'label' => '支付次数成功率',
                            'format' => 'raw',
                            'value' => function ($model) {
                                return ($model['pay_success_rate'] ?? 0) . '%';
                            },
                    ],
                    [
                            'attribute' => 'pay_success_numbers',
                            'label' => '放款支付成功次数',
                    ],
                    [
                            'attribute' => 'pay_amount',
                            'label' => '支付总金额(K)',
                            'value' => function ($model) {
                                if (empty($model['pay_amount'])) {
                                    return '-';
                                }

                                return convertFenToKiloYuan($model['pay_amount']);
                            },
                    ],
                    [
                            'attribute' => 'pay_success_amount_ratio',
                            'label' => '成功金额占比',
                            'format' => 'raw',
                            'value' => function ($model) {
                                return ($model['pay_success_amount_ratio'] ?? 0) . '%';
                            },
                    ],
                    [
                            'attribute' => 'pay_fee_amount',
                            'label' => '总计手续费金额(K)',
                            'value' => function ($model) {
                                if (empty($model['pay_fee_amount'])) {
                                    return '-';
                                }
                                return convertFenToKiloYuan($model['pay_fee_amount']);
                            },
                    ],
                    [
                            'attribute' => 'thousand_loan_cost',
                            'label' => '千元放款成本',
                    ],
                    [
                            'attribute' => 'pay_fail_amount',
                            'label' => '失败总金额(K)',
                            'value' => function ($model) {
                                if (empty($model['pay_fail_amount'])) {
                                    return '-';
                                }
                                return convertFenToKiloYuan($model['pay_fail_amount']);
                            },
                    ],
                    [
                            'attribute' => 'pay_fail_numbers',
                            'label' => '失败总次数',
                    ],
            ],
    ]) ?>
</div>
