<?php

use Carbon\Carbon;
use payment\models\paystats\RPCSAllGrantSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/** @var array $params */
/** @var RPCSAllGrantSearch $searchModel */
$this->title = '整体放款';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$comparisonData = $params['comparisonData'];

$dataProvider = new ArrayDataProvider([
    'allModels' => $comparisonData['tables']['data'] ?? [],
    'pagination' => false,
]);
$date = Carbon::now()->toDateTimeString();
function formatComparison($value, $isPercentage = true): string
{
    if ($value === null) {
        return '-';
    }
    $sign = $value >= 0 ? '+' : '';
    return $sign . $value . ($isPercentage ? '%' : '');
}

function formatNumber($value, $decimals = 2): string
{
    if ($value === null) {
        return '-';
    }
    return number_format($value, $decimals);
}

/** @var array $params */
?>
<!-- 整体放款数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">整体放款</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="box-body"
            ">
            <?php $form = ActiveForm::begin([
                'action' => ['all'],
                'method' => 'get',
                'type' => ActiveForm::TYPE_INLINE,
                'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
            ]); ?>

            <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                'options' => [
                    'placeholder' => '开始时间',
                    'autocomplete' => 'off',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                'options' => [
                    'placeholder' => '结束时间',
                    'autocomplete' => 'off',
                ],
            ]) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                'class' => 'btn btn-primary',
                'style' => 'margin-right: 10px;'
            ]) ?>

            <?= Html::a(Yii::t('grant', 'Reset'), ['all'], ['class' => 'btn btn-default']) ?>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <?= GridView::widget([
                'dataProvider' => $dataProvider,
                'tableOptions' => ['class' => 'table table-bordered table-striped'],
                'layout' => '<div class="box-header with-border">{summary}</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer pull-right"><small class="text-muted">数据更新时间:' . $comparisonData['tables']['maxDate'] ?? $date . '</small></div>',
                'columns' => [
                    [
                        'attribute' => 'type',
                        'label' => '',
                    ],
                    [
                        'attribute' => 'period',
                        'label' => '时间周期',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                [$startDate, $endDate] = explode(' ~ ', $model['period']);
                                return Html::a(
                                    $model['period'],
                                    ['all-detail', 'startDate' => $startDate, 'endDate' => $endDate],
                                    ['target' => '_blank',]
                                );
                            } else {
                                return $model['period'];
                            }
                        },
                    ],
                    [
                        'attribute' => 'import_counts',
                        'label' => '总计进件资产笔数',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['import_counts'], 0);
                            } else {
                                $value = $model['import_counts'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'import_success_rate',
                        'label' => '进件笔数放款成功率',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['import_success_rate']) . '%';
                            } else {
                                $value = $model['import_success_rate'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'pay_numbers',
                        'label' => '总计支付次数',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['pay_numbers'], 0);
                            } else {
                                $value = $model['pay_numbers'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'pay_success_rate',
                        'label' => '支付次数放款成功率',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['pay_success_rate']) . '%';
                            } else {
                                $value = $model['pay_success_rate'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'avg_pay_numbers_per_import',
                        'label' => '笔均支付次数',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['avg_pay_numbers_per_import']);
                            } else {
                                $value = $model['avg_pay_numbers_per_import'];
                                $class = isset($value) && $value <= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'import_amount',
                        'label' => '进件资产总金额(K)',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return convertFenToKiloYuan($model['import_amount']);
                            } else {
                                $value = $model['import_amount'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'import_success_amount',
                        'label' => '放款成功资产总金额(K)',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return convertFenToKiloYuan($model['import_success_amount']);
                            } else {
                                $value = $model['import_success_amount'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'pay_success_amount_ratio',
                        'label' => '成功金额占比',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['pay_success_amount_ratio']) . '%';
                            } else {
                                $value = $model['pay_success_amount_ratio'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'pay_fee_amount',
                        'label' => '总计手续费金额(K)',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return convertFenToKiloYuan($model['pay_fee_amount']);
                            } else {
                                $value = $model['pay_fee_amount'];
                                $class = isset($value) && $value >= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'thousand_loan_cost',
                        'label' => '千元放款成本',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['thousand_loan_cost']);
                            } else {
                                $value = $model['thousand_loan_cost'];
                                $class = isset($value) && $value <= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'import_fail_counts',
                        'label' => '失败资产总笔数',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return formatNumber($model['import_fail_counts'],0);
                            } else {
                                $value = $model['import_fail_counts'];
                                $class = isset($value) && $value <= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                    [
                        'attribute' => 'import_fail_amount',
                        'label' => '失败资产总金额(K)',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if ($model['type'] === '数值') {
                                return convertFenToKiloYuan($model['import_fail_amount']);
                            } else {
                                $value = $model['import_fail_amount'];
                                $class = isset($value) && $value <= 0 ? 'text-success' : 'text-danger';
                                return Html::tag('span', formatComparison($value), ['class' => $class]);
                            }
                        },
                    ],
                ],
            ]);
            ?>
        </div>
    </div>
    <!-- 每日放款和还款图表 -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h4 class="chart-title">支付放款笔数和成功率走势</h4>
                <div id="dailyLoanChart" style="height: 300px;"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h4 class="chart-title">支付放款次数和成功率走势</h4>
                <div id="dailyRepaymentChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>

    <!-- 进件放款和支付成本图表 -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h4 class="chart-title">进件放款均实付次数</h4>
                <div id="averagePaymentChart" style="height: 300px;"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h4 class="chart-title">支付成本趋势</h4>
                <div id="paymentCostChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    <div class="data-update-time">
        数据更新时间: <?= $comparisonData['chart']['maxDate'] ?? $date; ?>
    </div>
</div>
</div>
<?php
$chartDataJson = json_encode($comparisonData['chart'] ?? []);
$js = <<<JS
$(document).ready(function() {
    let chartData = {$chartDataJson};
    let charts = {} ;
    // 支付放款次数和成功率走势图
    if (chartData.importCounts && chartData.importCounts.dates) {
      charts.importCounts = ImportCountsChart(chartData.importCounts);
    }

    // 支付放款笔数和成功率走势图
    if (chartData.payNumbers && chartData.payNumbers.dates) {
       charts.payNumbers =  PayNumbersChart(chartData.payNumbers);
    }

    // 进件放款笔均支付次数图
    if (chartData.avgPayNumbers && chartData.avgPayNumbers.dates) {
       charts.avgPayNumbers =  AvgPayNumbersChart(chartData.avgPayNumbers);
    }
    // 支付成本趋势图
    if (chartData.thousandLoanCost && chartData.thousandLoanCost.dates) {
        charts.thousandLoanCost =  ThousandLoanCostChart(chartData.thousandLoanCost);
    }
    resizeChart(charts)
});

function renderChart(dom,option){
    let chartDom = document.getElementById(dom), chart = echarts.init(chartDom);
    chart.setOption(option)
    return chart;
}


// 支付放款次数和成功率走势图
function ImportCountsChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['放款笔数', '成功率']
        },
        xAxis: [
            {
                type: 'category',
                data: data.dates,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    rotate: 45
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '笔数',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '成功率',
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: '{value}%'
                }
            }
        ],
        series: [
            {
                name: '放款笔数',
                type: 'bar',
                data: data.counts,
                barWidth: '40%',
                itemStyle: {
                    color: '#1E90FF'
                }
            },
            {
                name: '成功率',
                type: 'line',
                yAxisIndex: 1,
                data: data.rates,
                lineStyle: {
                    color: '#32CD32'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };

   return  renderChart('dailyLoanChart',option)
}

// 支付放款笔数和成功率走势图
function PayNumbersChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['放款次数', '成功率']
        },
        xAxis: [
            {
                type: 'category',
                data: data.dates,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    rotate: 45
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '次',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '成功率',
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: '{value}%'
                }
            }
        ],
        series: [
            {
                name: '放款次数',
                type: 'bar',
                data: data.numbers,
                barWidth: '40%',
                itemStyle: {
                    color: '#1E90FF'
                }
            },
            {
                name: '成功率',
                type: 'line',
                yAxisIndex: 1,
                data: data.rates,
                lineStyle: {
                    color: '#32CD32'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
    return  renderChart('dailyRepaymentChart',option)
}

// 进件放款笔均支付次数图
function AvgPayNumbersChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['笔均支付次数']
        },
        xAxis: {
            type: 'category',
            data: data.dates,
            axisLabel: {
                rotate: 45,
                formatter: function(value) {
                    return value.substring(5); // 只显示月-日
                }
            }
        },
        yAxis: {
            type: 'value',
            min: 0.0,
            axisLabel: {
                formatter: '{value}'
            }
        },
        series: [
            {
                name: '笔均支付次数',
                type: 'line',
                data: data.avgNumbers,
                lineStyle: {
                    color: '#800080' // 紫色
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
   return renderChart('averagePaymentChart',option)
}

// 支付成本趋势图
function ThousandLoanCostChart(data) {
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            data: ['放款总手续费金额(K)', '千元放款成本']
        },
        xAxis: {
            type: 'category',
            data: data.dates,
            axisLabel: {
                rotate: 45,
                formatter: function(value) {
                    return value.substring(5);
                }
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '总成本(K)',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '千元成本',
                min: 0,
                axisLabel: {
                    formatter: '{value}'
                }
            }
        ],
        series: [
            {
                name: '放款总手续费金额(K)',
                type: 'bar',
                data: data.feeAmounts,
                barWidth: '40%',
                itemStyle: {
                    color: '#FF8C00'
                }
            },
            {
                name: '千元放款成本',
                type: 'line',
                yAxisIndex: 1,
                data: data.thousandCosts,
                lineStyle: {
                    color: '#4169E1'
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };
    return renderChart('paymentCostChart',option)
}
JS;
$this->registerJs($js);
?>



