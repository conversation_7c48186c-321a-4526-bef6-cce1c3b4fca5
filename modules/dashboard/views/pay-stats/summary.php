<?php

use payment\models\paystats\SummarizeByMonthSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;

/** @var array $params */
/** @var SummarizeByMonthSearch $searchModel */
/** @var array $leftTable */
/** @var array $rightTable */
/** @var array $leftChart */
/** @var array $rightChart */
$this->title = '汇总分月看板';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$leftTable = $params['leftTable'] ?? [];
$rightTable = $params['rightTable'] ?? [];
$leftChart = $params['leftChart'] ?? [];
$rightChart = $params['rightChart'] ?? [];
?>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">汇总分月看板</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="box-body">
                <?php
                $form = ActiveForm::begin([
                        'action' => ['summarize-by-month'],
                        'method' => 'get',
                        'type' => ActiveForm::TYPE_INLINE,
                        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                ]); ?>

                <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                        'options' => [
                                'placeholder' => '开始时间',
                                'autocomplete' => 'off',
                        ],
                        'pluginOptions' => [
                                'todayBtn' => 'linked',
                                'format' => 'yyyy-mm',
                                'todayHighlight' => true,
                                'autoclose' => true,
                                'minViewMode' => 1
                        ],
                ]) ?>

                <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                        'options' => [
                                'placeholder' => '结束时间',
                                'autocomplete' => 'off',
                        ],
                        'pluginOptions' => [
                                'todayBtn' => 'linked',
                                'format' => 'yyyy-mm',
                                'todayHighlight' => true,
                                'autoclose' => true,
                                'minViewMode' => 1
                        ],
                ]) ?>

                <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                        'class' => 'btn btn-primary',
                        'style' => 'margin-right: 10px;'
                ]) ?>

                <?= Html::a(Yii::t('grant', 'Reset'), ['summarize-by-month'], ['class' => 'btn btn-default']) ?>

                <?php ActiveForm::end(); ?>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <div id="chart-left" style="height: 300px;"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <div id="chart-right" style="height: 300px;"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= GridView::widget([
                    'dataProvider' => new ArrayDataProvider([
                            'allModels' => $leftTable,
                            'pagination' => false,
                    ]),
                    'tableOptions' => ['class' => 'table table-bordered table-striped'],
                    'columns' => [
                            [
                                    'attribute' => 'month',
                                    'label' => '月份',
                            ],
                            [
                                    'attribute' => 'import_success_counts',
                                    'label' => '放款成功笔数',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return number_format($model['import_success_counts'] ?? 0);
                                    },
                            ],
                            [
                                    'attribute' => 'loan_success_rate',
                                    'label' => '放款笔数成功率',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return ($model['loan_success_rate'] ?? 0) . '%';
                                    },
                            ],
                            [
                                    'attribute' => 'pay_success_amount',
                                    'label' => '放款成功总金额(K)',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return convertFenToKiloYuan($model['pay_success_amount'] ?? 0);
                                    },
                            ],
                            [
                                    'attribute' => 'pay_fee_amount',
                                    'label' => '放款手续费(K)',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return convertFenToKiloYuan($model['pay_fee_amount'] ?? 0);
                                    },
                            ],
                            [
                                    'attribute' => 'thousand_loan_cost',
                                    'label' => '千元放款成本',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return $model['thousand_loan_cost'] ?? '0';
                                    },
                            ],
                    ],
            ]);
            ?>
        </div>
        <div class="col-md-6">
            <?= GridView::widget([
                    'dataProvider' => new ArrayDataProvider([
                            'allModels' => $rightTable,
                            'pagination' => false,
                    ]),
                    'tableOptions' => ['class' => 'table table-bordered table-striped'],
                    'columns' => [
                            [
                                    'attribute' => 'month',
                                    'label' => '月份',
                            ],
                            [
                                    'attribute' => 'repay_success_counts_day',
                                    'label' => '还款成功笔数',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return number_format($model['repay_success_counts_day'] ?? 0);
                                    },
                            ],
                            [
                                    'attribute' => 'repay_success_rate',
                                    'label' => '还款笔数成功率',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return ($model['repay_success_rate'] ?? 0) . '%';
                                    },
                            ],
                            [
                                    'attribute' => 'repay_success_amount',
                                    'label' => '还款成功总金额(K)',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return convertFenToKiloYuan($model['repay_success_amount'] ?? 0, 2);
                                    },
                            ],
                            [
                                    'attribute' => 'repay_fee_amount',
                                    'label' => '还款手续费(K)',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return convertFenToKiloYuan($model['repay_fee_amount'] ?? 0, 2);
                                    },
                            ],
                            [
                                    'attribute' => 'thousand_repay_cost',
                                    'label' => '千元还款成本',
                                    'format' => 'raw',
                                    'value' => static function ($model) {
                                        return $model['thousand_repay_cost'] ?? '0';
                                    },
                            ],
                    ],
            ]);
            ?>
        </div>
    </div>

</div>
<?php
$left = json_encode($leftChart);
$right = json_encode($rightChart);
$js = <<<JS
    $(function () {
    const chartLeft = echarts.init(document.getElementById('chart-left'));
    const chartRight = echarts.init(document.getElementById('chart-right'));
    const left = {$left};
    const right = {$right};
     console.log(left,right);
    // 左图（放款/还款笔数 + 成功率）
    chartLeft.setOption({
        tooltip: {trigger: 'axis'},
        legend: {data: ['放款笔数', '还款笔数', '放款成功率', '还款成功率']},
        xAxis: {type: 'category', data: left.month},
        yAxis: [
            {type: 'value', name: '笔数'},
            {type: 'value', name: '成功率'}
        ],
        series: [
            {type: 'bar', name: '放款笔数', data: left.import_success_counts},
            {type: 'bar', name: '还款笔数', data: left.repay_success_counts_day},
            {type: 'line', name: '放款成功率', yAxisIndex: 1, data: left.loan_success_rate},
            {type: 'line', name: '还款成功率', yAxisIndex: 1, data: left.repay_success_rate}
        ]
    });

    // 右图（放款/还款金额 + 千元成本）
    chartRight.setOption({
        tooltip: {trigger: 'axis'},
        legend: {data: ['放款成功总金额(K)', '还款成功总金额(K)', '千元放款成本', '千元还款成本']},
        xAxis: {type: 'category', data: right.month},
        yAxis: [
            {type: 'value', name: '金额(K)'},
            {type: 'value', name: '千元成本'}
        ],
        series: [
            {type: 'bar', name: '放款成功总金额(K)', data: right.pay_success_amount},
            {type: 'bar', name: '还款成功总金额(K)', data: right.repay_success_amount},
            {type: 'line', name: '千元放款成本', yAxisIndex: 1, data: right.thousand_loan_cost},
            {type: 'line', name: '千元还款成本', yAxisIndex: 1, data: right.thousand_repay_cost}
        ]
    });

});
JS;

$this->registerJs($js);
?>
