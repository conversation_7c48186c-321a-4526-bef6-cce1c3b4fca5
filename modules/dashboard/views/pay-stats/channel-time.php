<?php

use payment\models\paystats\RPCSChannelTimeGrantSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use yii\helpers\Html;

/*** @var array $params */
/*** @var RPCSChannelTimeGrantSearch $searchModel */
$this->title = '分通道放款-分时';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
?>
<style>
    .chart-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        justify-content: space-between;
    }

    .chart-box {
        width: 48%;
        height: 400px;
        background: white;
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .date-filter-container {
        background-color: #f9f9f9;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #eaeaea;
    }
    
    .channel-filter-container {
        background-color: #f9f9f9;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #eaeaea;
    }
    
    .channel-filter-container label {
        display: block;
        margin-bottom: 10px;
        font-weight: 600;
        color: #333;
    }
    
    .channel-filter-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .channel-checkbox-item {
        display: inline-flex;
        align-items: center;
        background: white;
        padding: 5px 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .channel-checkbox-item:hover {
        border-color: #1890ff;
        background-color: #f0f7ff;
    }
    
    .channel-checkbox-item input[type="checkbox"] {
        margin-right: 5px;
    }
    
    .data-update-time {
        margin-top: 20px;
        font-size: 12px;
        color: #888;
        text-align: right;
    }
</style>
<!-- 分时放款数据 -->
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">分通道放款-分时</h3>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 15px;">
            <div class="col-md-12">
                <div class="search-form">
                    <?php $form = ActiveForm::begin([
                        'action' => ['channel-time'],
                        'method' => 'get',
                        'type'          => ActiveForm::TYPE_INLINE,
                        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
                    ]); ?>

                    <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '开始时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                        'options' => [
                            'placeholder' => '结束时间',
                            'autocomplete' => 'off',
                        ],
                    ]) ?>

                    <?= Html::submitButton('<i class="fa fa-search"></i> 查询', [
                        'class' => 'btn btn-primary',
                        'style' => 'margin-right: 10px;'
                    ]) ?>

                    <?= Html::a(Yii::t('grant', 'Reset'), ['channel-time'], ['class' => 'btn btn-default']) ?>


                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
        <div class="row channel-filter-container" id="channel-filter-container">
            <label>渠道交易趋势图：</label>
            <div class="channel-filter-wrapper">
            </div>
        </div>
        <!-- 分时放款图表 -->
        <div class="chart-container" id="chartContainer"></div>

        <div class="data-update-time">
            数据更新时间: <?= $params['maxDate'] ?>
        </div>
    </div>
</div>
<script>
    <?php $this->beginBlock('js') ?>

    $hourlyLoanDataJson = <?= json_encode($params['hourlyLoanData']) ?>


    // 解析后端传来的数据
    const hourlyLoanData = $hourlyLoanDataJson;
    const dates = hourlyLoanData.dates || [];
    const hours = hourlyLoanData.hours || [];
    const channels = hourlyLoanData.groups || [];
    const channelData = hourlyLoanData.groupData || {};

    // 动态生成渠道复选框
    function generateChannelFilters() {
        const filterContainer = $('.channel-filter-wrapper').empty();
        
        channels.forEach((channel, index) => {
            let checked = index === 0 ? 'checked' : '';
            filterContainer.append(`<label class="channel-checkbox-item"><input type="checkbox" class="channel-filter" value="${channel}" ${checked}> ${channel}</label>`);
        });
    }

    function generateCharts() {
        $('#chartContainer').empty();
        // 清空图表实例数组
        chartInstances = [];

        let selectedChannels = $(".channel-filter:checked").map(function () {
            return this.value;
        }).get();

        if (dates.length === 0 || hours.length === 0) {
            $('#chartContainer').append('<div class="alert alert-info">暂无数据</div>');
            return;
        }

        selectedChannels.forEach((channel, index) => {
            if (!channelData[channel]) return;

            let chartId1 = `chart_success_${index}`;
            let chartId2 = `chart_amount_${index}`;

            $('#chartContainer').append(`<div id="${chartId1}" class="chart-box"></div>`);
            $('#chartContainer').append(`<div id="${chartId2}" class="chart-box"></div>`);

            let chart1 = echarts.init(document.getElementById(chartId1));
            let chart2 = echarts.init(document.getElementById(chartId2));

            chartInstances.push(chart1);
            chartInstances.push(chart2);

            // 准备图表数据
            const successSeries = [];
            const amountSeries = [];

            dates.forEach((date, dateIndex) => {
                const dateLabel = date;
                const successData = channelData[channel].successData[date] || [];
                const amountData = channelData[channel].amountData[date] || [];

                successSeries.push({
                    name: dateLabel,
                    type: 'line',
                    data: successData
                });

                amountSeries.push({
                    name: dateLabel,
                    type: 'line',
                    data: amountData
                });
            });

            // 设置图表选项
            let option1 = {
                title: {text: `${channel} 支付成功次数走势图`, top: 10, left: 'center'},
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: dates, // 使用实际日期作为图例
                    bottom: 0,
                    padding: [0, 0, 10, 0],
                    type: 'scroll', //
                },
                grid: {left: '3%', right: '4%', bottom: '15%', containLabel: true},
                xAxis: {type: 'category', data: hours},
                yAxis: {type: 'value'},
                series: successSeries
            };

            let option2 = {
                title: {text: `${channel} 支付成功金额走势图 (千元)`, top: 10, left: 'center'},
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + param.value + ' 千元<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: dates, // 使用实际日期作为图例
                    bottom: 0,
                    padding: [0, 0, 10, 0],
                    type: 'scroll', //
                },
                grid: {left: '3%', right: '4%', bottom: '15%', containLabel: true},
                xAxis: {type: 'category', data: hours},
                yAxis: {type: 'value'},
                series: amountSeries
            };

            chart1.setOption(option1);
            chart2.setOption(option2);
        });
    }

    // 存储所有图表实例，用于窗口大小变化时重新渲染
    let chartInstances = [];

    // 窗口大小变化时重新渲染图表
    function resizeCharts() {
        chartInstances.forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }

    $(document).ready(function () {
        if (channels.length > 0) {
            generateChannelFilters();
            generateCharts();
            $(document).on('change', '.channel-filter', function () {
                generateCharts(); // 图表实例数组已在generateCharts函数中重置
            });

            // 添加窗口大小变化的事件监听
            $(window).on('resize', function () {
                if (chartInstances.length > 0) {
                    resizeCharts();
                }
            });
        } else {
            $('#chartContainer').append('<div class="alert alert-info">暂无数据</div>');
        }
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>

