<?php
/** @var array $params */

/** @var RPCSAllGrantSearch $searchModel */

use payment\models\paystats\RPCSAllGrantSearch;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;

$this->title = '放款明细数据';
$this->params['breadcrumbs'][] = $this->title;
$searchModel = $params['searchModel'];
$comparisonData = $params['data'];

function formatNumber($value, $decimals = 2): string
{
    if ($value === null) {
        return '-';
    }
    return number_format($value, $decimals);
}

echo GridView::widget([
    'dataProvider' => new ArrayDataProvider([
        'allModels' => $comparisonData ?? [],
        'pagination' => false,
    ]),
    'tableOptions' => ['class' => 'table table-bordered table-striped'],
    'columns' => [
        [
            'attribute' => 'date1',
            'label' => '日期',
        ],
        [
            'attribute' => 'import_counts',
            'label' => '总计进件资产笔数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['import_counts'], 0);
            },
        ],
        [
            'attribute' => 'import_success_rate',
            'label' => '进件笔数放款成功率',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['import_success_rate']) . '%';
            },
        ],
        [
            'attribute' => 'pay_numbers',
            'label' => '总计支付次数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['pay_numbers'], 0);
            },
        ],
        [
            'attribute' => 'pay_success_rate',
            'label' => '支付次数放款成功率',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['pay_success_rate']) . '%';
            },
        ],
        [
            'attribute' => 'avg_pay_numbers_per_import',
            'label' => '笔均支付次数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['avg_pay_numbers_per_import']);
            },
        ],
        [
            'attribute' => 'import_amount',
            'label' => '进件资产总金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['import_amount']);
            },
        ],
        [
            'attribute' => 'import_success_amount',
            'label' => '放款成功资产总金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['import_success_amount']);
            },
        ],
        [
            'attribute' => 'pay_success_amount_ratio',
            'label' => '成功金额占比',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['pay_success_amount_ratio']) . '%';
            },
        ],
        [
            'attribute' => 'pay_fee_amount',
            'label' => '总计手续费金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['pay_fee_amount']);
            },
        ],
        [
            'attribute' => 'thousand_loan_cost',
            'label' => '千元放款成本',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['thousand_loan_cost']);
            },
        ],
        [
            'attribute' => 'import_fail_counts',
            'label' => '失败资产总笔数',
            'format' => 'raw',
            'value' => function ($model) {
                return formatNumber($model['import_fail_counts'], 0);
            },
        ],
        [
            'attribute' => 'import_fail_amount',
            'label' => '失败资产总金额(K)',
            'format' => 'raw',
            'value' => function ($model) {
                return convertFenToKiloYuan($model['import_fail_amount']);
            },
        ],
    ],
]);