<?php

namespace dashboard\expression;

use dashboard\interfaces\ExpressionInterface;

/**
 *
 */
abstract class AbstractExpression implements ExpressionInterface
{
    /**
     * @var string
     */
    public string $column;

    /**
     * @var mixed
     */
    public $val;
    /**
     * @var string
     */
    public $subCondition = '';
    /**
     * @var array
     */
    protected array $params = [];

    /**
     * @param string $column
     * @param mixed  $val
     * @param mixed  $subCondition
     */
    public function __construct(string $column, $val, $subCondition = '')
    {
        $this->column = $column;
        $this->val = $val;
        $this->subCondition = $subCondition === '' ? 'or' : $subCondition;
    }


    /**
     * @return string[]
     */
    public static function getExpressionWithConditions(): array
    {
        return [
            'like' => LikeExpression::class,
            '=' => EqExpression::class,
            'in' => InExpression::class,
        ];
    }

    /**
     * [:prefix=>val]
     * @return array<string,mixed>
     */
    public function getParams(): array
    {
        return $this->params;
    }

    /**
     * @return string
     */
    abstract public function prefix(): string;

    /**
     * @return string
     */
    public function getPrefix(): string
    {
        return str_replace('_', '', ucwords($this->prefix() . $this->column, '_'));;
    }
}