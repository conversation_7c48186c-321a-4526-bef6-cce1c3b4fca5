<?php

namespace dashboard\expression;

/**
 *
 */
class LikeExpression extends AbstractExpression
{
    protected const PARAMS_PREFIX = ':like_';

    /**
     * @return string
     */
    public function getExpression(): string
    {
        $this->val = array_filter((array)$this->val);
        //NOTE:拼接占位符
        $prefix = $this->getPrefix();
        $conditions = [];
        if (!empty($this->val)) {
            foreach ($this->val as $i => $value) {
                $p = $prefix . $i;
                $this->params[$p] = $this->makeLikeStr($value);
                $conditions[] = sprintf(' ( `%s` like %s ) %s', $this->column, $p, $this->subCondition);
            }
            $sql = rtrim(implode(' ', $conditions), $this->subCondition);
        } else {
            $sql = '(1=1)';
        }

        return "( {$sql} )";
    }

    /**
     * @return string
     */
    public function prefix(): string
    {
        return self::PARAMS_PREFIX;
    }

    /**
     * @param string $str
     * @return string
     */
    protected function makeLikeStr(string $str): string
    {
        return '%' . $str . '%';
    }
}