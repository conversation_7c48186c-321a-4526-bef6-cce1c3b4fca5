<?php

namespace dashboard\expression;

/**
 *
 */
class EqExpression extends AbstractExpression
{
    protected const PARAMS_PREFIX = ':eq_';


    /**
     * @return string
     */
    public function getExpression(): string
    {
        if ($this->val !== '') {
            //NOTE:拼接占位符
            $prefix = $this->getPrefix();
            $this->params = [
                $prefix => $this->val,
            ];

            return sprintf(" (`%s`=%s) ", $this->column, $prefix);
        }

        return " (1=1)";
    }


    /**
     * @return string
     */
    public function prefix(): string
    {
        return self::PARAMS_PREFIX;
    }
}