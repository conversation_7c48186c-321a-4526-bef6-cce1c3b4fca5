<?php

namespace dashboard\expression;

class InExpression extends AbstractExpression
{
    public const PARAMS_PREFIX = ':in_';

    /**
     * @return string
     */
    public function prefix(): string
    {
        return self::PARAMS_PREFIX;
    }

    /**
     * @return string
     */
    public function getExpression(): string
    {
        $this->val = array_filter((array)$this->val);
        if (!empty($this->val)) {
            //NOTE:拼接占位符
            $prefix = $this->getPrefix();
            $prefixes = [];
            foreach ($this->val as $i => $value) {
                $p = $prefix . $i;
                $prefixes[] = $p;
                $this->params[$p] = $value;
            }

            return sprintf(' `%s` in (%s)', $this->column, implode(',', $prefixes));
        }

        return " (1=1)";
    }
}