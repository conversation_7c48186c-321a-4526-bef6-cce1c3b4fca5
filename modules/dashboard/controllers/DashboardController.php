<?php

namespace dashboard\controllers;

use dashboard\models\DashboardChart;
use datasource\models\DataSourceCacheModel;

use Yii;
use yii\caching\TagDependency;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\Response;

class DashboardController extends Controller
{
    public function actionGetData()
    {
        $request = Yii::$app->getRequest();
        $id      = $request->get('id');
        if (!$id) {
            return '{}';
        }

        $refresh = $request->get('refresh', false);

        $cacheKey = DashboardChart::buildKey($id);

        $cache = Yii::$app->getCache();

        if ($refresh || false === ($data = $cache->get($cacheKey))) {
            $chart = DashboardChart::findOne($id);

            $dataSet = $chart->dataSet;

            $data = [];
            foreach ($dataSet as $source) {
                $data[$source->id] = $source->makeData();
            }

            $data = Json::encode([
                'code'       => $chart->tpl_config,
                'data'       => $data,
                'needFormat' => (bool) $chart->tpl_id,
                'sort'       => $chart->getDataSource(),
            ]);

            $cache->set($cacheKey, $data, null, new TagDependency([
                'tags' => array_map(function (DataSourceCacheModel $dataSource) {
                    return $dataSource->getCacheTagKey();
                }, array_merge($dataSet, [
                    $chart,
                ], ($chart->tpl_id ? [$chart->tpl] : []))),
            ]));
        }

        return $data;
    }

    /**
     * @return string
     */
    protected function getSortKey()
    {
        return 'dashboard:sort:' . Yii::$app->getUser()->getId();
    }

    /**
     * 设置排序
     */
    public function actionSort()
    {
        $app     = Yii::$app;
        $request = $app->getRequest();

        $app->getCache()->set($this->getSortKey(), $request->post('data', []));

        return true;
    }

    /**
     * @return string|Response
     */
    public function actionIndex()
    {
        $app     = Yii::$app;
        $user    = $app->getUser();
        $session = $app->getSession();
        $cache   = $app->getCache();

        if ($user->isGuest) {
            return $this->redirect(['/site/login']);
        }

        $session->setFlash('error', '内部系统禁止对外提供截图');

        $order  = ['sort' => SORT_DESC, 'id' => SORT_DESC];
        $charts = DashboardChart::find()->orderBy($order)->all();
        $charts = ArrayHelper::index($charts, null, 'owner_system');

        $sort   = $cache->get($this->getSortKey());
        $sort   = $sort ? (array) $sort : [];
        $models = [];
        foreach ($sort as $k) {
            if (isset($charts[$k])) {
                $models[$k] = $charts[$k];
                unset($charts[$k]);
            }
        }
        $models += $charts;

        return $this->render('index', [
            'models' => $models,
        ]);
    }

}
