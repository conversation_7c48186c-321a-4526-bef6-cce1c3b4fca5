<?php

namespace cmdb\components;

use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class CmdbComponent extends RequestClient
{
    use ComponentTrait;

    public const FROM_SYSTEM = 'cmdb';

    /**
     * @param array $data
     *
     * @return bool
     */
    public function calculateRepayPlan(array $data): bool
    {
        return $this->post('v6/rate/repay/calculate', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'type'        => 'CalculateRepayPlan',
                'key'         => uuid_create(),
                'data'        => $data,
            ],
        ]);
    }

    /**
     * @param array $data
     *
     * @return bool
     */
    public function calculateLateFee(array $data): bool
    {
        return $this->post('v6/late/calculate', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'type'        => 'CalculateLateFee',
                'key'         => uuid_create(),
                'data'        => $data,
            ],
        ]);
    }
}
