<?php

namespace cmdb\behaviors;

use cmdb\models\Product;
use cmdb\models\ProductRate;
use cmdb\models\Rate;
use yii\base\Behavior;
use yii\base\ModelEvent;
use yii\db\ActiveRecord;
use yii\db\AfterSaveEvent;

class VoidBehavior extends Behavior
{
    public function events()
    {
        return [
            ActiveRecord::EVENT_BEFORE_UPDATE => function (ModelEvent $event) {
                /** @var Product|Rate $model */
                $model = $event->sender;

                if ($model->isAttributeChanged('product_status') && $model->isVoid()) {
                    $model->on(ActiveRecord::EVENT_AFTER_UPDATE, [$this, 'voidRelations']);
                }
            },
        ];
    }

    public function voidRelations(AfterSaveEvent $event)
    {
        /** @var Product|Rate $model */
        $model = $event->sender;

        $productRateQuery = $model
            ->getProductRate()
            ->andWhere(['not in', 'product_rate_status', [ProductRate::STATUS_VOID]]);

        /** @var ProductRate $prate */
        foreach ($productRateQuery->each() as $prate) {
            $prate->product_rate_status = ProductRate::STATUS_VOID;
            $prate->update(false, ['product_rate_status']);
        }
    }
}
