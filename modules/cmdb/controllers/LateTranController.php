<?php

namespace cmdb\controllers;

use Carbon\Carbon;
use cmdb\models\LateRule;
use cmdb\models\LateTran;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * LateTranController implements the CRUD actions for LateTran model.
 */
class LateTranController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Creates a new LateRule model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionCreate($id)
    {
        $lateRule = LateRule::findOne($id);
        if (!$lateRule) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        $model = new LateTran();

        $model->late_tran_late_no = $lateRule->late_no;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['late-rule/view', 'id' => $id]);
        } else {
            $model->late_tran_overdue_days_start = 1;
            $model->late_tran_overdue_days_end   = 99999;

            return $this->render('create', [
                'lateRule' => $lateRule,
                'model'    => $model,
            ]);
        }
    }

    /**
     * Updates an existing LateRule model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        $lateRule = $model->late;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['late-rule/view', 'id' => $lateRule->late_id]);
        }

        $model->late_tran_effective_start = Carbon::parse($model->late_tran_effective_start)->toDateString();
        return $this->render('update', [
            'model'    => $model,
            'lateRule' => $lateRule,
        ]);
    }

    /**
     * Deletes an existing LateRule model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws Throwable
     * @throws StaleObjectException
     * @throws NotFoundHttpException
     */
    public function actionDelete($id)
    {
        $model    = $this->findModel($id);
        $lateRule = $model->late;

        $model->delete();

        return $this->redirect(['late-rule/view', 'id' => $lateRule->late_id]);
    }


    public function actionRelease($id)
    {
        $model    = $this->findModel($id);
        $lateRule = $model->late;
        try {
            $model->release();
        } catch (\Exception $exception){
            Yii::$app->session->setFlash('error', $exception->getMessage());
        }

        return $this->redirect(['late-rule/view', 'id' => $lateRule->late_id]);
    }

    /**
     * Finds the LateRule model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return LateTran the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = LateTran::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
