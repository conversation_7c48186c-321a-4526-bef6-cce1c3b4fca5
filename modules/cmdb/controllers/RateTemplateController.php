<?php

namespace cmdb\controllers;

use Carbon\Carbon;
use cmdb\models\RateTemplate;
use cmdb\models\RateTemplateEntity;
use cmdb\models\RateTemplateSearch;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * RateTemplateController implements the CRUD actions for RateTemplate model.
 */
class RateTemplateController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all RateTemplate models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel  = new RateTemplateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new RateTemplate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate()
    {
        $model  = new RateTemplate();
        $entity = new RateTemplateEntity();

        $entity->startDate = Carbon::today()->toDateString();
        $entity->endDate   = '2048-12-31';

        if ($this->request->isPost) {
            $model->load($this->request->post());
            $entity->load($this->request->post());
            if ($entity->validate()) {
                $model->rate_template_config = json_encode($entity->toArray());
                if ($model->save()) {
                    return $this->redirect(['index']);
                }
            }
        }

        return $this->render('create', [
            'model'  => $model,
            'entity' => $entity,
        ]);
    }

    /**
     * Updates an existing RateTemplate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id ID
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model  = $this->findModel($id);
        $entity = new RateTemplateEntity();
        $entity->load((array)json_decode($model->rate_template_config, true), '');

        if ($this->request->isPost) {
            $model->load($this->request->post());
            $entity->load($this->request->post());
            if ($entity->validate()) {
                $model->rate_template_config = json_encode($entity->toArray());
                if ($model->save()) {
                    return $this->redirect(['index']);
                }
            }
        }

        return $this->render('update', [
            'model'  => $model,
            'entity' => $entity,
        ]);
    }

    /**
     * Deletes an existing RateTemplate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id ID
     *
     * @return mixed
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the RateTemplate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id ID
     *
     * @return RateTemplate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = RateTemplate::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
