<?php

namespace cmdb\controllers;

use cmdb\components\CmdbComponent;
use cmdb\models\Fee;
use cmdb\models\Rate;
use cmdb\models\RateTrial;
use cmdb\models\RateVarNum;
use Throwable;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use Yii;
use yii\base\InvalidConfigException;
use yii\data\ArrayDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\Request;
use yii\web\Response;

/**
 * @property Request $request
 */
class TrialController extends Controller
{
    public function actionRunTrial()
    {
        $data = $this->request->get();
        $rateModel = new RateTrial();
        $rateModel->load($data);
        $records = [];
        $options = [];
        $json = [];
        if ($rateModel->validate()) {
            [$records, $options, $json] = $rateModel->trial();
            if (!$rateModel->hasErrors()) {
                Yii::$app->getSession()->set('rate_number', $rateModel->rate_number);
                Yii::$app->getSession()->set('trialData', $records);
                $totalRow = [
                    'period' => '合计',
                    'date' => '-',
                ];
                $columnNames = array_merge(array_keys($options), ['principal', 'interest', 'total']);
                foreach ($columnNames as $cname) {
                    $totalRow[$cname] = array_sum(array_column($records, $cname));
                }
                $records[] = $totalRow;
            }
        }

        if ($rateModel->hasErrors()) {
            $errors = $rateModel->getErrors();
            $session = Yii::$app->getSession();
            foreach ($errors as $error) {
                $session->setFlash('error', implode('<br/>', (array)$error));
            }
        }

        return $this->render('run-trial', [
            'model' => $rateModel,
            'dataProvider' => new ArrayDataProvider([
                'allModels' => $records,
                'pagination' => false,
            ]),
            'columns' => RateTrial::columns($options),
            'json' => $json,
        ]);
    }

    /**
     * @param int $rate_id
     *
     * @return string
     */
    public function actionIndex($rate_id, $var = 0)
    {
        if ($var) {
            /** @var RateVarNum $varModel */
            $varModel = RateVarNum::findOne($rate_id);

            $rateNumbers = [
                $varModel->rate_var_num_source_num => $varModel->rate_var_num_source_num,
            ];

            /** @var RateTrial|Rate $rateModel */
            $rateModel = RateTrial::findOne(['rate_number' => $varModel->rate_var_num_source_num]);

            $rateVarNumQuery = RateVarNum::find()->where(['rate_var_num_source_num' => $rateModel->rate_number]);

            // 设置默认选中费率编号
            $rateModel->rate_number = $varModel->rate_var_num_code;
        } else {
            $rateModel = RateTrial::findOne($rate_id);
            $rateVarNumQuery = RateVarNum::find()->where(['rate_var_num_source_num' => $rateModel->rate_number]);
            $rateNumbers = [
                $rateModel->rate_number => $rateModel->rate_number,
            ];
        }

        $varNum = $rateVarNumQuery->select('rate_var_num_id')->indexBy('rate_var_num_code')->column();
        $varNumCodes = array_keys($varNum);

        $rateNumbers = array_merge($rateNumbers, array_combine($varNumCodes, $varNumCodes));

        if (count($rateModel->periodCategory) === 1) {
            $rateModel->period = current($rateModel->periodCategory);
        }

        return $this->render('index', [
            'model' => $rateModel,
            'rateNumbers' => $rateNumbers,
            'varNum' => $varNum,
        ]);
    }

    public function actionRateVarInfoHtml($rateNumber, $var)
    {
        if ($var) {
            /** @var RateVarNum $model */
            $model = RateVarNum::findOne(['rate_var_num_code' => $rateNumber]);
            $periodTerms = $model->periodTerms();
            $periodAmountPercents = $model->periodAmountPercents();
            $info = Json::decode($model->rate_var_num_fees);
            $rateConfigModel = $model->rate->getRateConfigs();
        } else {
            /** @var Rate $model */
            $model = Rate::findOne(['rate_number' => $rateNumber]);
            $rateConfigModel = $model->getRateConfigs();
            $info = $rateConfigModel
                ->select('rate_config_value')
                ->indexBy('rate_config_fee_code')
                ->column();
        }

        $configValRange = $rateConfigModel
            ->select(['rate_config_value_range'])
            ->indexBy('rate_config_fee_code')
            ->column();

        $configVatRate = $rateConfigModel
            ->select(['rate_config_vat_rate'])
            ->indexBy('rate_config_fee_code')
            ->column();

        $feesList = Fee::find()
            ->where([
                'fee_code' => array_keys($info),
            ])
            ->select('fee_name')
            ->indexBy('fee_code')
            ->column();
        $columns = [];
        foreach ($info as $key => $value) {
            $spanText = sprintf('费用取值范围:%s', $configValRange[$key] ?? '暂无');
            $columns[] = [
                'label' => $feesList[$key],
                'spanText' => $spanText,
                'disabled' => (bool)$var,
                'name' => sprintf('RateTrial[rateVarInfo][%s]', $key),
                'value' => number_format($value, 2, '.', ''),
                'vatRateText' => '增值税比例:' . floatval($configVatRate[$key]) . '%'
            ];
        }

        return $this->asJson([
            'code' => 0,
            'data' => [
                'feeColumns' => $columns,
                'periodTerms' => $periodTerms ?? [],
                'periodAmountPercents' => $periodAmountPercents ?? [],
            ],
        ]);
    }


    /**
     * @param $date
     * @param $late_num
     * @param $period
     * @param $trialAmount
     *
     * @return Response
     * @throws InvalidConfigException
     * @throws Throwable
     */
    public function actionLate($date, $late_num, $period, $trialAmount): Response
    {
        [$count, ,] = explode(',', $period);
        $session = Yii::$app->getSession();

        $trialData = $session->get('trialData');

        $tranList = [];
        foreach ($trialData as $row) {
            $tranList[] = [
                'type' => 'repayinterest',
                'period' => $row['period'],
                'amount' => $row['interest'],
                'repaid_amount' => 0,
                'due_at' => $row['date'],
                'last_calculate_at' => null,
                'status' => 'nofinish',
            ];
            $tranList[] = [
                'type' => 'repayprincipal',
                'period' => $row['period'],
                'amount' => $row['principal'],
                'repaid_amount' => 0,
                'due_at' => $row['date'],
                'last_calculate_at' => null,
                'status' => 'nofinish',
            ];

            foreach (['technical_service', 'after_loan_manage', 'service', 'lateinterest', 'fin_service'] as $feeType) {
                if (isset($row[$feeType])) {
                    $tranList[] = [
                        'type' => $feeType,
                        'period' => $row['period'],
                        'amount' => $row[$feeType],
                        'repaid_amount' => 0,
                        'due_at' => $row['date'],
                        'last_calculate_at' => null,
                        'status' => 'nofinish',
                    ];
                }
            }
        }

        $client = CmdbComponent::instance();
        $result = $client->calculateLateFee([
            'rate_number' => $session->get('rate_number'),
            'asset_tran_list' => $tranList,
            'calculate_at' => $date,
            'late_num' => $late_num,
            'total_periods' => (int)$count,
            'principal_amount' => (int)$trialAmount,
            'last_calculate_at' => null,
        ]);
        if (!$result) {
            return $this->asJson($client->getResponse());
        }

        return $this->asJson([
            'code' => 0,
            'data' => GridView::widget([
                'dataProvider' => new ArrayDataProvider([
                    'allModels' => ArrayHelper::getValue($client->getResponse(), 'data.late_tran_list', []),
                ]),
                'columns' => [
                    [
                        'label' => '费编码',
                        'attribute' => 'type',
                    ],
                    [
                        'label' => '费用类型',
                        'attribute' => 'description',
                    ],
                    [
                        'label' => '期次',
                        'attribute' => 'period',
                    ],
                    [
                        'label' => '金额',
                        'class' => MoneyDataColumn::class,
                        'attribute' => 'amount',
                    ],
                ],
            ]),
            'message' => null,
        ]);
    }
}
