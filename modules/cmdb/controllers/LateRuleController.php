<?php

namespace cmdb\controllers;

use cmdb\models\LateRule;
use cmdb\models\LateTran;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * LateRuleController implements the CRUD actions for LateRule model.
 */
class LateRuleController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all LateRule models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $query = LateRule::find()
            ->with([
                'rates' => function (ActiveQuery $query) {
                    $query->groupBy('rate_late_no')->select([
                        'rate_late_no',
                    ]);
                },
            ]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'attributes'   => [
                    'late_update_at',
                    'late_id',
                ],
                'defaultOrder' => [
                    'late_update_at' => SORT_DESC,
                    'late_id'        => SORT_DESC,
                ],
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single LateRule model.
     *
     * @param int $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        $query = $model->getTrans()->orderBy([
            'late_tran_fee_code'           => SORT_ASC,
            'late_tran_dependent'          => SORT_ASC,
            'late_tran_period'             => SORT_ASC,
            'late_tran_status'             => SORT_DESC,
            'late_tran_overdue_days_start' => SORT_ASC,
            'late_tran_overdue_days_end'   => SORT_ASC,
            'late_tran_effective_start'    => SORT_ASC,
        ]);

        $dataProvider = new ActiveDataProvider([
            'query'      => $query,
            'sort'       => false,
            'pagination' => false,
        ]);

        return $this->render('view', [
            'dataProvider' => $dataProvider,
            'model'        => $model,
        ]);
    }

    /**
     * Creates a new LateRule model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new LateRule();
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->late_id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing LateRule model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            $model->save();

            return $this->redirect(['view', 'id' => $model->late_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing LateRule model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws UserException
     * @throws InvalidConfigException
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        if ($model->used()) {
            throw new UserException('被使用的罚息不允许删除!');
        }

        $model::getDb()->transaction(function () use ($model) {
            LateTran::deleteAll([
                'late_tran_late_no' => $model->late_no,
            ]);

            $model->delete();
        });

        return $this->redirect(['index']);
    }

    /**
     * Finds the LateRule model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return LateRule the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): LateRule
    {
        if (($model = LateRule::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     * @throws UserException
     */
    public function actionGoLive($id)
    {
        $model = $this->findModel($id);

        $model->late_status = LateRule::STATUS_ONLINE;

        if (!$model->update()) {
            throw new UserException('上线失败, 请重试...');
        }

        return $this->redirect(['view', 'id' => $id]);
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     * @throws UserException
     */
    public function actionClosure($id)
    {
        $model = $this->findModel($id);

        $model->late_status = LateRule::STATUS_OFFLINE;

        if (!$model->update()) {
            throw new UserException('上线失败, 请重试...');
        }

        return $this->redirect(['view', 'id' => $id]);
    }
}
