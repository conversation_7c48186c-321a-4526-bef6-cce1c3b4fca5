<?php

namespace cmdb\controllers;

use cmdb\models\Product;
use cmdb\models\ProductRate;
use cmdb\models\ProductSearch;
use cmdb\models\Rate;
use cmdb\models\RateSearch;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\SqlDataProvider;
use yii\db\ActiveQuery;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ProductController implements the CRUD actions for Product model.
 */
class ProductController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'audit' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Product models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new ProductSearch();

        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Product model.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'model'        => $model,
        ]);
    }

    /**
     * Creates a new Product model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return Response|string
     */
    public function actionCreate()
    {
        $model = new Product();
        if ($model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->product_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Product model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws UserException
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->isVoid()) {
            throw new UserException('已上线或作废的产品不能编辑!');
        }

        if ($model->load(Yii::$app->request->post()) && $model->save(true, ['product_condition', 'product_memo'])) {
            return $this->redirect(['view', 'id' => $model->product_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * @param $id
     * @param $status
     *
     * @return Response
     * @throws Throwable
     * @throws UserException
     * @throws StaleObjectException
     * @throws NotFoundHttpException
     */
    public function actionAudit($id, $status)
    {
        $model = $this->findModel($id);

        $model->product_status = $status;

        if (!$model->update(true, ['product_status'])) {
            throw new UserException(json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
        }

        return $this->redirect('index');
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws Throwable
     * @throws UserException
     * @throws NotFoundHttpException
     */
    public function actionRelation($id)
    {
        $model = $this->findModel($id);

        if (!$model->isOnline()) {
            throw new UserException('上线状态的产品才能绑定费率!');
        }

        $session = Yii::$app->getSession();
        $numbers = $this->request->post('number');
        $state   = (bool)$this->request->post('state', false);
        if (empty($numbers)) {
            $session->addFlash('error', sprintf('操作失败: 请选择需要%s的场景!', $state ? '绑定' : '解绑'));
        } else {
            if ($state) {
                $rates = Rate::findAll(['rate_number' => $numbers]);
                if (empty($rates)) {
                    throw new UserException('费率不存在');
                }
                $result = 0;
                foreach ($rates as $rate) {
                    $result += (new ProductRate([
                        'product_rate_scope'  => $model->product_scope,
                        'product_rate_number' => $rate->rate_number,
                        'product_rate_status' => ProductRate::STATUS_ONLINE,
                    ]))->insert();
                }
                $session->addFlash('success', sprintf('绑定成功 %d 个费率', $result));
            } else {
                $result = ProductRate::deleteAll([
                    'product_rate_scope'  => $model->product_scope,
                    'product_rate_number' => $numbers,
                ]);
                $session->addFlash('info', sprintf('解绑成功 %d 个费率', $result));
            }
        }

        return $this->redirect(['view', 'id' => $model->product_id]);
    }

    /**
     * @return array
     * @throws Throwable
     * @throws InvalidConfigException
     * @throws UserException
     */
    public function actionSwitchStatus()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data = (array)Yii::$app->request->post('data', []);
        if (empty($data)) {
            throw new UserException('参数错误');
        }

        $data = ArrayHelper::index($data, null, 'state');

        $result = ProductRate::getDb()->transaction(function () use ($data) {
            do {
                $state  = key($data);
                $item   = current($data);
                $result = ProductRate::updateAll([
                    'product_rate_status' => $state,
                ], [
                    'product_rate_number' => array_column($item, 'number'),
                    'product_rate_scope'  => array_column($item, 'scope'),
                ]);
            } while ($result && next($data));

            return $result;
        });

        return [
            'code'    => intval(!$result),
            'message' => ($result ? '操作成功' : '操作失败'),
            'data'    => null,
        ];
    }

    /**
     * Finds the Product model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return Product the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Product::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
