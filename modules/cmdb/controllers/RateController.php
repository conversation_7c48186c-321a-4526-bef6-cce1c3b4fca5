<?php

namespace cmdb\controllers;

use cmdb\approvals\RateApproval;
use cmdb\models\Product;
use cmdb\models\ProductRate;
use cmdb\models\Rate;
use cmdb\models\RateEntity;
use cmdb\models\RateSearch;
use cmdb\models\RateTemplate;
use Exception;
use kartik\depdrop\DepDropAction;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\ArrayDataProvider;
use yii\db\ActiveQuery;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * RateController implements the CRUD actions for Rate model.
 */
class RateController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'delete'             => ['POST'],
                    'relation'           => ['POST'],
                    'add-rate-config'    => ['POST'],
                    'rate-template-list' => ['POST'],
                    'approval'           => ['POST'],
                ],
            ],
        ];
    }

    public function actions(): array
    {
        $isDefault = $this->request->get('isDefault', 1);

        return [
            'rate-template-list' => [
                'class'            => DepDropAction::class,
                'outputCallback'   => function ($capitalCode) use ($isDefault) {
                    $templates = RateTemplate::find()->alias('t')
                        ->innerJoinWith(['rateTemplateCapital' => fn(ActiveQuery $query) => $query->alias('c')], false)
                        ->where([
                            't.rate_template_status' => RateTemplate::STATUS_SHOW,
                            'c.capital_code'         => $capitalCode,
                        ])
                        ->select([
                            'id'   => 't.rate_template_id',
                            'name' => 't.rate_template_name',
                        ])
                        ->asArray()->all();

                    if ($isDefault) {
                        array_unshift($templates, [
                            'id'   => 0,
                            'name' => '-',
                        ]);
                    }

                    return array_unique($templates,SORT_REGULAR);
                },
                'selectedCallback' => function () use ($isDefault) {
                    if ($isDefault) {
                        return 0;
                    }

                    return null;
                },
            ],
        ];
    }

    /**
     * Lists all Rate models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new RateSearch();

        $auditing = (new RateApproval())->queryNotEndOfBusiness();

        $auditDataProvider = new ArrayDataProvider([
            'modelClass' => RateEntity::class,
            'key'        => 'rate_id',
            'allModels'  => $auditing,
            'pagination' => false,
        ]);

        $dataProvider = $searchModel->search($this->request->queryParams);
        $dataProvider->query->andFilterWhere(['not in', 'rate_id', array_keys($auditing)]);

        return $this->render('index', [
            'searchModel'       => $searchModel,
            'dataProvider'      => $dataProvider,
            'auditDataProvider' => $auditDataProvider,
        ]);
    }

    /**
     * @param $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * @param int $id
     *
     * @return Response
     */
    public function actionAudit(int $id): Response
    {
        $url = RateApproval::genAuditPreviewUrl($id);

        return $this->redirect($url);
    }

    public function actionUndo($id)
    {
        $model = current((new RateApproval())->queryNotEndOfBusiness(['id' => $id]));
        try {
            if (empty($model)) {
                throw new UserException('数据已处理');
            } else {
                $model->audit->getApproval()->revoke('撤销');
            }
        } catch (Throwable $e) {
            Yii::$app->getSession()->setFlash('warning', $e->getMessage());
        }

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * @param $id
     *
     * @return Response
     * @throws Throwable
     * @throws UserException
     * @throws NotFoundHttpException
     */
    public function actionRelation($id)
    {
        $model = $this->findModel($id);

        if ($model->isVoid()) {
            throw new UserException('已失效的费率不能绑定产品!');
        }

        $session = Yii::$app->getSession();
        $scope   = $this->request->post('scope');
        $state   = (bool)$this->request->post('state', false);
        if (empty($scope)) {
            $session->addFlash('error', sprintf('操作失败: 请选择需要%s的场景!', $state ? '绑定' : '解绑'));
        } elseif ($state) {
            $products = Product::findAll([
                'product_scope' => $scope,
            ]);
            if (empty($products)) {
                throw new UserException('使用场景不存在');
            }

            $result = 0;
            foreach ($products as $product) {
                $result += (new ProductRate([
                    'product_rate_scope'  => $product->product_scope,
                    'product_rate_number' => $model->rate_number,
                    'product_rate_status' => ProductRate::STATUS_ONLINE,
                ]))->insert();
            }
            $session->addFlash('success', sprintf('绑定成功 %d 个场景', $result));
        } else {
            $result = ProductRate::deleteAll([
                'product_rate_scope'  => $scope,
                'product_rate_number' => $model->rate_number,
            ]);
            $session->addFlash('info', sprintf('解绑成功 %d 个场景', $result));
        }

        return $this->redirect(['view', 'id' => $model->rate_id]);
    }

    /**
     * @return string|Response
     */
    public function actionCreate()
    {
        $model = new RateEntity();

        $model->rate_status = Rate::STATUS_OPEN;

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->rate_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    public function actionUpdate($id)
    {
        $model = RateEntity::findOne($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->rate_id]);
        }
        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * 申请上线
     *
     * @param int $id
     *
     * @return Response
     * @throws Throwable
     * @throws UserException
     * @throws InvalidConfigException
     */
    public function actionApproval(int $id): Response
    {
        $approval = new RateApproval([
            'id'     => $id,
            'status' => Rate::STATUS_PASS,
        ], [
            'backUrl' => Url::to(['index']),
        ]);

        if (empty($approval->queryNotEndOfBusiness(['id' => $id]))) {
            return $this->redirect($approval->audit());
        }

        Yii::$app->getSession()->setFlash('error', '已提交审核, 请确认是否重复提交');

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * 将产品标记为 overdue 状态
     *
     * @param $id
     *
     * @return Response
     * @throws InvalidConfigException
     * @throws Throwable
     * @throws UserException
     */
    public function actionOverdueRate($id): Response
    {
        $approval = new RateApproval([
            'id'     => $id,
            'status' => Rate::STATUS_OVERDUE,
        ], [
            'backUrl' => Url::to(['index']),
        ]);

        if (empty($approval->queryNotEndOfBusiness(['id' => $id]))) {
            return $this->redirect($approval->audit());
        }

        Yii::$app->getSession()->setFlash('error', '已提交审核, 请确认是否重复提交');

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * 作废
     *
     * @param $id
     *
     * @return Response
     * @throws Exception
     */
    public function actionVoidRate($id): Response
    {
        Rate::auditRate($id, Rate::STATUS_VOID);

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * Finds the Rate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return Rate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = RateEntity::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
