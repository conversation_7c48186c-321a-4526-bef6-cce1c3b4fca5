<?php

use cmdb\models\Product;
use cmdb\models\ProductSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ProductSearch */

$this->title                   = '业务产品';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', [
    'model' => $searchModel,
]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'attribute' => 'product_scope',
            'format'    => 'html',
            'value'     => function (Product $model) {
                return Html::a($model->product_scope, ['view', 'id' => $model->product_id]);
            },
        ],
        'product_memo',
        [
            'label'  => '-',
            'format' => 'raw',
            'value'  => function (Product $model) {
                /** @var ActiveQuery $query */
                $query = $model->getCapitals();
                $data  = $query->distinct()->select('capital_name')->column();
                $data  = array_chunk($data, 10);
                $data  = array_map(function ($row) {
                    return implode(', ', $row);
                }, $data);

                return Html::tag('div', vsprintf('<b>产品使用条件:</b> %s<br/><b>适用资金方:</b> %s', [
                    $model->product_condition,
                    implode('<br/>', $data),
                ]));
            },
        ],
        [
            'attribute' => 'product_status',
            'format'    => ['in', Product::STATUS_LIST],
        ],
        [
            'class'          => ActionColumn::class,
            'header'         => '审核',
            'template'       => '{online} {offline} {void}',
            'buttons'        => [
                'online'  => function ($url, $model, $key) {
                    return ActionColumn::newButton('上线', ['audit', 'id' => $key, 'status' => Product::STATUS_ONLINE], [
                        'title' => '上线',
                        'class' => 'btn-success',
                        'data'  => [
                            'confirm' => '确定要上线吗?',
                            'method'  => 'post',
                        ],
                    ]);
                },
                'offline' => function ($url, Product $model, $key) {
                    return ActionColumn::newButton('下线', ['audit', 'id' => $key, 'status' => Product::STATUS_OFFLINE], [
                        'title' => '下线',
                        'class' => 'btn-default',
                        'data'  => [
                            'confirm' => '确定要下线吗?',
                            'method'  => 'post',
                        ],
                    ]);
                },
                'void'    => function ($url, $model, $key) {
                    return ActionColumn::newButton('作废', ['audit', 'id' => $key, 'status' => Product::STATUS_VOID], [
                        'title' => '作废',
                        'class' => 'btn-danger',
                        'data'  => [
                            'confirm' => "作废产品，同时作废所有绑定关系。\n确定要作废吗?",
                            'method'  => 'post',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'online'  => function ($model) {
                    return in_array($model->product_status, [Product::STATUS_OPEN, Product::STATUS_OFFLINE]);
                },
                'offline' => function (Product $model) {
                    return in_array($model->product_status, [Product::STATUS_OPEN, Product::STATUS_ONLINE]);
                },
                'void'    => function ($model) {
                    return in_array($model->product_status, [
                        Product::STATUS_OPEN,
                        Product::STATUS_ONLINE,
                        Product::STATUS_OFFLINE,
                    ]);
                },
            ],
        ],
    ],
]);
