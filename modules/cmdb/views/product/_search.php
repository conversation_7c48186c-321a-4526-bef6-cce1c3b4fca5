<?php

use cmdb\models\Product;
use cmdb\models\ProductSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ProductSearch */
/* @var $form ActiveForm */
?>
<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            条件筛选
        </div>
    </div>
    <?php
    $form = ActiveForm::begin([
        'action'        => ['index'],
        'method'        => 'get',
        'type'          => ActiveForm::TYPE_INLINE,
        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
    ]); ?>
    <div class="box-body">
        <?= $form->field($model, 'product_scope')->textInput() ?>

        <?= $form->field($model, 'product_status')->widget(Select2::class, [
            'options'       => [
                'placeholder' => '状态',
            ],
            'data'          => Product::STATUS_LIST,
            'hideSearch'    => true,
            'pluginOptions' => [
                'width'      => '100px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('创建产品', ['create'], ['class' => 'btn btn-success']) ?>
    </div>
    <?php
    ActiveForm::end(); ?>
</div>
