<?php

use cmdb\models\Product;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Product */
$form = ActiveForm::begin();
?>

<div class="box-body">

    <?= $form->field($model, 'product_scope')->textInput([
        'maxlength' => true,
        'disabled'  => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'product_condition')->widget(CodeEditor::class, [
        'clientOptions' => [
            'maxLines' => 40,
        ],
    ]) ?>

    <?= $form->field($model, 'product_memo')->textInput(['maxlength' => true]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton($model->isNewRecord ? '创建' : '保存', [
        'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary',
    ]) ?>
    <?= Html::a('取消', ['index'], ['class' => 'btn btn-default']) ?>
</div>

<?php
ActiveForm::end(); ?>
