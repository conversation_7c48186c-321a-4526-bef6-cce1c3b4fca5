<?php

use cmdb\models\Capital;
use cmdb\models\Product;
use cmdb\models\ProductRate;
use cmdb\models\Rate;
use cmdb\models\RateEntity;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\assets\LayerAsset;
use xlerr\common\helpers\MoneyHelper;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model Product */

LayerAsset::register($this);

$this->title                   = $model->product_scope;
$this->params['breadcrumbs'][] = ['label' => '业务产品', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

$capitalMap = Capital::getCapitalCodeMap();

// 绑定列表遮罩层,作废后禁止绑定
$isOnline = !$model->isOnline() ? 'disabled' : '';
?>

<p>
    <?php
    if (in_array($model->product_status, [Product::STATUS_OPEN, Product::STATUS_ONLINE, Product::STATUS_OFFLINE])) {
        echo Html::a('编辑', ['update', 'id' => $model->product_id], [
            'class' => 'btn btn-primary',
        ]);
    }
    ?>
    <?= Html::a('返回列表', ['index'], ['class' => 'btn btn-default']) ?>
</p>
<div class="box box-info">
    <div class="box-header with-border">
        <div class="box-title"><?= Html::encode($this->title) ?></div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'options'    => [
                'class' => 'table table-striped table-bordered detail-view',
            ],
            'model'      => $model,
            'attributes' => [
                'product_id',
                'product_scope',
                [
                    'format'    => 'raw',
                    'attribute' => 'product_condition',
                    'value'     => CodeEditor::widget([
                        'name'          => 'product_condition',
                        'value'         => $model->product_condition,
                        'clientOptions' => [
                            'readOnly' => true,
                            'maxLines' => 40,
                        ],
                    ]),
                ],
                'product_memo:ntext',
                [
                    'label'     => Html::activeLabel($model, 'product_status'),
                    'attribute' => 'product_status',
                    'format'    => ['in', Product::STATUS_LIST],
                ],
                'product_create_at',
                'product_update_at',
            ],
        ]) ?>
    </div>
</div>


<?php
// 已绑定的场景
ActiveForm::begin([
    'action' => Url::to(['relation', 'id' => $model->product_id]),
]);
$layout = <<<HTML
<div class="box-header with-border">
    <div class="box-title">绑定的费率</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-sm btn-success" $isOnline id="bindBtn">绑定费率</button>
        <button type="submit" class="btn btn-sm btn-danger" $isOnline id="unbindBtn" data-confirm="确定将选中费率与当前场景的绑定关系解除吗?">将选中费率解除绑定</button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;

echo Gridview::widget([
    'layout'       => $layout,
    'dataProvider' => new ActiveDataProvider([
        'query'      => $model->getRates()->orderBy(['rate_id' => SORT_DESC]),
        'pagination' => false,
        'sort'       => false,
    ]),
    'columns'      => [
        [
            'attribute' => 'rate_number',
            'format'    => 'raw',
            'value'     => function (Rate $model) {
                return Html::a($model->rate_number, ['rate/view', 'id' => $model->rate_id], [
                    'target' => '_blank',
                ]);
            },
        ],
        [
            'attribute' => 'periodCategory',
            'value'     => function (RateEntity $model) {
                return $model->periodCategoryLabel();
            },
        ],
        [
            'attribute' => 'rate_capital_code',
            'format'    => ['in', $capitalMap],
        ],
        [
            'label'     => '适用本金范围（元）',
            'attribute' => 'rate_principal_min_amount',
            'value'     => function (Rate $model) {
                return vsprintf('%s - %s', [
                    MoneyHelper::f2y($model->rate_principal_min_amount, true),
                    MoneyHelper::f2y($model->rate_principal_max_amount, true),
                ]);
            },
        ],
        'late.late_name',
        [
            'class'           => CheckboxColumn::class,
            'name'            => 'number',
            'checkboxOptions' => function (Rate $rate) use ($model) {
                return [
                    'value'    => $rate->rate_number,
                    'disabled' => !$model->isOnline(),
                ];
            },
        ],
    ],
]);

ActiveForm::end();
?>

<div id="rate-list" style="padding: 15px; background-color: #ecf0f5; min-height: 100%; display: none">
    <?php
    ActiveForm::begin([
        'action' => Url::to(['relation', 'id' => $model->product_id]),
    ]);

    echo Html::hiddenInput('state', 1);

    echo Gridview::widget([
        'layout'       => '<div class="box-header with-border">
    <div class="box-title">待绑定的费率</div>
    <div class="box-tools pull-right">
        <button type="submit" class="btn btn-sm btn-primary" id="submitBindBtn" data-confirm="确定要将选中的费率与当前场景绑定吗?">绑定选中费率</button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>',
        'dataProvider' => new ActiveDataProvider([
            'query'      => RateEntity::find()->where([
                'rate_status' => Rate::STATUS_PASS,
            ])->andWhere([
                'not in',
                'rate_number',
                ProductRate::find()->where([
                    'product_rate_scope' => $model->product_scope,
                ])->select('product_rate_number'),
            ])->orderBy(['rate_id' => SORT_DESC]),
            'pagination' => false,
            'sort'       => false,
        ]),
        'columns'      => [
            [
                'attribute' => 'rate_number',
                'format'    => 'raw',
                'value'     => function (Rate $model) {
                    return Html::a($model->rate_number, ['rate/view', 'id' => $model->rate_id], [
                        'target' => '_blank',
                    ]);
                },
            ],
            [
                'attribute' => 'periodCategory',
                'value'     => function (RateEntity $model) {
                    return $model->periodCategoryLabel();
                },
            ],
            [
                'attribute' => 'rate_capital_code',
                'format'    => ['in', $capitalMap],
            ],
            [
                'label'     => '适用本金范围（元）',
                'attribute' => 'rate_principal_min_amount',
                'value'     => function (Rate $model) {
                    return vsprintf('%s - %s', [
                        MoneyHelper::f2y($model->rate_principal_min_amount, true),
                        MoneyHelper::f2y($model->rate_principal_max_amount, true),
                    ]);
                },
            ],
            'late.late_name',
            [
                'class'           => CheckboxColumn::class,
                'name'            => 'number',
                'checkboxOptions' => function (Rate $rate) {
                    return [
                        'value' => $rate->rate_number,
                    ];
                },
            ],
        ],
    ]);

    ActiveForm::end();
    ?>
</div>

<script>
    <?php $this->beginBlock('main') ?>
    $('#bindBtn').click(function () {
        layer.open({
            type: 1,
            area: ['80%', '90%'],
            shadeClose: true, //开启遮罩关闭
            content: $('#rate-list')
        })
    })
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['main']) ?>
</script>
