<?php

use cmdb\models\LateRule;
use cmdb\models\LateTran;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model cmdb\models\LateTran */
/* @var $lateRule cmdb\models\LateRule */

$this->title                   = $model->late_tran_late_no;
$this->params['breadcrumbs'][] = ['label' => '罚息规则', 'url' => ['late-rule/index']];
$this->params['breadcrumbs'][] = [
    'label' => '编号:' . $lateRule->late_no,
    'url'   => ['late-rule/view', 'id' => $lateRule->late_id],
];
$this->params['breadcrumbs'][] = '详情';
?>
<p>
    <?= Html::a('修改', ['update', 'id' => $model->late_tran_id], ['class' => 'btn btn-primary']) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'attributes' => [
                'late_tran_id',
                'late_tran_fee_code',
                'late_tran_fee_desc',
                'late_tran_period',
                [
                    'attribute' => 'late_tran_formula',
                    'format'    => ['in', LateRule::FORMULAS],
                ],
                'late_tran_value',
                'late_tran_dependent',
                [
                    'attribute' => 'late_tran_dependent_mode',
                    'format'    => ['in', LateTran::DEPENDENT_MODES],
                ],
                [
                    'attribute' => 'late_tran_dependent_type',
                    'format'    => ['in', LateTran::DEPENDENT_TYPES],
                ],
                [
                    'attribute' => 'late_tran_carry_mode',
                    'format'    => ['in', LateTran::CARRY_MODES],
                ],
                [
                    'attribute' => 'late_tran_carry_scale',
                    'format'    => ['in', LateTran::CARRY_SCALES],
                ],
                [
                    'label' => '逾期天数',
                    'value' => function (LateTran $model) {
                        return implode(' - ', [
                            $model->late_tran_overdue_days_start,
                            $model->late_tran_overdue_days_end,
                        ]);
                    },
                ],
                [
                    'label' => '每日罚息',
                    'value' => function (LateTran $model) {
                        return implode(' - ', [
                            $model->late_tran_min_daily_amount,
                            $model->late_tran_max_daily_amount,
                        ]);
                    },
                ],
                'late_tran_create_at',
            ],
        ]) ?>

    </div>
</div>
