<?php

use cmdb\models\LateRule;
use cmdb\models\LateTran;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ActiveForm;

/* @var $this View */
/* @var $model LateTran */
/* @var $lateRule LateRule */
?>

<?php
$form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'late_tran_fee_code')->textInput() ?>

    <?= $form->field($model, 'late_tran_fee_desc')->textInput() ?>

    <?= $form->field($model, 'late_tran_period')->textInput() ?>

    <?= $form->field($model, 'late_tran_formula')->widget(Select2::class, [
        'data'       => LateRule::FORMULAS,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_tran_value')->textInput() ?>

    <?= $form->field($model, 'late_tran_dependent')->textInput() ?>

    <?= $form->field($model, 'late_tran_dependent_mode')->widget(Select2::class, [
        'data'       => LateTran::DEPENDENT_MODES,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_tran_dependent_type')->widget(Select2::class, [
        'data'       => LateTran::DEPENDENT_TYPES,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_tran_carry_mode')->widget(Select2::class, [
        'data'       => LateTran::CARRY_MODES,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_tran_carry_scale')->widget(Select2::class, [
        'data'       => LateTran::CARRY_SCALES,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_tran_overdue_days_start') ?>

    <?= $form->field($model, 'late_tran_overdue_days_end') ?>

    <?= $form->field($model, 'late_tran_effective_start')->widget(DatePicker::class) ?>

    <?= $form->field($model, 'late_tran_min_daily_amount')->textInput() ?>

    <?= $form->field($model, 'late_tran_max_daily_amount')->textInput() ?>

</div>

<div class="box-footer">
    <?= Html::submitButton($model->isNewRecord ? '创建' : '保存', [
        'class' => $model->isNewRecord ? 'btn btn-success'
            : 'btn btn-primary',
    ]) ?>
    <?= Html::a('取消', ['late-rule/view', 'id' => $lateRule->late_id], [
        'class' => 'btn btn-default',
    ]) ?>
</div>

<?php
ActiveForm::end(); ?>
