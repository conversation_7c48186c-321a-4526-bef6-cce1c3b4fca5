<?php

use cmdb\models\LateRule;
use common\widgets\GridView;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title                   = '罚息规则';
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('创建罚息规则', ['create'], ['class' => 'btn btn-success']) ?>
</p>
<?= GridView::widget([
    'dataProvider'     => $dataProvider,
    'headerRowOptions' => [],
    'columns'          => [
        [
            'header'         => '操作',
            'class'          => 'xlerr\common\grid\ActionColumn',
            'visibleButtons' => [
                'delete' => function (LateRule $model, $id, $index) {
                    return !$model->used();
                },
            ],
        ],
        'late_no',
        'late_name',
        [
            'attribute' => 'late_limit_type',
            'format'    => ['in', LateRule::TYPE_LIST],
        ],
        [
            'attribute' => 'late_limit_formula',
            'format'    => ['in', LateRule::FORMULAS],
        ],
        'late_limit_value',
        'late_desc',
        'late_create_at',
        'late_update_at',
    ],
]); ?>
