<?php

use cmdb\models\RateTrial;
use kartik\widgets\ActiveForm;
use xlerr\common\assets\LayerAsset;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model RateTrial */
/* @var $dataProvider ArrayDataProvider */
/* @var $columns array */
/** @var $json string */
/** @var $rateNumbers array */
/** @var $varNum array */

LayerAsset::register($this);

$this->title = '费率编号试算';
$this->params['breadcrumbs'][] = '试算';
$this->params['breadcrumbs'][] = $this->title;
$config = RateTrial::config();

?>

<div class="box box-default">
    <div class="box-header with-border">
        <h1 class="box-title">试算产品参数</h1>
    </div>
    <?php
    $form = ActiveForm::begin([
        //        'action' => [''],
        'method' => 'post',
        'id' => 'trialForm',
        'type' => ActiveForm::TYPE_HORIZONTAL,
        'enableClientValidation' => true,
    ]); ?>
    <div class="box-body">
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'rate_number')->widget(Select2::class, [
                    'showToggleAll' => false,
                    'data' => $rateNumbers,
                    'hideSearch' => false,
                    'pluginOptions' => [
                        'multiple' => false,
                    ],
                ]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'signDate')->widget(DatePicker::class) ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'trialAmount')->widget(MoneyInput::class) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'period')->widget(Select2::class, [
                    'showToggleAll' => false,
                    'data' => (static function () use ($config, $model) {
                        $periodCategoryList = (array)($config['period_category_list'] ?? []);
                        $data = [];
                        foreach ($periodCategoryList as $key => $text) {
                            if (in_array($key, $model->periodCategory, true)) {
                                $data[$key] = $text;
                            }
                        }
                        return $data;
                    })(),
                    'hideSearch' => true,
                    'pluginOptions' => [
                        'multiple' => false,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('period'),
                    ],
                ]) ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'lateNum')->widget(Select2::class, [
                    'data' => array_combine($model->getRateLateRuleNo(), $model->getRateLateRuleNo()),
                    'hideSearch' => false,
                    'pluginOptions' => [
                        'multiple' => false,
                    ],
                ]) ?>
            </div>
        </div
    </div>
    <div id="tadpole">
        <div class="box-header with-border">
            <div class="box-title">
                还款参数信息
            </div>
        </div>
        <div class="box-body">
            <!--蝌蚪算法信息展示-->
            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'tadPoleTerms', [
                        'addon' => [
                            'append' => [
                                'content' => Html::tag('span', '请用英文,隔开'),
                                'asButton' => false,

                            ],
                        ],
                    ])->label('每期还款期限') ?>
                </div>
                <div class="col-md-6">
                    <?= $form->field($model, 'tadPoleAmountPercents', [
                        'addon' => [
                            'append' => [
                                'content' => Html::tag('span', '请用英文,隔开',),
                                'asButton' => false,
                            ],
                        ],
                    ])->label('每期还款本金比例') ?>
                </div>
            </div>
        </div>
    </div>
    <div class="box-header with-border">
        <div class="box-title">
            费率信息
        </div>
    </div>
    <div class="box-body">
        <div id="varRateInfo" class="row">
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('试算', [
            'class' => 'btn btn-primary',
            'id' => 'run-trial',
        ]) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<script>
    <?php $this->beginBlock('main') ?>

    const trialLateUrl = '<?= Url::to(['late'])?>',
        varNum = JSON.parse('<?= Json::encode($varNum) ?>'),
        rateNumberEl = $('#<?= Html::getInputId($model, 'rate_number') ?>'),
        rateVarInfoForm = $('#<?= Html::getInputId($model, 'rateVarInfo')?>'),
        lateNumEl = $('#<?= Html::getInputId($model, 'lateNum')?>'),
        periodEl = $('#<?= Html::getInputId($model, 'period')?>'),
        trialAmountEl = $('#<?= Html::getInputId($model, 'trialAmount')?>'),
        periodTermsEl = $('#<?= Html::getInputId($model, 'tadPoleTerms')?>'),
        periodAmountPercentsEl = $('#<?= Html::getInputId($model, 'tadPoleAmountPercents')?>'),
        formEl = $('#trialForm'),
        tadpoleInfo = {
            periodTerms: [],
            periodAmountPercents: []
        };

    rateNumberEl.change(function () {
        let val = rateNumberEl.val(),
            isVar = varNum.hasOwnProperty(val) ? 1 : 0;

        if (isVar) {
            periodTermsEl.attr('readonly', true)
            periodAmountPercentsEl.attr('readonly', true)
        } else {
            periodTermsEl.removeAttr('readonly')
            periodAmountPercentsEl.removeAttr('readonly')
        }

        $.post(`<?= Url::to(['rate-var-info-html']) ?>?rateNumber=${val}&var=${isVar}`, null, function (res) {
            const feeHtmlList = res.data.feeColumns.map(renderFeeHtmlInput)
            $('#varRateInfo').html(feeHtmlList.join(''));
            tadpoleInfo['periodTerms'] = res.data.periodTerms;
            tadpoleInfo['periodAmountPercents'] = res.data.periodAmountPercents;
            //验证动态费率编号
            //1.如果携带还款期次和本金比例
            if (res.data.periodTerms.length > 0) {
                let val = tadpoleInfo["periodTerms"].length + ',0,tadpole';
                periodEl.val(val)
                periodEl.find("option:not([value='" + val + "'])").attr('disabled', 'disabled');
            } else {
                periodEl.find("option").removeAttr('disabled');
            }
            setTimeout(() => {
                periodEl.change()
            }, 100)
        });
    }).change()

    periodEl.change(function () {
        const [period, _, periodType] = periodEl.val().split(',')
        if (periodType === 'tadpole') {
            periodTermsEl.val(fillValue(tadpoleInfo['periodTerms'], period, 0).join(','))
            periodAmountPercentsEl.val(fillValue(tadpoleInfo['periodAmountPercents'], period, 0).join(','))
            $('#tadpole').show();
        } else {
            $('#tadpole').hide();
        }
    })

    function fillValue(arr, len, val) {
        return arr.concat(new Array(len - arr.length).fill(val))
    }


    function renderFeeHtmlInput(fee) {
        const readonly = fee.disabled ? ' readonly' : '';
        return `
<div class="col-md-6 col-xs-12">
    <div class="form-group highlight-addon">
        <label class="control-label has-star col-md-2">${fee.label}</label>
        <div class="col-md-10">
            <div class="input-group">
                <input type="text"${readonly} class="form-control" name="${fee.name}" value="${fee.value}">
                <span class="input-group-addon" style="background-color: antiquewhite">${fee.spanText}</span>
            </div>
            <span class="text-danger">${fee.vatRateText}</span>
        </div>
    </div>
</div>`;
    }

    formEl.on('beforeSubmit', function (e) {
        setTimeout(() => {
            const form = $(this),
                periodTermsVal = periodTermsEl.val().split(','),
                periodAmountPercentsVal = periodAmountPercentsEl.val().split(','),
                [period, _, periodType] = periodEl.val().split(',');

            if (form.find('.has-error').length === 0) {
                //验证蝌蚪算法
                if (periodType === 'tadpole') {
                    if (!periodTermsVal.every(num => num > 0) || !periodAmountPercentsVal.every(num => num > 0)) {
                        window.alert('输入的还款期限或还款本金比例必须大于0');
                        return false;
                    }
                    if (periodTermsVal.length !== parseInt(period) || periodAmountPercentsVal.length !== parseInt(period)) {
                        window.alert('输入的还款期限或还款本金期次不等于对应的期次类型');
                        return false;
                    }

                    let periodAmountPercentsTotal = periodAmountPercentsVal.reduce((a, b) => a + (parseFloat(b) * 100), 0)
                    if (Math.round(periodAmountPercentsTotal) !== 10000) {
                        window.alert('输入的还款本金比例合计必须为100');
                        return false;
                    }
                }
                window.top.makeDialog({
                    type: 2,
                    title: '试算',
                    content: `run-trial?` + form.serialize(),
                    offset: 'rt' //显示在右上角
                });
            }
        }, 100)

        return false;
    })
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['main']) ?>
</script>
