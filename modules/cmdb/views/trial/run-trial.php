<?php

use cmdb\models\RateTrial;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use yii\bootstrap\Tabs;
use yii\data\ArrayDataProvider;
use yii\grid\GridView;
use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this yii\web\View */
/** @var $model RateTrial */
/* @var $dataProvider ArrayDataProvider */
/* @var $columns array */
/** @var $json string */

$this->title = '费率编号试算';
$this->params['breadcrumbs'][] = '试算';
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="box box-primary">
    <div class="box-header with-border">
        <h1 class="box-title">试算结果</h1>
    </div>
    <div class="box-body">
        <?= Tabs::widget([
            'items' => [
                [
                    'label' => 'Table',
                    'active' => true,
                    'content' => GridView::widget([
                        'dataProvider' => $dataProvider,
                        'columns' => $columns,
                        'layout' => '{items}',
                    ]),
                ],
                [
                    'label' => 'JSON',
                    'content' => Html::tag(
                        'pre',
                        json_encode($json, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES),
                        [
                            'style' => 'background-color: #fff; border-top:0; border-radius:0;',
                        ]
                    ),
                ],
            ],
        ]) ?>
    </div>
</div>

<?php
if (count($dataProvider->allModels) > 1) : ?>
    <div class="box box-warning">
        <div class="box-header with-border">
            <h3 class="box-title">试算罚息</h3>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'type' => ActiveForm::TYPE_HORIZONTAL,
            ]) ?>
            <div class="form-group required">
                <label class="control-label col-md-2">试算罚息日期</label>
                <div class="col-md-3">
                    <?= DatePicker::widget([
                        'class' => 'form-control',
                        'name' => 'trial_late_date',
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= Html::button('试算', [
                        'class' => 'btn btn-primary trial-late',
                    ]) ?>
                </div>
            </div>


            <?php ActiveForm::end() ?>
        </div>
    </div>

    <div id="trial_late_result" class="box-footer no-padding"></div>
<?php endif; ?>

<script>
    <?php $this->beginBlock('main') ?>

    const trialLateUrl = '<?= Url::to(['late'])?>';
    $('.trial-late').click(function () {
        $.getJSON(trialLateUrl, {
            'date': $('[name=trial_late_date]').val(),
            'late_num': '<?= $model->lateNum ?>',
            'period': '<?= $model->period ?>',
            'trialAmount': <?= $model->trialAmount ?>
        }, function (res) {
            if (res.code) {
                layer.open({
                    type: 1,
                    // skin: 'layui-layer-demo', //样式类名
                    closeBtn: 1, //不显示关闭按钮
                    shadeClose: true, //开启遮罩关闭
                    content: '<div class="box-body">' + res.message + '</div>'
                });
            } else {
                $('div#trial_late_result').html(res.data);
            }
        });
    });

    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['main']) ?>
</script>
