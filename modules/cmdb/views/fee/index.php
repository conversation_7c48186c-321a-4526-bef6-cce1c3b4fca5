<?php

use cmdb\models\Fee;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */

$this->title                   = '费用管理';
$this->params['breadcrumbs'][] = $this->title;

echo Html::tag('p', Html::a('创建费用', ['create'], ['class' => 'btn btn-success']));

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'    => 'xlerr\common\grid\ActionColumn',
            'template' => '{update}',
        ],
        [
            'attribute' => 'fee_type',
            'format'    => ['in', Fee::TYPE_LIST, '未设置'],
        ],
        [
            'attribute' => 'fee_code',
        ],
        [
            'attribute' => 'fee_name',
        ],
        [
            'attribute' => 'fee_status',
            'format'    => ['in', Fee::STATUS_LIST],
        ],
        [
            'attribute' => 'fee_memo',
        ],
        [
            'attribute' => 'fee_create_at',
        ],
        [
            'attribute' => 'fee_update_at',
        ],
    ],
]);
