<?php

use cmdb\models\Fee;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model cmdb\models\Fee */
/* @var $form yii\widgets\ActiveForm */
?>


<?php
$form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'fee_type')->widget(Select2::class, [
        'data'       => Fee::TYPE_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'fee_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'fee_code')->textInput([
        'maxlength' => true,
        'disabled'  => !$model->isNewRecord,
    ]) ?>

    <?= $form->field($model, 'fee_status')->widget(Select2::class, [
        'data'       => Fee::STATUS_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'fee_memo')->textarea([
        'rows' => 3,
    ]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php
ActiveForm::end(); ?>
