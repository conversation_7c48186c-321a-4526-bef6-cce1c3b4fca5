<?php

use cmdb\models\Capital;
use cmdb\models\Product;
use cmdb\models\Rate;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $model Rate */
/* @var $form ActiveForm */
?>
<div class="box box-default search">
    <div class="box-header with-border">
        <h1 class="box-title">条件筛选 <i class="fa fa-arrow-circle-down text-danger"></i></h1>
    </div>
    <?php
    $form = ActiveForm::begin([
        'action'        => [''],
        'method'        => 'get',
        'type'          => ActiveForm::TYPE_INLINE,
        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
    ]); ?>
    <div class="box-body">
        <?= $form->field($model, 'rate_number')->textInput() ?>

        <?= $form->field($model, 'rate_name')->textInput() ?>

        <?= $form->field($model, 'rate_capital_code')->widget(Select2::class, [
            'options'       => [
                'placeholder' => '适用资金方',
            ],
            'data'          => Capital::getCapitalCodeMap(),
            'theme'         => Select2::THEME_DEFAULT,
            'hideSearch'    => false,
            'pluginOptions' => [
                'width'      => '150px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'rate_status')->widget(Select2::class, [
            'options'       => [
                'placeholder' => '状态',
            ],
            'data'          => Rate::STATUS_LIST,
            'hideSearch'    => true,
            'pluginOptions' => [
                'width'      => '120px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'product_scope')->widget(Select2::class, [
            'options'       => [
                'placeholder' => '业务产品',
            ],
            'data'          => Product::find()
                ->where(['product_status' => Product::STATUS_ONLINE])
                ->select('product_scope')
                ->indexBy('product_scope')
                ->column(),
            'hideSearch'    => true,
            'pluginOptions' => [
                'width'      => '200px',
                'allowClear' => true,
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], [
            'class' => 'btn btn-default',
        ]) ?>

        <?= Html::a('创建费率', ['create'], ['class' => 'btn btn-success']) ?>

    </div>
    <?php
    ActiveForm::end(); ?>
</div>
