<?php

use cmdb\models\Capital;
use cmdb\models\Fee;
use cmdb\models\LateRule;
use cmdb\models\Rate;
use cmdb\models\RateConfig;
use cmdb\models\RateEntity;
use cmdb\models\RateTemplate;
use kartik\field\FieldRange;
use kartik\widgets\DepDrop;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\assets\LayerAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model RateEntity */

LayerAsset::register($this);

$config = RateEntity::config();

$rateTemplateList = RateTemplate::find()->select('rate_template_config')->indexBy('rate_template_id')->column();
foreach ($rateTemplateList as &$item) {
    $item = (array)json_decode($item, true);
}
if (!$model->isNewRecord || $model->hasErrors()) {
    $isDefault = true;
    $rateTemplateList[0] = $model->toTemplate();
}
$editable = !(!$model->isNewRecord && $model->rate_status === RateEntity::STATUS_PASS);
?>
<style>
    .table-responsive {
        overflow-x: auto;
        width: 100%;
    }

</style>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">
            费率配置
        </div>
    </div>

    <?php
    $form = ActiveForm::begin() ?>
    <div class="box-body">
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'rateCapitalChannel', [
                    'addon' => [
                        'append' => [
                            'content' => Html::a('资金方管理', ['capital/index'], [
                                'class' => 'btn btn-info btn-flat',
                                'target' => '_blank',
                            ]),
                            'asButton' => true,
                        ],
                    ],
                ])->widget(Select2::class, [
                    'data' => Capital::getCapitalCodeMap(),
                    'hideSearch' => false,
                    'options' => [
                        'prompt' => $model->getAttributeLabel('rateCapitalChannel'),
                    ],
                    'pluginOptions' => [
                        'multiple' => true,
                    ],
                ]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'rateTemplate', [
                    'addon' => [
                        'append' => [
                            'content' => Html::a('费率模板管理', ['rate-template/index'], [
                                'class' => 'btn btn-info btn-flat',
                                'target' => '_blank',
                            ]),
                            'asButton' => true,
                        ],
                    ],
                ])->widget(DepDrop::class, [
                    'disabled' => !$model->isNewRecord,
                    'type' => DepDrop::TYPE_SELECT2,
                    'select2Options' => [
                        'theme' => Select2::THEME_DEFAULT,
                        'hideSearch' => true,
                    ],
                    'options' => [
                        'placeholder' => '请选择费率模板',
                    ],
                    'pluginOptions' => [
                        'depends' => [Html::getInputId($model, 'rateCapitalChannel')],
                        'initDepends' => [Html::getInputId($model, 'rateCapitalChannel')],
                        'initialize' => true,
                        'url' => Url::to(['rate-template-list', 'isDefault' => $isDefault ?? false]),
                    ],
                ]) ?>
            </div>
        </div>
    </div>

    <div id="feeBox" style="display: none">
        <div class="box-footer">&nbsp;</div>

        <div class="box-header with-border">
            <div class="box-title">信息配置</div>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'rate_number', [
                        'addon' => [
                            'append' => [
                                'content' => Html::button('生成', [
                                    'id' => 'btnGenNumber',
                                    'class' => 'btn btn-default btn-flat',
                                    'disabled' => !$model->isNewRecord,
                                ]),
                                'asButton' => true,
                            ],
                        ],
                    ])->textInput([
                        'disabled' => !$model->isNewRecord,
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_name', [
                        'addon' => [
                            'append' => [
                                'content' => Html::button('生成', [
                                    'id' => 'btnGenName',
                                    'class' => 'btn btn-default btn-flat',
                                    'disabled' => !$model->isNewRecord,
                                ]),
                                'asButton' => true,
                            ],
                        ],
                    ])->textInput([
                        'disabled' => !$model->isNewRecord,
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_status')->widget(Select2::class, [
                        'data' => Rate::STATUS_LIST,
                        'options' => [
                            'disabled' => true,
                        ],
                    ]) ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <?= $form->field($model, 'rateLateRuleNo')->widget(Select2::class, [
                        'data' => LateRule::getLateTypeMap(),
                        'options' => [
                            'prompt' => $model->getAttributeLabel('rate_late_no'),
                        ],
                        'pluginOptions' => [
                            'multiple' => true,
                        ],
                    ]) ?>

                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'periodCategory')->widget(Select2::class, [
                        'showToggleAll' => false,
                        'data' => $config['period_category_list'] ?? [],
                        'hideSearch' => true,
                        'pluginOptions' => [
                            'multiple' => true,
                        ],
                        'options' => [
                            'prompt' => $model->getAttributeLabel('periodCategory'),
                            'options' => (function () use ($config) {
                                $periodCategoryList = (array)($config['period_category_list'] ?? []);
                                $periodCategoryListId = (array)($config['period_category_list_id'] ?? []);
                                $periodCategoryListTitle = (array)($config['period_category_list_title'] ?? []);
                                $availablePeriodCategory = (array)($config['available_period_category'] ?? []);
                                foreach ($periodCategoryList as $key => &$option) {
                                    $option = [
                                        'data' => [
                                            'id' => $periodCategoryListId[$key] ?? '',
                                            'title' => $periodCategoryListTitle[$key] ?? '',
                                        ],
                                        'disabled' => !in_array($key, $availablePeriodCategory, true),
                                    ];
                                }

                                return $periodCategoryList;
                            })(),
                        ],
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_interest_year_days')->widget(Select2::class, [
                        'disabled' => !$editable,
                        'data' => Rate::INTEREST_YEAR_DAYS,
                        'hideSearch' => true,
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_effect_day')->widget(Select2::class, [
                        'disabled' => !$editable,
                        'data' => $config['rate_effect_day'] ?? [],
                        'hideSearch' => true,
                        'options' => [
                            'prompt' => $model->getAttributeLabel('rate_effect_day'),
                        ],
                    ]) ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <?= FieldRange::widget([
                        'form' => $form,
                        'model' => $model,
                        'label' => '本金适用范围(元)',
                        'attribute1' => 'rate_principal_min_amount',
                        'attribute2' => 'rate_principal_max_amount',
                        'type' => FieldRange::INPUT_WIDGET,
                        'widgetClass' => MoneyInput::class,
                        'separator' => '至',
                        'widgetOptions1' => [
                            'options' => [
                                'placeholder' => $model->getAttributeLabel('rate_principal_min_amount'),
                            ],
                        ],
                        'widgetOptions2' => [
                            'options' => [
                                'placeholder' => $model->getAttributeLabel('rate_principal_max_amount'),
                            ],
                        ],
                    ]) ?>
                </div>
                <div class="col-md-6">
                    <?= FieldRange::widget([
                        'form' => $form,
                        'model' => $model,
                        'label' => '生效日期',
                        'attribute1' => 'rate_valid_start_date',
                        'attribute2' => 'rate_valid_end_date',
                        'type' => FieldRange::INPUT_WIDGET,
                        'widgetClass' => DatePicker::class,
                        'separator' => '至',
                        'widgetOptions1' => [
                            'options' => [
                                'placeholder' => $model->getAttributeLabel('rate_valid_start_date'),
                            ],
                        ],
                        'widgetOptions2' => [
                            'options' => [
                                'placeholder' => $model->getAttributeLabel('rate_valid_end_date'),
                            ],
                        ],
                    ]) ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_month_clear_day', [
                        'addon' => [
                            'append' => [
                                'content' => Html::a('?', 'javascript:;', [
                                    'class' => 'btn btn-flat btn-default show-clear-day-help',
                                ]),
                                'asButton' => true,
                            ],
                        ],
                    ])->widget(Select2::class, [
                        'data' => $config['month_clear_day_list'],
                        'hideSearch' => true,
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_clear_day', [
                        'addon' => [
                            'append' => [
                                'content' => Html::a('?', 'javascript:;', [
                                    'class' => 'btn btn-flat btn-default show-clear-day-help',
                                ]),
                                'asButton' => true,
                            ],
                        ],
                    ])->widget(Select2::class, [
                        'data' => $config['clear_day_list'],
                        'hideSearch' => true,
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <?= $form->field($model, 'rate_repay_date_formula')->widget(Select2::class, [
//                        'disabled'   => !$editable,
                        'data' => $config['rate_repay_date_formula'],
                        'hideSearch' => true,
                    ]) ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'rate_memo')->widget(CodeEditor::class, [
                        'clientOptions' => [
                            'mode' => CodeEditor::MODE_Text,
                            'minLines' => 10,
                        ],
                    ]) ?>
                </div>
                <div class="col-md-6">
                    <?= $form->field($model, 'rate_extra_json')->widget(CodeEditor::class, [
                        'clientOptions' => [
                            'minLines' => 10,
                        ],
                    ]) ?>
                </div>
            </div>
        </div>

        <div class="box-footer">&nbsp;</div>

        <div class="box-header with-border">
            <div class="box-title">费用项</div>
        </div>
        <div class="box-body no-padding table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                <tr>
                    <th>费用</th>
                    <th>费用计算方式</th>
                    <th>费用计算值</th>
                    <th>费用取值范围</span></th>
                    <th>增值税比例</th>
                    <th>还款方式</th>
                    <th>小数进位方式</th>
                    <th>计算顺序</th>

                </tr>
                </thead>
                <tbody id="fee-table"></tbody>
            </table>
        </div>
    </div>

    <div class="box-footer">
        <?= Html::activeHiddenInput($model, 'fees') ?>
        <?= Html::submitButton('保存', [
            'id' => 'saveBtn',
            'class' => 'btn btn-primary',
            'data' => [
                'confirm' => '确定信息填写无误并保存?',
            ],
        ]) ?>
    </div>
    <?php
    ActiveForm::end() ?>
</div>

<div class="box-body" id="clear-day-help" style="display: none">
    <ol>
        <li>
            <div>D$</div>
            <ul>
                <li>含义为还款日为账单日</li>
                <li>格式为D$N，其中N取值为1-31</li>
                <li>
                    示例D$24。表示当起息日小于等于自然日24日时，还款日取对日；起息日大于自然日24日时，还款日为每月的自然日24日
                </li>
            </ul>
        </li>
        <li>
            <div>D+</div>
            <ul>
                <li>含义为还款日为自然日加固定天数</li>
                <li>格式为D+N，其中N取值为1-31</li>
                <li>示例D+1。表示还款日为起息日加1日</li>
            </ul>
        </li>
        <li>
            <div>D-</div>
            <ul>
                <li>含义为还款日为自然日减固定天数</li>
                <li>格式为D-N，其中N取值为1-31</li>
                <li>示例D-24。表示还款日为起息日减24日</li>
            </ul>
        </li>
        <li>
            <div>T+</div>
            <ul>
                <li>含义为还款日为工作日加固定天数</li>
                <li>格式为T+N，其中N取值为1-31</li>
                <li>示例T+1。表示还款日为起息日加1日</li>
            </ul>
        </li>
        <li>
            <div>T-</div>
            <ul>
                <li>含义为还款日为工作日减固定天数</li>
                <li>格式为T-N，其中N取值为1-31</li>
                <li>示例T-24。表示还款日为起息日减24日</li>
            </ul>
        </li>
    </ol>
</div>

<script>
    <?php $this->beginBlock('rate_entity_js') ?>

    $('.show-clear-day-help').on('click', function () {
        layer.open({
            type: 1,
            title: false,
            shadeClose: true, //开启遮罩关闭
            content: $('#clear-day-help')
        })
    });

    (function (aceInstance) {
        function checkContinuousInterval(interval) {
            if (interval === '') {
                return true;
            }
            if (typeof interval !== 'string') {
                return false;
            }
            // 去掉空格
            interval = interval.replace(/\s/g, '');

            // 判断是否为离散区间
            const regex_discrete = /^\{([\d\.]+,)*([\d\.]+)\}$/;
            if (regex_discrete.test(interval)) {
                // 离散区间格式正确,判断每个元素是否为数字
                const numbers = interval.slice(1, -1).split(',');
                for (let i = 1; i < numbers.length; i++) {
                    if (isNaN(parseFloat(numbers[i]))) {
                        return false;
                    }
                }
                return true;
            }

            // 判断连续区间
            const regex_continuous = /^[[(]\s*([\d\.]+)\s*,\s*([\d\.]+)\s*[)\]]$/;
            const match = interval.match(regex_continuous);
            if (match !== null) {
                const left = parseFloat(match[1]), right = parseFloat(match[2]);
                if (isNaN(left) || isNaN(right)) {
                    return false;
                }
                return left < right;

            }

            return false;
        };
        const
            confirm = window.confirm,
            feeCodeList = <?= json_encode(Fee::feeMap()) ?>,
            repayTypeList = <?= json_encode(RateConfig::REPAY_TYPE_LIST) ?>,
            calculateTypeList = <?= json_encode(RateConfig::CALCULATE_TYPE_LIST) ?>,
            carryModeList = <?= json_encode(RateConfig::ROUND_LIST) ?>,
            rateTemplateList = <?= json_encode($rateTemplateList) ?>,
            rateCapitalCodeEl = $('#<?= Html::getInputId($model, 'rateCapitalChannel') ?>'),
            rateLateNoEl = $('#<?= Html::getInputId($model, 'rateLateRuleNo') ?>'),
            rateTemplateEl = $('#<?= Html::getInputId($model, 'rateTemplate') ?>'),
            rateNumberEl = $('#<?= Html::getInputId($model, 'rate_number') ?>'),
            rateNameEl = $('#<?= Html::getInputId($model, 'rate_name') ?>'),
            periodCategoryEl = $('#<?= Html::getInputId($model, 'periodCategory') ?>'),
            rateInterestYearDaysEl = $('#<?= Html::getInputId($model, 'rate_interest_year_days') ?>'),
            rateMinAmountEl = $('#<?= Html::getInputId($model, 'rate_principal_min_amount') ?>'),
            rateMaxAmountEl = $('#<?= Html::getInputId($model, 'rate_principal_max_amount') ?>'),
            rateStartDateEl = $('#<?= Html::getInputId($model, 'rate_valid_start_date') ?>'),
            rateEndDateEl = $('#<?= Html::getInputId($model, 'rate_valid_end_date') ?>'),
            rateMonthClearDayEl = $('#<?= Html::getInputId($model, 'rate_month_clear_day') ?>'),
            rateClearDayEl = $('#<?= Html::getInputId($model, 'rate_clear_day') ?>'),
            rateEffectDayEl = $('#<?= Html::getInputId($model, 'rate_effect_day') ?>'),
            rateRepayDateFormulaEl = $('#<?= Html::getInputId($model, 'rate_repay_date_formula') ?>'),
            rateFeesEl = $('#<?= Html::getInputId($model, 'fees') ?>'),
            rateExtraJsonEl = aceInstance['<?= Html::getInputId($model, 'rate_extra_json') ?>'],
            rateMemoEl = aceInstance['<?= Html::getInputId($model, 'rate_memo') ?>'],
            feeTableEl = $('#fee-table'),
            dropdown = function (value, list) {
                let select = ['<select class="form-control" style="width:250px" <?= $model->rate_status !== Rate::STATUS_OPEN ? 'disabled'
                    : ''?>>']
                for (const [key, txt] of Object.entries(list)) {
                    select.push(`<option value="${key}"${key === value ? ' selected' : ''}>${txt}</option>`)
                }
                select.push('</select>')
                return select.join('')
            },
            input = function (value, calculateType) {
                return `<div class="input-group" style="width:80px">
                    <input value="${value}" <?= $model->rate_status !== Rate::STATUS_OPEN ? 'disabled' : ''?> class="form-control"/>
                    <span class="input-group-addon">${calculateType === 'fixed_amount' ? '元' : '%'}</span>
                </div>`
            },
            inputD = function (value) {
                return `<input value="${value}" disabled class="form-control" style="width:100px"/>`
            },
            inputR = function (value) {
                return `<input value="${value}"  class="form-control value-range"/>`
            },
            inputVR = function (value) {
                return `<div class="input-group" style="width:100px"><input value="${value}"  class="form-control vat-rate"/><span class="input-group-addon">%</span></div>`
            },
            updateAddon = function (e) {
                $(e.target).parents('td').next().find('span.input-group-addon')
                    .text(e.target.value === 'fixed_amount' ? '元' : '%')
            },
            renderFeeTable = function (fees) {
                fees = JSON.parse(fees)
                let tr = ''
                fees.forEach((fee, i) => {
                    let icon = 'fa fa-fw fa-minus-square-o'
                    if (fees[i + 1] && fees[i + 1].__level > fee.__level) {
                        icon = 'fa fa-fw fa-plus-square-o'
                    }
                    tr += `<tr>
                            <td style="vertical-align: middle"><span style="margin-left:${fee.__level * 16}px" class="${icon}"></span>${feeCodeList[fee.feeCode]}</td>
                            <td class="editable">${dropdown(fee.calculateType, calculateTypeList)}</td>
                            <td class="editable">${input(fee.value, fee.calculateType)}</td>
                            <td>${inputR(fee.valueRange === undefined ? '' : fee.valueRange)}</td>
                            <td>${inputVR(fee.vatRate === undefined ? '' : fee.vatRate)}</td>
                            <td>${inputD(repayTypeList[fee.repayType])}</td>
                            <td class="editable">${dropdown(fee.carryMode, carryModeList)}</td>
                            <td>${inputD(fee.executionOrder)}</td>
                           </tr>`
                })
                feeTableEl.html(tr).on('change', 'select', updateAddon)
                $('.value-range:last').after($('<span class="text-danger" >取值范围,连续区间:支持全开(1,10)、全闭[1,10]、左开右闭(1,10]、左闭右开[1,10), 离散区间：{0.00,35.28,36.00}</span>'))
            },
            genParams = function () {
                let id = '', title = '';
                periodCategoryEl.find('option:selected').each(function () {
                    const self = $(this);
                    id += self.data('id') + '_';
                    title += self.data('title');
                });
                id = id.replace(/_$/, '')

                const capitalName = rateCapitalCodeEl.find('option:selected').text(),
                    symbolIndex = capitalName.lastIndexOf('-'),
                    name = capitalName.substr(0, symbolIndex),
                    alias = capitalName.substr(symbolIndex + 1)

                return {alias, name, id, title}
            },
            genNumber = function () {
                if (rateNumberEl.attr('disabled') !== undefined) {
                    return false
                }
                try {
                    const p = genParams(),
                        d = new Date(),
                        date = d.getFullYear() + `${d.getMonth() + 1}`.padStart(2, '0') + `${d.getDate()}`.padStart(2, '0'),
                        rateNum = [p.alias, p.id, date].join('_');

                    rateNumberEl.val(rateNum)
                } catch (e) {
                }
            },
            genName = function () {
                if (rateNameEl.attr('disabled') !== undefined) {
                    return false
                }
                try {
                    const p = genParams()
                    rateNameEl.val(p.name + p.title)
                } catch (e) {
                }
            };

        $('#btnGenNumber').on('click', genNumber)
        $('#btnGenName').on('click', genName)
        periodCategoryEl.on('change.select2', function (e) {
            genNumber()
            genName()
        })

        rateTemplateEl.on('depdrop:change change', function (e) {
            const box = $('#feeBox')
            if (!e.target.value) {
                box.hide(0)
                return false
            }

            box.show(0)
            const template = rateTemplateList[e.target.value];
            periodCategoryEl.val(template['periodCategory']).trigger('change.select2')
            rateLateNoEl.val(template['lateNo']).trigger('change.select2')
            rateInterestYearDaysEl.val(template['yearDayNum']).trigger('change.select2')
            rateMinAmountEl.val(template['minAmount'])
            rateMaxAmountEl.val(template['maxAmount'])
            rateStartDateEl.kvDatepicker('setDate', new Date(template['startDate']))
            rateEndDateEl.kvDatepicker('setDate', new Date(template['endDate']))
            rateMonthClearDayEl.val(template['monthClearDay']).trigger('change.select2')
            rateClearDayEl.val(template['clearDay']).trigger('change.select2')
            rateEffectDayEl.val(template['effectDay']).trigger('change.select2')
            rateRepayDateFormulaEl.val(template['repayDateFormula']).trigger('change.select2')
            rateExtraJsonEl.setValue(template['extraJson'])
            rateMemoEl.setValue(template['memo'])
            genNumber()
            genName()
            renderFeeTable(template['fees'])
        })

        $('#saveBtn').click(function () {
            let finish = true;
            const feesTemplate = JSON.parse(rateTemplateList[rateTemplateEl.val()]['fees'])
            feeTableEl.find('tr').each(function (i) {
                const self = $(this)
                feesTemplate[i].calculateType = self.find('select:eq(0)').val()
                feesTemplate[i].value = self.find('input:eq(0)').val()
                feesTemplate[i].carryMode = self.find('select:eq(1)').val()
                feesTemplate[i].valueRange = self.find('input:eq(1)').val()
                feesTemplate[i].vatRate = self.find('input:eq(2)').val()
                if (!checkContinuousInterval(feesTemplate[i].valueRange)) {
                    window.alert('取值范围格式错误:' + feesTemplate[i].valueRange)
                    finish = false
                    window.confirm = function () {
                        return false
                    }
                    return false;
                }
            })
            //验证蝌蚪贷
            let periodList = $(periodCategoryEl).val(),
                tadpoleCount = periodList.reduce((sum, item) => sum + item.includes('tadpole'), 0),
                result = tadpoleCount === 0 || tadpoleCount === periodList.length;
            if (!result) {
                window.alert('期次类型如果携带蝌蚪贷，所选的类型必须全是蝌蚪贷');
                return false;
            }
            rateFeesEl.val(JSON.stringify(feesTemplate))
            if (finish) {
                window.confirm = confirm
            }
        })
    })(aceInstance)
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['rate_entity_js']) ?>
</script>