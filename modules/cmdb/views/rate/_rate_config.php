<?php

use cmdb\models\Fee;
use cmdb\models\Rate;
use cmdb\models\RateConfig;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View $this
 * @var Rate $model
 */

$rateConfigs = $model->getRateConfigs()->asArray()->all();
$rateConfigs = iterator_to_array(RateConfig::arrange($rateConfigs));

$feeList = Fee::find()
    ->where([
        'fee_code' => array_column($rateConfigs, 'rate_config_fee_code'),
    ])
    ->select('fee_name')
    ->indexBy('fee_code')
    ->column();

$layout = <<<EOF
<div class="box-header with-border">
    <div class="box-title">费用项</div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
EOF;

echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => new ArrayDataProvider([
        'key' => 'rate_config_id',
        'allModels' => $rateConfigs,
        'pagination' => false,
        'sort' => false,
    ]),
    'columns' => [
        [
            'label' => '费用',
            'format' => 'html',
            'value' => function ($rateConfig, $key, $index) use ($rateConfigs, $feeList) {
                $options = [
                    'class' => 'fa fa-fw',
                    'style' => sprintf('margin-left: %dpx', 16 * $rateConfig['__level']),
                ];
                if ($rateConfig['__level'] + 1 === ($rateConfigs[$index + 1]['__level'] ?? null)) {
                    Html::addCssClass($options, 'fa-plus-square-o');
                } else {
                    Html::addCssClass($options, 'fa-minus-square-o');
                }

                return Html::tag('span', '', $options) . PHP_EOL . $feeList[$rateConfig['rate_config_fee_code']];
            },
        ],
        [
            'label' => '费用计算方式',
            'format' => ['in', RateConfig::CALCULATE_TYPE_LIST],
            'attribute' => 'rate_config_calculate_type',
        ],
        [
            'label' => '费用计算值',
            'value' => function ($rateConfig) {
                return RateConfig::valueFormat($rateConfig);
            },
        ],
        [
            'label' => '费用取值范围',
            'attribute' => 'rate_config_value_range',
        ],
        [
            'label' => '增值税比例',
            'attribute' => 'rate_config_vat_rate',
            'value' => function ($rateConfig) {
                return floatval($rateConfig['rate_config_vat_rate']) . '%';
            },
        ],
        [
            'label' => '还款方式',
            'format' => ['in', RateConfig::REPAY_TYPE_LIST],
            'attribute' => 'rate_config_repay_type',
        ],
        [
            'label' => '小数进位方式',
            'format' => ['in', RateConfig::ROUND_LIST],
            'attribute' => 'rate_config_carry_mode',
        ],
        [
            'label' => '计算顺序',
            'attribute' => 'rate_config_execution_order',
        ],
    ],
]);
