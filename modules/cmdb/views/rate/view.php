<?php

use Carbon\Carbon;
use cmdb\approvals\RateApproval;
use cmdb\models\Product;
use cmdb\models\ProductRate;
use cmdb\models\Rate;
use cmdb\models\RateEntity;
use cmdb\widgets\DetailView;
use xlerr\common\assets\LayerAsset;
use xlerr\common\helpers\MoneyHelper;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model RateEntity */

LayerAsset::register($this);

$this->title = '费率详情 ' . $model->rate_number;
$this->params['breadcrumbs'][] = ['label' => '费率配置', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

$isVoid = $model->isVoid() ? 'disabled' : '';

$productRate = $model->getProductRate()->with(['product'])->all();
$relations = array_column($productRate, 'product_rate_status', 'product_rate_scope');

$editable = in_array($model->rate_status, [Rate::STATUS_OPEN, Rate::STATUS_PASS]);
$btnList = [
    //    Html::a('快速编辑', ($editable ? ['update', 'id' => $model->rate_id] : 'javascript:;'), [
    //        'class'    => 'btn btn-primary',
    //        'disabled' => !$editable,
    //    ]),
    Html::a('编辑', ($editable ? ['update', 'id' => $model->rate_id] : 'javascript:;'), [
        'class' => 'btn btn-primary',
        'disabled' => !$editable,
    ]),
    Html::a('试算', ['trial/index', 'rate_id' => $model->rate_id], [
        'target' => '_blank',
        'class' => 'btn btn-primary',
    ]),
];

$auditing = !empty((new RateApproval())->queryNotEndOfBusiness(['id' => $model->rate_id]));

if ($auditing) {
    $btnList[] = Html::button('审核中', [
        'class' => 'btn btn-warning',
        'disabled' => true,
    ]);
} else {
    if (in_array($model->rate_status, [Rate::STATUS_OPEN, Rate::STATUS_OVERDUE])) {
        $btnList[] = Html::a('申请上线', ['approval', 'id' => $model->rate_id], [
            'class' => 'btn btn-success',
            'title' => '申请上线',
            'data' => [
                'confirm' => '确定要发起申请上线审核吗?',
                'method' => 'post',
            ],
        ]);
    } elseif ($model->rate_status == Rate::STATUS_PASS) {
        $btnList[] = Html::a('仅收罚息', ['overdue-rate', 'id' => $model->rate_id], [
            'class' => 'btn btn-warning',
            'title' => '仅收罚息',
            'data' => [
                'confirm' => '确定仅收罚息吗?',
                'method' => 'post',
            ],
        ]);
    }
    if ($model->rate_status == Rate::STATUS_OPEN) {
        $btnList[] = Html::a('作废', ['void-rate', 'id' => $model->rate_id], [
            'class' => 'btn btn-danger',
            'title' => '作废',
            'data' => [
                'confirm' => '确定要作废吗?',
                'method' => 'post',
            ],
        ]);
    }
}
$btnList[] = Html::a('返回列表', ['index'], [
    'class' => 'btn btn-default',
]);
echo Html::tag('p', implode(' ', $btnList));
?>

<div class="box box-info">
    <div class="box-header with-border">
        <div class="box-title">费率参数详情</div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'krajeeDialogSettings' => [
                'overrideYiiConfirm' => false,
            ],
            'enableEditMode' => false,
            'attributes' => [
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_number',
                        ],
                        [
                            'label' => '资金方名称',
                            'format' => 'raw',
                            'value' => function () use ($model) {
                                return implode(
                                    ',',
                                    array_column($model->getCapital(), 'capital_name')
                                ) ?? '-';
                            },
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_name',
                        ],
                        [
                            'label' => '适用本金范围（元）',
                            'value' => vsprintf('%s - %s', [
                                MoneyHelper::f2y($model->rate_principal_min_amount, true),
                                MoneyHelper::f2y($model->rate_principal_max_amount, true),
                            ]),
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'label' => Html::activeLabel($model, 'periodCategory'),
                            'value' => $model->periodCategoryLabel(),
                        ],
                        [
                            'attribute' => 'rate_interest_year_days',
                            'format' => ['in', Rate::INTEREST_YEAR_DAYS],
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_status',
                            'format' => ['in', Rate::STATUS_LIST],
                        ],
                        [
                            'label' => '生效日期',
                            'value' => vsprintf('%s - %s', [
                                Carbon::parse($model->rate_valid_start_date)->toDateString(),
                                Carbon::parse($model->rate_valid_end_date)->toDateString(),
                            ]),
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'label' => Html::activeLabel($model, 'rate_late_no'),
                            'format' => 'raw',
                            'value' => function () use ($model) {
                                return implode(
                                    ',',
                                    array_column($model->getLate(), 'late_name')
                                ) ?? '-';
                            },
                        ],
                        [
                            'label' => Html::activeLabel($model, 'rate_effect_day'),
                            'value' => $model->rate_effect_day,
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_month_clear_day',
                        ],
                        [
                            'attribute' => 'rate_clear_day',
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_create_at',
                        ],
                        [
                            'attribute' => 'rate_update_at',
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_extra_json',
                            'format' => 'ntext',
                        ],
                        [
                            'attribute' => 'rate_memo',
                            'format' => 'ntext',
                        ],
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'rate_repay_date_formula',
                            'format' => ['in', RateEntity::config('rate_repay_date_formula')],
                        ],
                        [
                            'label' => '',
                            'value' => function () {
                                return '';
                            },
                        ],
                    ],
                ],
            ],
        ]) ?>
    </div>
</div>

<?php
echo $this->render('_rate_config', [
    'model' => $model,
    'rateConfigs' => $model->rateConfigs,
    'disabled' => false,
    'view' => true,
]);

// 已绑定的场景
ActiveForm::begin([
    'action' => Url::to(['relation', 'id' => $model->rate_id]),
]);
$layout = <<<HTML
<div class="box-header with-border">
    <div class="box-title">绑定的场景</div>
    <div class="box-tools pull-right">
        <button type="button" class="btn btn-sm btn-success" $isVoid id="bindBtn">绑定场景</button>
        <button type="submit" class="btn btn-sm btn-danger" $isVoid id="unbindBtn" data-confirm="确定将选中场景与当前费率的绑定关系解除吗?">将选中场景解除绑定</button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
HTML;

echo Gridview::widget([
    'layout' => $layout,
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getProducts()->orderBy(['product_id' => SORT_DESC]),
        'pagination' => false,
        'sort' => false,
    ]),
    'columns' => [
        [
            'attribute' => 'product_scope',
            'format' => 'raw',
            'value' => function ($row) {
                return Html::a($row['product_scope'], ['product/view', 'id' => $row['product_id']], [
                    'data-pjax' => 0, // 让pjax忽略该链接
                    'target' => '_blank',
                ]);
            },
        ],
        [
            'attribute' => 'product_condition',
            'format' => ['NJson', 50],
        ],
        'product_memo:ntext',
        [
            'class' => CheckboxColumn::class,
            'name' => 'scope',
            'checkboxOptions' => function (Product $product) use ($model) {
                return [
                    'value' => $product->product_scope,
                    'disabled' => $model->isVoid(),
                ];
            },
        ],
    ],
]);
ActiveForm::end();
?>

<div id="product-list" style="padding: 15px; background-color: #ecf0f5; min-height: 100%; display: none">
    <?php
    ActiveForm::begin([
        'action' => Url::to(['relation', 'id' => $model->rate_id]),
    ]);

    echo Html::hiddenInput('state', 1);

    echo Gridview::widget([
        'layout' => '<div class="box-header with-border">
    <div class="box-title">待绑定的场景</div>
    <div class="box-tools pull-right">
        <button type="submit" class="btn btn-sm btn-primary" id="submitBindBtn" data-confirm="确定要将选中的场景与当前费率绑定吗?">绑定选中场景</button>
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>',
        'dataProvider' => new ActiveDataProvider([
            'query' => Product::find()->where([
                'product_status' => Product::STATUS_ONLINE,
            ])->andWhere([
                'not in',
                'product_scope',
                ProductRate::find()->where([
                    'product_rate_number' => $model->rate_number,
                ])->select('product_rate_scope'),
            ])->orderBy(['product_id' => SORT_DESC]),
            'pagination' => false,
            'sort' => false,
        ]),
        'columns' => [
            [
                'attribute' => 'product_scope',
                'format' => 'raw',
                'value' => function ($row) {
                    return Html::a($row['product_scope'], ['product/view', 'id' => $row['product_id']], [
                        'data-pjax' => 0, // 让pjax忽略该链接
                        'target' => '_blank',
                    ]);
                },
            ],
            [
                'attribute' => 'product_condition',
                'format' => ['NJson', 50],
            ],
            'product_memo:ntext',
            [
                'class' => CheckboxColumn::class,
                'name' => 'scope',
                'checkboxOptions' => function (Product $product) {
                    return [
                        'value' => $product->product_scope,
                    ];
                },
            ],
        ],
    ]);

    ActiveForm::end();
    ?>
</div>
<?php
echo $this->render('_rate_var_num', [
    'model' => $model,
    'disabled' => false,
    'view' => true,
]);
?>
<script>
    <?php $this->beginBlock('main') ?>
    $('#bindBtn').click(function () {
        layer.open({
            type: 1,
            title: false,
            area: ['80%', '90%'],
            shadeClose: true, //开启遮罩关闭
            content: $('#product-list')
        })
    })
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['main']) ?>
</script>
