<?php

use cmdb\models\Rate;
use cmdb\models\RateVarNum;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\View;

/** @var  $rateVars RateVarNum */
/**
 * @var View $this
 * @var Rate $model
 */
$vars = $model->getRateVarNums()->asArray()->all();
$layout = <<<EOF
<div class="box-header with-border">
    <div class="box-title">动态费率编号</div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
EOF;

echo GridView::widget([
    'layout' => $layout,
    'dataProvider' => new ArrayDataProvider([
        'key' => 'rate_var_num_id',
        'allModels' => $vars,
        'pagination' => false,
        'sort' => false,
    ]),
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{trial}',
            'buttons' => [
                'trial' => function ($url, $varModel) use ($model) {
                    return Html::a(
                        '试算',
                        ['trial/index', 'rate_id' => $varModel['rate_var_num_id'] ?? null, 'var' => 1],
                        [
                            'target' => '_blank',
                            'class' => 'btn btn-primary',
                        ]
                    );
                },
            ],
        ],
        [
            'label' => '动态费率编号',
            'attribute' => 'rate_var_num_code',
        ],
        [
            'label' => '原始动态费率编号',
            'attribute' => 'rate_var_num_raw_code',
        ],
        [
            'label' => '源费率编号',
            'attribute' => 'rate_var_num_source_num',
        ],
        [
            'label' => '罚息规则编码',
            'attribute' => 'rate_var_num_late_num',
        ],
        [
            'label' => '状态',
            'format' => ['in', RateVarNum::STATUS_LIST],
            'attribute' => 'rate_var_num_status',
        ],
    ],
]);