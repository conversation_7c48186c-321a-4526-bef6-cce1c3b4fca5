<?php

use Carbon\Carbon;
use cmdb\models\Rate;
use cmdb\models\RateEntity;
use cmdb\models\RateSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\helpers\MoneyHelper;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $auditDataProvider ArrayDataProvider */
/* @var $searchModel RateSearch */

$this->title = '费率管理页面';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', [
    'model' => $searchModel,
]);

if ($auditDataProvider->getTotalCount()) {
    echo GridView::widget([
        'dataProvider' => $auditDataProvider,
        'options' => [
            'class' => 'box box-warning',
        ],
        'layout' => '<div class="box-body table-responsive no-padding">{items}</div>',
        'rowOptions' => function (Rate $model) {
            return [
                'style' => 'background-color: ' . [
                        Rate::STATUS_PASS => '#dff0d8',
                        Rate::STATUS_OVERDUE => '#fcf8e3',
                    ][$model->audit->approval->getData('status')],
            ];
        },
        'columns' => [
            [
                'header' => '待审核数据',
                'class' => ActionColumn::class,
                'template' => '{audit} {undo}',
                'buttons' => [
                    'audit' => function ($url) {
                        return ActionColumn::newButton('审核', $url, [
                            'class' => 'btn-facebook',
                            'target' => '_blank',
                            'data' => [
                                'method' => 'post',
                            ],
                        ]);
                    },
                    'undo' => function ($url) {
                        return ActionColumn::newButton('撤销', $url, [
                            'class' => 'btn-danger',
                            'data' => [
                                'confirm' => '您确定要撤销该审核吗',
                                'method' => 'post',
                            ],
                        ]);
                    },
                ],
                'visibleButtons' => [
                    'audit' => function (Rate $model) {
                        return $model->audit->approvalEntries->auditable();
                    },
                    'undo' => function (Rate $model) {
                        return $model->audit->audit_creator_id === Yii::$app->getUser()->getId();
                    },
                ],
            ],
            [
                'attribute' => 'rate_number',
                'format' => 'raw',
                'value' => function (Rate $model) {
                    return Html::a($model->rate_number, ['view', 'id' => $model->rate_id], [
                        'title' => '查看详情',
                        'target' => '_blank',
                    ]);
                },
            ],
            'rate_name',
            [
                'label' => '适用本金额范围（元）',
                'attribute' => 'rate_principal_min_amount',
                'value' => function (Rate $model) {
                    if ($model->rate_principal_min_amount == $model->rate_principal_max_amount) {
                        return MoneyHelper::f2y($model->rate_principal_min_amount, true);
                    }

                    return vsprintf('%s - %s', [
                        MoneyHelper::f2y($model->rate_principal_min_amount, true),
                        MoneyHelper::f2y($model->rate_principal_max_amount, true),
                    ]);
                },
            ],
            [
                'attribute' => 'periodCategory',
                'value' => function (RateEntity $model) {
                    return $model->periodCategoryLabel();
                },
            ],
            [
                'attribute' => 'rate_interest_year_days',
            ],
            [
                'label' => '当前状态',
                'attribute' => 'rate_status',
                'format' => ['in', Rate::STATUS_LIST],
                'value' => function (Rate $model) {
                    return $model->getOldAttribute('rate_status');
                },
            ],
            [
                'label' => '变更后状态',
                'attribute' => 'rate_status',
                'format' => ['in', Rate::STATUS_LIST],
            ],
            [
                'label' => '生效日期范围',
                'attribute' => 'rate_valid_start_date',
                'format' => 'raw',
                'value' => function ($model) {
                    return vsprintf('%s - %s', [
                        Carbon::parse($model->rate_valid_start_date)->toDateString(),
                        Carbon::parse($model->rate_valid_end_date)->toDateString(),
                    ]);
                },
            ],
            [
                'label' => '资金方',
                'format' => 'raw',
                'value' => function (RateEntity $model) {
                    return implode(
                        ',',
                        array_column($model->getCapital(), 'capital_name')
                    ) ?? '-';
                },
            ],
        ],
    ]);
}
echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'header' => '操作',
            'template' => '{trial}',
            'buttons' => [
                'trial' => function ($url, $model, $key) {
                    return ActionColumn::newButton('试算', ['trial/index', 'rate_id' => $key], [
                        'title' => '试算',
                        'target' => '_blank',
                        'class' => 'btn-success',
                    ]);
                },
            ],
        ],
        [
            'attribute' => 'rate_number',
            'format' => 'raw',
            'value' => function (RateSearch $model) {
                return Html::a($model->rate_number, ['view', 'id' => $model->rate_id], [
                    'title' => '查看详情',
                    'target' => '_blank',
                ]);
            },
        ],
        'rate_name',
        [
            'label' => '绑定的产品',
            'format' => 'raw',
            'value' => function (RateSearch $model) {
                $data = $model->getProductRate()->distinct()->limit(3)->select('product_rate_scope')->asArray()->all();
                $data = array_column($data, 'product_rate_scope');
                if (count($data) > 2) {
                    $content = implode(', ', array_splice($data, 0, 2)) . '...';

                    return Html::a($content, ['view', 'id' => $model->rate_id, '#' => 'bind-relations']);
                } else {
                    return implode(', ', $data);
                }
            },
        ],
        [
            'label' => '适用本金额范围（元）',
            'attribute' => 'rate_principal_min_amount',
            'value' => function (RateSearch $model) {
                if ($model->rate_principal_min_amount == $model->rate_principal_max_amount) {
                    return MoneyHelper::f2y($model->rate_principal_min_amount, true);
                }

                return vsprintf('%s - %s', [
                    MoneyHelper::f2y($model->rate_principal_min_amount, true),
                    MoneyHelper::f2y($model->rate_principal_max_amount, true),
                ]);
            },
        ],
        [
            'attribute' => 'periodCategory',
            'value' => function (RateSearch $model) {
                return $model->periodCategoryLabel();
            },
        ],
        [
            'attribute' => 'rate_interest_year_days',
        ],
        [
            'attribute' => 'rate_status',
            'format' => ['in', Rate::STATUS_LIST],
        ],
        [
            'label' => '生效日期范围',
            'attribute' => 'rate_valid_start_date',
            'format' => 'raw',
            'value' => function ($model) {
                return vsprintf('%s - %s', [
                    Carbon::parse($model->rate_valid_start_date)->toDateString(),
                    Carbon::parse($model->rate_valid_end_date)->toDateString(),
                ]);
            },
        ],
        [
            'label' => '资金方',
            'format' => 'raw',
            'value' => function (Rate $model) {
                return implode(',', $model->getRateCapitalChannel());
            },
        ],
        [
            'label' => '罚息规则',
            'format' => 'raw',
            'value' => function (Rate $model) {
                return implode(',', $model->getRateLateRuleNo());
            },
        ],
    ],
]);
