<?php

use cmdb\models\Capital;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model \cmdb\models\RateTemplateSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php
        $form = ActiveForm::begin([
            'action'        => ['index'],
            'method'        => 'get',
            'type'          => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'rate_template_name') ?>

        <?= $form->field($model, 'capitalCode', [
            'options' => [
                'style' => 'min-width: 160px',
            ],
        ])->widget(Select2::class, [
            'data'          => Capital::getCapitalCodeMap(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options'       => [
                'prompt' => '资金方',
            ],
        ]) ?>

        <div class="form-group">
            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
            <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>
            <?= Html::a('创建模板', ['create'], ['class' => 'btn btn-success']) ?>
        </div>

        <?php
        ActiveForm::end(); ?>

    </div>
</div>