<?php

use cmdb\models\RateTemplate;
use cmdb\models\RateTemplateSearch;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel RateTemplateSearch */

$this->title                   = '费率模板';
$this->params['breadcrumbs'][] = $this->title;


echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns'      => [
        [
            'class'    => 'xlerr\common\grid\ActionColumn',
            'template' => '{update} {delete}',
        ],

        'rate_template_id',
        'rate_template_name',
        [
            'label' => '适用资金方',
            'value' => function (RateTemplate $model) {
                return implode(', ', array_column($model->capitals, 'capital_name'));
            },
        ],
        [
            'attribute' => 'rate_template_status',
            'format'    => ['in', RateTemplate::STATUS_LIST],
        ],
        //        'rate_template_config:ntext',
        'rate_template_created_at',
        'rate_template_updated_at',
        // 'rate_template_created_by',
        // 'rate_template_updated_by',

    ],
]);
