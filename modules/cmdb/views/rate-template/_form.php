<?php

use cmdb\models\Capital;
use cmdb\models\Fee;
use cmdb\models\LateRule;
use cmdb\models\Rate;
use cmdb\models\RateConfig;
use cmdb\models\RateEntity;
use cmdb\models\RateTemplate;
use cmdb\models\RateTemplateEntity;
use kartik\field\FieldRange;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\assets\LayerAsset;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\MoneyInput;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model RateTemplate */
/* @var $entity RateTemplateEntity */
/* @var $fee RateConfig */
/* @var $rate Rate */

LayerAsset::register($this);

$config = RateEntity::config();

$form = ActiveForm::begin();
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">
            模板信息
        </div>
    </div>

    <div class="box-body">
        <div class="row">
            <div class="col-md-3">
                <?= $form->field($model, 'rate_template_name')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-8">
                <?= $form->field($entity, 'capitals', [
                    'addon' => [
                        'append' => [
                            'content' => Html::a('资金方管理', ['capital/index'], [
                                'class' => 'btn btn-info btn-flat',
                                'target' => '_blank',
                            ]),
                            'asButton' => true,
                        ],
                    ],
                ])->widget(Select2::class, [
                    'data' => Capital::getCapitalCodeMap(),
                    'pluginOptions' => [
                        'multiple' => true,
                    ],
                ]) ?>
            </div>
            <div class="col-md-1">
                <?= $form->field($model, 'rate_template_status')->widget(Select2::class, [
                    'data' => RateTemplate::STATUS_LIST,
                    'hideSearch' => true,
                ]) ?>
            </div>
        </div>
    </div>

    <div class="box-footer">&nbsp;</div>

    <div class="box-header with-border">
        <div class="box-title">
            费率信息
        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-3">
                <?= $form->field($entity, 'lateNo')->widget(Select2::class, [
                    'data' => LateRule::getLateTypeMap(),
                    'pluginOptions' => [
                        'multiple' => true,
                    ],
                ]) ?>
            </div>

            <div class="col-md-3">
                <?= $form->field($entity, 'periodCategory')->widget(Select2::class, [
                    'data' => $config['period_category_list'] ?? [],
                    'showToggleAll' => false,
                    'pluginOptions' => [
                        'multiple' => true,
                    ],
                    'options' => [
                        'options' => (function () use ($config) {
                            $periodCategoryList = (array)($config['period_category_list'] ?? []);
                            $availablePeriodCategory = (array)($config['available_period_category'] ?? []);
                            foreach ($periodCategoryList as $key => &$option) {
                                $option = [
                                    'disabled' => !in_array($key, $availablePeriodCategory, true),
                                ];
                            }

                            return $periodCategoryList;
                        })(),
                    ],
                ]) ?>
            </div>

            <div class="col-md-3">
                <?= $form->field($entity, 'yearDayNum')->widget(Select2::class, [
                    'data' => Rate::INTEREST_YEAR_DAYS,
                    'hideSearch' => true,
                ]) ?>
            </div>

            <div class="col-md-3">
                <?= $form->field($entity, 'effectDay')->widget(Select2::class, [
                    'data' => $config['rate_effect_day'] ?? [],
                    'hideSearch' => true,
                ]) ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <?= FieldRange::widget([
                    'form' => $form,
                    'model' => $entity,
                    'label' => '本金适用范围(元)',
                    'attribute1' => 'minAmount',
                    'attribute2' => 'maxAmount',
                    'type' => FieldRange::INPUT_WIDGET,
                    'widgetClass' => MoneyInput::class,
                    'separator' => '至',
                    'widgetOptions1' => [
                        'options' => [
                            'placeholder' => $entity->getAttributeLabel('minAmount'),
                        ],
                    ],
                    'widgetOptions2' => [
                        'options' => [
                            'placeholder' => $entity->getAttributeLabel('maxAmount'),
                        ],
                    ],
                ]) ?>
            </div>

            <div class="col-md-6">
                <?= FieldRange::widget([
                    'form' => $form,
                    'model' => $entity,
                    'label' => '生效日期',
                    'attribute1' => 'startDate',
                    'attribute2' => 'endDate',
                    'type' => FieldRange::INPUT_WIDGET,
                    'widgetClass' => DatePicker::class,
                    'separator' => '至',
                    'widgetOptions1' => [
                        'options' => [
                            'placeholder' => $entity->getAttributeLabel('startDate'),
                        ],
                    ],
                    'widgetOptions2' => [
                        'options' => [
                            'placeholder' => $entity->getAttributeLabel('endDate'),
                        ],
                    ],
                ]) ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <?= $form->field($entity, 'monthClearDay', [
                    'addon' => [
                        'append' => [
                            'content' => Html::a('?', 'javascript:;', [
                                'class' => 'btn btn-flat btn-default show-clear-day-help',
                            ]),
                            'asButton' => true,
                        ],
                    ],
                ])->widget(Select2::class, [
                    'data' => $config['month_clear_day_list'],
                    'hideSearch' => true,
                ]) ?>
            </div>
            <div class="col-md-3">
                <?= $form->field($entity, 'clearDay', [
                    'addon' => [
                        'append' => [
                            'content' => Html::a('?', 'javascript:;', [
                                'class' => 'btn btn-flat btn-default show-clear-day-help',
                            ]),
                            'asButton' => true,
                        ],
                    ],
                ])->widget(Select2::class, [
                    'data' => $config['clear_day_list'],
                    'hideSearch' => true,
                ]) ?>
            </div>
            <div class="col-md-3">
                <?= $form->field($entity, 'repayDateFormula')->widget(Select2::class, [
                    'data' => $config['rate_repay_date_formula'] ?? [],
                    'hideSearch' => true,
                ]) ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <?= $form->field($entity, 'memo')->widget(CodeEditor::class, [
                    'clientOptions' => [
                        'mode' => CodeEditor::MODE_Text,
                        'minLines' => 10,
                    ],
                ]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($entity, 'extraJson')->widget(CodeEditor::class, [
                    'clientOptions' => [
                        'minLines' => 10,
                    ],
                ]) ?>
            </div>
        </div>

    </div>

    <div class="box-footer">&nbsp;</div>

    <div class="box-header with-border">
        <div class="box-title">
            费用项
        </div>
        <div class="box-tools">
            <button type="button" class="btn btn-success btn-sm pull-right" data-target="#addRateConfig"
                    data-toggle="modal">
                添加费用
            </button>
        </div>
    </div>
    <div class="box-body no-padding">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th>费用</th>
                <th>费用计算方式</th>
                <th>费用计算值</th>
                <th>费用取值范围</th>
                <th>增值税比例</th>
                <th>还款方式</th>
                <th>小数进位方式</th>
                <th>计算顺序</th>
                <th class="action-column">&nbsp;</th>
            </tr>
            </thead>
            <tbody id="fee-table"></tbody>
        </table>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('取消', ['index'], ['class' => 'btn btn-default']) ?>
    </div>
</div>

<?php

echo Html::activeHiddenInput($entity, 'fees');

ActiveForm::end();

?>

<div class="modal fade" id="addRateConfig" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">新增费用参数</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">费用</label>
                            <div class="input-group">
                                <?= Select2::widget([
                                    'name' => '__fee_code',
                                    'data' => Fee::feeMap(true),
                                    'hideSearch' => true,
                                ]) ?>
                                <span class="input-group-btn">
                                    <?= Html::a('费管理', ['fee/index'], [
                                        'class' => 'btn btn-info btn-flat',
                                        'target' => '_blank',
                                    ]) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">父级费用</label>
                            <?= Select2::widget([
                                'name' => '__parent',
                                'data' => ['' => '顶级费用项'],
                                'hideSearch' => true,
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">还款方式</label>
                            <?= Select2::widget([
                                'name' => '__repay_type',
                                'data' => RateConfig::REPAY_TYPE_LIST,
                                'hideSearch' => true,
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">
                                计算方式
                                <a href="###" data-toggle="popover"
                                   data-placement="right"
                                   data-trigger="focus"
                                   title="计算方式"
                                   data-html="true"
                                   data-content="<p>年化综合息费：除等额本息差、倒减方式其于均支持.</p>
                                                      <p>资方成本费用：除等额本息差、倒减方式其于均支持.</p>
                                                      <p>其它费用：计算方式均可配置.</p>">
                                    ?
                                </a>
                            </label>
                            <?= Select2::widget([
                                'name' => '__calculate_type',
                                'data' => RateConfig::CALCULATE_TYPE_LIST,
                                'hideSearch' => true,
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">进位方式</label>
                            <?= Select2::widget([
                                'name' => '__carry_mode',
                                'data' => RateConfig::ROUND_LIST,
                                'hideSearch' => true,
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">费用计算值</label>
                            <div class="input-group">
                                <?= Html::textInput('__value', null, [
                                    'class' => 'form-control',
                                ]) ?>
                                <div class="input-group-addon" id="div-unit">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">计算顺序</label>
                            <?= Html::textInput('__execution_order', 10, [
                                'class' => 'form-control',
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">费用取值范围</label>
                            <?= Html::textInput('__value_range', null, [
                                'class' => 'form-control',
                            ]) ?>
                            <span class="text-danger">取值范围,连续区间:支持全开(1,10)、全闭[1,10]、左开右闭(1,10]、左闭右开[1,10), 离散区间：{0.00,35.28,36.00}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">增值税比例</label>
                            <div class="input-group">
                                <?= Html::textInput('__vat_rate', null, [
                                    'class' => 'form-control',
                                ]) ?>
                                <div class="input-group-addon" id="div-unit">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">备注</label>
                            <?= Html::textInput('__memo', null, [
                                'class' => 'form-control',
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button type="button" id="btnRateConfigSave" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>
</div>

<div class="box-body" id="clear-day-help" style="display: none">
    <ol>
        <li>
            <div>D$</div>
            <ul>
                <li>含义为还款日为账单日</li>
                <li>格式为D$N，其中N取值为1-31</li>
                <li>
                    示例D$24。表示当起息日小于等于自然日24日时，还款日取对日；起息日大于自然日24日时，还款日为每月的自然日24日
                </li>
            </ul>
        </li>
        <li>
            <div>D+</div>
            <ul>
                <li>含义为还款日为自然日加固定天数</li>
                <li>格式为D+N，其中N取值为1-31</li>
                <li>示例D+1。表示还款日为起息日加1日</li>
            </ul>
        </li>
        <li>
            <div>D-</div>
            <ul>
                <li>含义为还款日为自然日减固定天数</li>
                <li>格式为D-N，其中N取值为1-31</li>
                <li>示例D-24。表示还款日为起息日减24日</li>
            </ul>
        </li>
        <li>
            <div>T+</div>
            <ul>
                <li>含义为还款日为工作日加固定天数</li>
                <li>格式为T+N，其中N取值为1-31</li>
                <li>示例T+1。表示还款日为起息日加1日</li>
            </ul>
        </li>
        <li>
            <div>T-</div>
            <ul>
                <li>含义为还款日为工作日减固定天数</li>
                <li>格式为T-N，其中N取值为1-31</li>
                <li>示例T-24。表示还款日为起息日减24日</li>
            </ul>
        </li>
    </ol>
</div>

<script type="text/javascript">
    <?php $this->beginBlock('feesJs') ?>

    $('[data-toggle="popover"]').popover();

    $('.show-clear-day-help').on('click', function () {
        layer.open({
            type: 1,
            title: false,
            shadeClose: true, //开启遮罩关闭
            content: $('#clear-day-help')
        })
    });

    (function () {
        function checkContinuousInterval(interval) {
            if (interval === '') {
                return true;
            }
            if (typeof interval !== 'string') {
                return false;
            }
            // 去掉空格
            interval = interval.replace(/\s/g, '');

            // 判断是否为离散区间
            const regex_discrete = /^\{([\d\.]+,)*([\d\.]+)\}$/;
            if (regex_discrete.test(interval)) {
                // 离散区间格式正确，判断每个元素是否为数字
                const numbers = interval.slice(1, -1).split(',');
                for (let i = 1; i < numbers.length; i++) {
                    if (isNaN(parseFloat(numbers[i]))) {
                        return false;
                    }
                }
                return true;
            }

            // 判断连续区间
            const regex_continuous = /^[[(]\s*([\d\.]+)\s*,\s*([\d\.]+)\s*[)\]]$/;
            const match = interval.match(regex_continuous);
            if (match !== null) {
                const left = parseFloat(match[1]), right = parseFloat(match[2]);
                if (isNaN(left) || isNaN(right)) {
                    return false;
                }

                return left < right;
            }

            return false;
        }

        const feeOrder = <?= json_encode(Fee::executionOrder())?>,
            feeCodeList = <?= json_encode(Fee::feeMap()) ?>,
            repayTypeList = <?= json_encode(RateConfig::REPAY_TYPE_LIST) ?>,
            calculateTypeList = <?= json_encode(RateConfig::CALCULATE_TYPE_LIST) ?>,
            carryModeList = <?= json_encode(RateConfig::ROUND_LIST) ?>,
            periodCategoryEl = $('#<?= Html::getInputId($entity, 'periodCategory') ?>'),
            __fee_code = $('[name=__fee_code]'),
            __parent = $('[name=__parent]'),
            __repay_type = $('[name=__repay_type]'),
            __calculate_type = $('[name=__calculate_type]'),
            __carry_mode = $('[name=__carry_mode]'),
            __value = $('[name=__value]'),
            __execution_order = $('[name=__execution_order]'),
            __memo = $('[name=__memo]'),
            __value_range = $('[name=__value_range]'),
            __vat_rate = $('[name=__vat_rate]'),
            unitDiv = $('#div-unit'),
            btnRateConfigSave = $('#btnRateConfigSave');

        // 事件绑定
        __calculate_type.on('change', function (e) {
            let v = e.target.value, feeCode = __fee_code.val();
            if (feeCode === 'comprehensive_interest' || feeCode === 'capital_cost') {
                if (v === 'acpi_diff' || v === 'subduction') {
                    $(this).val('acpi').change();
                    v = 'acpi';
                    alert('综合自费率、资方成本 不支持 等额本息差额，倒减计算方式！')
                }
            }

            unitDiv.text(v === 'fixed_amount' ? '元' : '%');
        })

        __fee_code.on('change', (e) => __execution_order.val(feeOrder[e.target.value] * 10)).change();

        const FeeTable = function () {
            const self = this
            self.$input = $('#<?= Html::getInputId($entity, 'fees') ?>')
            self.$el = $('#fee-table')
            self.fees = JSON.parse(self.$input.val() || '[]')

            self.fees.forEach(fee => self.addParentOption(fee.feeCode))

            this.$el.on('click', 'a.del-fee', function () {
                if (window.confirm('确定要删除该费用项及其下级费用项吗?')) {
                    self.removeFee($(this).parents('tr').index())
                }
            })

            this.render()
        }

        FeeTable.prototype.removeFee = function (index) {
            const level = this.fees[index].__level
            do {
                __parent.find(`option[value="${this.fees[index].feeCode}"]`).remove()
                __parent.trigger('change')
                this.fees.splice(index, 1)
                this.$el.find(`tr:eq(${index})`).remove()
            } while (this.fees[index].__level > level)
        }

        FeeTable.prototype.render = function () {
            this.$input.val(JSON.stringify(this.fees))
            let tr = ''
            this.fees.forEach((fee, i) => {
                let icon = 'fa fa-fw fa-minus-square-o'
                if (this.fees[i + 1] && this.fees[i + 1].__level > fee.__level) {
                    icon = 'fa fa-fw fa-plus-square-o'
                }
                tr += `
                <tr>
                    <td><span class="${icon}" style="margin-left:${fee.__level * 16}px"></span>${feeCodeList[fee.feeCode]}</td>
                    <td>${calculateTypeList[fee.calculateType]}</td>
                    <td>${fee.value + (fee.calculateType === 'fixed_amount' ? '元' : '%')}</td>
                    <td>${fee.valueRange ? fee.valueRange : ''}</td>
                    <td>${(fee.vatRate ? fee.vatRate : 0) + '%'}</td>
                    <td>${repayTypeList[fee.repayType]}</td>
                    <td>${carryModeList[fee.carryMode]}</td>
                    <td>${fee.executionOrder}</td>
                    <td><a class="del-fee" href="###"><i class="glyphicon glyphicon-trash"></i></a></td>
                </tr>
                `
            })
            this.$el.html(tr)
        }

        FeeTable.prototype.addParentOption = function (feeCode) {
            __parent.append(new Option(feeCodeList[feeCode], feeCode)).trigger('change')
        }

        const feeTable = new FeeTable()

        btnRateConfigSave.on('click', function () {
            const value = Number(__value.val())
            if (isNaN(value) || value < 0) {
                window.alert('费用计算值只能是大于等于0的数字')
                __value.focus()
                return false
            }

            const feeCode = __fee_code.val(),
                exists = feeTable.fees.findIndex(function (fee) {
                    return fee.feeCode === feeCode
                })

            if (exists >= 0) {
                window.alert('不能配置同一费用项: ' + feeCodeList[feeCode])
                return false
            }

            if (!checkContinuousInterval(__value_range.val())) {
                window.alert('取值范围格式错误:' + __value_range.val())
                return false;
            }

            const vatRate = Number(__vat_rate.val())
            if (isNaN(vatRate) || vatRate < 0) {
                window.alert('增值税比例只能是大于等于0的数字')
                vatRate.focus()
                return false
            }

            const parent = __parent.val(), newFee = {
                feeCode: feeCode,
                repayType: __repay_type.val(),
                calculateType: __calculate_type.val(),
                carryMode: __carry_mode.val(),
                value: value,
                executionOrder: __execution_order.val(),
                memo: __memo.val(),
                valueRange: __value_range.val(),
                __level: 0,
                vatRate: vatRate,
            }

            let pFeeIndex = feeTable.fees.findIndex(function (fee) {
                return fee.feeCode === parent
            }), pFee = feeTable.fees[pFeeIndex]

            if (pFee && pFeeIndex >= 0) {
                newFee.__level = pFee.__level + 1
                do {
                    pFeeIndex++
                } while (feeTable.fees[pFeeIndex] && feeTable.fees[pFeeIndex].__level >= newFee.__level)
                feeTable.fees.splice(pFeeIndex, 0, newFee)
            } else {
                feeTable.fees.push(newFee)
            }

            feeTable.addParentOption(newFee.feeCode)

            window.alert('保存成功')
            __value.val('')

            feeTable.render()
        })
    })()
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['feesJs']) ?>
</script>

