<?php

use cmdb\models\LateRule;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */

$this->title                   = '罚息规则';
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('创建罚息规则', ['create'], ['class' => 'btn btn-success']) ?>
</p>
<?= GridView::widget([
    'dataProvider'     => $dataProvider,
    'headerRowOptions' => [],
    'columns'          => [
        [
            'header'         => '操作',
            'class'          => 'xlerr\common\grid\ActionColumn',
            'visibleButtons' => [
                'update' => function (LateRule $model) {
                    return $model->late_status === LateRule::STATUS_OPEN;
                },
                'delete' => function (LateRule $model, $id, $index) {
                    return !$model->used();
                },
            ],
        ],
        'late_no',
        'late_name',
        [
            'attribute' => 'late_limit_type',
            'format'    => ['in', LateRule::TYPE_LIST],
        ],
        [
            'attribute' => 'late_limit_formula',
            'format'    => ['in', LateRule::FORMULAS],
        ],
        'late_limit_value',
        [
            'attribute' => 'late_status',
            'format'    => ['in', LateRule::STATUS_LISTS],
        ],
        'late_desc',
        'late_create_at',
        'late_update_at',
    ],
]); ?>
