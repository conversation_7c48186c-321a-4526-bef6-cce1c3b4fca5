<?php

use Carbon\Carbon;
use cmdb\models\LateRule;
use cmdb\models\LateTran;
use cmdb\widgets\DetailView;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\DataProviderInterface;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/** @var $this View */
/** @var $model LateRule */
/** @var $dataProvider DataProviderInterface */

$this->title                   = $model->late_no;
$this->params['breadcrumbs'][] = ['label' => '罚息规则', 'url' => ['index']];
$this->params['breadcrumbs'][] = '详情';
?>
    <p>
        <?php
        $btnList = [];
        if ($model->late_status === LateRule::STATUS_OPEN) {
            $btnList[] = Html::a('修改', ['update', 'id' => $model->late_id], ['class' => 'btn btn-primary']);
        }
        if ($model->late_status !== LateRule::STATUS_ONLINE) {
            $btnList[] = Html::a('上线', ['go-live', 'id' => $model->late_id], ['class' => 'btn btn-success']);
        }

        if ($model->late_status === LateRule::STATUS_ONLINE) {
            $btnList[] = Html::a('下线', ['closure', 'id' => $model->late_id], ['class' => 'btn btn-danger']);
        }

        echo implode(' ', $btnList);
        ?>

        <?= Html::a('返回列表', ['index'], ['class' => 'btn btn-default']) ?>
    </p>

    <div class="box box-primary">
        <div class="box-header with-border">
            <div class="box-title">详情</div>
        </div>

        <div class="box-body no-padding">
            <?= DetailView::widget([
                'model'      => $model,
                'attributes' => [
                    'late_id',
                    'late_no',
                    'late_name',
                    [
                        'attribute' => 'late_limit_type',
                        'format'    => ['in', LateRule::TYPE_LIST],
                    ],
                    [
                        'attribute' => 'late_limit_formula',
                        'format'    => ['in', LateRule::FORMULAS],
                    ],
                    'late_limit_value',
                    [
                        'attribute' => 'late_status',
                        'format'    => ['in', LateRule::STATUS_LISTS],
                    ],
                    'late_desc:ntext',
                ],
            ]) ?>

        </div>
    </div>

<?php
$addBtn = Html::a('添加', ['late-tran/create', 'id' => $model->late_id], [
    'class' => 'btn btn-sm btn-primary',
    'target' => '_blank',
]);
$layout = <<<HTML
<div class="box-header with-border">
    <h3 class="box-title">罚息计算细则</h3>
    <div class="box-tools pull-right">
    $addBtn
    </div>
</div>
<div class="box-body table-responsive no-padding">{items}</div>
<div class="box-footer">{summary}{pager}</div>
HTML;

echo GridView::widget([
    'layout'       => $layout,
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'          => ActionColumn::class,
            'template'       => '{release} {update} {delete}',
            'buttons'        => [
                'release' => function ($url) {
                    return ActionColumn::newButton('上线', $url, [
                        'class' => 'btn-success',
                    ]);
                },
            ],
            'urlCreator'     => function (string $action, LateTran $model, int $key): string {
                return Url::to(['late-tran/' . $action, 'id' => $key]);
            },
            'visibleButtons' => [
                'release' => function (LateTran $lateTran): bool {
                    return !$lateTran->released();
                },
                'update'  => function (LateTran $lateTran): bool {
                    return !$lateTran->released();
                },
                'delete'  => function (LateTran $lateTran): bool {
                    return !$lateTran->released();
                },
            ],
        ],
        [
            'label'     => 'ID',
            'attribute' => 'late_tran_id',
        ],
        [
            'attribute' => 'late_tran_status',
            'format'    => ['in', LateTran::STATUS_MAP],
        ],
        'late_tran_fee_code',
        'late_tran_fee_desc',
        [
            'label'     => '期次',
            'attribute' => 'late_tran_period',
        ],
        [
            'attribute' => 'late_tran_formula',
            'format'    => ['in', LateRule::FORMULAS],
        ],
        'late_tran_value',
        [
            'label'     => '费用类型',
            'attribute' => 'late_tran_dependent',
        ],
        [
            'label'     => '费用值类型',
            'attribute' => 'late_tran_dependent_mode',
            'format'    => ['in', LateTran::DEPENDENT_MODES],
        ],
        [
            'label'     => '费用值计算方式',
            'attribute' => 'late_tran_dependent_type',
            'format'    => ['in', LateTran::DEPENDENT_TYPES],
        ],
        [
            'attribute' => 'late_tran_carry_mode',
            'format'    => ['in', LateTran::CARRY_MODES],
        ],
        [
            'attribute' => 'late_tran_carry_scale',
            'format'    => ['in', LateTran::CARRY_SCALES],
        ],
        [
            'label' => '逾期天数',
            'value' => function (LateTran $model) {
                return implode(' - ', [
                    $model->late_tran_overdue_days_start,
                    $model->late_tran_overdue_days_end,
                ]);
            },
        ],
        [
            'label' => '每日罚息',
            'value' => function (LateTran $model) {
                return implode(' - ', [
                    $model->late_tran_min_daily_amount,
                    $model->late_tran_max_daily_amount,
                ]);
            },
        ],
        [
            'label' => '生效日期',
            'value' => function (LateTran $model) {
                if ($model->late_tran_effective_start) {
                    $startDate = Carbon::parse($model->late_tran_effective_start)->toDateString();
                } else {
                    $startDate = '1971-01-01';
                }

                return implode(' - ', [
                    $startDate,
                    Carbon::parse($model->late_tran_effective_end)->toDateString(),
                ]);
            },
        ],
        'late_tran_create_at',
    ],
]);
