<?php

use cmdb\models\LateRule;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ActiveForm;

/* @var $this View */
/* @var $model LateRule */
/* @var $form ActiveForm */
?>

<?php
$form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'late_no')->textInput(['maxlength' => true, 'disabled' => !$model->isNewRecord]) ?>

    <?= $form->field($model, 'late_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'late_limit_type')->widget(Select2::class, [
        'data'       => LateRule::TYPE_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_limit_formula')->widget(Select2::class, [
        'data'       => LateRule::FORMULAS,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'late_limit_value')->textInput() ?>

    <?= $form->field($model, 'late_desc')->textarea(['rows' => 4]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton($model->isNewRecord ? '创建' : '保存', [
        'class' => $model->isNewRecord ? 'btn btn-success'
            : 'btn btn-primary',
    ]) ?>
</div>

<?php
ActiveForm::end(); ?>
