<?php

use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */

$this->title                   = '资金方';
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('创建资金方', ['create'], ['class' => 'btn btn-success']) ?>
</p>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns'      => [
        [
            'class'    => 'xlerr\common\grid\ActionColumn',
            'template' => '{update}',
        ],
        'capital_name',
        'capital_code',
        'capital_desc',
        'capital_alias',
        'capital_create_at',
        'capital_update_at',
    ],
]); ?>
