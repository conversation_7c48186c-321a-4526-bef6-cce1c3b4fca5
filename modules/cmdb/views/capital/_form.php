<?php

use cmdb\models\Capital;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ActiveForm;

/* @var $this View */
/* @var $model Capital */
/* @var $form ActiveForm */

$form = ActiveForm::begin();
?>

<div class="box-body">

    <?= $form->field($model, 'capital_name')->textInput(['maxlength' => true]) ?>
    <?= $form->field($model, 'capital_code')->textInput(['maxlength' => true, 'disabled' => !$model->isNewRecord]) ?>
    <?= $form->field($model, 'capital_alias')->textInput(['maxlength' => true]) ?>
    <?= $form->field($model, 'capital_desc')->textInput(['maxlength' => true]) ?>
</div>

<div class="box-footer">
    <?= Html::submitButton($model->isNewRecord ? '新增' : '保存', [
        'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary',
    ]) ?>
</div>
<?php
ActiveForm::end(); ?>
