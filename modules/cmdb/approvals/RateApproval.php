<?php

namespace cmdb\approvals;

use Carbon\Carbon;
use cmdb\models\Rate;
use cmdb\models\RateEntity;
use common\models\User;
use waterank\audit\AvoidApproval;
use waterank\audit\models\ApprovalEntries;
use waterank\audit\models\Audit;
use waterank\audit\provider\AvoidProvider;
use waterank\audit\task\BusinessBeginTask;
use waterank\audit\task\BusinessEndTask;
use xlerr\common\assets\LayerAsset;
use Yii;
use yii\base\UserException;
use yii\helpers\Url;
use yii\web\Response;
use yii\web\View;

use function waterank\audit\authClient;
use function xlerr\adminlte\userFullName;

class RateApproval extends AvoidApproval
{
    public static function genAuditPreviewUrl(int $id): string
    {
        $self  = new self();
        $model = current($self->queryNotEndOfBusiness(['id' => $id]));
        if (empty($model)) {
            Yii::$app->getSession()->setFlash('warning', '数据已处理');

            return Yii::$app->getRequest()->getReferrer();
        }

        /** @var AvoidProvider $avoidProvider */
        $avoidProvider = authClient(self::AUTH_CLIENT_ID);

        return Url::to([$avoidProvider->previewUrl, 'id' => $model->audit->approvalEntries->id]);
    }

    /**
     * @return array<string, Rate>
     */
    public function queryNotEndOfBusiness(array $condition = []): array
    {
        $query = Audit::find()
            ->where(['audit_type' => $this->type()])
            ->andWhere(['>', 'audit_oa_id', 0])
            ->andWhere(['!=', 'business_status', Audit::BUSINESS_END]);

        $kvModels = [];
        /** @var Audit $audit */
        foreach ($query->each() as $audit) {
            /** @var self $approval */
            $approval = $audit->getApproval();
            foreach ($condition as $ck => $cv) {
                if ($approval->getData($ck) != $cv) {
                    continue 2;
                }
            }

            $model = $approval->generateModel();

            $model->audit = $audit;

            $kvModels[$model->rate_id] = $model;
        }

        return $kvModels;
    }

    protected function generateModel(): Rate
    {
        $modelId = $this->getData('id');
        $model   = RateEntity::findOne($modelId);

        $model->rate_status = $this->getData('status');

        return $model;
    }


    /**
     * 审批成功提交到OA
     */
    public function submitApprovalSuccess(): Response
    {
        return Yii::$app->getResponse()->redirect($this->getOptions('backUrl'));
    }

    /**
     * @param Audit $audit
     * @param       $status
     *
     * @return void
     * @throws UserException
     */
    public function callback(Audit $audit, $status): void
    {
        parent::callback($audit, $status);

        if ($audit->audit_status !== Audit::STATUS_SUCCESS) {
            return;
        }

        $id     = $this->getData('id');
        $status = $this->getData('status');

        if ($status === Rate::STATUS_PASS) {
            $affectedRowCount = Rate::updateAll([
                'rate_status' => $status,
            ], [
                'rate_status' => [Rate::STATUS_OPEN, Rate::STATUS_OVERDUE],
                'rate_id'     => $id,
            ]);
        } elseif ($status === Rate::STATUS_OVERDUE) {
            $affectedRowCount = Rate::updateAll([
                'rate_status' => $status,
            ], [
                'rate_status' => Rate::STATUS_PASS,
                'rate_id'     => $id,
            ]);
        } else {
            throw new UserException(sprintf('费率审批不允许提交[%s]状态', $status));
        }

        if ((int)$affectedRowCount !== 1) {
            throw new UserException('修改费率状态失败');
        }

        BusinessBeginTask::process([
            'audit_id'      => $audit->audit_id,
            'status_detail' => [
                $id => 'processing',
            ],
        ]);

        BusinessEndTask::process([
            'audit_id'      => $audit->audit_id,
            'status'        => Audit::BUSINESS_END,
            'status_detail' => [
                $id => 'success',
            ],
            'memo'          => '审核通过',
            'finish_time'   => Carbon::now()->toDateTimeString(),
        ]);
    }

    public static function auditors(array $userIds = null): array
    {
        $userIds = $userIds ?? Yii::$app->getAuthManager()->getUserIdsByRole('RateApproval');

        return parent::auditors($userIds);
    }

    public function listTitle(): string
    {
        $rate = Rate::findOne($this->getData('id'));

        return vsprintf('%s(<code>%s</code>)费率编号<code>%s</code>申请', [
            $rate->rate_name,
            $rate->rate_number,
            [
                Rate::STATUS_PASS    => '上线',
                Rate::STATUS_OVERDUE => '仅收罚息',
            ][$this->getData('status')],
        ]);
    }

    public function preview(View $view, ApprovalEntries $approvalEntries): string
    {
        LayerAsset::register($view);

        $status = $this->getData('status');
        $rate   = Rate::findOne($this->getData('id'));

        $boxColor = [Rate::STATUS_PASS => 'success', Rate::STATUS_OVERDUE => 'warning'][$status];

        $desc = [
            Rate::STATUS_PASS    => '上线',
            Rate::STATUS_OVERDUE => '仅收罚息',
        ][$status];

        $user = User::findOne($approvalEntries->audit->audit_creator_id);
        $user = userFullName($user);

        return <<<HTML
<div class="box box-{$boxColor}">
    <div class="box-header with-border">
        <div class="box-title">费率编号审批</div>
    </div>
    <div class="box-body">
        <h4>
            {$rate->rate_name}(<a href="javascript:;" onclick="layer.open({type: 2, title: false, area: ['90%', '90%'], shadeClose: true, content: '/cmdb/rate/view?id={$rate->rate_id}'})">{$rate->rate_number}</a>)费率编号<code style="font-size: 100%">{$desc}</code>申请
        </h4>
        <div>
            申请人: {$user}
        </div>
    </div>
    <div class="box-footer">{$this->actionButtons($approvalEntries)}</div>
</div>
HTML;
    }

    public function canAgree(): bool
    {
        $status = [
            Rate::STATUS_PASS    => [Rate::STATUS_OPEN, Rate::STATUS_OVERDUE],
            Rate::STATUS_OVERDUE => Rate::STATUS_PASS,
        ][$this->getData('status')];

        return (bool)Rate::find()->where([
            'rate_id'     => $this->getData('id'),
            'rate_status' => $status,
        ])->exists();
    }
}
