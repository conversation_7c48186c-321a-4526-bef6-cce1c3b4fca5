<?php

namespace cmdb\models;

use Carbon\Carbon;
use Exception;
use RuntimeException;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%late_tran}}".
 *
 * @property int $late_tran_id                 主键
 * @property string $late_tran_late_no            罚息规则编码
 * @property string $late_tran_fee_code           费用编码:lateinterest,latemanage,lateservice
 * @property string $late_tran_fee_desc           费用名称:罚息管理费
 * @property int $late_tran_period             期次(0-所有期次计算规则都一样)
 * @property string $late_tran_formula            计算规则（FIXED_AMOUNT-固定值,单位元；FIXED_PERCENT-固定比例，10%，配置10）
 * @property string $late_tran_value              计算规则值(每日)
 * @property string $late_tran_dependent          在使用fixed_percent计算罚息时，对应的费用类型，eg：repayprincipal-本金，repayinterest，service等（参照asset_tran_type）
 * @property string $late_tran_dependent_mode     取费用类型时，ORIGIN-原始值，BALANCE-剩余值
 * @property string $late_tran_dependent_type     费用还款方式：PERIOD:按期收取，CONTRACT:按合同收取（所有期次值之和）
 * @property string $late_tran_carry_mode         费用进位方式，HALF_EVEN-四舍六入五成双，HALF_UP-四舍五入，DOWN-向下取整，UP-向上取整
 * @property int $late_tran_carry_scale        费用进位精度(0-元，1-角，2-分)
 * @property int $late_tran_overdue_days_start 开始逾期天数
 * @property int $late_tran_overdue_days_end   结束逾期天数
 * @property string $late_tran_min_daily_amount   每日罚息最小值
 * @property string $late_tran_max_daily_amount   每日罚息最大值
 * @property string $late_tran_create_at
 * @property string $late_tran_update_at
 * @property string $late_tran_effective_start    生效起始日期
 * @property string $late_tran_effective_end      生效截止日期
 * @property string $late_tran_status             罚息细则生效状态，online：上线，offline：下线
 * @property-read LateRule $late
 */
class LateTran extends ActiveRecord
{
    public const DEPENDENT_MODE_ORIGIN = 'ORIGIN';
    public const DEPENDENT_MODE_BALANCE = 'BALANCE';
    public const DEPENDENT_MODES = [
        self::DEPENDENT_MODE_ORIGIN => '原始值',
        self::DEPENDENT_MODE_BALANCE => '剩余值',
    ];

    public const DEPENDENT_TYPE_PERIOD = 'PERIOD';
    public const DEPENDENT_TYPE_CONTRACT = 'CONTRACT';
    public const DEPENDENT_TYPE_LAST_PERIOD = 'LAST_PERIOD';
    public const DEPENDENT_TYPE_PERIOD_LIMIT_LATE = 'PERIOD_LIMIT_LATE';

    public const DEPENDENT_TYPES = [
        self::DEPENDENT_TYPE_PERIOD => '按期收取',
        self::DEPENDENT_TYPE_CONTRACT => '按合同收取',
        self::DEPENDENT_TYPE_LAST_PERIOD => '按最后一期收取',
        self::DEPENDENT_TYPE_PERIOD_LIMIT_LATE => '本金取当期，每期罚息收取天数上限为下期借款天数',
    ];

    public const CARRY_MODE_HALF_EVEN = 'HALF_EVEN'; // 四舍六入五成双
    public const CARRY_MODE_HALF_UP = 'HALF_UP';     // 四舍五入
    public const CARRY_MODE_DOWN = 'DOWN';           // 向下取整
    public const CARRY_MODE_UP = 'UP';               // 向上取整
    public const CARRY_MODES = [
        self::CARRY_MODE_HALF_EVEN => '四舍六入五成双',
        self::CARRY_MODE_HALF_UP => '四舍五入',
        self::CARRY_MODE_DOWN => '向下取整',
        self::CARRY_MODE_UP => '向上取整',
    ];

    public const CARRY_SCALE_YUAN = 0;
    public const CARRY_SCALE_JIAO = 1;
    public const CARRY_SCALE_FEN = 2;
    public const CARRY_SCALES = [
        self::CARRY_SCALE_YUAN => '元',
        self::CARRY_SCALE_JIAO => '角',
        self::CARRY_SCALE_FEN => '分',
    ];

    public const STATUS_ONLINE = 'online';
    public const STATUS_OFFLINE = 'offline';

    public const STATUS_MAP = [
        self::STATUS_ONLINE => '上线',
        self::STATUS_OFFLINE => '未上线',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%late_tran}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'late_tran_late_no',
                    'late_tran_fee_code',
                    'late_tran_fee_desc',
                    'late_tran_effective_start',
                ],
                'required',
            ],
            [
                'late_tran_status',
                'default',
                'value' => self::STATUS_OFFLINE,
            ],
            [
                [
                    'late_tran_period',
                    'late_tran_carry_scale',
                ],
                'integer',
            ],
            [['late_tran_overdue_days_start', 'late_tran_overdue_days_end'], 'integer', 'min' => 1, 'max' => 99999],
            [
                ['late_tran_formula', 'late_tran_dependent_mode', 'late_tran_dependent_type', 'late_tran_carry_mode'],
                'string',
            ],
            [['late_tran_value', 'late_tran_min_daily_amount', 'late_tran_max_daily_amount'], 'number'],
            [['late_tran_create_at', 'late_tran_update_at'], 'safe'],
            [['late_tran_late_no', 'late_tran_fee_code', 'late_tran_fee_desc'], 'string', 'max' => 64],
            [['late_tran_dependent'], 'string', 'max' => 60],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'late_tran_id' => '主键',
            'late_tran_late_no' => '罚息规则编码',
            'late_tran_fee_code' => '费用编码',
            'late_tran_fee_desc' => '费用名称',
            'late_tran_period' => '期次(0-所有期次计算规则都一样)',
            'late_tran_formula' => '计算规则',
            'late_tran_value' => '计算规则值(每日)',
            'late_tran_dependent' => '收取罚息对应的费用类型',
            'late_tran_dependent_mode' => '收取罚息对应的费用值类型',
            'late_tran_dependent_type' => '收取罚息对应的费用值计算方式',
            'late_tran_carry_mode' => '费用进位方式',
            'late_tran_carry_scale' => '费用进位精度',
            'late_tran_overdue_days_start' => '开始逾期天数',
            'late_tran_overdue_days_end' => '结束逾期天数',
            'late_tran_min_daily_amount' => '每日罚息最小值',
            'late_tran_max_daily_amount' => '每日罚息最大值',
            'late_tran_create_at' => '创建时间',
            'late_tran_update_at' => '更新时间',
            'late_tran_effective_start' => '生效起始日期',
            'late_tran_effective_end' => '生效截止日期',
            'late_tran_status' => '生效状态',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getLate(): ActiveQuery
    {
        return $this->hasOne(LateRule::class, ['late_no' => 'late_tran_late_no']);
    }

    /**
     * 已上线/已发布
     *
     * @return bool
     */
    public function released(): bool
    {
        return $this->late_tran_status === self::STATUS_ONLINE;
    }

    public function release()
    {
        if ($this->late_tran_overdue_days_start > $this->late_tran_overdue_days_end) {
            throw new RuntimeException('开始逾期天数必须小于或等于结束逾期天数');
        }

        $overdueDayQuery = self::find()->where([
            'late_tran_late_no' => $this->late_tran_late_no,
            'late_tran_fee_code' => $this->late_tran_fee_code,
            'late_tran_dependent' => $this->late_tran_dependent,
            'late_tran_period' => $this->late_tran_period,
            'late_tran_status' => self::STATUS_ONLINE,
        ]);

        // 判断是是否存在逾期天数一样的
        $hasEqual = (clone $overdueDayQuery)->andWhere([
            'late_tran_overdue_days_start' => $this->late_tran_overdue_days_start,
            'late_tran_overdue_days_end' => $this->late_tran_overdue_days_end,
        ])->exists();

        if (!$hasEqual) {
            $hasNotEqual = (clone $overdueDayQuery)->andWhere([
                'or',
                ['!=', 'late_tran_overdue_days_start', $this->late_tran_overdue_days_start],
                ['!=', 'late_tran_overdue_days_end', $this->late_tran_overdue_days_end],
            ])->exists();
            if ($hasNotEqual) {
                $gtStartDay = (clone $overdueDayQuery)->andWhere([
                    '>=',
                    'late_tran_overdue_days_end',
                    $this->late_tran_overdue_days_start,
                ])->all();

                if ($gtStartDay) {
                    $gtStartDayIds = array_column($gtStartDay, 'late_tran_id');
                    throw new RuntimeException(vsprintf('逾期天数必须为连续的值,[%s]', [implode(',', $gtStartDayIds)]));
                }

                $equalStartDay = $overdueDayQuery->andWhere([
                    'late_tran_overdue_days_end' => $this->late_tran_overdue_days_start - 1,
                ])->all();

                if (!$equalStartDay) {
                    throw new RuntimeException(
                        vsprintf(
                            '逾期天数必须为连续的值,没有找到逾期结束天数为[%s]的数据',
                            [$this->late_tran_overdue_days_start - 1]
                        )
                    );
                }
            }
        }

        $query = self::find()->where([
            'late_tran_late_no' => $this->late_tran_late_no,
            'late_tran_fee_code' => $this->late_tran_fee_code,
            'late_tran_dependent' => $this->late_tran_dependent,
            'late_tran_period' => $this->late_tran_period,
            'late_tran_overdue_days_start' => $this->late_tran_overdue_days_start,
            'late_tran_overdue_days_end' => $this->late_tran_overdue_days_end,
            'late_tran_status' => self::STATUS_ONLINE,
        ]);

        $transaction = self::getDb()->beginTransaction();
        try {
            // 判断是否为第一次新增
            if ($query->count() > 0) {
                /** @var self[] $prev */
                $prev = $query
                    ->andWhere(['>', 'late_tran_effective_end', $this->late_tran_effective_start])
                    ->all();
                if (count($prev) !== 1) {
                    $prevIds = array_column($prev, 'late_tran_id');
                    throw new RuntimeException(vsprintf('找到多条生效截止日期大于当前生效日期数据,[%s]', [implode(',', $prevIds)]));
                }

                $prev = current($prev);

                $prevEffectiveEnd = Carbon::parse($this->late_tran_effective_start)->subDay();

                if (!empty($prev->late_tran_effective_start) && Carbon::parse($prev->late_tran_effective_start) > $prevEffectiveEnd) {
                    throw new RuntimeException(vsprintf('当前生效日期必须大于最大生效日期,[%s]', [$prev->late_tran_id]));
                }

                $prev->late_tran_effective_end = $prevEffectiveEnd->toDateString();
                if (!$prev->save(true, ['late_tran_effective_end'])) {
                    throw new RuntimeException(json_encode($prev->getErrors()));
                }
            }

            $this->late_tran_status = self::STATUS_ONLINE;
            if (!$this->save(true, ['late_tran_status'])) {
                throw new RuntimeException(json_encode($this->getErrors()));
            }
            $transaction->commit();
        } catch (Exception $exception) {
            $transaction->rollBack();
            throw $exception;
        }
    }
}
