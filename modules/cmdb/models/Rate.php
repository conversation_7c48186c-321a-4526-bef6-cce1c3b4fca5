<?php

namespace cmdb\models;

use cmdb\behaviors\VoidBehavior;
use Exception;
use waterank\audit\models\Audit;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "rate".
 *
 * @property int                $rate_id
 * @property string             $rate_number
 * @property string             $rate_name
 * @property string             $rate_memo
 * @property int                $rate_principal_min_amount
 * @property int                $rate_principal_max_amount
 * @property int                $rate_interest_year_days
 * @property string             $rate_status
 * @property string             $rate_valid_start_date
 * @property string             $rate_valid_end_date
 * @property string             $rate_capital_code
 * @property string             $rate_create_at
 * @property string             $rate_update_at
 * @property string             $rate_late_no
 * @property string             $rate_extra_json
 * @property string             $rate_month_clear_day
 * @property string             $rate_clear_day
 * @property string             $rate_effect_day
 * @property string             $rate_repay_date_formula
 * @property string             $rate_periods
 * @property-read RateConfig[]  $rateConfigs
 * @property-read Capital       $capital
 * @property-read Late          $late
 * @property-read Product[]     $products
 * @property-read ProductRate[] $productRates
 * @property-read RateVarNum[]  $rateVarNums
 */
class Rate extends ActiveRecord
{
    public const STATUS_OPEN = 'open';
    public const STATUS_PASS = 'pass';
    public const STATUS_VOID = 'void';
    public const STATUS_OVERDUE = 'overdue';
    public const STATUS_LIST = [
        self::STATUS_OPEN => '新增',
        self::STATUS_PASS => '正常',
        self::STATUS_VOID => '失效',
        self::STATUS_OVERDUE => '仅算罚息',
    ];

    public const PERIOD_TYPE_DAY = 'day';
    public const PERIOD_TYPE_MONTH = 'month';
    public const PERIOD_TYPES = [
        self::PERIOD_TYPE_DAY => '天',
        self::PERIOD_TYPE_MONTH => '月',
    ];

    public const INTEREST_YEAR_DAYS = [
        '360' => '360天/年',
        '365' => '365天/年',
    ];

    public const DATE_TYPE_D = 'D+N';
    public const DATE_TYPE_T = 'T+N';
    public const DATE_TYPES = [
        self::DATE_TYPE_D => 'D+N',
        self::DATE_TYPE_T => 'T+N',
    ];

    /** @var Audit */
    public $audit;

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'rate';
    }

    /**
     * @return object|Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    public function behaviors(): array
    {
        return [
            VoidBehavior::class,
        ];
    }

    /**
     * @return array
     */
    public function transactions(): array
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    public function isVoid(): bool
    {
        return $this->rate_status == self::STATUS_VOID;
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'rate_number',
                    'rate_name',
                    'rate_principal_min_amount',
                    'rate_principal_max_amount',
                    'rate_interest_year_days',
                    'rate_valid_start_date',
                    'rate_valid_end_date',
                    'rate_late_no',
                ],
                'required',
            ],
            [['rate_memo', 'rate_status'], 'string'],
            [
                [
                    'rate_principal_min_amount',
                    'rate_principal_max_amount',
                    'rate_interest_year_days',
                    'rate_add_n',
                ],
                'integer',
            ],
            [['rate_valid_start_date', 'rate_valid_end_date', 'rate_create_at', 'rate_update_at'], 'safe'],
            [['rate_month_clear_day', 'rate_clear_day', 'rate_effect_day', 'rate_repay_date_formula'], 'string'],
            [['rate_number'], 'string', 'max' => 50],
            [['rate_name'], 'string', 'max' => 30],
            [['rate_extra_json'], 'string', 'max' => 256],
            [['rate_capital_code', 'rate_periods', 'rateCapitalChannel', 'rateLateRuleNo'], 'safe'],
            [['rate_number'], 'unique', 'message' => '费率编号已存在'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'rate_id' => 'ID',
            'rate_number' => '费率编号',
            'rate_name' => '费率名称',
            'rate_memo' => '需求',
            'rate_principal_min_amount' => '适用本金-起始值',
            'rate_principal_max_amount' => '适用本金-结束值',
            'rate_interest_year_days' => '利率年化天数',
            'rate_status' => '状态',
            'rate_valid_start_date' => '生效日期',
            'rate_valid_end_date' => '结束日期',
            'rate_capital_code' => '适用资金方',
            'rateCapitalChannel' => '适用资金方',
            'rateLateRuleNo' => '罚息规则',
            'rate_late_no' => '罚息规则',
            'rate_create_at' => '创建时间',
            'rate_update_at' => '更新时间',
            'rate_extra_json' => '额外信息',
            'rate_month_clear_day' => '月结日',
            'rate_clear_day' => '结清日',
            'rate_effect_day' => '起息日计算方式',
            'rate_repay_date_formula' => '还款日算法',
        ];
    }

    public function getRateCapitalChannel(): array
    {
        return (array)preg_split('/\s*,\s*/', trim((string)$this->rate_capital_code), -1, PREG_SPLIT_NO_EMPTY);
    }

    public function setRateCapitalChannel($uses): void
    {
        $this->rate_capital_code = implode(',', array_filter((array)$uses));
    }

    public function getRateLateRuleNo(): array
    {
        return (array)preg_split('/\s*,\s*/', trim((string)$this->rate_late_no), -1, PREG_SPLIT_NO_EMPTY);
    }

    public function setRateLateRuleNo($rules): void
    {
        $this->rate_late_no = implode(',', array_filter((array)$rules));
    }

    /**
     * @return ActiveQuery
     */
    public function getRateConfigs(): ActiveQuery
    {
        return $this->hasMany(RateConfig::class, ['rate_config_rate_number' => 'rate_number']);
    }

    public function getRateVarNums(): ActiveQuery
    {
        return $this->hasMany(RateVarNum::class, ['rate_var_num_source_num' => 'rate_number']);
    }

    public function getCapital(): array
    {
        $codes = $this->getRateCapitalChannel();

        return Capital::find()->where(['capital_code' => $codes])->all();
    }

    public function getLate(): array
    {
        return LateRule::find()->where(['late_no' => $this->getRateLateRuleNo()])->all();
    }

    public function getProducts(): ActiveQuery
    {
        return $this->hasMany(Product::class, ['product_scope' => 'product_rate_scope'])
            ->via('productRate');
    }

    public function getProductRate(): ActiveQuery
    {
        return $this->hasMany(ProductRate::class, ['product_rate_number' => 'rate_number']);
    }

    /**
     * @param $rateId
     * @param $rateStatus
     *
     * @throws Exception
     */
    public static function auditRate($rateId, $rateStatus)
    {
        $rate = self::findOne($rateId);
        if (!$rate) {
            throw new Exception(sprintf('不存在，费率【%s】', $rateId));
        }
        if (self::STATUS_PASS == $rateStatus) {
            if ($rate->rate_status == self::STATUS_VOID) {
                throw new Exception(sprintf('状态为【%s】,不允许审核', $rate->rate_status));
            }
            if (self::STATUS_PASS == $rate->rate_status) {
                throw new Exception(sprintf('状态为【%s】已审核通过，不可再次审核', $rate->rate_status));
            }
        } elseif (self::STATUS_VOID == $rateStatus) {
            if (in_array($rate->rate_status, [self::STATUS_OVERDUE, self::STATUS_PASS])) {
                throw new Exception(sprintf('状态为【%s】,不允许修改', $rate->rate_status));
            }
        } elseif (self::STATUS_OVERDUE == $rateStatus) {
            if ($rate->rate_status == self::STATUS_VOID) {
                throw new Exception(sprintf('状态为【%s】,不允许修改', $rate->rate_status));
            }
        }
        $rate->rate_status = $rateStatus;
        if (!$rate->save()) {
            throw new Exception(json_encode($rate->getErrors(), JSON_UNESCAPED_UNICODE));
        }
    }
}
