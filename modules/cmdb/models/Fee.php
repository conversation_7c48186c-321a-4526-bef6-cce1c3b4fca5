<?php

namespace cmdb\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "fee".
 *
 * @property int    $fee_id
 * @property int    $fee_type
 * @property string $fee_code
 * @property string $fee_name
 * @property string $fee_memo
 * @property int    $fee_status
 * @property int    $fee_create_user_id
 * @property string $fee_create_user_name
 * @property string $fee_create_at
 * @property string $fee_update_at
 */
class Fee extends ActiveRecord
{
    public const FEE_TYPE_COMPREHENSIVE = 1;
    public const FEE_TYPE_INTEREST      = 2;
    public const FEE_TYPE_SPLIT_FEE     = 3;
    public const TYPE_LIST              = [
        self::FEE_TYPE_COMPREHENSIVE => '年化综合息费',
        self::FEE_TYPE_INTEREST      => '利息',
        self::FEE_TYPE_SPLIT_FEE     => '拆分费用',
    ];

    public const FEE_CODE_COMPREHENSIVE     = 'comprehensive_interest';
    public const FEE_CODE_CAPITAL_COST      = 'capital_cost';
    public const FEE_CODE_INTEREST          = 'interest';
    public const FEE_CODE_SERVICE           = 'service';
    public const FEE_CODE_AFTER_LOAN_MANAGE = 'after_loan_manage';
    public const FEE_CODE_TECHNICAL_SERVICE = 'technical_service';
    public const CODE_LIST                  = [
        self::FEE_CODE_COMPREHENSIVE     => 10,
        self::FEE_CODE_CAPITAL_COST      => 20,
        self::FEE_CODE_INTEREST          => 30,
        self::FEE_CODE_SERVICE           => 40,
        self::FEE_CODE_AFTER_LOAN_MANAGE => 50,
        self::FEE_CODE_TECHNICAL_SERVICE => 60,
    ];

    public const STATUS_SHOW = 'show';
    public const STATUS_HIDE = 'hide';
    public const STATUS_LIST = [
        self::STATUS_SHOW => '显示',
        self::STATUS_HIDE => '隐藏',
    ];

    /**
     * @return array[]
     */
    public function behaviors(): array
    {
        return [
            [
                'class'              => BlameableBehavior::class,
                'updatedByAttribute' => null,
                'createdByAttribute' => 'fee_create_user_id',
                'value'              => function () {
                    return Yii::$app->getUser()->getIdentity()->getId();
                },
            ],
            [
                'class'              => BlameableBehavior::class,
                'updatedByAttribute' => null,
                'createdByAttribute' => 'fee_create_user_name',
                'value'              => function () {
                    return Yii::$app->getUser()->getIdentity()->getId();
                },
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'fee';
    }

    /**
     * @return Connection
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['fee_type', 'fee_code', 'fee_name'], 'required'],
            [['fee_type', 'fee_create_user_id'], 'integer'],
            [['fee_create_at', 'fee_update_at'], 'safe'],
            [['fee_status'], 'string'],
            [['fee_code', 'fee_name', 'fee_memo', 'fee_create_user_name'], 'string', 'max' => 50],
            [['fee_code'], 'unique', 'message' => '编码重复'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'fee_id'               => 'ID',
            'fee_type'             => '费用类型',
            'fee_code'             => '费用编码',
            'fee_name'             => '费用名称',
            'fee_memo'             => '费用备注',
            'fee_status'           => '可见性',
            'fee_create_user_id'   => '创建人ID',
            'fee_create_user_name' => '创建人姓名',
            'fee_create_at'        => '创建时间',
            'fee_update_at'        => '更新时间',
        ];
    }

    /**
     * @param bool $onlyVisible
     *
     * @return array
     */
    public static function feeMap(bool $onlyVisible = false): array
    {
        $query = self::find();
        if ($onlyVisible) {
            $query->where(['fee_status' => self::STATUS_SHOW]);
        }

        return $query
            ->select('fee_name')
            ->indexBy('fee_code')
            ->column();
    }

    /**
     * 获取费用执行顺序
     *
     * @return array
     */
    public static function executionOrder(): array
    {
        return self::find()->select('fee_id')->indexBy('fee_code')->column();
    }
}
