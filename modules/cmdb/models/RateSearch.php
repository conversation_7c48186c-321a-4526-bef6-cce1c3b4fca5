<?php

namespace cmdb\models;

use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

class RateSearch extends RateEntity
{
    public $product_scope;
    public $state;
    public $bind;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['rate_name', 'rate_number', 'rate_capital_code', 'product_scope'], 'safe'],
            [['rate_status'], 'in', 'range' => array_keys(self::STATUS_LIST)],
            [['rate_interest_year_days'], 'in', 'range' => array_keys(self::INTEREST_YEAR_DAYS)],
            [['state', 'bind'], 'string'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'defaultOrder' => [
                    'rate_update_at' => SORT_DESC,
                    'rate_id'        => SORT_DESC,
                ],
                'attributes'   => [
                    'rate_update_at',
                    'rate_id',
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        if ($this->product_scope) {
            $query
                ->distinct()
                ->innerJoinWith([
                    'productRate' => function (ActiveQuery $query) {
                        $query->alias('pr')
                            ->onCondition(['pr.product_rate_scope' => $this->product_scope]);
                    },
                ], false);
        }

        if ($this->rate_principal_min_amount) {
            $query->andWhere([
                'and',
                ['<=', 'rate_principal_min_amount', $this->rate_principal_min_amount * 100],
                ['>=', 'rate_principal_max_amount', $this->rate_principal_min_amount * 100],
            ]);
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'rate_status'             => $this->rate_status,
            'rate_interest_year_days' => $this->rate_interest_year_days,
            'rate_capital_code'       => $this->rate_capital_code,
        ]);

        $query->andFilterWhere(['like', 'rate_name', $this->rate_name])
            ->andFilterWhere(['like', 'rate_number', $this->rate_number]);

        return $dataProvider;
    }
}
