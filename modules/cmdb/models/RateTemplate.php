<?php

namespace cmdb\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\base\ModelEvent;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\AfterSaveEvent;
use yii\db\Connection;

/**
 * This is the model class for table "{{%rate_template}}".
 *
 * @property int            $rate_template_id         ID
 * @property string         $rate_template_name       模板名称
 * @property int            $rate_template_status     是否可见
 * @property string         $rate_template_config     模板配置
 * @property string         $rate_template_created_at 创建时间
 * @property string         $rate_template_updated_at 修改时间
 * @property int            $rate_template_created_by 创建人
 * @property int            $rate_template_updated_by 修改人
 * @property-read Capital[] $capitals
 */
class RateTemplate extends ActiveRecord
{
    public const STATUS_SHOW = 'show';
    public const STATUS_HIDE = 'hide';
    public const STATUS_LIST = [
        self::STATUS_SHOW => '显示',
        self::STATUS_HIDE => '隐藏',
    ];

    public function init($runValidation = true, $attributeNames = null)
    {
        $this->on(self::EVENT_AFTER_INSERT, function (AfterSaveEvent $event) {
            /** @var self $model */
            $model    = $event->sender;
            $config   = json_decode($model->rate_template_config, true);
            $capitals = (array)($config['capitals'] ?? null);
            $data     = [];
            foreach ($capitals as $capitalCode) {
                $data[] = [$model->rate_template_id, $capitalCode];
            }
            RateTemplateCapital::getDb()
                ->createCommand()
                ->batchInsert(RateTemplateCapital::tableName(), ['rate_template_id', 'capital_code'], $data)
                ->execute();
        });

        $this->on(self::EVENT_AFTER_UPDATE, function (AfterSaveEvent $event) {
            /** @var self $model */
            $model = $event->sender;

            $deleteEvent = new ModelEvent(['sender' => $model]);
            $model->trigger(self::EVENT_BEFORE_DELETE, $deleteEvent);
            $model->trigger(self::EVENT_AFTER_INSERT, $event);
        });

        $this->on(self::EVENT_BEFORE_DELETE, function (ModelEvent $event) {
            /** @var self $model */
            $model = $event->sender;

            RateTemplateCapital::deleteAll([
                'rate_template_id' => $model->rate_template_id,
            ]);
        });

        parent::init();
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%rate_template}}';
    }

    /**
     * @return Connection
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    public function behaviors(): array
    {
        return [
            [
                'class'              => BlameableBehavior::class,
                'createdByAttribute' => 'rate_template_created_by',
                'updatedByAttribute' => 'rate_template_updated_by',
                'value'              => function () {
                    return Yii::$app->getUser()->getId();
                },
            ],
        ];
    }

    /**
     * @return array
     */
    public function transactions(): array
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['rate_template_config'], 'string'],
            [['rate_template_created_at', 'rate_template_updated_at'], 'safe'],
            [['rate_template_created_by', 'rate_template_updated_by'], 'string'],
            [['rate_template_status'], 'string'],
            [['rate_template_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'rate_template_id'         => 'ID',
            'rate_template_name'       => '模板名称',
            'rate_template_status'     => '是否可见',
            'rate_template_config'     => '模板配置',
            'rate_template_created_at' => '创建时间',
            'rate_template_updated_at' => '修改时间',
            'rate_template_created_by' => '创建人',
            'rate_template_updated_by' => '修改人',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getCapitals(): ActiveQuery
    {
        return $this->hasMany(Capital::class, ['capital_code' => 'capital_code'])
            ->via('rateTemplateCapital');
    }

    /**
     * @return ActiveQuery
     */
    public function getRateTemplateCapital(): ActiveQuery
    {
        return $this->hasMany(RateTemplateCapital::class, ['rate_template_id' => 'rate_template_id']);
    }
}
