<?php

namespace cmdb\models;

use Yii;
use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "rate_var_num".
 *
 * @property int $rate_var_num_id
 * @property string $rate_var_num_code       动态费率编号，原始动态费率编号超过48位时，取MD5值
 * @property string $rate_var_num_raw_code   原始动态费率编号
 * @property string $rate_var_num_source_num 源费率编号
 * @property string $rate_var_num_late_num   罚息规则编码
 * @property string $rate_var_num_fees       费率细则(json字符串)
 * @property string $rate_var_num_status     状态：open:新增，void:作废，online:线上运行,offline:下线
 * @property string $rate_var_num_create_at  创建时间
 * @property string $rate_var_num_update_at  更新时间
 * @property string $rate_var_num_period_terms  每期还款期限
 * @property string $rate_var_num_period_amount_percents  每期还款本金比例
 * @property Rate $rate
 */
class RateVarNum extends \yii\db\ActiveRecord
{

    public const STATUS_LIST = [
        'open' => '新增',
        'void' => '作废',
        'pass' => '已上线',
        'offline' => '下线',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'rate_var_num';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbCmdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rate_var_num_source_num', 'rate_var_num_late_num'], 'required'],
            [['rate_var_num_create_at', 'rate_var_num_update_at'], 'safe'],
            [['rate_var_num_code', 'rate_var_num_source_num'], 'string', 'max' => 48],
            [['rate_var_num_raw_code'], 'string', 'max' => 256],
            [['rate_var_num_late_num'], 'string', 'max' => 64],
            [['rate_var_num_fees'], 'string', 'max' => 512],
            [['rate_var_num_status'], 'string', 'max' => 12],
            [['rate_var_num_code'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'rate_var_num_id' => 'id',
            'rate_var_num_code' => '动态费率编号，原始动态费率编号超过48位时，取MD5值',
            'rate_var_num_raw_code' => '原始动态费率编号',
            'rate_var_num_source_num' => '源费率编号',
            'rate_var_num_late_num' => '罚息规则编码',
            'rate_var_num_fees' => '费率细则(json字符串)',
            'rate_var_num_status' => '状态：open:新增，void:作废，online:线上运行,offline:下线',
            'rate_var_num_create_at' => '创建时间',
            'rate_var_num_update_at' => '更新时间',
            'rate_var_num_period_terms' => '每期还款期限',
            'rate_var_num_period_amount_percents' => '每期还款本金比例',
        ];
    }

    public function periodAmountPercents(): array
    {
        return Json::decode($this->rate_var_num_period_amount_percents);
    }

    public function periodTerms(): array
    {
        return Json::decode($this->rate_var_num_period_terms);
    }

    /**
     * @return ActiveQuery
     */
    public function getRate(): ActiveQuery
    {
        return $this->hasOne(Rate::class, ['rate_number' => 'rate_var_num_source_num']);
    }
}
