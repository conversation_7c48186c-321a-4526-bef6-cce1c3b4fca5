<?php

namespace cmdb\models;

use yii\data\ActiveDataProvider;

/**
 * RateTemplateSearch represents the model behind the search form about `cmdb\models\RateTemplate`.
 */
class RateTemplateSearch extends RateTemplate
{
    public $capitalCode;

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['capitalCode'], 'string'],
            [['rate_template_name'], 'trim'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = RateTemplate::find()
            ->alias('t')
            ->innerJoinWith(['capitals']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'attributes'   => ['t.rate_template_id'],
                'defaultOrder' => ['t.rate_template_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query
            ->andFilterWhere(['capital.capital_code' => $this->capitalCode])
            ->andFilterWhere(['like', 't.rate_template_name', $this->rate_template_name]);

        return $dataProvider;
    }
}
