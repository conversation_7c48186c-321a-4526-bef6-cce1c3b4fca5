<?php

namespace cmdb\models;

use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%product_rate}}".
 *
 * @property int    $product_rate_id
 * @property string $product_rate_scope  资产使用的场景
 * @property string $product_rate_number 资产使用的场景
 * @property string $product_rate_memo   备注
 * @property string $product_rate_status 状态：open:新增，void:作废，online:线上运行,offline:下线
 * @property string $product_rate_create_at
 * @property string $product_rate_update_at
 */
class ProductRate extends ActiveRecord
{
    public const STATUS_OPEN    = 'open';
    public const STATUS_VOID    = 'void';
    public const STATUS_ONLINE  = 'online';
    public const STATUS_OFFLINE = 'offline';

    public const STATUS = [
        self::STATUS_OPEN    => '新增',
        self::STATUS_VOID    => '作废',
        self::STATUS_ONLINE  => '上线',
        self::STATUS_OFFLINE => '下线',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%product_rate}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_rate_memo', 'product_rate_status'], 'string'],
            [['product_rate_create_at', 'product_rate_update_at'], 'safe'],
            [['product_rate_scope', 'product_rate_number'], 'string', 'max' => 50],
            [
                ['product_rate_scope', 'product_rate_number'],
                'unique',
                'targetAttribute' => ['product_rate_scope', 'product_rate_number'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'product_rate_id'        => 'Product Rate ID',
            'product_rate_scope'     => '资产使用的场景',
            'product_rate_number'    => '资产使用的场景',
            'product_rate_memo'      => '备注',
            'product_rate_status'    => '状态：open:新增，void:作废，online:线上运行,offline:下线',
            'product_rate_create_at' => 'Product Rate Create At',
            'product_rate_update_at' => 'Product Rate Update At',
        ];
    }

    public function getProduct()
    {
        return $this->hasOne(Product::class, ['product_scope' => 'product_rate_scope']);
    }

    public function getRate()
    {
        return $this->hasOne(Rate::class, ['rate_number' => 'product_rate_number']);
    }
}
