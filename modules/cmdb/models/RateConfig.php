<?php

namespace cmdb\models;

use Generator;
use xlerr\common\helpers\MoneyHelper;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "rate_config".
 *
 * @property int $rate_config_id
 * @property string $rate_config_rate_number
 * @property string $rate_config_fee_code
 * @property int $rate_config_fee_type
 * @property string $rate_config_repay_type
 * @property string $rate_config_calculate_type
 * @property string $rate_config_carry_mode
 * @property string $rate_config_memo
 * @property float $rate_config_value
 * @property int $rate_config_parent_id
 * @property int $rate_config_execution_order
 * @property string $rate_config_value_range
 * @property string $rate_config_create_at
 * @property string $rate_config_update_at
 * @property float $rate_config_vat_rate
 * @property Fee $fee
 * @property String $parentFeeName
 * @property String $valueFormat
 */
class RateConfig extends ActiveRecord
{
    public const BEGINNING_DEDUCTION = 'beginning_deduction';
    public const ENDING_DEDUCTION = 'ending_deduction';
    public const COLLECT_SCHEDULE = 'collect_schedule';
    public const REPAY_TYPE_LIST = [
        self::BEGINNING_DEDUCTION => '期初收取',
        self::ENDING_DEDUCTION => '期末收取',
        self::COLLECT_SCHEDULE => '按期收取',
    ];

    public const ROUND_HALF_UP = 'round_half_up';
    public const ROUND_HALF_EVEN = 'round_half_even';
    public const ROUND_DOWN = 'round_down';
    public const ROUND_UP = 'round_up';
    public const ROUND_LIST = [
        self:: ROUND_HALF_UP => '四舍五入',
        self:: ROUND_HALF_EVEN => '四舍六入五成双',
        self:: ROUND_DOWN => '向下舍掉',
        self:: ROUND_UP => '向前进位',
    ];

    public const ACPI = 'acpi';
    public const ACPI_V2 = 'acpi_v2';
    public const ACPI_V2_BY_DAY = 'acpi_v2_by_day';
    public const ACPI_P1_BY_DAY = 'acpi_p1_by_day';
    public const AVERAGECAPITAL = 'averagecapital';
    public const EQUAL = 'equal';
    public const EQUAL_BY_DAY = 'equal_by_day';
    public const RTLATAIO = 'rtlataio';
    public const SCHEDULE = 'schedule';
    public const ACPI_DIFF = 'acpi_diff';
    public const SUBDUCTION = 'subduction';
    public const FIXED_AMOUNT = 'fixed_amount';
    public const FIXED_PERCENT = 'fixed_percent';
    public const CREDIT_BY_DAY = 'credit_by_day';
    public const PILP = 'per_interest_last_principal';
    public const TADPOLE = 'tadpole';
    public const TADPOLE_TOTAL_TERM_PRINCIPAL_PERCENTS = 'tadpole_total_term_principal_percents';
    public const TADPOLE_TOTAL_TERM_PRINCIPAL_PERCENTS_FIXED = 'tadpole_total_term_principal_percents_fixed';
    public const PROPORTIONAL = 'proportional';


    public const CALCULATE_TYPE_LIST = [
        self::ACPI => '等额本息',
        self::ACPI_V2 => '等额本息V2',
        self::ACPI_V2_BY_DAY => '等额本息v2(按天计息)',
        self::ACPI_P1_BY_DAY => '等额本息（首期按天计息）',
        self::AVERAGECAPITAL => '等额本金',
        self::EQUAL => '等本等息（年化率）',
        self::EQUAL_BY_DAY => '等本等息（年化按天计息）',
        self::RTLATAIO => '一次性到期还本付息',
        // self::SCHEDULE       => '按期配置',
        self::ACPI_DIFF => '等额本息差额',
        self::SUBDUCTION => '倒减',
        self::FIXED_AMOUNT => '固定金额',
        self::FIXED_PERCENT => '固定比例',
        self::CREDIT_BY_DAY => '在贷按日计息',
        self::PILP => '按月付息,到期还本',
        self::TADPOLE => '蝌蚪算法',
        self::TADPOLE_TOTAL_TERM_PRINCIPAL_PERCENTS => '蝌蚪算法(总本金*每期本金比例*日息*总占用天数)',
        self::TADPOLE_TOTAL_TERM_PRINCIPAL_PERCENTS_FIXED => '蝌蚪算法(总本金*每期本金比例*固定比例)',
        self::PROPORTIONAL => '按比例扣除(如增值税VAT，必须依附于某一具体费用项)',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'rate_config';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['rate_config_parent_id'], 'default', 'value' => 0],
            [['rate_config_vat_rate'], 'default', 'value' => 0],
            [['rate_config_fee_type', 'rate_config_value', 'rate_config_execution_order'], 'required'],
            [['rate_config_fee_type', 'rate_config_parent_id', 'rate_config_execution_order'], 'integer'],
            [['rate_config_repay_type', 'rate_config_carry_mode', 'rate_config_memo'], 'string'],
            [['rate_config_value', 'rate_config_vat_rate'], 'number'],
            [
                ['rate_config_create_at', 'rate_config_update_at', 'rate_config_value_range', 'rate_config_vat_rate'],
                'safe',
            ],
            [['rate_config_rate_number', 'rate_config_fee_code', 'rate_config_calculate_type'], 'string', 'max' => 50],
            [
                ['rate_config_fee_code'],
                'unique',
                'targetAttribute' => ['rate_config_rate_number', 'rate_config_fee_code'],
                'message' => '费用项已存在',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'rate_config_id' => 'ID',
            'rate_config_rate_number' => '费率编号',
            'rate_config_fee_code' => '费用',
            'rate_config_fee_type' => '费用类型',
            'rate_config_repay_type' => '还款方式',
            'rate_config_calculate_type' => '计算方式',
            'rate_config_carry_mode' => '进位方式',
            'rate_config_memo' => '备注',
            'rate_config_value' => '费用计算值',
            'rate_config_parent_id' => '父级费用',
            'rate_config_execution_order' => '执行顺序',
            'rate_config_create_at' => '创建时间',
            'rate_config_update_at' => '更新时间',
        ];
    }

    /**
     * 关联查询费用
     *
     * @return ActiveQuery
     */
    public function getFee(): ActiveQuery
    {
        return $this->hasOne(Fee::class, ['fee_code' => 'rate_config_fee_code']);
    }

    /**
     * @return float
     */
    public function calculateValue(): float
    {
        if ($this->rate_config_calculate_type === RateConfig::FIXED_AMOUNT) {
            return (float)MoneyHelper::f2y($this->rate_config_value);
        } else {
            return $this->rate_config_value;
        }
    }

    /**
     * @return string
     */
    public function getValueFormat(): string
    {
        return self::valueFormat($this);
    }

    /**
     * @param $rateConfig
     *
     * @return string
     */
    public static function valueFormat($rateConfig): string
    {
        if ($rateConfig['rate_config_calculate_type'] == self::FIXED_AMOUNT) {
            return MoneyHelper::f2y($rateConfig['rate_config_value'], true) . '元';
        } else {
            return floatval($rateConfig['rate_config_value']) . '%';
        }
    }

    /**
     * 通过 FeeCode 获取FeeType
     *
     * @param $feeCode
     *
     * @return int
     */
    public static function feeType($feeCode): int
    {
        return [
            Fee::FEE_CODE_COMPREHENSIVE => Fee::FEE_TYPE_COMPREHENSIVE,
            Fee::FEE_CODE_INTEREST => Fee::FEE_TYPE_INTEREST,
        ][$feeCode] ?? Fee::FEE_TYPE_SPLIT_FEE;
    }

    /**
     * @param array $rateConfigs
     * @param int $parentId
     * @param int $level
     *
     * @return Generator
     */
    public static function arrange(array $rateConfigs, int $parentId = 0, int $level = 0): Generator
    {
        foreach ($rateConfigs as $i => $rateConfig) {
            if ($parentId === (int)$rateConfig['rate_config_parent_id']) {
                $rateConfig['__level'] = $level;
                yield $rateConfig;
                unset($rateConfigs[$i]);
                $subGenerator = self::arrange($rateConfigs, $rateConfig['rate_config_id'], $level + 1);
                foreach ($subGenerator as $item) {
                    yield $item;
                }
            }
        }
    }
}
