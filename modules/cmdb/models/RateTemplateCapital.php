<?php

namespace cmdb\models;

use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%rate_template_capital}}".
 *
 * @property int $rate_template_id 费率模板编号
 * @property int $capital_code     资金方编号
 */
class RateTemplateCapital extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%rate_template_capital}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['rate_template_id', 'capital_code'], 'required'],
            [['rate_template_id'], 'integer'],
            [['rate_template_id', 'capital_code'], 'unique', 'targetAttribute' => ['rate_template_id', 'capital_code']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'rate_template_id' => '费率模板编号',
            'capital_code'     => '资金方编号',
        ];
    }
}
