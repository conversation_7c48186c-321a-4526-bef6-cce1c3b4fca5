<?php

namespace cmdb\models;

use yii\base\Model;

/**
 * @property string $fees
 */
class RateTemplateEntity extends Model
{
    public $capitals;
    public $periodCategory;
    public $lateNo;
    public $yearDayNum;
    public $minAmount;
    public $maxAmount;
    public $startDate;
    public $endDate;
    public $extraJson;
    public $memo;
    public $monthClearDay;
    public $clearDay;
    public $effectDay;
    public $repayDateFormula;

    private array $fees = [];

    /**
     * @return string
     */
    public function getFees(): string
    {
        return json_encode($this->fees);
    }

    /**
     * @param string $fees
     */
    public function setFees(string $fees): void
    {
        $this->fees = (array)json_decode($fees, true);
    }

    public function fields(): array
    {
        return parent::fields() + ['fees' => 'fees'];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['capitals', 'periodCategory', 'lateNo', 'yearDayNum'], 'required'],
            [
                ['monthClearDay', 'clearDay'],
                'safe',
            ],
            [
                ['effectDay', 'repayDateFormula', 'startDate', 'endDate', 'extraJson', 'memo', 'fees'],
                'safe',
            ],
            [['minAmount', 'maxAmount'], 'integer', 'min' => 0, 'max' => 99999999999],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'capitals'         => '适用资金方',
            'lateNo'           => '罚息规则',
            'periodCategory'   => '期次类型',
            'yearDayNum'       => '利率年化天数',
            'minAmount'        => '起始金额',
            'maxAmount'        => '结束金额',
            'startDate'        => '开始日期',
            'endDate'          => '结束日期',
            'extraJson'        => '额外信息',
            'memo'             => '需求',
            'monthClearDay'    => '月结日',
            'clearDay'         => '结清日',
            'effectDay'        => '起息日计算方式',
            'repayDateFormula' => '还款日算法',
        ];
    }
}
