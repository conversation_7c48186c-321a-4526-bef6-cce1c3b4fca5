<?php

namespace cmdb\models;

use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

/**
 * @property-read Capital[] $capitals
 */
class ProductSearch extends Product
{
    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['product_scope'], 'safe'],
            [['product_status'], 'in', 'range' => array_keys(self::STATUS_LIST)],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'defaultOrder' => [
                    'product_update_at' => SORT_DESC,
                    'product_id'        => SORT_DESC,
                ],
                'attributes'   => [
                    'product_update_at',
                    'product_id',
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'product_status' => $this->product_status,
        ]);

        $query->andFilterWhere(['like', 'product_scope', $this->product_scope]);

        return $dataProvider;
    }

    /**
     * @return ActiveQuery
     */
    public function getCapitals(): ActiveQuery
    {
        return $this->hasMany(Capital::class, ['capital_code' => 'rate_capital_code'])
            ->via('rates');
    }
}
