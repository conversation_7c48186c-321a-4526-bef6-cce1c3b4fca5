<?php

namespace cmdb\models;

use Carbon\Carbon;
use Exception;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use xlerr\common\helpers\MoneyHelper;
use yii\base\UserException;
use yii\db\AfterSaveEvent;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * @property array $periodCategory
 * @property array $rateLateRuleNo
 */
class RateEntity extends Rate
{
    public $rateTemplate;

    public $fees;

    public function init()
    {
        parent::init();
        $this->on(self::EVENT_AFTER_INSERT, function (AfterSaveEvent $event) {
            /** @var self $rate */
            $rate = $event->sender;

            $parentIdMap = [];

            $fees = (array)json_decode($rate->fees, true);
            foreach ($fees as $fee) {
                $rateConfig = new RateConfig();

                $rateConfig->rate_config_rate_number = $rate->rate_number;
                $rateConfig->rate_config_parent_id = $parentIdMap[$fee['__level'] - 1] ?? null;
                $rateConfig->rate_config_fee_code = $fee['feeCode'];
                $rateConfig->rate_config_repay_type = $fee['repayType'];
                $rateConfig->rate_config_calculate_type = $fee['calculateType'];
                $rateConfig->rate_config_carry_mode = $fee['carryMode'];
                $rateConfig->rate_config_value = $fee['value'];
                $rateConfig->rate_config_execution_order = $fee['executionOrder'];
                $rateConfig->rate_config_memo = $fee['memo'];
                $rateConfig->rate_config_value_range = $fee['valueRange'];
                $rateConfig->rate_config_vat_rate = $fee['vatRate'];
                $rateConfig->rate_config_fee_type = RateConfig::feeType($rateConfig->rate_config_fee_code);
                if ($rateConfig->rate_config_calculate_type === RateConfig::FIXED_AMOUNT) {
                    $rateConfig->rate_config_value = MoneyHelper::y2f($rateConfig->rate_config_value);
                }

                if (!$rateConfig->save()) {
                    throw new Exception(Json::encode($rateConfig->getErrors()));
                }

                $parentIdMap[(int)$fee['__level']] = $rateConfig->rate_config_id;
            }
        });

        $this->on(self::EVENT_AFTER_UPDATE, function (AfterSaveEvent $event) {
            /** @var self $rate */
            $rate = $event->sender;

            /** @var RateConfig[] $rateConfigs */
            $rateConfigs = $rate->getRateConfigs()->indexBy('rate_config_fee_code')->all();
            $fees = (array)json_decode($rate->fees, true);
            foreach ($fees as $fee) {
                $rateConfig = $rateConfigs[$fee['feeCode']] ?? null;
                if (!$rateConfig) {
                    // 逻辑上是不能出现这种情况的
                    throw new UserException('修改一个未知的费用项');
                }
                $rateConfig->rate_config_calculate_type = $fee['calculateType'];
                $rateConfig->rate_config_carry_mode = $fee['carryMode'];
                $rateConfig->rate_config_value = $fee['value'];
                $rateConfig->rate_config_value_range = $fee['valueRange'];
                $rateConfig->rate_config_vat_rate = $fee['vatRate'];
                if ($rateConfig->rate_config_calculate_type === RateConfig::FIXED_AMOUNT) {
                    $rateConfig->rate_config_value = MoneyHelper::y2f($rateConfig->rate_config_value);
                }
                if (!$rateConfig->save()) {
                    throw new Exception(Json::encode($rateConfig->getErrors()));
                }
            }
        });
    }

    public function rules(): array
    {
        return [
            [['rateTemplate'], 'default', 'value' => 0],
            [['rateTemplate'], 'required', 'message' => '请选择{attribute}'],
            [
                [
                    'rate_capital_code',
                    'rate_number',
                    'rate_name',
                    'rate_late_no',
                    'rate_interest_year_days',
                    'rate_effect_day',
                    'rate_repay_date_formula',
                    'rate_valid_start_date',
                    'rate_valid_end_date',
                    'periodCategory',
                ],
                'required',
            ],
            [['periodCategory'], 'each', 'rule' => ['match', 'pattern' => '/^\d+,\d+,(day|month|tadpole)$/']],
            [['rate_effect_day'], 'match', 'pattern' => '/^[DT]\+\d+$/'],
            [
                [
                    'rate_principal_min_amount',
                    'rate_principal_max_amount',
                ],
                'integer',
                'min' => 1,
                'max' => 9999999999,
                'tooSmall' => '金额不能小于0.01元',
                'tooBig' => '金额不能大于99999999.99元',
            ],
            [['rate_extra_json', 'rate_memo'], 'string'],
            [
                ['rate_month_clear_day', 'rate_clear_day', 'rateCapitalChannel', 'rateLateRuleNo'],
                'safe',
            ],
            [['fees'], 'safe'],
            [['rate_number'], 'unique', 'message' => '费率编号已存在'],
        ];
    }

    public function attributeLabels(): array
    {
        return array_merge(parent::attributeLabels(), [
            'rateTemplate' => '费率模板',
            'periodCategory' => '期次类型',
        ]);
    }

    /**
     * @return string
     */
    public function feesToString(): string
    {
        $fees = [];
        $rateConfigs = $this->rateConfigs;
        foreach ($rateConfigs as $rateConfig) {
            $fees[$rateConfig->rate_config_id] = [
                'feeCode' => $rateConfig->rate_config_fee_code,
                'repayType' => $rateConfig->rate_config_repay_type,
                'calculateType' => $rateConfig->rate_config_calculate_type,
                'carryMode' => $rateConfig->rate_config_carry_mode,
                'value' => $rateConfig->calculateValue(),
                'executionOrder' => $rateConfig->rate_config_execution_order,
                'valueRange' => $rateConfig->rate_config_value_range,
                'vatRate' => (float)$rateConfig->rate_config_vat_rate,
                '__level' => ($fees[$rateConfig->rate_config_parent_id]['__level'] ?? -1) + 1,
            ];
        }
        return json_encode(array_values($fees));
    }

    /**
     * @param mixed $key
     *
     * @return mixed
     * @throws KVException
     */
    public static function config($key = null)
    {
        $config = KeyValue::takeAsArray('rate_template_config', 'cmdb');

        $periodCategory = (array)ArrayHelper::remove($config, 'period_category');

        $periodKey = function ($item) {
            return vsprintf(
                '%d,%d,%s',
                ArrayHelper::filter($item, [
                    'period_count',
                    'period_term',
                    'period_type',
                ])
            );
        };

        $config += [
            'period_category_list' => ArrayHelper::map($periodCategory, $periodKey, 'label'),
            'period_category_list_title' => ArrayHelper::map($periodCategory, $periodKey, 'title'),
            'period_category_list_id' => ArrayHelper::map($periodCategory, $periodKey, 'key'),
            'available_period_category' => array_map(
                $periodKey,
                array_filter($periodCategory, function ($item) {
                    return $item['available'] ?? false;
                })
            ),
        ];

        if ($key) {
            return $config[$key] ?? null;
        }

        return $config;
    }

    public function setPeriodCategory($values = [])
    {
        if (empty($values)) {
            $values = [];
        }
        $data = [];
        foreach ($values as $value) {
            [$count, $term, $type] = explode(',', $value);
            array_push($data, [
                'periodCount' => $count,
                'periodTerm' => $term,
                'periodType' => $type,
            ]);
        }

        $this->rate_periods = Json::encode($data);
    }

    public function getPeriodCategory(): array
    {
        $data = (array)(json_decode($this->rate_periods, true) ?? []);
        foreach ($data as &$row) {
            $row = vsprintf('%d,%d,%s', [
                $row['periodCount'],
                $row['periodTerm'],
                $row['periodType'],
            ]);
        }

        return $data;
    }

    public function periodCategoryLabel(): string
    {
        $config = RateEntity::config('period_category_list');

        $result = [];
        foreach ($this->periodCategory as $period) {
            $result[] = $config[$period] ?? $period;
        }

        return implode(', ', $result);
    }

    /**
     * @return array
     */
    public function toTemplate(): array
    {
        return [
            'periodCategory' => $this->periodCategory,
            'lateNo' => $this->rateLateRuleNo,
            'yearDayNum' => $this->rate_interest_year_days,
            'effectDay' => $this->rate_effect_day,
            'repayDateFormula' => $this->rate_repay_date_formula,
            'minAmount' => $this->rate_principal_min_amount,
            'maxAmount' => $this->rate_principal_max_amount,
            'startDate' => Carbon::parse($this->rate_valid_start_date)->toDateString(),
            'endDate' => Carbon::parse($this->rate_valid_end_date)->toDateString(),
            'extraJson' => $this->rate_extra_json,
            'memo' => $this->rate_memo,
            'monthClearDay' => $this->rate_month_clear_day,
            'clearDay' => $this->rate_clear_day,
            'fees' => $this->fees ?? $this->feesToString(),
        ];
    }
}
