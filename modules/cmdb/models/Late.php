<?php

namespace cmdb\models;

use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%late}}".
 *
 * @property int             $late_id            主键
 * @property string          $late_no            罚息规则编码
 * @property string          $late_name          罚息规则名称
 * @property string          $late_desc          计算规则描述
 * @property string          $late_limit_type    计算上限取值方式：PERIOD:按期收取，CONTRACT:按合同收取（所有期次值之和）
 * @property string          $late_limit_formula 计算上限规则 FIXED_AMOUNT 固定值,单位元.FIXED_PERCENT 固定比列
 * @property string          $late_limit_value   上限值
 * @property string          $late_status        状态：open:新增，online:线上运行,offline:下线
 * @property string          $late_create_at
 * @property string          $late_update_at
 * @property-read Rate[]     $rates
 * @property-read LateTran[] $trans
 */
class Late extends ActiveRecord
{
    public const TYPE_PERIOD = 'PERIOD';
    public const TYPE_CONTRACT = 'CONTRACT';
    public const TYPE_CONTRACT_ALL_FEES = 'CONTRACT_ALL_FEES';
    public const TYPE_CONTRACT_FEES_DELAY = 'CONTRACT_FEES_DELAY';

    public const TYPE_LIST = [
        self::TYPE_PERIOD => '依据当期本金限制当期罚息上限',
        self::TYPE_CONTRACT => '依据合同本金限制整笔罚息上限',
        self::TYPE_CONTRACT_ALL_FEES => '依据合同本金限制整笔息费上限',
        self::TYPE_CONTRACT_FEES_DELAY => '依据合同本金限制整笔息费展期费上限',
    ];

    public const FORMULA_FIXED_PERCENT = 'FIXED_PERCENT';
    public const FORMULA_FIXED_AMOUNT = 'FIXED_AMOUNT';
    public const FORMULAS = [
        self::FORMULA_FIXED_AMOUNT => '固定值',
        self::FORMULA_FIXED_PERCENT => '固定比例',
    ];

    public const STATUS_OPEN = 'open';
    public const STATUS_ONLINE = 'online';
    public const STATUS_OFFLINE = 'offline';
    public const STATUS_LISTS = [
        self::STATUS_OPEN => '新增',
        self::STATUS_ONLINE => '上线',
        self::STATUS_OFFLINE => '下线',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%late}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['late_status'], 'default', 'value' => self::STATUS_OPEN],
            [['late_no', 'late_name', 'late_limit_type', 'late_limit_formula', 'late_status'], 'required'],
            [['late_limit_type'], 'in', 'range' => array_keys(self::TYPE_LIST)],
            [['late_limit_formula'], 'in', 'range' => array_keys(self::FORMULAS)],
            [['late_status'], 'in', 'range' => array_keys(self::STATUS_LISTS)],
            [['late_limit_value'], 'default', 'value' => 0],
            [['late_limit_value'], 'number'],
            [['late_no', 'late_name'], 'string', 'max' => 64],
            [['late_desc'], 'string', 'max' => 256],
            [['late_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'late_id' => '主键',
            'late_no' => '罚息规则编码',
            'late_name' => '罚息规则名称',
            'late_desc' => '计算规则描述',
            'late_limit_type' => '计算上限取值方式',
            'late_limit_formula' => '计算上限规则',
            'late_limit_value' => '上限值',
            'late_status' => '状态',
            'late_create_at' => '创建时间',
            'late_update_at' => '更新时间',
        ];
    }

    public function getRates(): ActiveQuery
    {
        return $this->hasMany(Rate::class, ['rate_late_no' => 'late_no']);
    }

    public function getTrans(): ActiveQuery
    {
        return $this->hasMany(LateTran::class, ['late_tran_late_no' => 'late_no']);
    }

    /**
     * 已被使用
     *
     * @return bool
     */
    public function used(): bool
    {
        if ($this->isRelationPopulated('rates')) {
            return (bool)$this->rates;
        } else {
            return $this->getRates()->limit(1)->exists();
        }
    }
}
