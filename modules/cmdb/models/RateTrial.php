<?php

namespace cmdb\models;

use Carbon\Carbon;
use cmdb\components\CmdbComponent;
use Exception;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\helpers\MoneyHelper;

class RateTrial extends RateEntity
{
    public int $trialAmount = 0;
    public $period;
    public $rateVarInfo;
    public $lateNum;
    public $isVar = false;
    public $periodTerms;
    public $periodAmountPercents;

    public function setTadPoleTerms($val)
    {
        $this->periodTerms = $this->filterTadPoleVal($val);
    }

    /**
     * @param string $val
     *
     * @return array|float[]|null
     */
    protected function filterTadPoleVal(string $val): ?array
    {
        $data = array_filter((array)explode(',', $val));
        if (!$data) {
            return null;
        }
        return array_map("floatval", $data);
    }

    public function getTadPoleTerms(): string
    {
        return implode(',', (array)$this->periodTerms);
    }

    public function setTadPoleAmountPercents($val)
    {
        $this->periodAmountPercents = $this->filterTadPoleVal($val);
    }

    public function getTadPoleAmountPercents(): string
    {
        return implode(',', (array)$this->periodAmountPercents);
    }

    public function rules(): array
    {
        return [
            [['rate_number', 'signDate', 'trialAmount', 'period', 'lateNum'], 'required'],
            [
                [
                    'rateVarInfo',
                    'lateNum',
                    'isVar',
                    'periodTerms',
                    'periodAmountPercents',
                    'tadPoleTerms',
                    'tadPoleAmountPercents',
                ],
                'safe',
            ],
            [
                ['rateVarInfo'],
                'filter',
                'filter' => function ($data) {
                    $feeMaps = Fee::feeMap();
                    foreach ($data as $type => $v) {
                        if (!preg_match('/^\d+(\.\d{2})?$/', $v)) {
                            $this->addError(
                                'rate_number',
                                vsprintf('[%s]费用值:[%s]必须是整数或带两位小数', [
                                    $feeMaps[$type],
                                    $data,
                                ])
                            );
                        }
                    }

                    return $data;
                },
            ],
            [
                ['trialAmount'],
                'integer',
                'min' => $this->rate_principal_min_amount,
                'max' => $this->rate_principal_max_amount,
                'tooSmall' => sprintf('试算金额不能小于%s元', MoneyHelper::f2y($this->rate_principal_min_amount)),
                'tooBig' => sprintf('试算金额不能大于%s元', MoneyHelper::f2y($this->rate_principal_max_amount)),
            ],
        ];
    }

    /**
     * @return string
     */
    public function getSignDate(): string
    {
        return Carbon::parse($this->rate_valid_start_date)->toDateString();
    }

    /**
     * @param $date
     */
    public function setSignDate($date)
    {
        $this->rate_valid_start_date = $date;
    }


    public function attributeLabels(): array
    {
        return array_merge(parent::attributeLabels(), [
            'signDate' => '签约日期',
            'trialAmount' => '试算金额（元）',
            'period' => '期次类型',
            'rateVarInfo' => '动态费率信息',
            'lateNum' => '罚息编码',
            'periodTerms' => '每期还款期限',
            'periodAmountPercents' => '每期还款本金比例',
        ]);
    }

    /**
     * @return array|array[]
     */
    public function trial(): array
    {
        try {
            $client = CmdbComponent::instance();
            [$count, $term, $type] = explode(',', $this->period);
            $requestParams = [
                'sign_date' => $this->getSignDate(),
                'apply_amount' => $this->trialAmount,
                'period_count' => $count,
                'period_term' => $term,
                'period_type' => $type,
                'product_number' => $this->rate_number,
                'rate_var_info' => [
                    'fees' => $this->rateVarInfo,
                    'late_num' => $this->lateNum,
                    'period_terms' => $this->periodTerms,
                    'period_amount_percents' => $this->periodAmountPercents,
                ],
            ];

            $result = $client->calculateRepayPlan($requestParams);
            if (!$result) {
                throw new Exception($client->getError());
            }

            return self::parse($client->getResponse());
        } catch (Exception $ex) {
            $this->addError('rate_number', 'API:' . $ex->getMessage());

            return [[], [], []];
        }
    }

    /**
     * @param $response
     *
     * @return array
     */
    public static function parse($response): array
    {
        $calculate_result = $response['data']['calculate_result'] ?? [];
        $principal = $calculate_result['principal'] ?? [];
        $interest = $calculate_result['interest'] ?? [];
        $fees = $calculate_result['fee'] ?? [];
        $tax = $calculate_result['tax'] ?? [];
        $options = [];

        foreach (['fee' => $fees, 'tax' => $tax] as $category => $dataSet) {
            foreach ($dataSet as $type => $data) {
                $options[$type] = $data[0]['name'] ?? '';
            }
        }

        $maxLength = max(count($interest), count($fees));
        $records = [];

        // 先息后本算法，仅最后一期有本金
        $lastPrincipal = count($principal) === 1 ? $principal[0]['amount'] : 0;

        for ($i = 0; $i < $maxLength; $i++) {
            $record = [
                'principal' => self::principalForAlgorithm($i, $maxLength, $principal, $lastPrincipal),
                'interest' => $interest[$i]['amount'] ?? 0,
            ];

            foreach (['fees' => $fees, 'tax' => $tax] as $category => $dataSet) {
                foreach ($dataSet as $type => $data) {
                    $record[$type] = $data[$i]['amount'] ?? 0;
                }
            }

            $record += [
                'total' => array_sum($record),
                'date' => $interest[$i]['date'] ?? null,
                'period' => $interest[$i]['period'] ?? null,
            ];

            $records[$i] = $record;
        }

        return [$records, $options, $response];
    }

    /**
     * @param $i
     * @param $maxLength
     * @param $principal
     * @param $lastPrincipal
     * @return int
     * 针对先息后本算法，仅最后一期有本金
     */
    protected static function principalForAlgorithm($i, $maxLength, $principal, $lastPrincipal)
    {
        //如果是只有一期本金，且是最后一期
        if ($lastPrincipal && ($i == $maxLength - 1)) {
            return $lastPrincipal;
            //如果是只有一期本金,但不是最后一期
        } elseif ($lastPrincipal) {
            return 0;
        }

        return $principal[$i]['amount'] ?? 0;
    }

    /**
     * @param $options
     *
     * @return array
     */
    public static function columns($options): array
    {
        $feeColumns = [];
        foreach ($options as $attribute => $label) {
            $feeColumns[] = [
                'label' => $label,
                'attribute' => $attribute,
                'class' => MoneyDataColumn::class,
            ];
        }

        return array_merge([
            [
                'label' => '期次',
                'attribute' => 'period',
            ],
            [
                'label' => '计划还款日期',
                'attribute' => 'date',
            ],
            [
                'label' => '应还本金',
                'attribute' => 'principal',
                'class' => MoneyDataColumn::class,
            ],
            [
                'label' => '应还利息',
                'attribute' => 'interest',
                'class' => MoneyDataColumn::class,
            ],
        ], $feeColumns, [
            [
                'label' => '每期应还合计',
                'attribute' => 'total',
                'class' => MoneyDataColumn::class,
            ],
        ]);
    }
}
