<?php

namespace cmdb\models;

use cmdb\behaviors\VoidBehavior;
use Exception;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%product}}".
 *
 * @property int               $product_id
 * @property string            $product_scope       资产使用的场景
 * @property string            $product_memo        备注
 * @property string            $product_condition   产品条件
 * @property string            $product_status      状态：open:新增，void:作废，online:线上运行,offline:下线
 * @property string            $product_create_at
 * @property string            $product_update_at
 * @property-read RateEntity[] $rates
 */
class Product extends ActiveRecord
{
    public const STATUS_OPEN    = 'open';
    public const STATUS_VOID    = 'void';
    public const STATUS_ONLINE  = 'online';
    public const STATUS_OFFLINE = 'offline';

    public const STATUS_LIST_ACTIVE = [
        self::STATUS_OPEN    => '新增',
        self::STATUS_ONLINE  => '上线',
        self::STATUS_OFFLINE => '下线',
    ];

    public const STATUS_LIST = [
        self::STATUS_OPEN    => '新增',
        self::STATUS_ONLINE  => '上线',
        self::STATUS_OFFLINE => '下线',
        self::STATUS_VOID    => '作废',
    ];

    public const PRODUCT_REPAY_TYPE_ACPI = 'acpi';

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%product}}';
    }

    public function transactions(): array
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    public function behaviors(): array
    {
        return [
            VoidBehavior::class,
        ];
    }

    /**
     * @return object|Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['product_status'], 'default', 'value' => Product::STATUS_OPEN],
            [['product_scope', 'product_status'], 'required'],
            [
                ['product_scope'],
                'filter',
                'filter' => function ($value) {
                    return strtolower(trim($value));
                },
            ],
            [
                ['product_status'],
                'in',
                'strict' => true,
                'range'  => function (Product $model, $attribute) {
                    if ($model->getIsNewRecord()) {
                        return [Product::STATUS_OPEN];
                    }

                    $status = $model->getOldAttribute($attribute);
                    switch ($status) {
                        case Product::STATUS_OPEN:
                            $range = [Product::STATUS_ONLINE, Product::STATUS_OFFLINE, Product::STATUS_VOID];
                            break;
                        case Product::STATUS_ONLINE:
                            $range = [Product::STATUS_OFFLINE, Product::STATUS_VOID];
                            break;
                        case Product::STATUS_OFFLINE:
                            $range = [Product::STATUS_ONLINE, Product::STATUS_VOID];
                            break;
                        case Product::STATUS_VOID:
                            $range = [];
                            break;
                        default:
                            throw new InvalidConfigException('未知产品类型:' . $status);
                    }

                    return $range;
                },
            ],
            [['product_condition'], 'string', 'max' => 512],
            [['product_memo'], 'string'],
            [['product_create_at', 'product_update_at'], 'safe'],
            [['product_scope'], 'string', 'max' => 50],
            [['product_scope'], 'unique', 'message' => '{attribute}`{value}`已存在'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'product_id'        => 'ID',
            'product_scope'     => '业务产品',
            'product_memo'      => '备注',
            'product_condition' => '产品使用条件',
            'product_status'    => '状态',
            'product_create_at' => '创建时间',
            'product_update_at' => '更新时间',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getRates(): ActiveQuery
    {
        return $this->hasMany(RateEntity::class, ['rate_number' => 'product_rate_number'])
            ->via('productRate');
    }

    /**
     * @return ActiveQuery
     */
    public function getProductRate(): ActiveQuery
    {
        return $this->hasMany(ProductRate::class, ['product_rate_scope' => 'product_scope']);
    }

    /**
     * @return bool
     */
    public function getHasActiveProductRate(): bool
    {
        return $this->getProductRate()
            ->onCondition(['product_rate_status' => ProductRate::STATUS_ONLINE])
            ->limit(1)
            ->exists();
    }

    /**
     * 判断产品是否作废
     *
     * @return bool
     */
    public function isVoid(): bool
    {
        return $this->product_status == Product::STATUS_VOID;
    }

    /**
     * 判断产品是否上线
     *
     * @return bool
     */
    public function isOnline(): bool
    {
        return $this->product_status == Product::STATUS_ONLINE;
    }

    /**
     * 判断产品是否下线
     *
     * @return bool
     */
    public function isOffline(): bool
    {
        return $this->product_status == Product::STATUS_OFFLINE;
    }

    /**
     * @throws Exception
     */
    public function delete()
    {
        throw new Exception('禁止删除');
    }
}
