<?php

namespace cmdb\models;

use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;

/**
 * This is the model class for table "capital".
 *
 * @property int                 $capital_id
 * @property string              $capital_name
 * @property string              $capital_code
 * @property string              $capital_desc
 * @property string              $capital_alias
 * @property string              $capital_create_at
 * @property string              $capital_update_at
 * @property-read RateTemplate[] $rateTemplates
 */
class Capital extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'capital';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('dbCmdb');
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['capital_name', 'capital_code'], 'required'],
            [['capital_create_at', 'capital_update_at'], 'safe'],
            [['capital_name', 'capital_code', 'capital_alias'], 'string', 'max' => 50],
            [['capital_desc'], 'string', 'max' => 500],
            [['capital_code'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'capital_id'        => 'ID',
            'capital_name'      => '资金方名称',
            'capital_code'      => '资金方code',
            'capital_desc'      => '资金方描述',
            'capital_alias'     => '资金方别名',
            'capital_create_at' => '创建时间',
            'capital_update_at' => '更新时间',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getRateTemplates(): ActiveQuery
    {
        return $this->hasMany(RateTemplate::class, ['rate_template_id' => 'rate_template_id'])
            ->via('rateTemplateCapital');
    }

    /**
     * @return ActiveQuery
     */
    public function getRateTemplateCapital(): ActiveQuery
    {
        return $this->hasMany(RateTemplateCapital::class, ['capital_code' => 'capital_code']);
    }

    /**
     * 获取资金方map
     *
     * @return array
     */
    public static function getCapitalMap(): array
    {
        return self::find()
            ->select([
                'capital_name' => new Expression("CONCAT(capital_name, '-', capital_alias)"),
            ])
            ->indexBy('capital_id')
            ->orderBy(['capital_id' => SORT_DESC])
            ->column();
    }

    /**
     * 获取资金方map
     *
     * @return array
     */
    public static function getCapitalCodeMap(): array
    {
        return self::find()
            ->select(new Expression('CONCAT(capital_name, \'-\', capital_alias)'))
            ->indexBy('capital_code')
            ->orderBy(['capital_id' => SORT_DESC])
            ->column();
    }
}
