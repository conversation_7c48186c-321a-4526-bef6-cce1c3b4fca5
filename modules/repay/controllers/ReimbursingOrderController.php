<?php

namespace repay\controllers;

use kartik\depdrop\DepDropAction;
use repay\models\ReimbursingOrder;
use repay\models\ReimbursingOrderSearch;
use Xlerr\SettlementFlow\Models\Rule;
use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class ReimbursingOrderController extends Controller
{
    public function actionIndex()
    {
        $searchModel = new ReimbursingOrderSearch();
        $params = $this->request->get();
        $dataProvider = $searchModel->search($params);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actions(): array
    {
        return [
            'workflow-config-list' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($channel) {
                    $query = Rule::find()
                        ->where(['status' => Rule::STATUS_ACTIVE])
                        ->select(['id', 'fee_type']);
                    if ($channel) {
                        $query->andWhere(['channel' => $channel]);
                    }
                    $all = $query->asArray()->all();
                    $data = [];
                    foreach ($all as $item) {
                        $data[] = [
                            'id' => $item['id'],
                            'name' => Rule::feeTypeList()[$item['fee_type']] ?? '未知',
                        ];
                    }

                    return $data;
                },
                'selectedCallback' => function ($channel) {
                    return Yii::$app->getRequest()->get('default', '');
                },
            ],
        ];
    }


    public function actionInitial(int $id): Response
    {
        $session = Yii::$app->getSession();
        $referrerUrl = $this->request->getReferrer();

        $cnt = ReimbursingOrder::updateAll([
            'biz_status' => 'initial',
        ], [
            'id' => $id,
            'biz_status' => 'unconfirmed',
        ]);

        if ($cnt) {
            $session->addFlash('success', '操作成功');
        } else {
            $session->addFlash('error', '操作失败,请稍后再试!');
        }

        return $this->redirect($referrerUrl);
    }

    public function actionCancel(int $id): Response
    {
        $session = Yii::$app->getSession();
        $referrerUrl = $this->request->getReferrer();

        $cnt = ReimbursingOrder::updateAll([
            'biz_status' => 'canceled',
        ], [
            'id' => $id,
            'biz_status' => [
                'unconfirmed',
                'initial',
                'enqueued',
                'trialed',
                'applied',
                'canceled',
            ],
        ]);

        if ($cnt) {
            $session->addFlash('success', '操作成功');
        } else {
            $session->addFlash('error', '操作失败,请稍后再试!');
        }

        return $this->redirect($referrerUrl);
    }

    /**
     * @param int $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the Withhold model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id 主键
     *
     * @return ReimbursingOrder
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ReimbursingOrder
    {
        if (($model = ReimbursingOrder::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
