<?php

namespace repay\controllers;

use repay\models\DataReportFinanceIncomeStatementSearch;
use repay\models\DataReportFinancePaymentFeeSearch;
use yii\web\Controller;

class ReportController extends Controller
{
    public function actionPaymentFee(): string
    {
        $params = $this->request->get();
        $searchModel = new DataReportFinancePaymentFeeSearch();
        $dataProvider = $searchModel->search($params);
        return $this->render('payment-fee/index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionIncomeStatement(): string
    {
        $params = $this->request->get();
        $searchModel = new DataReportFinanceIncomeStatementSearch();
        $params['toSystem'] = 'Finance';
        $dataProvider = $searchModel->search($params);
        return $this->render('income-statement/index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionFinanceGeneralCost(): string
    {
        $params = $this->request->get();
        $searchModel = new DataReportFinanceIncomeStatementSearch();
        $params['toSystem'] = 'Finance_general_cost';
        $dataProvider = $searchModel->search($params);
        return $this->render('finance-general-cost/index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}
