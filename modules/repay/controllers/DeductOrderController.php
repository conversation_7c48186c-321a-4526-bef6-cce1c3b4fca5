<?php

namespace repay\controllers;

use repay\components\RepayHttpComponent;
use repay\models\DeductDetail;
use repay\models\DeductOrderSearch;
use repay\models\Provision;
use Yii;
use yii\base\UserException;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use function xlerr\adminlte\userFullName;

/**
 * ProvisionController implements the CRUD actions for Provision model.
 */
class DeductOrderController extends Controller
{
    /**
     * Lists all Provision models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new DeductOrderSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionDetails(string $itemNo, string $serialNo): string
    {
        $query = DeductDetail::find()->andWhere([
            'deduct_detail_deduct_order_serial_no' => $serialNo,
            'deduct_detail_item_no' => $itemNo]);
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['deduct_detail_create_at'],
                'defaultOrder' => ['deduct_detail_create_at' => SORT_DESC],
            ],
        ]);
        return $this->render('details', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Provision model.
     * @param int $provision_id 拨备垫资主键
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($provision_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($provision_id),
        ]);
    }


    /**
     * @param string $itemNo
     * @param string $serialNo
     * @return void|Response
     */
    public function actionDecreaseCancel(string $itemNo, string $serialNo, string $withholdSerialNo)
    {
        $session = Yii::$app->getSession();
        if ($this->request->isPost) {
            try {
                $client = RepayHttpComponent::instance();
                $data = [
                    'asset_item_no' => $itemNo,
                    'serial_no' => $serialNo,
                    'operator' => userFullName(),
                    'withhold_serial_no' => $withholdSerialNo,
                    'operator_id' => Yii::$app->getUser()->getIdentity()->getId()
                ];
                $ret = $client->periodDeductReverse($data);
                if (!$ret) {
                    throw new UserException($client->getError());
                }
                $session->setFlash('success', '请求成功!');
            } catch (\Throwable $exception) {
                $session->setFlash('error', $exception->getMessage());
            }
            return $this->redirect($this->request->getReferrer());
        }
    }


    /**
     * Finds the Provision model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $provision_id 拨备垫资主键
     * @return Provision the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($provision_id)
    {
        if (($model = Provision::findOne(['provision_id' => $provision_id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
