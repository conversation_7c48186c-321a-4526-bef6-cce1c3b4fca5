<?php

namespace repay\components;

use Carbon\Carbon;
use common\models\User;
use GuzzleHttp\RequestOptions;
use repay\models\ManualRepay;
use repay\models\operates\DecreaseFee;
use repay\models\operates\IncreaseFee;
use repay\models\operates\Recharge;
use repay\models\operates\RepayMsgSync;
use repay\models\operates\RepayPeriod;
use repay\models\operates\RepayRefund;
use repay\models\operates\RepayReverse;
use repay\models\WithholdDetail;
use repay\models\WithholdUpdate;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;
use function xlerr\adminlte\userFullName;

class RepayHttpComponent extends RequestClient
{
    use ComponentTrait;

    public string $fromSystem = 'BIZ';

    public function settleDebt($assetItemNo, $channelKey, User $operator): bool
    {
        return $this->post('asset/settleDebt', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => uuid_create(),
                'type' => 'AssetSettleDebt',
                'data' => [
                    'asset_item_no' => $assetItemNo,
                    'recharge_serial_no' => $channelKey,
                    'operator_name' => $operator->username,
                ],
            ],
        ]);
    }

    /**
     * 费用减免
     *
     * @param DecreaseFee $decrease
     *
     * @return bool
     */
    public function decrease(DecreaseFee $decrease): bool
    {
        return $this->post('assetTran/decrease', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => sprintf('decrease_%s', Carbon::now()->format('YmdHisu')),
                'type' => 'AssetDecrease',
                'data' => [
                    'asset_item_no' => $decrease->itemNo,
                    'period' => (int)$decrease->period,
                    'amount' => (int)$decrease->amount,
                    'type' => $decrease->type,
                    'send_change_mq' => (bool)$decrease->sendChargeMq,
                    'comment' => $decrease->comment,
                    'operator_name' => $decrease->operator->username,
                ],
            ],
        ]);
    }

    /**
     * @param IncreaseFee $increase
     *
     * @return bool
     */
    public function increase(IncreaseFee $increase): bool
    {
        return $this->post('assetTran/increase', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => uuid_create(),
                'type' => 'AssetIncrease',
                'data' => [
                    'asset_item_no' => $increase->assetItemNo,
                    'period' => (int)$increase->period,
                    'amount' => (int)$increase->amount,
                    'type' => $increase->type,
                    'send_change_mq' => (bool)$increase->sendChangeMq,
                    'comment' => $increase->comment,
                    'operator_name' => userFullName(),
                ],
            ],
        ]);
    }

    /**
     * 充值
     *
     * @param Recharge $recharge
     *
     * @return bool
     */
    public function recharge(Recharge $recharge): bool
    {
        return $this->post('account/recharge-encrypt', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => sprintf('recharge_%s', Carbon::now()->format('YmdHisu')),
                'type' => 'AccountRecharge',
                'data' => [
                    'user_id_num_encrypt' => $recharge->userId,
                    'amount' => (int)$recharge->amount,
                    'serial_no' => $recharge->serialNo,
                    'asset_item_no' => $recharge->itemNo,
                    'date' => Carbon::toSystemDateTimeString($recharge->date),
                    'comment' => $recharge->comment,
                    'withhold_recharge' => (bool)$recharge->withholdRecharge,
                    'send_change_mq' => (bool)$recharge->sendChargeMq,
                    'operator_name' => $recharge->operator->username,
                ],
            ],
        ]);
    }

    /**
     * 还清
     *
     * @param RepayPeriod $repayPeriod
     *
     * @return bool
     */
    public function repayPeriod(RepayPeriod $repayPeriod): bool
    {
        return $this->post('asset/repayPeriod', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => sprintf('repay_period_%s', Carbon::now()->format('YmdHisu')),
                'type' => 'AssetRepayPeriod',
                'data' => [
                    'period' => (int)$repayPeriod->period,
                    'comment' => $repayPeriod->comment,
                    'asset_item_no' => $repayPeriod->itemNo,
                    'recharge_serial_no' => $repayPeriod->serialNo,
                    'operator_id' => (int)$repayPeriod->operator->id,
                    'operator_name' => $repayPeriod->operator->username,
                    'send_change_mq' => (bool)$repayPeriod->sendChargeMq,
                ],
            ],
        ]);
    }

    /**
     * 还款逆操作
     *
     * @param RepayReverse $repayReverse
     *
     * @return bool
     */
    public function repayReverse(RepayReverse $repayReverse): bool
    {
        return $this->post('asset/repayReverse', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => sprintf('repay_reverse_%s', Carbon::now()->format('YmdHisu')),
                'type' => 'AssetRepayReverse',
                'data' => [
                    'type' => $repayReverse->type,
                    'asset_item_no' => $repayReverse->itemNo,
                    'serial_no' => $repayReverse->serialNo,
                    'operator_name' => $repayReverse->operator->username,
                    'comment' => $repayReverse->comment,
                    'fromSystem' => $this->fromSystem,
                    'send_change_mq' => (bool)$repayReverse->sendChargeMq,
                ],
            ],
        ]);
    }

    /**
     * 手动还款成功
     *
     * @param ManualRepay $model
     * @param string $tradeId
     * @param string $status
     * @param int $amount
     * @param string $memo
     *
     * @return bool
     */
    public function offlineRepay(ManualRepay $model, $tradeId, $status, $amount, $memo): bool
    {
        return $this->post('paydayloan/offline/withhold-deal', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => uuid_create(),
                'type' => 'OfflineWithholdDeal',
                'data' => [
                    'serial_no' => $model->serialNo,
                    'request_no' => $model->requestNo,
                    'card_no' => $model->userRepayCardNo,
                    'status' => $status,
                    'amount' => $amount,
                    'trade_id' => $tradeId,
                    'comment' => $memo,
                ],
            ],
        ]);
    }

    /**
     * 自动匹配失败后，人工强制将资产和代扣流水关联
     *
     * @param $assetItemNo
     * @param $amount
     * @param $tradeId
     * @param $memo
     *
     * @return bool
     */
    public function manualOfflineRepay($assetItemNo, $amount, $tradeId, $memo): bool
    {
        return $this->post('paydayloan/offline/withhold-deal', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => uuid_create(),
                'type' => 'OfflineWithholdDeal',
                'data' => [
                    'item_no' => $assetItemNo,
                    'status' => 'success',
                    'amount' => $amount,
                    'trade_id' => $tradeId,
                    'comment' => $memo,
                ],
            ],
        ]);
    }

    /**
     *根据资产编号查询重复扣款可退款流水接口
     *
     * @param string $itemNo
     *
     * @return bool
     */
    public function getRefundList(string $itemNo): bool
    {
        return $this->get('/page/asset/repeated-withhold/refunds', [
            RequestOptions::QUERY => [
                'from_system' => $this->fromSystem,
                'item_no' => $itemNo,
            ],
        ]);
    }


    /**
     * 手动同步消息
     *
     * @param RepayMsgSync $repayMsgSync
     *
     * @return bool
     */
    public function manualSync(RepayMsgSync $repayMsgSync): bool
    {
        $itemNos = explode(',', $repayMsgSync->itemNo);
        $serialNos = explode(',', $repayMsgSync->serialNo);

        /**
         * asset（资产）、 withhold（代扣）、 recharge（充值） 、repay（还款）、fox_asset（fox资产同步）、asset_delay（展期）
         */
        switch ($repayMsgSync->type) {
            case RepayMsgSync::TYPE_ASSET:
                break;
            case RepayMsgSync::TYPE_WITHHOLD:
            case RepayMsgSync::TYPE_RECHARGE:
            case RepayMsgSync::TYPE_REPAY:
            case RepayMsgSync::TYPE_ASSET_DELAY:
                $itemNos = null;
                break;
            case RepayMsgSync::TYPE_FOX_ASSET:
                $serialNos = null;
                break;
        }

        return $this->post('/page/msg/manual-sync', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'type' => 'manualSyncMQ',
                'key' => md5('refund' . $repayMsgSync->type . Carbon::now()->format('YmdHisu')),
                'data' => [
                    'type' => $repayMsgSync->type,
                    'itemNos' => $itemNos,
                    'serialNos' => $serialNos,
                ],
            ],
        ]);
    }


    /**
     * 重复扣款退款接口
     *
     * @param RepayRefund $refund
     *
     * @return bool
     */
    public function refund(RepayRefund $refund): bool
    {
        switch ($refund->action) {
            case 'asset_delay': // 展期资产退款
                $uri = '/trade/order-refund-apply';
                $type = 'TradeRefund';
                $refundType = 'WITHDRAW';
                break;
            case 'withdraw':
            case 'online':
                $uri = '/page/asset/repeated-withhold/online-refund';
                $type = 'OnlineRepeatRefund';
                $refundType = strtoupper($refund->action);
                break;
            case 'offline':
                $uri = '/page/asset/repeated-withhold/offline-refund';
                $type = 'OfflineRepeatRefund';
                $refundType = strtoupper($refund->action);
                break;
            default:
                return false;
        }

        return $this->post($uri, [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'type' => $type,
                'key' => md5('refund' . $type . Carbon::now()->format('YmdHisu')),
                'data' => [
                    'refund_withhold_serial_no' => $refund->refundWithholdSerialNo,
                    'refund_amount' => $refund->amount,
                    'refund_card_uuid' => $refund->uuid,
                    'refund_channel' => $refund->channel,
                    'refund_type' => $refundType,
                    'operator' => $refund->operator->username,
                ],
            ],
        ]);
    }

    public function withholdUpdate(WithholdUpdate $model): bool
    {
        return $this->post('withhold/withhold-update', [
            RequestOptions::JSON => [
                'type' => 'withhold-update',
                'from_system' => 'PORTAL',
                'key' => uuid_create(),
                'data' => [
                    'withholdId' => $model->withhold_id,
                    'withholdSerialNo' => $model->withhold_serial_no,
                    'withholdThirdSerialNo' => $model->withhold_third_serial_no,
                    'withholdChannel' => $model->withhold_channel,
                    'withholdStatus' => $model->withhold_status,
                    'withholdFinishAt' => $model->withhold_finish_at,
                    'withholdChannelKey' => $model->withhold_channel_key,
                    'withholdComment' => $model->withhold_comment,
                    'isExpired' => (bool)$model->is_expired,
                ],
            ],
        ]);
    }

    /**
     * @param WithholdDetail $model
     *
     * @return bool
     */
    public function withholdDetailUpsert(WithholdDetail $model): bool
    {
        return $this->post('withhold/withhold-detail-upsert', [
            RequestOptions::JSON => [
                'type' => 'withhold-update',
                'from_system' => 'PORTAL',
                'key' => uuid_create(),
                'data' => [
                    'withholdDetailId' => $model->withhold_detail_id,
                    'withholdDetailSerialNo' => $model->withhold_detail_serial_no,
                    'withholdDetailAssetItemNo' => $model->withhold_detail_asset_item_no,
                    'withholdDetailPeriod' => (int)$model->withhold_detail_period,
                    'withholdDetailAssetTranType' => $model->withhold_detail_asset_tran_type,
                    'withholdDetailWithholdAmount' => (int)$model->withhold_detail_withhold_amount,
                ],
            ],
        ]);
    }

    public function withholdDetailDelete(WithholdDetail $model): bool
    {
        return $this->post('withhold/withhold-detail-delete', [
            RequestOptions::JSON => [
                'type' => 'withhold-update',
                'from_system' => 'PORTAL',
                'key' => uuid_create(),
                'data' => [
                    'withholdDetailId' => $model->withhold_detail_id,
                ],
            ],
        ]);
    }

    public function periodDeductReverse(array $data = []): bool
    {
        return $this->post('asset/period-deduct-reverse', [
            RequestOptions::JSON => [
                'type' => 'assetPeriodDecreaseCancel',
                'from_system' => 'PORTAL',
                'key' => uuid_create(),
                'data' => $data,
            ],
        ]);
    }
}
