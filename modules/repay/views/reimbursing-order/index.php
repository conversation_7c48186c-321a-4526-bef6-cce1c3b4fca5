<?php

use repay\models\ReimbursingOrder;
use repay\models\ReimbursingOrderSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use Xlerr\SettlementFlow\Models\Rule;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\helpers\StringHelper;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ReimbursingOrderSearch */

$this->title = '偿付订单记录';
$this->params['breadcrumbs'][] = $this->title;
$feeTypes = Rule::feeTypeList();

echo $this->render('_search', [
    'model' => $searchModel,
]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => ' {initial} {view} {cancel}',
            'buttons' => [
                'initial' => static function ($url) {
                    return DialogActionColumn::newButton('设置为未处理', $url, [
                        'class' => 'btn-twitter',
                        'data' => [
                            'confirm' => '确定设置为未处理状态吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
                'view' => static function ($url) {
                    return DialogActionColumn::newButton('查看', $url, [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
                'cancel' => static function ($url) {
                    return DialogActionColumn::newButton('取消', $url, [
                        'class' => 'btn-warning',
                        'data' => [
                            'confirm' => '确定设置为已取消状态吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'initial' => static function (ReimbursingOrderSearch $model) {
                    return $model->biz_status === 'unconfirmed';
                },
                'cancel' => static function (ReimbursingOrderSearch $model) {
                    return !in_array($model->biz_status, ['canceled', 'completed']);
                },
            ],
        ],
        'serial_no',
        'channel',
        'asset_item_no',
        [
            'attribute' => 'biz_type',
            'format' => ['in', ReimbursingOrder::getBizTypeList()],
        ],
        [
            'label' => '状态',
            'attribute' => 'biz_status',
            'format' => ['in', ReimbursingOrder::STATUS_LIST],
        ],
        'apply_serial_no',
        'start_period',
        'end_period',
        [
            'label' => '订单来源类型',
            'attribute' => 'source_type',
            'format' => ['in', ReimbursingOrder::SOURCE_LIST],
        ],
        [
            'label' => '制单规则ID',
            'attribute' => 'manual_settlement_rule_id',
            'format' => static function ($id) use ($feeTypes) {
                if (!($config = Rule::findOne($id))) {
                    return $id;
                }

                // /cpop-settlement/rule/index?inc_type=&channel=zhenxing_zhongji&fee_type=&procedure=
                return Html::a($id . ' : ' . $feeTypes[$config->fee_type], [
                    '/cpop-settlement/rule/index',
                    'channel' => $config->channel,
                    'inc_type' => $config->inc_type,
                    'fee_type' => $config->fee_type,
                ], [
                    'class' => 'layer-dialog',
                ]);
            },
        ],
        'expect_at',
        'due_at',
        'apply_at',
        'finish_at',
        'trial_at',
        'out_order_no',
        [
            'label' => '扩展字段',
            'attribute' => 'extend_info',
            'format' => static function ($val) {
                $val = (string)$val;
                if ($data = Json::decode($val)) {
                    return Html::a(StringHelper::truncate($val, 30), '', [
                        'class' => 'layer-dialog',
                        'data' => [
                            'layer-title' => '扩展字段',
                            'layer-content' => sprintf(
                                '<pre style="border: 0; border-radius: 0;">%s</pre>',
                                Json::encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                            ),
                        ],
                    ]);
                }

                return $val;
            },
        ],
        [
            'label' => '备注',
            'attribute' => 'memo',
            'format' => static function ($val) {
                return Html::a(StringHelper::truncate($val, 30), '', [
                    'class' => 'layer-dialog',
                    'data' => [
                        'layer-title' => '备注',
                        'layer-content' => sprintf('<pre style="border: 0; border-radius: 0;">%s</pre>', $val),
                    ],
                ]);
            },
        ],
        'created_at',
        'updated_at',
    ],
]);
