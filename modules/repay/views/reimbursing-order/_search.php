<?php

use kartik\depdrop\DepDrop;
use repay\models\ReimbursingOrder;
use repay\models\ReimbursingOrderSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\data\Pagination;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model ReimbursingOrderSearch */
/* @var $pagination Pagination */

?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'channel', [
            'options' => [
                'style' => 'min-width: 200px',
            ],
        ])->widget(Select2::class, [
            'data' => capitalChannelList(),
            'hideSearch' => false,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'id' => 'channel-input',
                'prompt' => $model->getAttributeLabel('channel'),
            ],
        ]) ?>

        <?= $form->field($model, 'manual_settlement_rule_id', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width: 200px',
            ],
        ])->widget(DepDrop::class, [
            'type' => DepDrop::TYPE_SELECT2,
            'select2Options' => [
                'theme' => Select2::THEME_DEFAULT,
                'hideSearch' => false,
                'pluginOptions' => [
                    'allowClear' => true,
                ],
            ],
            'options' => [
                'placeholder' => '制单规则',

            ],
            'pluginOptions' => [
                'depends' => ['channel-input'],
                'initDepends' => ['channel-input'],
                'initialize' => true,
                'params' => [],
                'url' => Url::to(['workflow-config-list', 'default' => $model->manual_settlement_rule_id]),
            ],
        ]) ?>
        <?= $form->field($model, 'asset_item_no') ?>

        <?= $form->field($model, 'serial_no') ?>

        <?= $form->field($model, 'source_type')->widget(Select2::class, [
            'data' => ReimbursingOrder::SOURCE_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('source_type'),
            ],
        ]) ?>

        <?= $form->field($model, 'out_order_no') ?>

        <?= $form->field($model, 'biz_type', [
            'options' => [
                'style' => 'min-width: 100px',
            ],
        ])->widget(Select2::class, [
            'data' => ReimbursingOrder::getBizTypeList(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('biz_type'),
            ],
        ]) ?>

        <?= $form->field($model, 'biz_status', [
            'options' => [
                'style' => 'min-width: 200px',
            ],
        ])->widget(Select2::class, [
            'data' => ReimbursingOrder::STATUS_LIST,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'hideSearch' => true,
            'options' => [
                'multiple' => true,
                'prompt' => $model->getAttributeLabel('biz_status'),
            ],
        ]) ?>

        <?= $form->field($model, 'start_period') ?>

        <?= $form->field($model, 'end_period') ?>

        <?= $form->field($model, 'expectAtStartDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'expectAtEndDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'dueAtStartDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'dueAtEndDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'finishAtStartDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'finishAtEndDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'applyAtStartDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'applyAtEndDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'trialAtStartDate')->widget(DatePicker::class) ?>

        <?= $form->field($model, 'trialAtEndDate')->widget(DatePicker::class) ?>


        <?= Html::submitButton(' <i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>