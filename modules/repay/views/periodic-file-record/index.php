<?php

use repay\models\PeriodicFileRecord;
use repay\models\PeriodicFileRecordSearch;
use repay\models\PeriodicFileRule;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $searchModel PeriodicFileRecordSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '账单文件处理记录';
$this->params['breadcrumbs'][] = $this->title;
?>

    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'type' => ActiveForm::TYPE_INLINE,
                'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
            ]) ?>
            <?= $form->field($searchModel, 'periodic_file_record_loan_channel')->widget(Select2::class, [
                'data' => capitalChannelList(),
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => $searchModel->getAttributeLabel('periodic_file_record_loan_channel'),
                ],
            ]) ?>

            <?= $form->field($searchModel, 'type')->widget(Select2::class, [
                'data' => PeriodicFileRule::TRANSMISSION_TYPE_LIST,
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => '传输类型',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'periodic_file_record_target_channel') ?>


            <?= $form->field($searchModel, 'periodic_file_record_file_code') ?>

            <?= $form->field($searchModel, 'periodic_file_record_status')->widget(Select2::class, [
                'data' => PeriodicFileRecord::STATUS_LIST,
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => '状态',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                'options' => [
                    'placeholder' => '创建开始时间',
                    'autocomplete' => 'off',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                'options' => [
                    'placeholder' => '创建结束时间',
                    'autocomplete' => 'off',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'periodic_file_record_business_date')->widget(DatePicker::class, [
                'options' => [
                    'autocomplete' => 'off',
                ],
            ]) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

            <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>

            <?php
            ActiveForm::end() ?>
        </div>
    </div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => CheckboxColumn::class,
            'checkboxOptions' => static function (PeriodicFileRecord $model) {
                return [
                    'disabled' => in_array($model->periodic_file_record_status, ['initial', 'enqueued'])
                ];
            },
        ],
        [
            'class' => DialogActionColumn::class,
            'template' => '{reset} {view} {cancel}',
            'header' => Html::button('批量重新执行', [
                'class' => 'btn btn-xs btn-danger batch-reset',
            ]),
            'buttons' => [
                'reset' => static function ($url, PeriodicFileRecord $model) {
                    return Html::button('重新执行', [
                        'class' => 'btn btn-xs btn-primary reset-btn',
                        'data' => [
                            'id' => $model->periodic_file_record_id
                        ]
                    ]);
                },
                'cancel' => static function ($url, PeriodicFileRecord $model) {
                    return Html::button('取消', [
                        'class' => 'btn btn-xs btn-default cancel-btn',
                        'data' => [
                            'id' => $model->periodic_file_record_id,
                        ]
                    ]);
                },
            ],
            'visibleButtons' => [
                'reset' => static function (PeriodicFileRecord $model) {
                    return !in_array($model->periodic_file_record_status, ['initial', 'enqueued']);
                },
                'cancel' => static function (PeriodicFileRecord $model) {
                    return !in_array($model->periodic_file_record_status, ['canceled', 'completed']);
                },
            ]
        ],
        'periodic_file_record_loan_channel',
        [
            'attribute' => 'fileRule.period_file_rule_transmission_type',
            'format' => ['in', PeriodicFileRule::TRANSMISSION_TYPE_LIST],
        ],
        [
            'attribute' => 'periodic_file_record_target_channel',
            'label' => '目标通道(KOS/Key)'
        ],
        [
            'label' => '文件类型',
            'attribute' => 'periodic_file_record_file_codee',
            'value' => static function (PeriodicFileRecord $fileRecord) {
                return $fileRecord->fileRule->period_file_rule_file_name . ' - ' . $fileRecord->periodic_file_record_file_code;
            },
        ],
        'periodic_file_record_business_date',
        'periodic_file_record_retry_times',
        [
            'attribute' => 'periodic_file_record_status',
            'format' => ['in', PeriodicFileRecord::STATUS_LIST],
        ],
        'periodic_file_record_execute_time',
        'periodic_file_record_memo',
        'periodic_file_record_create_at',
        'periodic_file_record_update_at',
    ],
]);
$resetUrl = Url::to(['batch-reset']);
$cancelUrl = Url::to(['cancel']);
$js = <<<JS
    $(document)
        .on('click', 'button.reset-btn', function (e) {
            const id = e.target.getAttribute("data-id")
            resetRequest([id])
        })
        .on('click', 'button.batch-reset', function () {
            const ids = $('[name=\'selection[]\']:checked').map(function () { 
                return $(this).val(); 
            }).get();
            
            if (!ids.length) {
                layer.alert('请先勾选需要修改的数据!');
            } else {
                resetRequest(ids)
            }
        }).on('click', 'button.cancel-btn', function (e) {
           const id = e.target.getAttribute("data-id");
           layer.prompt({
            title: '请输入取消原因',
            formType: 2, 
            area: ['300px', '150px'] 
           }, function (cancelReason, index) {
            if (cancelReason) {
                cancelRequest(id,cancelReason)
                layer.close(index);
            } else {
                layer.alert('取消原因不能为空!');
            }
        });
    });

    function resetRequest(ids) {
        layer.confirm('确认重推?', {
            title: '重推',
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.post('{$resetUrl}', { ids: ids.join(',') }, function (data, status) {
                if (data.code === 0) {
                    layer.msg(data.message);
                     setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                } else {
                    layer.alert(data.message);
                }
            }).fail(function () {
               layer.alert('网络请求错误,请稍候再试!');
          });
        });
    }
    
    function cancelRequest(id,memo) {
         $.post('{$cancelUrl}', { 
                    id : id,
                    memo:memo
                }, function (data, status) {
                if (data.code === 0) {
                    layer.msg(data.message);
                     setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                } else {
                    layer.alert(data.message);
                }
            }).fail(function () {
               layer.alert('网络请求错误,请稍候再试!');
            });
    }
JS;

$this->registerJs($js);


