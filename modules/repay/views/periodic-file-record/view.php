<?php

use contract\components\ContractComponent;
use repay\models\PeriodicFileRecord;
use repay\models\PeriodicFileRule;
use xlerr\CodeEditor\CodeEditor;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model PeriodicFileRecord */

$this->title = $model->periodic_file_record_id;
$this->params['breadcrumbs'][] = ['label' => '账单文件处理记录', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('首页', ['index'], ['class' => 'btn btn-default']) ?>
</p>
<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>

    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'periodic_file_record_id',
                'periodic_file_record_rule_id',
                'periodic_file_record_loan_channel',
                'periodic_file_record_target_channel',
                'periodic_file_record_period_type',
                [
                    'attribute' => 'fileRule.period_file_rule_transmission_type',
                    'format' => ['in', PeriodicFileRule::TRANSMISSION_TYPE_LIST],
                ],
                'periodic_file_record_file_code',
                'periodic_file_record_process_date',
                'periodic_file_record_business_date',
                'periodic_file_record_execute_time',
                'periodic_file_record_file_digest',
                'periodic_file_record_shard_num',
                [
                    'attribute' => 'periodic_file_record_target_file_url',
                    'captionOptions' => [
                        'style' => 'width: 10%',
                    ],
                    'format' => 'raw',
                    'value' => CodeEditor::widget([
                        'name' => 'value_show',
                        'value' => $model->periodic_file_record_target_file_url,
                        'clientOptions' => [
                            'readOnly' => true,
                            'mode' => CodeEditor::MODE_Text,
                            'maxLines' => 5,
                        ],
                    ]),
                ],
                [
                    'attribute' => 'periodic_file_record_store_file_url',
                    'captionOptions' => [
                        'style' => 'width: 10%',
                    ],
                    'format' => 'raw',
                    'value' => function (PeriodicFileRecord $model) {
                        $urlArray = array_filter(explode(',', $model->periodic_file_record_store_file_url));
                        $html = '<div class="button-container">';
                        foreach ($urlArray as $url) {
                            $parsedUrl = parse_url($url);
                            $path = $parsedUrl['path'];
                            $fileName = basename($path);
                            $downloadUrl = Url::to(['download', 'url' => $url]);
                            $html .= Html::a($model->periodic_file_record_file_code . '-' . $fileName, $downloadUrl, [
                                'class' => 'btn btn-primary custom-btn',
                                'target' => '_blank',
                            ]);
                        }
                        $html .= '</div>';
                        return $html;
                    }
                ],
                ['attribute' => 'periodic_file_record_status', 'format' => ['in', PeriodicFileRecord::STATUS_LIST],],
                'periodic_file_record_memo',
                'periodic_file_record_retry_times',
                ['attribute' => 'periodic_file_record_confirm_flag', 'format' => ['in', ['未确认', '已确认']],],
                'periodic_file_record_create_at',
                'periodic_file_record_update_at',
            ],
            'template' => "<tr><th width='10%'>{label}</th><td>{value}</td></tr>",
        ]) ?>
    </div>
</div>
<style>
    .button-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .custom-btn {
        padding: 5px 10px;
        font-size: 12px;
        border-radius: 4px;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
    }

</style>
