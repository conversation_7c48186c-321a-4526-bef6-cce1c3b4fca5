<?php

use repay\models\PeriodicFileRule;
use kartik\widgets\TimePicker;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model PeriodicFileRule */


?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_loan_channel')->widget(Select2::class, [
                'data' => capitalChannelList(),
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_target_channel')->textInput()->hint('<span class="text-danger">资方文件为资方名称,担保方文件为担保方名称</span>',['style' => 'margin-top:5px']) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_file_name')->textInput() ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_file_code')->textInput() ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-8">
            <?= $form->field($model, 'period_file_rule_file_desc')->widget(CodeEditor::class, [
                'clientOptions' => [
                    'mode' => CodeEditor::MODE_Text,
                    'minLines' => 5,
                    'maxLines' => 10,
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_period_type')->widget(Select2::class, [
                'data' => [
                    'D' => '日',
                    'M' => '月'
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_period_value')->textInput() ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_push_time')->widget(TimePicker::class, [
                'pluginOptions' => [
                    'showSeconds' => true,
                    'showMeridian' => false,
                    'minuteStep' => 1,
                    'secondStep' => 5,
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_push_type')->widget(Select2::class, [
                'data' => [
                    'ftp' => 'ftp',
                    'kos' => 'kos',
                    'api' => 'api',
                ],
                'options' => [
                    'prompt' => '推送方式',
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_push_channel')->textInput() ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_file_content_format')->widget(Select2::class, [
                'data' => [
                    'csv' => 'csv',
                    'json' => 'json',
                    'json_line' => 'json_line'
                ],
                'options' => [
                    'prompt' => '文件内容格式',
                ],
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_file_name_pattern')->textInput() ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_validation_file_name_pattern')->textInput() ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_single_file_max_row')->textInput() ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_allow_repeat')->widget(Select2::class, [
                'data' => [
                    '1' => '允许',
                    '0' => '不允许',
                ]
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-8">
            <?= $form->field($model, 'period_file_rule_expect_during_time')->label('推送时间段 <span class="text-danger">例:00:30～00:50</span>') ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_expire_notify_mode')->widget(Select2::class, [
                'data' => [
                    'manual' => 'manual',
                    'api' => 'api',
                ]
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_dataflow_kv') ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_status')->widget(Select2::class, [
                'data' => PeriodicFileRule::STATUS_LIST,
                'options' => [
                        'placeholder' => '请选择状态'
                ]
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'period_file_rule_transmission_type')->widget(Select2::class, [
                'data' => PeriodicFileRule::TRANSMISSION_TYPE_LIST,
                'options' => [
                    'placeholder' => '请选择传输类型'
                ]
            ]) ?>
        </div>
    </div>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
