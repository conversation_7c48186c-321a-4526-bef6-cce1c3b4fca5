<?php

use repay\models\PeriodicFileRule;
use repay\models\PeriodicFileRuleSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\helpers\StringHelper;
use yii\web\View;

/* @var $this View */
/* @var $searchModel PeriodicFileRuleSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '账单文件规则维护';
$this->params['breadcrumbs'][] = $this->title;
?>

    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'type' => ActiveForm::TYPE_INLINE,
                'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
            ]) ?>
            <?= $form->field($searchModel, 'period_file_rule_loan_channel')->widget(Select2::class, [
                'data' => capitalChannelList(),
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => $searchModel->getAttributeLabel('period_file_rule_loan_channel'),
                ],
            ]) ?>

            <?= $form->field($searchModel, 'period_file_rule_transmission_type')->widget(Select2::class, [
                'data' => PeriodicFileRule::TRANSMISSION_TYPE_LIST,
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => '传输类型',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'period_file_rule_target_channel') ?>

            <?= $form->field($searchModel, 'period_file_rule_file_name') ?>

            <?= $form->field($searchModel, 'period_file_rule_file_code') ?>

            <?= $form->field($searchModel, 'period_file_rule_status')->widget(Select2::class, [
                'data' => PeriodicFileRule::STATUS_LIST,
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                    'width' => '200px',
                ],
                'options' => [
                    'prompt' => '状态',
                ],
            ]) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

            <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>
            <?= Html::a('新建', ['create'], ['class' => 'btn btn-success']) ?>
            <?php
            ActiveForm::end() ?>
        </div>
    </div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update} {view} {supplementary-push} {change}',
            'buttons' => [
                'supplementary-push' => static function ($url) {
                    return DialogActionColumn::newButton('重新执行', $url, [
                        'class' => 'btn-warning',
                        'data' => [
                        ],
                        'target' => '_blank',
                    ]);
                },
                'change' => static function ($url, PeriodicFileRule $model) {
                    $title = $model->period_file_rule_status === 0 ? '设置为有效' : '设置为无效';
                    $class = $model->period_file_rule_status === 0 ? 'btn-danger' : 'btn-default';
                    return DialogActionColumn::newButton($title, $url, [
                        'class' => $class,
                        'data' => [
                            'confirm' => sprintf('确定%s状态吗?', $title),
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
        ],
        'period_file_rule_loan_channel',
        [
            'attribute' => 'period_file_rule_transmission_type',
            'format' => ['in', PeriodicFileRule::TRANSMISSION_TYPE_LIST],
        ],
        'period_file_rule_target_channel',
        'period_file_rule_file_name',
        'period_file_rule_file_code',
        'period_file_rule_period_type',
        'period_file_rule_period_value',
        [
            'attribute' => 'period_file_rule_file_desc',
            'format' => 'raw',
            'value' => static function (PeriodicFileRule $model) {
                return Html::a(StringHelper::truncate($model->period_file_rule_file_desc, 30), '', [
                    'class' => 'layer-dialog',
                    'data' => [
                        'layer-title' => '文件规则描述',
                        'layer-content' => $model->period_file_rule_file_desc,
                        'layer-dialog-width' => '600px',
                        'layer-dialog-height' => '300px',
                    ],
                ]);
            },
        ],
        [
            'attribute' => 'period_file_rule_dataflow_kv',
            'format' => 'raw',
            'value' => function (PeriodicFileRule $model) {
                return Html::a(
                    $model->period_file_rule_dataflow_kv,
                    ['/key-value/index', 'namespace' => 'corebiz', 'group' => 'KV', 'key' => $model->period_file_rule_dataflow_kv],
                    [
                        'target' => '_blank',
                    ]
                );
            },
        ],
        [
            'attribute' => 'period_file_rule_status',
            'format' => ['in', PeriodicFileRule::STATUS_LIST],
        ],
        'period_file_rule_create_at',
    ],
]);



