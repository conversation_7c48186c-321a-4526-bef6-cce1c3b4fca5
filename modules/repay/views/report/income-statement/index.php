<?php

use repay\models\DataReportFinanceIncomeStatementSearch;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use Xlerr\CostManagement\Model\DataReportFinanceIncomeStatement;
use yii\data\DataProviderInterface;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var DataReportFinanceIncomeStatementSearch $searchModel
 */
$this->title = '资方收支数据';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '上报日期',
            'attribute' => 'report_date',
        ],
        [
            'label' => '资金方',
            'attribute' => 'loan_channel',
        ],
        [
            'label' => '费用类型',
            'attribute' => 'type',
        ],
        [
            'label' => '成本类型',
            'attribute' => 'cost_type',
        ],
        [
            'label' => '币种',
            'attribute' => 'currency',
        ],
        [
            "label" => "金额",
            "attribute" => "amount",
            'class' => MoneyDataColumn::class
        ],
        [
            "label" => "状态",
            "attribute" => "status",
            'format' => ['in', DataReportFinanceIncomeStatement::STATUS_LIST]
        ],
        [
            'label' => '我方主体',
            'attribute' => 'subject',
        ],
        [
            'label' => '上报月份',
            'attribute' => 'data_month',
        ],
        [
            'label' => '批次号',
            'attribute' => 'batch_number',
        ],
        [
            'label' => '创建时间',
            'attribute' => 'create_at',
        ],
    ],
]);
