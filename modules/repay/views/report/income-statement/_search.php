<?php


use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use Xlerr\CostManagement\Model\DataReportFinanceIncomeStatement;
use yii\helpers\Html;
use yii\web\View;
use repay\models\DataReportFinanceIncomeStatementSearch;

/**
 * @var $this View
 * @var $model DataReportFinanceIncomeStatementSearch
 */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['income-statement'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>

        <?= $form->field($model, 'loan_channel', ['options' => [
            'style' => 'min-width: 150px',
        ]])->widget(Select2::class, [
            'data' => capitalChannelList(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '资金方',
            ]
        ]) ?>

        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '开始时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '结束时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'reportStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '上报开始时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'reportEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '上报结束时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'status', ['options' => [
            'style' => 'min-width: 150px',
        ]])->widget(Select2::class, [
            'data' => DataReportFinanceIncomeStatement::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '状态',
            ]
        ]) ?>

        <?= $form->field($model, 'type') ?>
        <?= $form->field($model, 'data_month') ?>
        <?= $form->field($model, 'batch_number')->textInput()->label('批次号') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['income-statement'], ['class' => 'btn btn-default']) ?>

        <?= Html::a('上报审计', ['/cost-management/data-report-finance-income-statement'], ['class' => 'btn btn-success']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>
