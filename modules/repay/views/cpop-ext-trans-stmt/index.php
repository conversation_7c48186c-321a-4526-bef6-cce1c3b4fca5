<?php

use repay\models\DataReportFinancePaymentFeeSearch;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use Xlerr\CostManagement\Model\DataReportFinancePaymentFee;
use yii\data\DataProviderInterface;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var DataReportFinancePaymentFeeSearch $searchModel
 */
$this->title = '通道成本';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '上报日期',
            'attribute' => 'report_date',
        ],
        [
            'label' => '成本类型',
            'attribute' => 'type',
        ],
        [
            'label' => '币种',
            'attribute' => 'currency',
        ],
        [
            "label" => "金额",
            "attribute" => "amount",
            'class' => MoneyDataColumn::class
        ],
        [
            "label" => "状态",
            "attribute" => "status",
            'format' => ['in', DataReportFinancePaymentFee::STATUS_LIST]
        ],
        [
            "label" => "支付类型",
            "attribute" => "payment_type",
            'format' => ['in', ['prepay' => '预付', 'paylater' => '后付']]
        ],
        [
            'label' => '商户主体',
            'attribute' => 'merchant_subject',
        ],
        [
            'label' => '我方主体',
            'attribute' => 'subject',
        ],
        [
            'label' => '商户号',
            'attribute' => 'merchant_number',
        ],
        [
            'label' => '上报月份',
            'attribute' => 'data_month',
        ],
        [
            'label' => '批次号',
            'attribute' => 'batch_number',
        ],
        [
            'label' => '创建时间',
            'attribute' => 'create_at',
        ],
    ],
]);
