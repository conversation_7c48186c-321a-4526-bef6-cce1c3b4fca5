<?php

use repay\models\DeductOrder;
use repay\models\DeductOrderSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;

/** @var yii\web\View $this */
/** @var DeductOrderSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = '资产减免';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{details} {decrease-cancel}',
            'buttons' => [
                'details' => static function ($url, DeductOrderSearch $model) {
                    return DialogActionColumn::newButton('查询明细', [
                        'details',
                        'itemNo' => $model->deduct_order_item_no,
                        'serialNo' => $model->deduct_order_serial_no
                    ], [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
                'decrease-cancel' => static function ($url, DeductOrderSearch $model) {
                    return ActionColumn::newButton('取消减免', [
                        'decrease-cancel',
                        'itemNo' => $model->deduct_order_item_no,
                        'serialNo' => $model->deduct_order_serial_no,
                        'withholdSerialNo' => $model->deduct_order_withhold_serial_no,
                    ], [
                        'class' => 'btn-primary',
                        'data' => [
                            'confirm' => '您确定要执行取消减免操作吗?',
                            'method' => 'post',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'decrease-cancel' => function (DeductOrderSearch $model) {
                    return $model->deduct_order_status == DeductOrder::STATUS_EFFECTIVE;
                },
            ],
        ],
        'deduct_order_item_no',
        [
            'attribute' => 'deduct_order_type',
            'format' => ['in', DeductOrder::TYPE_LIST],
        ],
        'deduct_order_serial_no',
        'deduct_order_withhold_serial_no',
        [
            'attribute' => 'deduct_order_status',
            'format' => ['in', DeductOrder::STATUS_LIST],
        ],
        [
            'label' => '请求原始金额',
            'attribute' => 'deduct_order_apply_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '实际扣减金额',
            'attribute' => 'deduct_order_actual_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '完成时间',
            'attribute' => 'deduct_order_update_at',
        ],
        [
            'label' => '预计过期时间',
            'attribute' => 'deduct_order_expire_date',
        ],
        [
            'label' => '来源',
            'attribute' => 'deduct_order_source',
        ],
        [
            'label' => '创建人',
            'attribute' => 'deduct_order_creator',
        ],
        [
            'label' => '操作人',
            'attribute' => 'deduct_order_operator',
        ],
        'deduct_order_create_at',
    ],
]);


