<?php


use repay\models\DeductOrder;
use repay\models\DeductOrderSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model DeductOrderSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'deduct_order_type', ['options' => [
            'style' => 'min-width: 150px',
        ]])->widget(Select2::class, [
            'data' => DeductOrder::TYPE_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '项目类型',
            ]
        ]) ?>
        <?= $form->field($model, 'deduct_order_serial_no') ?>
        <?= $form->field($model, 'deduct_order_item_no') ?>
        <?= $form->field($model, 'deduct_order_status', ['options' => [
            'style' => 'min-width: 150px',
        ]])->widget(Select2::class, [
            'data' => DeductOrder::STATUS_LIST,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '减免状态',
            ]
        ]) ?>
        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建开始时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '创建结束时间',
                'autocomplete' => 'off',
            ],
        ]) ?>

        <?= $form->field($model, 'deduct_order_withhold_serial_no') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>