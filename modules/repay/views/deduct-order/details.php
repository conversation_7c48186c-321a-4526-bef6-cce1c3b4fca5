<?php

use repay\models\DeductDetail;
use repay\models\DeductOrder;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel DeductDetail */

$this->title = '资产减免明细';
$this->params['breadcrumbs'][] = $this->title;

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '资产编号',
            'attribute' => 'deduct_detail_item_no',
        ],
        [
            'label' => '代扣流水号',
            'attribute' => 'deduct_detail_deduct_order_serial_no',
        ],
        [
            'label' => '还款计划类型',
            'attribute' => 'deduct_detail_tran_type',
            'format' => ['in', DeductDetail::TRAN_TYPE_LIST],
        ],
        [
            'label' => '还款类型编号',
            'attribute' => 'deduct_detail_tran_no'
        ],
        [
            'label' => '期次',
            'attribute' => 'deduct_detail_tran_period'
        ],
        [
            'label' => '状态',
            'attribute' => 'deduct_detail_status',
            'format' => ['in', DeductDetail::STATUS_LIST],
        ],
        [
            'label' => '状态',
            'attribute' => 'deduct_detail_type',
            'format' => ['in', DeductDetail::TYPE_LIST],
        ],
        [
            'attribute' => 'deduct_detail_original_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'deduct_detail_actual_amount',
            'class' => MoneyDataColumn::class,
        ],
        'deduct_detail_remark',
        'deduct_detail_create_at',
        'deduct_detail_update_at',
    ],
]);
