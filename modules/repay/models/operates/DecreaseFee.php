<?php


namespace repay\models\operates;


use backend\behaviors\FormatterBehavior;
use common\models\User;
use repay\models\Asset;
use Yii;

class DecreaseFee extends OperateTool
{
    /**
     * @var string
     */
    public $itemNo;

    /**
     * @var int
     */
    public $period;

    /**
     * @var int
     */
    public $amount;

    /**
     * @var string
     */
    public $type;

    /**
     * @var int 0|1
     */
    public $sendChargeMq = 0;

    /**
     * @var string
     */
    public $comment;

    /**
     * @var User
     */
    public $operator;

    /**
     * @return array
     */
    public static function typeList()
    {
        return [
            'lateinterest'      => '罚息',
            'service'           => '服务费',
            'technical_service' => '技术服务费',
            'repayinterest'     => '利息',
            'latefin_service'   => '逾期金融服务费',
            'fin_service'       => '金融服务费',
        ];
    }

    public function rules()
    {
        return [
            [['operator'], 'default', 'value' => Yii::$app->getUser()->getIdentity()],
            [['itemNo', 'period', 'amount', 'type', 'sendChargeMq', 'operator'], 'required'],
            [['itemNo', 'comment'], 'string'],
            [
                ['itemNo'],
                'exist',
                'targetClass'     => Asset::class,
                'targetAttribute' => ['itemNo' => 'asset_item_no'],
                'message'         => '资产不存在',
            ],
            [['type'], 'in', 'range' => array_keys(self::typeList())],
            [['period', 'amount'], 'integer'],
            [['sendChargeMq'], 'boolean'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'itemNo'       => '资产编号',
            'period'       => '期次',
            'amount'       =>  sprintf('减免金额(%s)',FormatterBehavior::currencyUnit()),
            'type'         => '类型',
            'comment'      => '备注',
            'sendChargeMq' => '发送充值MQ',
        ];
    }

    public function submit(): bool
    {
        if (!$this->validate()) {
            return false;
        }

        if (!$this->sdk->decrease($this)) {
            Yii::$app->getSession()->setFlash('error', $this->sdk->getError());

            return false;
        }

        return true;
    }
}
