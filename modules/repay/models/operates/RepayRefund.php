<?php

namespace repay\models\operates;

use repay\models\RefundRequest;
use repay\models\Withhold;
use repay\models\WithholdHis;
use waterank\audit\models\Audit;
use Yii;
use yii\base\Model;
use yii\base\UserException;

class RepayRefund extends Model
{
    public const ACTION_ONLINE   = 'online';
    public const ACTION_OFFLINE  = 'offline';
    public const ACTION_WITHDRAW = 'withdraw';
    public const ACTION_ASSET_DELAY = 'asset_delay';

    public $refundWithholdSerialNo;
    public $amount;
    public $uuid;
    public $channel;
    public $operator;
    public $action;

    public function formName(): string
    {
        return '';
    }

    /**
     * @param string                    $serialNo
     * @param Withhold|WithholdHis|null $withhold
     *
     * @return array{string, string} [prefix, complete]
     * @throws UserException
     */
    public static function businessKey(string $serialNo, $withhold = null): array
    {
        $withhold = $withhold ?? Withhold::findOne(['withhold_serial_no' => $serialNo]) ??
            WithholdHis::findOne(['withhold_serial_no' => $serialNo]);
        if (!$withhold) {
            throw new UserException('代扣表中没有该还款订单号: ' . $serialNo);
        }

        $time   = preg_replace('/(\s|\.)/', '', microtime());
        $prefix = strtoupper(vsprintf('refund_%d_%s', [
            $withhold->withhold_id,
            substr(md5($serialNo), 8, 16),
        ]));

        return [$prefix, sprintf('%s_%s', $prefix, $time)];
    }

    public function rules(): array
    {
        return [
            [['refundWithholdSerialNo', 'action', 'amount', 'channel', 'uuid'], 'required'],
            [['refundWithholdSerialNo', 'channel', 'uuid'], 'string'],
            [['action'], 'in', 'range' => [self::ACTION_WITHDRAW, self::ACTION_ONLINE, self::ACTION_OFFLINE, self::ACTION_ASSET_DELAY]],
            [['amount'], 'integer', 'min' => 1],
            [
                ['refundWithholdSerialNo'],
                'filter',
                'filter' => function (string $serialNo) {
                    // 同一笔代扣不允许同时发起多笔退款

                    $hasRefunding = Withhold::find()
                        ->innerJoinWith(['refundRequests'], false)
                        ->where([
                            'withhold_serial_no'    => $serialNo,
                            'refund_request_status' => [
                                // 这些状态表示退款还在处理中, 没有到最终状态
                                RefundRequest::STATUS_READY,
                                RefundRequest::STATUS_REFUNDING,
                                RefundRequest::STATUS_FAIL,
                                RefundRequest::STATUS_WITHDRAWING,
                            ],
                        ])->exists();

                    $hasHisRefunding = WithholdHis::find()
                        ->innerJoinWith(['refundRequests'], false)
                        ->where([
                            'withhold_serial_no'    => $serialNo,
                            'refund_request_status' => [
                                // 这些状态表示退款还在处理中, 没有到最终状态
                                RefundRequest::STATUS_READY,
                                RefundRequest::STATUS_REFUNDING,
                                RefundRequest::STATUS_FAIL,
                                RefundRequest::STATUS_WITHDRAWING,
                            ],
                        ])->exists();

                    [$prefix] = self::businessKey($serialNo);

                    $hasAuditing = Audit::find()
                        ->where([
                            'and',
                            ['in', 'audit_status', [Audit::STATUS_PROCESSING, Audit::STATUS_SUCCESS]],
                            ['in', 'business_status', [Audit::BUSINESS_NEW, Audit::BUSINESS_PROCESSING]],
                            ['like', 'business_key', $prefix . '%', false],
                        ])
                        ->exists();

                    if ($hasRefunding || $hasHisRefunding || $hasAuditing) {
                        $this->addError('refundWithholdSerialNo', '同一笔还款订单不允许同时发起多笔退款, 请稍后重试...');
                    }

                    return $serialNo;
                },
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'refundWithholdSerialNo' => '还款订单号',
            'operator'               => '退款操作人',
            'uuid'                   => 'CARD UUID',
            'channel'                => '退款通道',
            'amount'                 => '退款金额',
        ];
    }

    public function fields(): array
    {
        return array_merge(parent::fields(), [
            'business_key' => function () {
                return self::businessKey($this->refundWithholdSerialNo);
            },
            'amount_human' => function () {
                return Yii::$app->getFormatter()->format($this->amount, 'formatAmount');
            },
        ]);
    }
}
