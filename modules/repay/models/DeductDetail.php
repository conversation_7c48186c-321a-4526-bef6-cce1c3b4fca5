<?php

namespace repay\models;

use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "deduct_detail".
 *
 * @property int $deduct_detail_id
 * @property string $deduct_detail_deduct_order_serial_no 减免流水号
 * @property string $deduct_detail_item_no
 * @property string $deduct_detail_tran_type
 * @property string $deduct_detail_tran_no
 * @property int $deduct_detail_tran_period
 * @property string $deduct_detail_status 减免状态new,effective, cancel, success
 * @property string $deduct_detail_type 拨备减免，直接减免
 * @property int $deduct_detail_original_amount 减免前金额
 * @property int $deduct_detail_actual_amount 实际扣减金额
 * @property string|null $deduct_detail_remark 备注
 * @property string $deduct_detail_create_at
 * @property string $deduct_detail_update_at
 * @property DeductOrder $deductOrder
 */
class DeductDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'deduct_detail';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['deduct_detail_tran_type', 'deduct_detail_type'], 'required'],
            [['deduct_detail_tran_period', 'deduct_detail_original_amount', 'deduct_detail_actual_amount'], 'integer'],
            [['deduct_detail_create_at', 'deduct_detail_update_at'], 'safe'],
            [['deduct_detail_deduct_order_serial_no', 'deduct_detail_item_no', 'deduct_detail_tran_no', 'deduct_detail_status'], 'string', 'max' => 64],
            [['deduct_detail_tran_type'], 'string', 'max' => 50],
            [['deduct_detail_type'], 'string', 'max' => 32],
            [['deduct_detail_remark'], 'string', 'max' => 512],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'deduct_detail_id' => 'Deduct Detail ID',
            'deduct_detail_deduct_order_serial_no' => '减免流水号',
            'deduct_detail_item_no' => 'Deduct Detail Item No',
            'deduct_detail_tran_type' => 'Deduct Detail Tran Type',
            'deduct_detail_tran_no' => 'Deduct Detail Tran No',
            'deduct_detail_tran_period' => 'Deduct Detail Tran Period',
            'deduct_detail_status' => '减免状态',
            'deduct_detail_type' => '减免类型',
            'deduct_detail_original_amount' => '减免前金额',
            'deduct_detail_actual_amount' => '实际扣减金额',
            'deduct_detail_remark' => '备注',
            'deduct_detail_create_at' => '创建时间',
            'deduct_detail_update_at' => '修改时间',
        ];
    }

    public const STATUS_NEW = "new";
    public const STATUS_EFFECTIVE = "effective";
    public const STATUS_CANCEL = "cancel";
    public const STATUS_SUCCESS = "success";

    public const STATUS_LIST = [
        self::STATUS_NEW => '新建',
        self::STATUS_EFFECTIVE => '生效中',
        self::STATUS_CANCEL => '取消',
        self::STATUS_SUCCESS => '结清成功',
    ];

    public const TRAN_TYPE_GRANT = 'grant';
    public const TRAN_TYPE_REPAY_PRINCIPAL = 'repayprincipal';
    public const TRAN_TYPE_REPAY_INTEREST = 'repayinterest';
    public const TRAN_TYPE_SERVICES = 'service';
    public const TRAN_TYPE_TECHNICAL_SERVICE = 'technical_service';
    public const TRAN_TYPE_AFTER_LOAN_MANAGE = 'after_loan_manage';
    public const TRAN_TYPE_MANAGE = 'manage';
    public const TRAN_TYPE_LATE_INTEREST = 'lateinterest';
    public const TRAN_TYPE_LATE_MANAGE = 'latemanage';
    public const TRAN_TYPE_LATE_SERVICE = 'lateservice';
    public const TRAN_TYPE_CREDIT_FEE = 'credit_fee';
    public const TRAN_TYPE_FIN_SERVICE = 'fin_service';
    public const TRAN_TYPE_LATE_FIN_SERVICE = 'latefin_service';

    public const TRAN_TYPE_LIST = [
        self::TRAN_TYPE_GRANT => '放款',
        self::TRAN_TYPE_REPAY_PRINCIPAL => '偿还本金',
        self::TRAN_TYPE_REPAY_INTEREST => '偿还利息',
        self::TRAN_TYPE_SERVICES => '服务费',
        self::TRAN_TYPE_TECHNICAL_SERVICE => '技术服务费',
        self::TRAN_TYPE_AFTER_LOAN_MANAGE => '贷后管理费',
        self::TRAN_TYPE_MANAGE => '管理费',
        self::TRAN_TYPE_LATE_INTEREST => '逾期利息',
        self::TRAN_TYPE_LATE_MANAGE => '逾期管理费',
        self::TRAN_TYPE_LATE_SERVICE => '逾期服务费',
        self::TRAN_TYPE_CREDIT_FEE => '授信费',
        self::TRAN_TYPE_FIN_SERVICE => '金融服务费',
        self::TRAN_TYPE_LATE_FIN_SERVICE => '逾期金融服务费',
    ];

    public const TYPE_PROVISION_DEDUCT = 'provision_deduct';
    public const TYPE_DIRECT_DEDUCT = 'direct_deduct';

    public const TYPE_LIST = [
        self::TYPE_PROVISION_DEDUCT => '拨备减免',
        self::TYPE_DIRECT_DEDUCT => '直接减免'
    ];

    /**
     * @return ActiveQuery
     */
    public function getDeductOrder(): ActiveQuery
    {
        return $this->hasOne(DeductOrder::class, ['deduct_order_serial_no' => 'deduct_detail_deduct_order_serial_no']);
    }
}
