<?php

namespace repay\models;

use kvmanager\models\KeyValue;
use Yii;

/**
 * This is the model class for table "periodic_file_rule".
 *
 * @property int $period_file_rule_id
 * @property string $period_file_rule_loan_channel 资金方
 * @property string $period_file_rule_target_channel 目标通道（某个资方、某个担保方）
 * @property string $period_file_rule_file_name 文件名，例：对客放款明细文件
 * @property string $period_file_rule_file_desc 文件规则描述，例：合作机构将D日的所有对客放款成功的流水生成文件，并上传到文件服务器
 * @property string $period_file_rule_file_code 文件编码，例：loan_detail
 * @property string $period_file_rule_period_type 期类型（D日,M月）
 * @property int $period_file_rule_period_value 期值,结合type，例：值为1时，如果是period_type=D则d+1，如果是period_type=M则代表每月1日
 * @property string $period_file_rule_push_time 执行推送时间
 * @property string $period_file_rule_push_type 推送方式，ftp , kos，api
 * @property string $period_file_rule_push_channel 推送channel名称，ftp为channel名称；kos为bucket名称；api为空
 * @property string $period_file_rule_file_name_pattern 文件名格式,ftp文件名称格式；kos为kosKey名称；api为空. 例： loan_yyyymmdd
 * @property string $period_file_rule_validation_file_name_pattern 校验文件名称格式；kos为空；api为空
 * @property string $period_file_rule_file_content_format 文件内容格式结构化文本（csv，json;）
 * @property string $period_file_rule_single_file_max_row 单个文件最大行，超过就分片
 * @property string $period_file_rule_expect_during_time 预期推送时间段;例：00:30～00:50
 * @property int $period_file_rule_allow_repeat 是否允许重复上传，1:允许，0:不允许
 * @property string $period_file_rule_expire_notify_mode 超时补偿方式,manual:人工通知，api:接口通知
 * @property int $period_file_rule_status 规则状态，1:生效，0:失效
 * @property string $period_file_rule_dataflow_kv 流程配置kv
 * @property string $period_file_rule_transmission_type 流程配置kv
 * @property string $period_file_rule_create_at 创建时间
 * @property string $period_file_rule_update_at 更新时间
 */
class PeriodicFileRule extends \yii\db\ActiveRecord
{
    public const STATUS_LIST = [
        0 => '失效',
        1 => '生效'
    ];

    public const TRANSMISSION_TYPE_LIST = [
        'upload' => '上传',
        'download' => '下载'
    ];

    /**
     * @return array|object|string
     * @throws \kvmanager\KVException
     */
    public static function getPeriodType()
    {
        return KeyValue::take('file_rule_period_type');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'periodic_file_rule';
    }

    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['period_file_rule_loan_channel', 'period_file_rule_target_channel', 'period_file_rule_file_name', 'period_file_rule_file_code',
                'period_file_rule_file_desc', 'period_file_rule_period_type', 'period_file_rule_period_value', 'period_file_rule_push_time', 'period_file_rule_push_type',
                'period_file_rule_file_name_pattern', 'period_file_rule_allow_repeat', 'period_file_rule_expire_notify_mode', 'period_file_rule_dataflow_kv', 'period_file_rule_status', 'period_file_rule_file_content_format', 'period_file_rule_transmission_type'], 'required'],
            [['period_file_rule_period_type', 'period_file_rule_push_type', 'period_file_rule_expire_notify_mode'], 'string'],
            [['period_file_rule_period_value', 'period_file_rule_allow_repeat', 'period_file_rule_status'], 'integer'],
            [['period_file_rule_push_time', 'period_file_rule_create_at', 'period_file_rule_update_at'], 'safe'],
            [['period_file_rule_loan_channel', 'period_file_rule_target_channel', 'period_file_rule_file_code', 'period_file_rule_file_content_format', 'period_file_rule_single_file_max_row', 'period_file_rule_expect_during_time'], 'string', 'max' => 32],
            [['period_file_rule_file_name'], 'string', 'max' => 100],
            [['period_file_rule_file_desc'], 'string', 'max' => 512],
            [['period_file_rule_push_channel', 'period_file_rule_file_name_pattern', 'period_file_rule_validation_file_name_pattern'], 'string', 'max' => 128],
            [['period_file_rule_dataflow_kv'], 'string', 'max' => 64],
            [['period_file_rule_period_value'], 'integer', 'min' => 0],
            [['period_file_rule_file_desc', 'period_file_rule_target_channel', 'period_file_rule_file_name', 'period_file_rule_file_code', 'period_file_rule_period_value', 'period_file_rule_push_channel', 'period_file_rule_validation_file_name_pattern', 'period_file_rule_file_name_pattern'],
                'match',
                'pattern' => '/[\x{4e00}-\x{9fa5}]/u',
                'not' => true, 'message' => '{attribute} 不能包含中文字符'
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'period_file_rule_id' => 'ID',
            'period_file_rule_loan_channel' => '机构编码',
            'period_file_rule_target_channel' => '目标通道',
            'period_file_rule_file_name' => '文件名',
            'period_file_rule_file_desc' => '文件规则描述',
            'period_file_rule_file_code' => '文件编码',
            'period_file_rule_period_type' => '期类型',
            'period_file_rule_period_value' => '期值',
            'period_file_rule_push_time' => '执行时间段',
            'period_file_rule_push_type' => '执行方式',
            'period_file_rule_push_channel' => '目标channel',
            'period_file_rule_file_name_pattern' => '文件名格式',
            'period_file_rule_validation_file_name_pattern' => '校验文件名称格式',
            'period_file_rule_file_content_format' => '文件内容',
            'period_file_rule_single_file_max_row' => '单个文件最大行，超过就分片',
            'period_file_rule_expect_during_time' => '预期执行时间段',
            'period_file_rule_allow_repeat' => '是否允许重复上传',
            'period_file_rule_expire_notify_mode' => '超时补偿方式',
            'period_file_rule_status' => '规则状态',
            'period_file_rule_dataflow_kv' => '流程配置kv',
            'period_file_rule_create_at' => '创建时间',
            'period_file_rule_update_at' => '更新时间',
            'period_file_rule_transmission_type' => '传输类型',
        ];
    }
}
