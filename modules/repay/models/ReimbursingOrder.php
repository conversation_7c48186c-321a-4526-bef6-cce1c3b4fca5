<?php

namespace repay\models;

use finance\models\Asset;
use grant\models\AssetBorrower;
use kvmanager\models\KeyValue;
use Xlerr\SettlementFlow\Models\Rule;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "reimbursing_order".
 *
 * @property int $id
 * @property string $serial_no 流水号
 * @property string $channel 资方
 * @property string $asset_item_no 资产编号
 * @property int $start_period 开始期次
 * @property int $end_period 结束期次
 * @property string $source_type 订单来源类型
 * @property int $manual_settlement_rule_id 手动结算规则id
 * @property string $biz_type 业务类型：compensate、buyback、offline
 * @property string $biz_status 状态：unconfirmed（未确认）、initial（未处理）、enqueued（已入队）、applied（已申请）、completed（已完成）、canceled（已取消）
 * @property string $expect_at 预计发生时间
 * @property string $due_at 到期日
 * @property string $apply_at 业务申请时间
 * @property string $finish_at 业务实际完成时间
 * @property string|null $out_order_no 外部交易流水
 * @property string|null $memo 备注
 * @property string|null $extend_info 扩展字段
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $apply_serial_no 申请流水号
 * @property string $trial_at 试算时间
 * @property ReimbursingOrderDetail $orderDetail
 * @property Rule $rule
 * @property Asset $asset
 * @property AssetBorrower $borrower
 */
class ReimbursingOrder extends ActiveRecord
{
    public const STATUS_LIST = [
        'unconfirmed' => '未确认',
        'initial' => '未处理',
        'enqueued' => '已入队',
        'trialed' => '已试算',
        'applied' => '已申请',
        'completed' => '已完成/成功',
        'canceled' => '已取消',
    ];

    public const SOURCE_LIST = [
        'agreement' => '按协议自动结算',
        'statement' => '按结算单结算',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cpop_reimbursing_order';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['start_period', 'end_period', 'manual_settlement_rule_id'], 'integer'],
            [['expect_at', 'due_at', 'apply_at', 'finish_at', 'created_at', 'updated_at'], 'safe'],
            [['serial_no', 'asset_item_no', 'out_order_no'], 'string', 'max' => 64],
            [['channel', 'source_type', 'biz_type', 'biz_status'], 'string', 'max' => 32],
            [['memo', 'extend_info'], 'string', 'max' => 255],
            [
                ['asset_item_no', 'period', 'biz_type', 'manual_settlement_rule_id'],
                'unique',
                'targetAttribute' => ['asset_item_no', 'period', 'biz_type', 'manual_settlement_rule_id']
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'serial_no' => '流水号',
            'channel' => '资方',
            'asset_item_no' => '资产编号',
            'start_period' => '开始期次',
            'end_period' => '结束期次',
            'source_type' => '订单来源类型',
            'manual_settlement_rule_id' => '手动结算规则ID',
            'biz_type' => '业务类型',
            'biz_status' => '状态',
            'expect_at' => '预计发生时间',
            'due_at' => '到期日',
            'apply_at' => '业务申请时间',
            'finish_at' => '交易完成时间',
            'out_order_no' => '外部交易流水',
            'memo' => '备注',
            'extend_info' => '扩展字段',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'apply_serial_no' => '申请流水号',
            'trial_at' => '试算时间',
        ];
    }

    public function getOrderDetail(): ActiveQuery
    {
        return $this->hasMany(ReimbursingOrderDetail::class, [
            'serial_no' => 'serial_no',
        ]);
    }

    public function getRule(): ActiveQuery
    {
        return $this->hasOne(Rule::class, [
            'id' => 'manual_settlement_rule_id',
        ]);
    }

    public function getAsset(): ActiveQuery
    {
        return $this->hasOne(Asset::class, [
            'asset_item_no' => 'asset_item_no',
        ]);
    }

    public function getBorrower(): ActiveQuery
    {
        return $this->hasOne(AssetBorrower::class, ['asset_borrower_item_no' => 'asset_item_no']);
    }

    public static function getBizTypeList()
    {
        static $list = null;
        if ($list === null) {
            $list = KeyValue::take('reimbursing_order_biz_type_list');
        }
        return $list;
    }
}
