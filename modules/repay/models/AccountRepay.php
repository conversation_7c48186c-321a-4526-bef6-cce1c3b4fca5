<?php

namespace repay\models;

use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%account_repay}}".
 *
 * @property int    $account_repay_id
 * @property string $account_repay_no                 还款编号
 * @property int    $account_repay_account_no         账户编号
 * @property string $account_repay_recharge_serial_no 充值订单号
 * @property string $account_repay_order_type         还款订单数据类型：asset资产还款，combo_order 期缴订单,trade_order 订单等
 * @property string $account_repay_order_no           资产编号/期缴订单编号/交易订单编号
 * @property string $account_repay_tran_no            data_type对应的业务主键，如asset对应的asset_tran_no，combo_order对应combo_order_tran_no, trade_order对应trade_no
 * @property int    $account_repay_amount             还款金额
 * @property string $account_repay_create_at          创建时间
 * @property string $account_repay_update_at          更新时间
 */
class AccountRepay extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%account_repay}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'account_repay_no',
                    'account_repay_account_no',
                    'account_repay_recharge_serial_no',
                    'account_repay_order_no',
                    'account_repay_tran_no',
                ],
                'required',
            ],
            [['account_repay_account_no', 'account_repay_amount'], 'integer'],
            [['account_repay_order_type'], 'string'],
            [['account_repay_create_at', 'account_repay_update_at'], 'safe'],
            [
                [
                    'account_repay_no',
                    'account_repay_recharge_serial_no',
                    'account_repay_order_no',
                    'account_repay_tran_no',
                ],
                'string',
                'max' => 64,
            ],
            [
                ['account_repay_tran_no', 'account_repay_recharge_serial_no', 'account_repay_order_type'],
                'unique',
                'targetAttribute' => [
                    'account_repay_tran_no',
                    'account_repay_recharge_serial_no',
                    'account_repay_order_type',
                ],
            ],
            [['account_repay_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'account_repay_id'                 => 'Account Repay ID',
            'account_repay_no'                 => '还款编号',
            'account_repay_account_no'         => '账户编号',
            'account_repay_recharge_serial_no' => '充值订单号',
            'account_repay_order_type'         => '还款订单数据类型：asset资产还款，combo_order 期缴订单,trade_order 订单等',
            'account_repay_order_no'           => '资产编号/期缴订单编号/交易订单编号',
            'account_repay_tran_no'            => 'data_type对应的业务主键，如asset对应的asset_tran_no，combo_order对应combo_order_tran_no, trade_order对应trade_no',
            'account_repay_amount'             => '还款金额',
            'account_repay_create_at'          => '创建时间',
            'account_repay_update_at'          => '更新时间',
        ];
    }

    public function getAccountRepayLog(): ActiveQuery
    {
        return $this->hasOne(AccountRepayLog::class, [
             'account_repay_log_repay_no' => 'account_repay_no'
        ]);
    }
}
