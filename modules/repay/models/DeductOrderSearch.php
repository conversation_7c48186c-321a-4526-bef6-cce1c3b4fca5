<?php

namespace repay\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * ProvisionSearch represents the model behind the search form of `repay\models\Provision`.
 */
class DeductOrderSearch extends DeductOrder
{
    public $startDate;
    public $endDate;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                ['deduct_order_type', 'deduct_order_item_no', 'deduct_order_serial_no', 'startDate', 'endDate', 'deduct_order_withhold_serial_no', 'deduct_order_status'
                ],
                'safe'
            ],
        ];
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['deduct_order_create_at'],
                'defaultOrder' => ['deduct_order_create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        $query->andFilterWhere([
            'deduct_order_item_no' => $this->deduct_order_item_no,
            'deduct_order_type' => $this->deduct_order_type,
            'deduct_order_status' => $this->deduct_order_status,
        ]);

        $query
            ->andFilterWhere(['like', 'deduct_order_serial_no', $this->deduct_order_serial_no])
            ->andFilterWhere(['like', 'deduct_order_withhold_serial_no', $this->deduct_order_withhold_serial_no])
            ->andFilterWhere(['>=', 'deduct_order_create_at', $this->startDate])
            ->andFilterWhere([
                '<',
                'deduct_order_create_at',
                Carbon::parse($this->endDate)->addDay()->toDateString(),
            ]);

        return $dataProvider;
    }
}
