<?php

namespace repay\models;


use Carbon\Carbon;
use Xlerr\CostManagement\Model\DataReportFinancePaymentFee;
use yii\data\ActiveDataProvider;

class DataReportFinancePaymentFeeSearch extends DataReportFinancePaymentFee
{
    public $startDate;
    public $reportStartDate;
    public $endDate;
    public $reportEndDate;

    public $toSystem;
    public function formName()
    {
        return '';
    }


    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'merchant_subject' => '商户主体',
            'type' => '成本类型',
            ''
        ]);
    }

    public function rules()
    {
        return [
            [
                ['startDate'], 'default', 'value' => Carbon::parse('1 months ago')->toDateString()
            ],
            [
                ['endDate'], 'default', 'value' => Carbon::now()->toDateString()
            ],
            [['startDate', 'endDate', 'reportStartDate', 'reportEndDate', 'status', 'data_month', 'batch_number', 'subject', 'merchant_subject', 'type','toSystem'], 'safe'],
        ];
    }

    public function search($params)
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['create_at'],
                'defaultOrder' => ['create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }
        $query->andFilterWhere([
            '>=',
            'create_at',
            $this->startDate ? Carbon::parse($this->startDate)->toDateString() : null,
        ])->andFilterWhere([
            '<',
            'create_at',
            $this->endDate ? Carbon::parse($this->endDate)->addDay()->toDateString() : null,
        ]);

        $query->andFilterWhere([
            '>=',
            'report_date',
            $this->reportStartDate ? Carbon::parse($this->reportStartDate)->toDateString() : null,
        ])->andFilterWhere([
            '<',
            'report_date',
            $this->reportEndDate ? Carbon::parse($this->reportEndDate)->addDay()->toDateString() : null,
        ]);

        $query->andFilterWhere([
            'status' => $this->status,
            'type' => $this->type,
            'batch_number' => $this->batch_number,
            'subject' => $this->subject,
            'data_month' => $this->data_month,
            'merchant_subject' => $this->merchant_subject,
            'to_system' => $this->toSystem
        ]);

        return $dataProvider;
    }
}
