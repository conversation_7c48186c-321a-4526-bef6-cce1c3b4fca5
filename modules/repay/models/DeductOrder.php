<?php

namespace repay\models;

use Yii;

/**
 * This is the model class for table "deduct_order".
 *
 * @property int $deduct_order_id
 * @property string $deduct_order_request_key 请求key
 * @property string $deduct_order_serial_no 减免流水号
 * @property string $deduct_order_item_no
 * @property string $deduct_order_type period_deduction
 * @property string $deduct_order_status 减免状态new,effective, cancel, success
 * @property string $deduct_order_source 减免来源
 * @property string $deduct_order_expire_date 预计过期时间
 * @property string $deduct_order_withhold_serial_no 代扣流水号
 * @property int $deduct_order_apply_amount 请求原始金额合计
 * @property int $deduct_order_actual_amount 实际扣减金额合计
 * @property string|null $deduct_order_creator 创建人
 * @property string|null $deduct_order_operator 操作人
 * @property string|null $deduct_order_remark 备注
 * @property string|null $deduct_order_extend 扩展字段
 * @property string $deduct_order_create_at
 * @property string $deduct_order_update_at
 */
class DeductOrder extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'deduct_order';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    public const TYPE_CAPITAL_DECREASE = 'period_deduction';   // 按期减免

    public const TYPE_LIST = [
        self::TYPE_CAPITAL_DECREASE => '按期减免',
    ];

    public const STATUS_NEW = "new";
    public const STATUS_EFFECTIVE = "effective";
    public const STATUS_CANCEL = "cancel";
    public const STATUS_SUCCESS = "success";

    public const STATUS_LIST = [
        self::STATUS_NEW => '新建',
        self::STATUS_EFFECTIVE => '生效中',
        self::STATUS_CANCEL => '取消',
        self::STATUS_SUCCESS => '结清成功',
    ];


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['deduct_order_type'], 'required'],
            [['deduct_order_expire_date', 'deduct_order_create_at', 'deduct_order_update_at'], 'safe'],
            [['deduct_order_apply_amount', 'deduct_order_actual_amount'], 'integer'],
            [['deduct_order_extend'], 'string'],
            [['deduct_order_request_key', 'deduct_order_serial_no', 'deduct_order_item_no'], 'string', 'max' => 64],
            [['deduct_order_type', 'deduct_order_status', 'deduct_order_source', 'deduct_order_creator', 'deduct_order_operator'], 'string', 'max' => 32],
            [['deduct_order_remark'], 'string', 'max' => 512],
            [['deduct_order_serial_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'deduct_order_id' => 'Deduct Order ID',
            'deduct_order_request_key' => '请求key',
            'deduct_order_serial_no' => '减免流水号',
            'deduct_order_item_no' => '资产编号',
            'deduct_order_type' => '项目类型',
            'deduct_order_status' => '减免状态',
            'deduct_order_source' => '减免来源',
            'deduct_order_expire_date' => '预计过期时间',
            'deduct_order_apply_amount' => '请求原始金额合计',
            'deduct_order_actual_amount' => '实际扣减金额合计',
            'deduct_order_creator' => '创建人',
            'deduct_order_operator' => '操作人',
            'deduct_order_remark' => '备注',
            'deduct_order_extend' => '扩展字段',
            'deduct_order_create_at' => '创建时间',
            'deduct_order_update_at' => '修改时间',
            'deduct_order_withhold_serial_no' => '代扣流水号',
        ];
    }
}
