<?php

namespace repay\models;

use Yii;

/**
 * This is the model class for table "cpop_ext_trans_stmt".
 *
 * @property int $id primary key
 * @property string $channel channel
 * @property string $asset_item_no asset item no
 * @property string|null $serial_no channel trade serial no
 * @property string $type biz type
 * @property int|null $loan_amount loan amount
 * @property int|null $trade_amount trade amount
 * @property string $trade_at trade date
 * @property int $start_period start period
 * @property int $principal principal
 * @property int $interest interest
 * @property int $end_period end_period
 * @property string $finish_at trade finish date
 * @property string|null $status open、close
 * @property string $create_at create date
 * @property string $update_at update date
 */
class CpopExtTransStmt extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cpop_ext_trans_stmt';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['channel', 'asset_item_no', 'type', 'trade_at', 'start_period', 'end_period', 'finish_at'], 'required'],
            [['loan_amount', 'trade_amount', 'start_period', 'end_period', 'principal', 'interest'], 'integer'],
            [['trade_at', 'finish_at', 'create_at', 'update_at'], 'safe'],
            [['channel', 'asset_item_no', 'serial_no', 'type'], 'string', 'max' => 64],
            [['status'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'primary key',
            'channel' => 'channel',
            'asset_item_no' => 'asset item no',
            'serial_no' => 'channel trade serial no',
            'type' => 'biz type',
            'loan_amount' => 'loan amount',
            'trade_amount' => 'trade amount',
            'trade_at' => 'trade date',
            'start_period' => 'start period',
            'end_period' => 'end_period',
            'finish_at' => 'trade finish date',
            'status' => 'open、close',
            'create_at' => 'create date',
            'update_at' => 'update date',
            'principal' => 'principal',
            'interest' => 'interest',
        ];
    }
}
