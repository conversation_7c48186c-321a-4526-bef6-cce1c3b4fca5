<?php

namespace repay\models;

use Carbon\Carbon;
use Xlerr\CostManagement\Model\DataReportFinanceIncomeStatement;
use yii\data\ActiveDataProvider;

class DataReportFinanceIncomeStatementSearch extends DataReportFinanceIncomeStatement
{

    public $startDate;
    public $reportStartDate;
    public $endDate;
    public $reportEndDate;

    public function formName()
    {
        return '';
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'type' => '费用类型',
            'data_month' => '上报月份',
            'batch_number' => '批次号',
        ]);
    }

    public function rules()
    {
        return [
            [
                ['startDate'], 'default', 'value' => Carbon::parse('1 months ago')->toDateString()
            ],
            [
                ['endDate'], 'default', 'value' => Carbon::now()->toDateString()
            ],
            [['startDate', 'endDate', 'reportStartDate', 'reportEndDate', 'status', 'data_month', 'batch_number', 'type', 'loan_channel'], 'safe'],
        ];
    }

    public function search($params)
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['create_at'],
                'defaultOrder' => ['create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }
        $query->andFilterWhere([
            '>=',
            'create_at',
            $this->startDate ? Carbon::parse($this->startDate)->toDateString() : null,
        ])->andFilterWhere([
            '<',
            'create_at',
            $this->endDate ? Carbon::parse($this->endDate)->addDay()->toDateString() : null,
        ]);

        $query->andFilterWhere([
            '>=',
            'report_date',
            $this->reportStartDate ? Carbon::parse($this->reportStartDate)->toDateString() : null,
        ])->andFilterWhere([
            '<',
            'report_date',
            $this->reportEndDate ? Carbon::parse($this->reportEndDate)->addDay()->toDateString() : null,
        ]);

        $query->andFilterWhere([
            'status' => $this->status,
            'type' => $this->type,
            'batch_number' => $this->batch_number,
            'data_month' => $this->data_month,
            'loan_channel' => $this->loan_channel,
        ]);

        return $dataProvider;
    }
}
