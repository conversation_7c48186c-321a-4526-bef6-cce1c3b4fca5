<?php

namespace repay\models;

use Yii;

/**
 * This is the model class for table "{{%asset_tran}}".
 *
 * @property int $asset_tran_id
 * @property string $asset_tran_no
 * @property string $asset_tran_asset_no
 * @property string $asset_tran_asset_item_no
 * @property string $asset_tran_category
 * @property string $asset_tran_type grant;repayinterest;repayprincipal;fin_service;lateinterest
 * @property string|null $asset_tran_description
 * @property int $asset_tran_amount amount=repaid_amount+balance_amount
 * @property int $asset_tran_decrease_amount
 * @property int $asset_tran_repaid_amount
 * @property int $asset_tran_balance_amount
 * @property int $asset_tran_total_amount total_amount=amount+decrease_amount
 * @property string $asset_tran_status
 * @property string $asset_tran_due_at
 * @property string $asset_tran_finish_at
 * @property int $asset_tran_period
 * @property string|null $asset_tran_late_status normal
 * @property string $asset_tran_remark
 * @property int $asset_tran_repay_priority The smaller the number, the higher the priority.
 * @property string $asset_tran_trade_at
 * @property string $asset_tran_create_at
 * @property string|null $asset_tran_rbiz_create_at
 * @property string $asset_tran_update_at
 * @property string|null $asset_tran_rbiz_update_at
 * @property string $asset_tran_last_sync_time
 */
class AssetTran extends \yii\db\ActiveRecord
{
    public const CATEGORY_GRANT = 'grant';
    public const CATEGORY_PRINCIPAL = 'principal';
    public const CATEGORY_INTEREST = 'interest';
    public const CATEGORY_FEE = 'fee';
    public const CATEGORY_LATE = 'late';

    public const TYPE_FIN_SERVICE = 'fin_service';
    public const TYPE_REPAYINTEREST = 'repayinterest';
    public const TYPE_REPAYPRINCIPAL = 'repayprincipal';
    public const TYPE_INSURANCE = 'insurance';
    public const TYPE_PROVISION = 'provision';
    public const TYPE_LATEINTEREST = 'lateinterest';
    public const TYPE_TAX_FIN_SERVICE = 'tax_fin_service';

    public const TYPE_LIST = [
        self::TYPE_FIN_SERVICE => '服务费',
        self::TYPE_REPAYINTEREST => '利息',
        self::TYPE_REPAYPRINCIPAL => '本金',
        self::TYPE_LATEINTEREST => '罚息',
        self::TYPE_INSURANCE => '保费',
        self::TYPE_PROVISION => '拨备金',
        self::TYPE_TAX_FIN_SERVICE => '服务税费',
    ];

    public const STATUS_FINISH = 'finish';
    public const STATUS_NOFINISH = 'nofinish';

    public const STATUS_LIST = [
        self::STATUS_FINISH => '已结清',
        self::STATUS_NOFINISH => '未结清',
    ];

    public function rules()
    {
        return [
            [
                [
                    'asset_tran_no',
                    'asset_tran_type',
                    'asset_tran_status',
                    'asset_tran_due_at',
                    'asset_tran_finish_at',
                    'asset_tran_period',
                    'asset_tran_repay_priority',
                    'asset_tran_trade_at',
                ],
                'required',
            ],
            [['asset_tran_category', 'asset_tran_status'], 'string'],
            [
                [
                    'asset_tran_amount',
                    'asset_tran_decrease_amount',
                    'asset_tran_repaid_amount',
                    'asset_tran_balance_amount',
                    'asset_tran_total_amount',
                    'asset_tran_period',
                    'asset_tran_repay_priority',
                ],
                'integer',
            ],
            [
                [
                    'asset_tran_due_at',
                    'asset_tran_finish_at',
                    'asset_tran_trade_at',
                    'asset_tran_create_at',
                    'asset_tran_rbiz_create_at',
                    'asset_tran_update_at',
                    'asset_tran_rbiz_update_at',
                    'asset_tran_last_sync_time',
                ],
                'safe',
            ],
            [['asset_tran_no', 'asset_tran_asset_no', 'asset_tran_late_status'], 'string', 'max' => 20],
            [['asset_tran_asset_item_no'], 'string', 'max' => 48],
            [['asset_tran_type'], 'string', 'max' => 32],
            [['asset_tran_description'], 'string', 'max' => 64],
            [['asset_tran_remark'], 'string', 'max' => 2048],
            [['asset_tran_no'], 'unique'],
            [
                ['asset_tran_asset_item_no', 'asset_tran_period', 'asset_tran_type'],
                'unique',
                'targetAttribute' => ['asset_tran_asset_item_no', 'asset_tran_period', 'asset_tran_type'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_tran_id' => Yii::t('finance', '表主键'),
            'asset_tran_no' => 'Asset Tran No',
            'asset_tran_asset_no' => 'Asset Tran Asset No',
            'asset_tran_asset_item_no' => Yii::t('finance', '资产编号'),
            'asset_tran_category' => Yii::t('finance', '分类'),
            'asset_tran_type' => Yii::t('finance', '交易类型'),
            'asset_tran_description' => Yii::t('finance', '交易类型中文描述'),
            'asset_tran_amount' => Yii::t('finance', '实际交易金额'),
            'asset_tran_decrease_amount' => Yii::t('finance', '减免金额'),
            'asset_tran_repaid_amount' => Yii::t('finance', '已还金额'),
            'asset_tran_balance_amount' => Yii::t('finance', '剩余还款金额 '),
            'asset_tran_total_amount' => Yii::t('finance', '总金额'),
            'asset_tran_status' => Yii::t('finance', '状态'),
            'asset_tran_due_at' => Yii::t('finance', '预期还款时间'),
            'asset_tran_finish_at' => Yii::t('finance', '完成时间'),
            'asset_tran_period' => Yii::t('finance', '期次'),
            'asset_tran_late_status' => Yii::t('finance', '逾期状态'),
            'asset_tran_remark' => Yii::t('finance', '备注'),
            'asset_tran_repay_priority' => 'The smaller the number, the higher the priority.',
            'asset_tran_trade_at' => Yii::t('finance', '交易时间'),
            'asset_tran_create_at' => Yii::t('finance', '创建时间'),
            'asset_tran_rbiz_create_at' => Yii::t('finance', 'Rbiz创建时间'),
            'asset_tran_update_at' => 'Asset Tran Update At',
            'asset_tran_rbiz_update_at' => 'Asset Tran Rbiz Update At',
            'asset_tran_last_sync_time' => 'Asset Tran Last Sync Time',
        ];
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset_tran}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }
}
