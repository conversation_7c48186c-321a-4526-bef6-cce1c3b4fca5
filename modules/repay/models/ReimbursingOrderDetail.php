<?php

namespace repay\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "reimbursing_order_detail".
 *
 * @property int $id
 * @property string $serial_no 流水号
 * @property string $asset_item_no 资产编号
 * @property string $trans_type 费用类型
 * @property int $origin_amount 原始金额
 * @property int $settlement_amount 结算金额
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class ReimbursingOrderDetail extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cpop_reimbursing_order_detail';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['origin_amount', 'settlement_amount'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['serial_no', 'asset_item_no'], 'string', 'max' => 64],
            [['trans_type'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'serial_no' => '流水号',
            'asset_item_no' => '资产编号',
            'trans_type' => '费用类型',
            'origin_amount' => '原始金额',
            'settlement_amount' => '结算金额',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }
}
