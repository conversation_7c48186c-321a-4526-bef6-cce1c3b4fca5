<?php

namespace repay\models;

use Carbon\Carbon;
use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%withhold}}".
 *
 * @property int $withhold_id                 主键
 * @property string $withhold_serial_no          代扣流水号，业务主键
 * @property int|null $withhold_order              代扣顺序
 * @property string $withhold_request_no         代扣请求编号
 * @property int $withhold_amount             代扣金额（单位:分）
 * @property string|null $withhold_channel            代扣渠道，根据不同的放款渠道进行路由
 * @property string $withhold_status             状态
 * @property string $withhold_sub_status         子状态,normal:正常,payment_cancel:协议支付取消,agreement:协议支付
 * @property string|null $withhold_channel_code       代扣渠道返回的code
 * @property string|null $withhold_channel_message    代扣渠道返回的消息描述
 * @property string|null $withhold_channel_key
 * @property int|null $withhold_channel_fee        代扣渠道费用（单位:分）
 * @property string|null $withhold_comment            备注
 * @property string|null $withhold_error_code         根据代扣渠道返回的状态自定义的code
 * @property string|null $withhold_custom_code        自定义code
 * @property string $withhold_supplement         是否为补单 Y N
 * @property string|null $withhold_third_serial_no    代扣通道返回的流水号
 * @property string|null $withhold_extend_info        针对一些特殊处理保存数据
 * @property string $withhold_create_at          创建时间
 * @property string $withhold_update_at          更新时间
 * @property string|null $withhold_execute_at         执行时间
 * @property string|null $withhold_finish_at          代扣完成时间
 * @property int|null $withhold_version            版本号
 * @property string $withhold_req_key            代扣请求key
 * @property string $withhold_user_name          姓名
 * @property string $withhold_user_idnum         身份证号
 * @property string $withhold_user_phone         手机号
 * @property string $withhold_user_uuid
 * @property string $withhold_card_num           用户卡号
 * @property string $withhold_capital_receive_at 资方接收时间
 * @property string|null $withhold_call_back          回调地址
 * @property string $withhold_payment_card_num   用户确认的还款卡
 * @property string $withhold_payment_card_type  用户还款账户类型
 * @property string $withhold_payment_mode
 * @property-read WithholdOrder[] $withholdOrder
 * @property-read RefundRequest[] $refundRequests
 * @property-read AccountRepay[] $accountRepays
 * @property-read WithholdRequest[] $withholdRequests
 * @property-read WithholdDetail[] $withholdDetail
 * @property-read AccountRecharge $accountRecharge
 * @property-read AssetDelay $assetDelay
 */
class Withhold extends \yii\db\ActiveRecord
{
    public const STATUS_READY = 'ready';
    public const STATUS_PROCESS = 'process';
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAIL = 'fail';
    public const STATUS_CANCEL = 'cancel';
    public const STATUS_LIST = [
        self::STATUS_SUCCESS => self::STATUS_SUCCESS,
        self::STATUS_FAIL => self::STATUS_FAIL,
        self::STATUS_CANCEL => self::STATUS_CANCEL,
        self::STATUS_READY => self::STATUS_READY,
        self::STATUS_PROCESS => self::STATUS_PROCESS,
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%withhold}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'withhold_serial_no',
                    'withhold_request_no',
                    'withhold_amount',
                    'withhold_status',
                    'withhold_req_key',
                    'withhold_user_name',
                    'withhold_user_idnum',
                    'withhold_user_phone',
                    'withhold_card_num',
                ],
                'required',
            ],
            [['withhold_order', 'withhold_amount', 'withhold_channel_fee', 'withhold_version'], 'integer'],
            [['withhold_status', 'withhold_sub_status', 'withhold_supplement', 'withhold_extend_info'], 'string'],
            [
                [
                    'withhold_create_at',
                    'withhold_update_at',
                    'withhold_execute_at',
                    'withhold_finish_at',
                    'withhold_capital_receive_at',
                ],
                'safe',
            ],
            [
                [
                    'withhold_serial_no',
                    'withhold_request_no',
                    'withhold_channel_code',
                    'withhold_error_code',
                    'withhold_custom_code',
                    'withhold_user_name',
                    'withhold_user_idnum',
                    'withhold_user_phone',
                    'withhold_card_num',
                ],
                'string',
                'max' => 32,
            ],
            [['withhold_channel'], 'string', 'max' => 50],
            [['withhold_channel_message', 'withhold_comment'], 'string', 'max' => 255],
            [['withhold_channel_key', 'withhold_third_serial_no', 'withhold_req_key'], 'string', 'max' => 64],
            [['withhold_call_back'], 'string', 'max' => 500],
            [['withhold_serial_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withhold_id' => Yii::t('repay', '主键'),
            'withhold_serial_no' => Yii::t('repay', '代扣流水号，业务主键'),
            'withhold_order' => Yii::t('repay', '代扣顺序'),
            'withhold_request_no' => Yii::t('repay', '代扣请求编号'),
            'withhold_amount' => Yii::t('repay', '代扣金额（单位:分）'),
            'withhold_channel' => Yii::t('repay', '代扣渠道，根据不同的放款渠道进行路由'),
            'withhold_status' => Yii::t('repay', '状态'),
            'withhold_sub_status' => Yii::t(
                'repay',
                '子状态,normal:正常,payment_cancel:协议支付取消,agreement:协议支付'
            ),
            'withhold_channel_code' => Yii::t('repay', '代扣渠道返回的code'),
            'withhold_channel_message' => Yii::t('repay', '代扣渠道返回的消息描述'),
            'withhold_channel_key' => Yii::t('repay', 'Withhold Channel Key'),
            'withhold_channel_fee' => Yii::t('repay', '代扣渠道费用（单位:分）'),
            'withhold_comment' => Yii::t('repay', '备注'),
            'withhold_error_code' => Yii::t('repay', '根据代扣渠道返回的状态自定义的code'),
            'withhold_custom_code' => Yii::t('repay', '自定义code'),
            'withhold_supplement' => Yii::t('repay', '是否为补单 Y N '),
            'withhold_third_serial_no' => Yii::t('repay', '代扣通道返回的流水号'),
            'withhold_extend_info' => Yii::t('repay', '针对一些特殊处理保存数据'),
            'withhold_create_at' => Yii::t('repay', '创建时间'),
            'withhold_update_at' => Yii::t('repay', '更新时间'),
            'withhold_execute_at' => Yii::t('repay', '执行时间'),
            'withhold_finish_at' => Yii::t('repay', '代扣完成时间'),
            'withhold_version' => Yii::t('repay', '版本号'),
            'withhold_req_key' => Yii::t('repay', '代扣请求key'),
            'withhold_user_name' => Yii::t('repay', '姓名'),
            'withhold_user_idnum' => Yii::t('repay', '身份证号'),
            'withhold_user_phone' => Yii::t('repay', '手机号'),
            'withhold_card_num' => Yii::t('repay', '用户卡号'),
            'withhold_capital_receive_at' => Yii::t('repay', '资方接收时间'),
            'withhold_call_back' => Yii::t('repay', '回调地址'),
            'withhold_payment_card_num' => Yii::t('repay', '还款账户'),
            'withhold_payment_card_type' => Yii::t('repay', '还款账户类型'),
        ];
    }

    public function getWithholdRequests(): ActiveQuery
    {
        return $this->hasMany(WithholdRequest::class, ['withhold_request_no' => 'withhold_request_no']);
    }

    public function getWithholdOrder(): ActiveQuery
    {
        return $this->hasMany(WithholdOrder::class, ['withhold_order_serial_no' => 'withhold_serial_no']);
    }

    public function getWithholdDetail(): ActiveQuery
    {
        return $this->hasMany(WithholdDetail::class, ['withhold_detail_serial_no' => 'withhold_serial_no']);
    }

    public function getRefundRequests(): ActiveQuery
    {
        return $this->hasMany(RefundRequest::class, ['refund_request_withhold_channel_key' => 'withhold_channel_key']);
    }

    public function getAccountRepays(): ActiveQuery
    {
        return $this->hasMany(AccountRepay::class, ['account_repay_recharge_serial_no' => 'withhold_channel_key']);
    }

    public function getAccountRecharge(): ActiveQuery
    {
        return $this->hasOne(AccountRecharge::class, ['account_recharge_serial_no' => 'withhold_channel_key']);
    }

    public function getAssetDelay(): ActiveQuery
    {
        return $this->hasOne(AssetDelay::class, ['asset_delay_withhold_serial_no' => 'withhold_serial_no']);
    }


    public function extraFields()
    {
        $fields = [
            'withhold_create_at',
            'withhold_update_at',
            'withhold_execute_at',
            'withhold_finish_at',
            'withhold_capital_receive_at',
            'withhold_confirm_at',
        ];

        return array_fill_keys($fields, function (Withhold $asset, string $attr): string {
            $asset->$attr ??= '1971-01-01';
            return Carbon::toSystemDateTimeString($asset->$attr);
        });
    }
}
