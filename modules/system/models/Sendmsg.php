<?php

namespace system\models;

use Yii;

/**
 * This is the model class for table "{{%sendmsg}}".
 *
 * @property int         $sendmsg_id
 * @property string      $sendmsg_order_no    消息业务编号
 * @property string      $sendmsg_type        消息具体类型
 * @property string      $sendmsg_content     消息内容
 * @property string      $sendmsg_memo        备注
 * @property string      $sendmsg_tosystem    msg发送的系统
 * @property string      $sendmsg_status      消息状态
 * @property string      $sendmsg_next_run_at 下次发送时间
 * @property string      $sendmsg_create_at   消息创建时间
 * @property string      $sendmsg_update_at   更新时间
 * @property int         $sendmsg_version     消息版本
 * @property int         $sendmsg_priority    消息优先级
 * @property int         $sendmsg_retrytimes  消息重试次数
 * @property string|null $sendmsg_response_data
 */
class Sendmsg extends \yii\db\ActiveRecord
{
    const STATUS_OPEN       = 'open';
    const STATUS_RUNNING    = 'running';
    const STATUS_ERROR      = 'error';
    const STATUS_TERMINATED = 'terminated';
    const STATUS_CLOSE      = 'close';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%sendmsg}}';
    }

    public static function statusList()
    {
        return [
            self::STATUS_OPEN       => '新增',
            self::STATUS_RUNNING    => '运行中',
            self::STATUS_ERROR      => '错误',
            self::STATUS_TERMINATED => '终止',
            self::STATUS_CLOSE      => '关闭',
        ];
    }

    public static function priorityList()
    {
        return [
            1 => '队列一',
            2 => '队列二',
            3 => '队列三',
            4 => '队列四',
            5 => '队列五',
        ];
    }

    /**
     * @param string $type
     *
     * @return array [$type, Task::class]
     */
    public static function clazz($type)
    {
        $taskList = [
            'gbiz'   => SendmsgGbiz::class,
            'rbiz'   => SendmsgRbiz::class,
            'dcs'    => SendmsgDcs::class,
            'paySvr' => SendmsgPaySvr::class,
        ];

        if (!array_key_exists($type, $taskList)) {
            $type  = 'dcs';
            $class = SendmsgDcs::class;
        } else {
            $class = $taskList[$type];
        }

        Yii::debug($class);

        return [$type, $class];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sendmsg_type', 'sendmsg_content', 'sendmsg_status'], 'required'],
            [['sendmsg_content', 'sendmsg_status', 'sendmsg_response_data'], 'string'],
            [['sendmsg_next_run_at', 'sendmsg_create_at', 'sendmsg_update_at'], 'safe'],
            [['sendmsg_version', 'sendmsg_priority', 'sendmsg_retrytimes'], 'integer'],
            [['sendmsg_order_no', 'sendmsg_tosystem'], 'string', 'max' => 64],
            [['sendmsg_type'], 'string', 'max' => 45],
            [['sendmsg_memo'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'sendmsg_id'            => Yii::t('system', '主键'),
            'sendmsg_order_no'      => Yii::t('system', '业务编号'),
            'sendmsg_type'          => Yii::t('system', '消息类型'),
            'sendmsg_content'       => Yii::t('system', '消息内容'),
            'sendmsg_memo'          => Yii::t('system', '备注'),
            'sendmsg_tosystem'      => Yii::t('system', '终端系统'),
            'sendmsg_status'        => Yii::t('system', '状态'),
            'sendmsg_next_run_at'   => Yii::t('system', '下次发送时间'),
            'sendmsg_create_at'     => Yii::t('system', '创建时间'),
            'sendmsg_update_at'     => Yii::t('system', '更新时间'),
            'sendmsg_version'       => Yii::t('system', '版本'),
            'sendmsg_priority'      => Yii::t('system', '优先级'),
            'sendmsg_retrytimes'    => Yii::t('system', '重试次数'),
            'sendmsg_response_data' => Yii::t('system', '响应内容'),
        ];
    }
}
