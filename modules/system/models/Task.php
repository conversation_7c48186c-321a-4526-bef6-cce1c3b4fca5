<?php

namespace system\models;

use Carbon\Carbon;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%task}}".
 *
 * @property int $task_id
 * @property string $task_order_no
 * @property string $task_type
 * @property string $task_request_data
 * @property string $task_response_data
 * @property string $task_memo
 * @property string $task_status
 * @property string $task_next_run_at
 * @property string $task_create_at
 * @property string $task_update_at
 * @property int $task_version
 * @property int $task_priority
 * @property int $task_retrytimes
 */
class Task extends ActiveRecord
{
    const STATUS_OPEN = 'open';
    const STATUS_RUNNING = 'running';
    const STATUS_ERROR = 'error';
    const STATUS_TERMINATED = 'terminated';
    const STATUS_CLOSE = 'close';
    const STATUS_LIST = [
        self::STATUS_OPEN => '待处理',
        self::STATUS_RUNNING => '处理中',
        self::STATUS_ERROR => '失败',
        self::STATUS_TERMINATED => '终止',
        self::STATUS_CLOSE => '关闭',
    ];

    /**
     * @param string $type
     *
     * @return array [$type, Task::class]
     */
    public static function clazz($type)
    {
        $taskList = [
            'gbiz' => TaskGbiz::class,
            'rbiz' => TaskRbiz::class,
            'dcs' => TaskDcs::class,
            'paySvr' => TaskPaySvr::class,
        ];

        if (!array_key_exists($type, $taskList)) {
            $type = 'dcs';
            $class = TaskDcs::class;
        } else {
            $class = $taskList[$type];
        }

        Yii::debug($class);

        return [$type, $class];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%task}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['task_request_data', 'task_response_data'], 'default', 'value' => ''],
            [['task_type', 'task_status'], 'required'],
            [['task_request_data', 'task_response_data', 'task_status'], 'string'],
            [
                ['task_next_run_at'],
                'filter',
                'filter' => function ($value) {
                    return Carbon::parse($value)->toDateTimeString();
                },
            ],
            [['task_create_at', 'task_update_at'], 'safe'],
            [['task_version', 'task_priority', 'task_retrytimes'], 'integer'],
            [['task_order_no'], 'string', 'max' => 64],
            [['task_type'], 'string', 'max' => 45],
            [['task_memo'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'task_id' => 'ID',
            'task_order_no' => '编号',
            'task_type' => '任务类型',
            'task_request_data' => '请求数据',
            'task_response_data' => '响应数据',
            'task_memo' => '描述',
            'task_status' => '状态',
            'task_next_run_at' => '下次执行时间',
            'task_create_at' => '创建时间',
            'task_update_at' => '更新时间',
            'task_version' => '任务版本',
            'task_priority' => '优先级',
            'task_retrytimes' => '重试次数',
        ];
    }
}
