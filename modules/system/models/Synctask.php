<?php

namespace system\models;

use Yii;

/**
 * This is the model class for table "{{%synctask}}".
 *
 * @property int    $synctask_id
 * @property string $synctask_type          任务类型
 * @property string $synctask_key           任务键值
 * @property string $synctask_from_system   任务来源系统
 * @property string $synctask_request_data  任务数据，Json格式
 * @property string $synctask_response_data 任务执行完车后，返回结果数据，Json格式
 * @property string $synctask_memo          任务执行中出现异常时,纪录异常日志
 * @property string $synctask_status        任务状态，初始状态Open， 执行中为runing, 错误为error，执行完成为close,错误次数达上限为terminated
 * @property string $synctask_create_at
 * @property string $synctask_update_at
 * @property int    $synctask_retrytimes
 * @property string $synctask_order_no      业务主键
 * @property string $synctask_last_run_at   最后执行时间
 */
class Synctask extends \yii\db\ActiveRecord
{
    const STATUS_OPEN       = 'open';
    const STATUS_RUNNING    = 'running';
    const STATUS_ERROR      = 'error';
    const STATUS_TERMINATED = 'terminated';
    const STATUS_CLOSE      = 'close';
    const STATUS_LIST       = [
        self::STATUS_OPEN       => '待处理',
        self::STATUS_RUNNING    => '处理中',
        self::STATUS_ERROR      => '失败',
        self::STATUS_TERMINATED => '成功',
        self::STATUS_CLOSE      => '关闭',
    ];

    /**
     * @param string $type
     *
     * @return array [$type, Task::class]
     */
    public static function clazz($type)
    {
        $taskList = [
            'gbiz'   => SynctaskGbiz::class,
            'rbiz'   => SynctaskRbiz::class,
            'dcs'    => SynctaskDcs::class,
            'paySvr' => SynctaskPaySvr::class,
        ];

        if (!array_key_exists($type, $taskList)) {
            $type  = 'dcs';
            $class = SynctaskDcs::class;
        } else {
            $class = $taskList[$type];
        }

        return [$type, $class];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%synctask}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['synctask_request_data', 'synctask_response_data', 'synctask_memo', 'synctask_status'], 'string'],
            [['synctask_create_at', 'synctask_update_at', 'synctask_last_run_at'], 'safe'],
            [['synctask_retrytimes'], 'integer'],
            [['synctask_type', 'synctask_key', 'synctask_from_system'], 'string', 'max' => 50],
            [['synctask_order_no'], 'string', 'max' => 64],
            [
                ['synctask_key', 'synctask_type', 'synctask_from_system'],
                'unique',
                'targetAttribute' => ['synctask_key', 'synctask_type', 'synctask_from_system'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'synctask_id'            => 'ID',
            'synctask_type'          => '类型',
            'synctask_key'           => '键',
            'synctask_from_system'   => '来源',
            'synctask_request_data'  => '请求数据',
            'synctask_response_data' => '响应数据',
            'synctask_memo'          => '备注',
            'synctask_status'        => '状态',
            'synctask_create_at'     => '创建时间',
            'synctask_update_at'     => '修改时间',
            'synctask_retrytimes'    => '重试次数',
            'synctask_order_no'      => '业务键',
            'synctask_last_run_at'   => '最后执行时间',
        ];
    }
}
