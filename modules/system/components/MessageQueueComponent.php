<?php

namespace system\components;

use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Utils;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use system\exceptions\MessageQueueApiException;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

class MessageQueueComponent extends RequestClient
{
    use ComponentTrait;

    /**
     * @var string
     */
    private static $requestPath;

    protected function getHandlerStack()
    {
        $stack = parent::getHandlerStack();

        $stack->push(Middleware::mapRequest(function (RequestInterface $request) {
            self::$requestPath = $request->getUri()->getPath();

            return $request;
        }));

        $stack->push(Middleware::mapResponse(function (ResponseInterface $response) {
            if ($response->getStatusCode() !== 200) {
                throw new MessageQueueApiException($response->getStatusCode() . ': ' . $response->getReasonPhrase() . PHP_EOL . (string)$response->getBody());
            }

            return $response;
        }));

        /**
         * 特殊处理返回值不符合默认规则的接口
         */
        $stack->push(Middleware::mapResponse(function (ResponseInterface $response) {
            $statisticList = [
                '/api/get-queue-statistic-info',
                '/api/get-subscribe-statistic-info',
                '/api/get-topic-statistic-info',
            ];
            if (in_array(self::$requestPath, $statisticList)) {
                $response = $response->withBody(Utils::streamFor(json_encode([
                    'code' => self::SUCCESS,
                    'message' => 'ok',
                    'data' => json_decode((string)$response->getBody()),
                ])));
            }

            return $response;
        }));

        return $stack;
    }
}
