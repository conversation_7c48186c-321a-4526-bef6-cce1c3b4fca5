<?php

namespace system\components;

use kvmanager\models\KeyValue;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use xlerr\httpca\ComponentTrait;
use yii\base\InvalidConfigException;
use yii\grid\DataColumn;

class ExpressionComponent extends ExpressionLanguage
{
    use ComponentTrait;

    protected function registerFunctions()
    {
        $phpFunc = KeyValue::take('expression_config');
        foreach ($phpFunc as $func) {
            $this->addFunction(ExpressionFunction::fromPhp($func));
        }
    }

    /**
     * @param            $data
     * @param            $key
     * @param int        $index
     * @param DataColumn $column
     *
     * @return mixed
     * @throws InvalidConfigException
     * @example 解决需要计算只能配置闭包，导致不能存储问题
     * ```php
     * 'columns' => [
     *         [
     *              'label' => '本息合计',
     *              'attribute' => 'principal+interest',
     *              'value' => ['\\system\\components\\ExpressionComponent', 'magicColumn'],
     *              'class' => '\\xlerr\\common\\grid\\MoneyDataColumn',
     *         ]
     * ]
     * ```
     */
    public static function magicColumn($data, $key, int $index, DataColumn $column)
    {
        return ExpressionComponent::instance()->evaluate($column->attribute, [
            'row' => $data,
        ]);
    }

    public static function expression($data, $key, int $index, DataColumn $column)
    {
        return ExpressionComponent::instance()->evaluate($column->attribute, $data);
    }

    /**
     * @param int|string $key
     * @param array      $set
     * @param mixed      $default
     *
     * @return mixed
     */
    public static function takeValue($key, array $set, $default = null)
    {
        return $set[$key] ?? $default;
    }
}
