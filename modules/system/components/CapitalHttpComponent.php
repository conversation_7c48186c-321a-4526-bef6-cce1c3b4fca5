<?php

namespace system\components;

use Carbon\Carbon;
use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;

/**
 * Class CapitalHttpComponent
 *
 * @package system\components
 */
class CapitalHttpComponent extends RequestClient
{
    use ComponentTrait;

    public const LOAN_TYPE_IN = 1; // 贷: 流入
    public const LOAN_TYPE_OUT = 2; // 借: 流出

    public $fromSystem;

    /**
     * 流水查询
     *
     * @param string $date
     * @param string $account
     * @param int|null $loanType
     *
     * @return bool
     */
    public function flowTransactions(string $date, string $account, int $loanType = null)
    {
        return $this->post('flow-query/get-transactions', [
            RequestOptions::JSON => [
                'from_system' => $this->fromSystem,
                'key' => md5(uniqid('getTransactions', true) . microtime()),
                'type' => 'getTransactions',
                'data' => [
                    'date' => Carbon::toSystemDateTimeString($date),
                    'account' => $account,
                    'loan_type' => $loanType,
                ],
            ],
        ]);
    }

    /**
     * 通道充值流程查询接口
     * https://git.kuainiujinke.com/b/capital-docs/-/wikis/apis/BIZ%E7%B3%BB%E7%BB%9F/biz#%E4%BA%8C%E5%8D%81%E4%B8%80%E9%80%9A%E9%81%93%E5%85%85%E5%80%BC%E6%B5%81%E7%A8%8B%E6%9F%A5%E8%AF%A2%E6%8E%A5%E5%8F%A3
     *
     * @param string $region
     * @param string $startDate
     * @param string $endDate
     *
     * @return bool
     */
    public function partnerFeeFlow(string $region, string $startDate, string $endDate): bool
    {
        return $this->get('oa/api/payment/get-partner-fee-flow', [
            RequestOptions::QUERY => [
                'business_region' => $region,
                'start_time' => $startDate,
                'end_time' => $endDate,
            ],
        ]);
    }

    /**
     * 通道成本付款信息查询
     * https://git.kuainiujinke.com/b/capital-docs/-/wikis/apis/BIZ%E7%B3%BB%E7%BB%9F/biz#%E4%BA%8C%E5%8D%81%E4%BA%8C%E9%80%9A%E9%81%93%E6%88%90%E6%9C%AC%E4%BB%98%E6%AC%BE%E4%BF%A1%E6%81%AF%E6%9F%A5%E8%AF%A2
     *
     * @param string $region
     * @param string $startDate
     * @param string $endDate
     *
     * @return bool
     */
    public function paymentChannelCost(string $region, string $startDate, string $endDate): bool
    {
        return $this->get('oa/api/payment/get-payment-channel-cost', [
            RequestOptions::QUERY => [
                'business_region' => $region,
                'start_time' => $startDate,
                'end_time' => $endDate,
            ],
        ]);
    }
}
