<?php


namespace system\components;

use Carbon\Carbon;
use finance\models\Withdraw;
use GuzzleHttp\RequestOptions;
use payment\models\ErrorReconciList;
use payment\models\MemberCard;
use Psr\Http\Message\ResponseInterface;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;
use Yii;
use yii\data\Pagination;

/**
 * Class HttpClient
 *
 * @package system\components
 */
class PaymentHttpComponent extends RequestClient
{
    use ComponentTrait;

    /**
     * @var string
     */
    public $merchantName;

    /**
     * @var string
     */
    protected $signKey;

    protected $apiUrl;

    /**
     * @param $key
     */
    public $callBackInfo;

    public function setSignKey($key)
    {
        $this->signKey = $key;
    }

    protected function buildSignWord(array $params)
    {
        ksort($params);
        $argStr = '';
        foreach ($params as $key => $value) {
            if (is_array($value)) {
                $value = $this->buildSignWord($value);
            }
            if ($value === '' or is_null($value)) {
                continue;
            }
            $argStr .= "$key=$value&";
        }

        return $argStr;
    }

    protected function sign(array $params)
    {
        unset($params['sign']);

        return md5($this->buildSignWord($params) . $this->signKey);
    }

    /**
     * 通道余额查询
     *
     * @param string $channelName
     *
     * @return bool
     */
    public function channelBalanceQuery(string $channelName)
    {
        $bodyRaw = [
            'merchant_name' => $this->merchantName,
            'channel_name' => $channelName,
        ];

        return $this->post('withdraw/balance', [
            RequestOptions::JSON => array_merge($bodyRaw, [
                'sign' => $this->sign($bodyRaw),
            ]),
        ]);
    }

    /**
     * @param ErrorReconciList $model
     * @param Pagination $pagination
     *
     * @return bool
     */
    public function withholdReceiptErrorList(ErrorReconciList $model, Pagination $pagination): bool
    {
        return $this->post('reconci/queryErrorList', [
            RequestOptions::JSON => [
                'from_system' => 'portal',
                'key' => uuid_create(),
                'type' => 'ErrorReconciList',
                'reconci_type' => $model->reconci_type,
                'channel_name' => $model->channel_name,
                'channel_key' => $model->channel_key,
                'status' => $model->paysvr_status === '' ? null : $model->paysvr_status,
                'channel_resp_code' => $model->channel_resp_code,
                'channel_resp_message' => $model->channel_resp_message,
                'start_date' => Carbon::toSystemDateTimeString($model->start_date),
                'end_date' => Carbon::toSystemDateTimeString($model->end_date),
                'page_num' => Yii::$app->getRequest()->get($pagination->pageParam, 1),
                'page_size' => $pagination->getPageSize(),
            ],
        ]);
    }

    /**
     * @param $channelName
     * @param $channelKey
     *
     * @return bool
     */
    public function fixWithdrawReceipt($channelName, $channelKey): bool
    {
        return $this->post('reconci/fixWithdrawReceipt', [
            RequestOptions::JSON => [
                'from_system' => 'portal',
                'key' => uuid_create(),
                'type' => 'WithdrawFixData',
                'channel_name' => $channelName,
                'channel_key' => $channelKey,
            ],
        ]);
    }

    /**
     * @param $channelName
     * @param $channelKey
     * @param $operator
     *
     * @return bool
     */
    public function fixWithholdReceipt($channelName, $channelKey, $operator): bool
    {
        return $this->post('reconci/fixWithholdReceipt', [
            RequestOptions::JSON => [
                'from_system' => 'portal',
                'key' => uuid_create(),
                'type' => 'WithholdFixData',
                'channel_name' => $channelName,
                'channel_key' => $channelKey,
                'operator_user' => $operator,
            ],
        ]);
    }

    /**
     * @param ResponseInterface $response
     *
     * @return array
     */
    protected function handleResponse(ResponseInterface $response)
    {
        $content = (string)$response->getBody();
        $data = (array)json_decode($content, true) + [
                'code' => self::FAILURE,
                'message' => '返回值格式错误<br/>' . $content,
                'data' => null,
            ];
        // 将`code`转换为`self::SUCCESS|self::FAILURE`
        if ($this->apiUrl === 'withdraw/autoWithdraw') {
            if (in_array($data['code'] ?? 1, [0, 2], true)) {
                $data['code'] = self::SUCCESS;
            } else {
                $data['code'] = self::FAILURE;
            }
        } elseif ($data['code'] !== self::SUCCESS) {
            $data['code'] = self::FAILURE;
        }

        return $data;
    }

    public function autoWithdraw(Withdraw $withdraw, string $attachmentUrl = ''): bool
    {
        $this->apiUrl = 'withdraw/autoWithdraw';

        $config = externalComponentConfig('selfApi');

        $callbackUri = rtrim($config['baseUri'] ?? '', '/') . '/withdraw-callback';

        return $this->post('withdraw/autoWithdraw', [
            RequestOptions::JSON => [
                'merchant_name' => 'PORTAL',
                'merchant_key' => $withdraw->withdraw_merchant_key,
                'channel_name' => $withdraw->withdraw_channel,
                'trade_no' => $withdraw->withdraw_trade_no,
                'card_uuid' => $withdraw->withdraw_receive_uuid,
                'user_uuid' => $withdraw->withdraw_receive_userid,
                'amount' => $withdraw->withdraw_amount,
                'reason' => $withdraw->withdraw_reason,
                'sign_company' => '',
                'biz_type' => $withdraw->withdraw_biz_type,
                'callback' => $callbackUri,
                'download_invoice_url' => $attachmentUrl,
            ],
        ]);
    }

    public function autoWithdrawQuery(string $merchantKey): bool
    {
        $this->post('withdraw/query', [
            RequestOptions::JSON => [
                'merchant_name' => 'PORTAL',
                'merchant_key' => $merchantKey,
            ],
        ]);

        return true;
    }

    public function memberRegister(MemberCard $memberCard): bool
    {
        return $this->post('card/memberRegister', [
            RequestOptions::JSON => [
                'merchant_name' => 'PORTAL',
                'merchant_key' => uuid_create(),
                'user_uuid' => $memberCard->user_uuid,
                'card_uuid' => $memberCard->card_uuid,
                'member_type' => $memberCard->member_type,
                'account_type' => $memberCard->account_type,
                'account_no' => $memberCard->account_no,
                'user_name' => $memberCard->user_name,
                'bank_code' => $memberCard->bank_code,
                'mobile' => $memberCard->mobile,
                'email' => $memberCard->email,
                'id_num' => $memberCard->id_num,
                'id_type' => $memberCard->id_type,
                'address' => $memberCard->address,
                'receiver_type' => $memberCard->receiver_type,
                'large_account' => $memberCard->large_account,
                'max_withdraw_amount' => $memberCard->max_withdraw_amount,
            ],
        ]);
    }

    public function queryMemberCards($memberType = null): bool
    {
        return $this->post('card/queryMemberCards', [
            RequestOptions::JSON => [
                'merchant_name' => 'PORTAL',
                'merchant_key' => uuid_create(),
                'member_type' => (string)$memberType,
            ],
        ]);
    }

    /**
     * @return bool
     */
    public function manualClearCache(): bool
    {
        return $this->post('manual/clearCache/default');
    }

    /**
     * @param string $channel
     * @param string $date
     * @param string $operator
     *
     * @return bool
     */
    public function fixFailedWithdrawReceipt(string $channel, string $date, string $operator): bool
    {
        return $this->post('reconci/fixFailedWithdrawReceipt', [
            RequestOptions::JSON => [
                'from_system' => 'portal',
                'key' => uuid_create(),
                'type' => 'WithdrawFixData',
                'channel_name' => $channel,
                'date' => Carbon::toSystemDateTimeString($date),
                'operator_user' => $operator,
            ],
        ]);
    }

    public function fixSuccessWithdrawReceipt(
        string $channelName,
        string $channelKey,
        $channelInnerKey,
        string $operator
    ): bool {
        return $this->post('reconci/fixSuccessWithdrawReceipt', [
            RequestOptions::JSON => [
                'from_system' => 'portal',
                'key' => uuid_create(),
                'type' => 'withdrawFixSuccessDataSync',
                'channel_name' => $channelName,
                'channel_key' => $channelKey,
                'channel_inner_key' => $channelInnerKey,
                'operator_user' => $operator,
            ],
        ]);
    }

    /**
     * 查询通道代扣订单(支付系统透传数据)
     *
     * @param array $data
     *
     * @return bool
     */
    public function transactionWithholdQuery(array $data): bool
    {
        return $this->post('transaction/withholdQuery', [
            RequestOptions::JSON => array_merge($data, [
                'merchant_name' => 'portal',
            ]),
        ]);
    }

    /**
     * 查询通道代付订单(支付系统透传数据)
     *
     * @param array $data
     *
     * @return bool
     */
    public function transactionWithdrawQuery(array $data): bool
    {
        return $this->post('transaction/withdrawQuery', [
            RequestOptions::JSON => array_merge($data, [
                'merchant_name' => 'portal',
            ]),
        ]);
    }

    /**
     * 回调补推数据
     *
     * @param array $data
     *
     * @return bool
     */
    public function manualCallback(array $data): bool
    {
        return $this->post('manual/callback', [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * 代付修改完成时间
     *
     * @param array{merchant_name:string,channel_name:string,channel_key:string,finished_at:string,reason:string} $data
     *
     * @return bool
     */
    public function withdrawUpdate(array $data): bool
    {
        return $this->post('withdraw/update', [
            RequestOptions::JSON => $data,
        ]);
    }


    /**
     * 代扣修改
     *
     * @param array{merchant_name:string,channel_name:string,channel_key:string,finished_at:string,reason:string,status:int} $data
     *
     * @return bool
     */
    public function withholdUpdate(array $data): bool
    {
        return $this->post('withhold/update', [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * @param array{id:string,adjust_type:string,amount:string,fee_type:string,remark:string,operator:string} $data
     *
     * @return bool
     */
    public function feeAdjust(array $data): bool
    {
        return $this->post('fee/adjust', [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * @param string $bizType
     * @param string $orderNo
     * @param string $tradeNo
     * @param string $channelName
     * @param string $inAccountUserUuid
     * @param string $inAccountCardUuid
     * @param int $amount
     * @param string $memo
     *
     * @return void
     */
    public function transfer(
        string $bizType,
        string $orderNo,
        string $tradeNo,
        string $channelName,
        string $inAccountUserUuid,
        string $inAccountCardUuid,
        int $amount,
        string $memo = ''
    ): void {
        $this->post('transfer/transfer', [
            RequestOptions::JSON => [
                'biz_type' => $bizType,
                'merchant_name' => $this->merchantName,
                'merchant_key' => $orderNo,
                'trade_no' => $tradeNo,
                'asset_item_no' => '',
                'memo' => $memo,
                'from_channel_key' => '',
                'channel_name' => $channelName,
                'user_uuid' => $inAccountUserUuid,
                'card_uuid' => $inAccountCardUuid,
                'amount' => $amount,
            ],
        ]);
    }

    /**
     * @param string $orderNo
     *
     * @return void
     */
    public function query(string $orderNo): void
    {
        $this->post('transfer/query', [
            RequestOptions::JSON => [
                'merchant_name' => $this->merchantName,
                'merchant_key' => $orderNo,
            ],
        ]);
    }
}
