<?php

namespace system\controllers;

use Carbon\Carbon;
use system\models\Task;
use system\models\TaskSearch;
use Yii;
use yii\base\DynamicModel;
use yii\base\UserException;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\NotFoundHttpException;
use yii\web\Request;

/**
 * TaskController implements the CRUD actions for Task model.
 * @property-read Request $request
 */
class TaskController extends SystemParamAuthController
{
    /**
     * @var Task
     */
    public $modelClass;

    public function getSystemParam()
    {
        return $this->request->get('system', 'dcs');
    }

    public function init()
    {
        parent::init();
        [$type, $class] = Task::clazz($this->getSystemParam());
        $this->request->setQueryParams(array_merge($this->request->queryParams, [
            'system' => $type,
        ]));

        $this->modelClass = $class;
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ]);
    }

    /**
     * Lists all Task models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel  = new TaskSearch($this->modelClass);
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Task model.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Task model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param $id
     *
     * @return mixed
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionRun($id)
    {
        throw new UserException('待接入');
//        $model = $this->findModel($id);
//
//        $code    = -1;
//        $message = null;
//
//        if (Yii::$app->request->isPost) {
//            $client = AccessLayer::getInstance();
//            if ($client->runTask($model)) {
//                return $this->redirect(['index']);
//            }
//
//            $code    = $client->getCode();
//            $message = $client->getError();
//        }
//
//        return $this->render('run-task', [
//            'model'   => $model,
//            'code'    => $code,
//            'message' => $message,
//        ]);
    }

    /**
     * Updates an existing Task model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load($this->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Task model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect($this->request->referrer);
    }

    /**
     * Finds the Task model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return Task the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $class = $this->modelClass;
        if (($model = $class::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionBatchUpdate()
    {
        $id = $this->request->get('id');
        $id = explode(',', $id);

        $tasks = $this->modelClass::findAll([
            'task_id' => $id,
        ]);

        $model = new DynamicModel([
            'task_status',
            'task_next_run_at' => Carbon::now()->toDateTimeString(),
        ], [
            'attributeLabels' => [
                'task_status'      => '状态',
                'task_next_run_at' => '下次执行时间',
            ],
        ]);

        $model->addRule(['task_status', 'task_next_run_at'], 'required');

        if ($this->request->isPost && $model->load($this->request->post())) {
            try {
                if ($this->modelClass::updateAll($model->getAttributes(), ['task_id' => $id]) >= 0) {
                    Yii::$app->session->addFlash('success', '提交成功!');

                    return '<script> window.top.reloadCurrentTab(); </script>';
                }
            } catch (\Exception $e) {
                Yii::$app->session->addFlash('error', '批量操作失败: ' . $e->getMessage());
            }
        }

        return $this->render('batch-update', [
            'model' => $model,
            'tasks' => $tasks,
        ]);
    }
}
