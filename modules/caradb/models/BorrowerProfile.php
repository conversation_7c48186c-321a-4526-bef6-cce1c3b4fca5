<?php

namespace caradb\models;

use Yii;

/**
 * This is the model class for table "borrower_profile".
 *
 * @property int $borrower_id
 * @property string $borrower_user_uuid Unique Borrower ID assigned by P2P
 * @property string $borrower_user_name User Name
 * @property string $borrower_identity Borrower ID Number
 * @property string $borrower_mobile Mobile
 * @property string $borrower_mobile_enc Mobile(enc)
 * @property string $borrower_email Email
 * @property string $borrower_email_enc
 * @property string $borrower_tax_num Tax_num
 * @property string $borrower_birthplace Place of birth
 * @property string $borrower_birthday Birthday(format: yyyy-MM-dd)
 * @property int $borrower_gender Gender
 * @property string|null $borrower_address
 * @property int $borrower_city_id City id
 * @property int $borrower_province_id Province id
 * @property string $borrower_zip_code Zip code
 * @property int $borrower_religion_id Religion(default:0 unknows)
 * @property int $borrower_marriage_status Marriage status(default 0. unknows)
 * @property int $borrower_profession_id Profession type. default 0: unknows
 * @property int $borrower_work_field_id Work field type
 * @property int $borrower_online_work_id Online work(default 0 unknows)
 * @property int $borrower_income_id Income id, default 0:unknows
 * @property int $borrower_exact_income
 * @property int $borrower_total_asset_id Total assets id (default 0:unknows)
 * @property int $borrower_work_years_id Working years(default 0:unknows)
 * @property int $borrower_degree_id Academic degree(default 0:unknows)
 * @property int $borrower_own_house_id Have own house(default 0:unknows)
 * @property string $borrower_mother_name Mothers Name
 * @property string $borrower_districts Contains the district recorded on the debtors identity card/KTP.
 * @property string $borrower_sub_districts Contains the sub-district recorded on the debtors identity card/KTP.
 * @property string|null $borrower_workplace
 * @property string $borrower_business_code Code of Business according to place of work
 * @property string $borrower_reg_at Register time
 * @property string $borrower_create_at Create time 
 * @property string $borrower_update_at Update time
 */
class BorrowerProfile extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'borrower_profile';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('caraDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['borrower_gender', 'borrower_city_id', 'borrower_province_id', 'borrower_religion_id', 'borrower_marriage_status', 'borrower_profession_id', 'borrower_work_field_id', 'borrower_online_work_id', 'borrower_income_id', 'borrower_exact_income', 'borrower_total_asset_id', 'borrower_work_years_id', 'borrower_degree_id', 'borrower_own_house_id'], 'integer'],
            [['borrower_reg_at', 'borrower_create_at', 'borrower_update_at'], 'safe'],
            [['borrower_user_uuid', 'borrower_tax_num', 'borrower_districts', 'borrower_sub_districts', 'borrower_business_code'], 'string', 'max' => 32],
            [['borrower_user_name', 'borrower_identity', 'borrower_email', 'borrower_mother_name'], 'string', 'max' => 255],
            [['borrower_mobile', 'borrower_mobile_enc', 'borrower_email_enc', 'borrower_birthplace'], 'string', 'max' => 128],
            [['borrower_birthday', 'borrower_zip_code'], 'string', 'max' => 10],
            [['borrower_address', 'borrower_workplace'], 'string', 'max' => 1024],
            [['borrower_user_uuid'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'borrower_id' => 'Borrower ID',
            'borrower_user_uuid' => 'Unique Borrower ID assigned by P2P',
            'borrower_user_name' => 'User Name',
            'borrower_identity' => 'Borrower ID Number',
            'borrower_mobile' => 'Mobile',
            'borrower_mobile_enc' => 'Mobile(enc)',
            'borrower_email' => 'Email',
            'borrower_email_enc' => 'Borrower Email Enc',
            'borrower_tax_num' => 'Tax_num',
            'borrower_birthplace' => 'Place of birth',
            'borrower_birthday' => 'Birthday(format: yyyy-MM-dd)',
            'borrower_gender' => 'Gender',
            'borrower_address' => 'Borrower Address',
            'borrower_city_id' => 'City id',
            'borrower_province_id' => 'Province id',
            'borrower_zip_code' => 'Zip code',
            'borrower_religion_id' => 'Religion(default:0 unknows)',
            'borrower_marriage_status' => 'Marriage status(default 0. unknows)',
            'borrower_profession_id' => 'Profession type. default 0: unknows',
            'borrower_work_field_id' => 'Work field type',
            'borrower_online_work_id' => 'Online work(default 0 unknows)',
            'borrower_income_id' => 'Income id, default 0:unknows',
            'borrower_exact_income' => 'Borrower Exact Income',
            'borrower_total_asset_id' => 'Total assets id (default 0:unknows)',
            'borrower_work_years_id' => 'Working years(default 0:unknows)',
            'borrower_degree_id' => 'Academic degree(default 0:unknows)',
            'borrower_own_house_id' => 'Have own house(default 0:unknows)',
            'borrower_mother_name' => 'Mothers Name',
            'borrower_districts' => 'Contains the district recorded on the debtors identity card/KTP.',
            'borrower_sub_districts' => 'Contains the sub-district recorded on the debtors identity card/KTP.',
            'borrower_workplace' => 'Borrower Workplace',
            'borrower_business_code' => 'Code of Business according to place of work',
            'borrower_reg_at' => 'Register time',
            'borrower_create_at' => 'Create time ',
            'borrower_update_at' => 'Update time',
        ];
    }
}
