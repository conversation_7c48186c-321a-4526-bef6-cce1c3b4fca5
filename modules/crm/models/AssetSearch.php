<?php

namespace crm\models;

use Carbon\Carbon;
use finance\models\Asset;
use grant\models\AssetBorrower;
use grant\models\WithdrawOrder;
use grant\models\WithdrawRecord;
use xlerr\desensitise\Desensitise;
use yii\data\ActiveDataProvider;
use yii\data\DataProviderInterface;
use yii\db\Expression;

use function xlerr\desensitise\encrypt;

class AssetSearch extends Asset
{
    public $withdraw_record_channel = '';
    public $withdraw_record_channel_key = '';
    public $asset_borrower_mobile = '';
    public $mobile;
    public $order_no;
    public $start_time;
    public $end_time;
    public $item_no;

    public function formName(): string
    {
        return '';
    }

    public function rules(): array
    {
        return [
            [['start_time', 'end_time'], 'string'],
            [['item_no', 'mobile', 'order_no'], 'trim'],
            [
                [
                    'start_time',
                    'end_time',
                    'order_no',
                    'mobile',
                    'item_no',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @param array $params
     *
     * @return DataProviderInterface
     */
    public function search(array $params): DataProviderInterface
    {
        $this->load($params);
        $query = self::find()
            ->where(['not in', 'asset_status', [self::STATUS_SIGN, self::STATUS_SALE]]);
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => [
                    'asset_id',
                ],
                'defaultOrder' => [
                    'asset_id' => SORT_DESC,
                ],
            ],
        ]);
        if (!$this->validate()) {
            $dataProvider->models = [];

            return $dataProvider;
        }

        $itemNoList = [];
        if ($this->mobile) {
            $itemNoList = AssetBorrower::find()
                ->where([
                    'asset_borrower_mobile' => encrypt($this->mobile, Desensitise::TYPE_PHONE_NUMBER)->hash,
                ])
                ->select('asset_borrower_item_no')
                ->column();
        }
        if (!empty($this->order_no)) {
            /**
             * @var WithdrawRecord $record
             */
            $record = WithdrawRecord::find()->where([
                'withdraw_record_channel_key' => $this->order_no,
            ])->one();
            if (!$record || !$record->withdraw_record_channel) {
                $dataProvider->models = [];

                return $dataProvider;
            }
            //在此求交集
            $itemNoWithOrderNo =
                (array)rtrim($record->withdrawOrder->withdraw_order_asset_item_no ?? '', WithdrawOrder::SPLICE_STR);
            $itemNoList = $this->mobile ? array_intersect($itemNoList, $itemNoWithOrderNo) : $itemNoWithOrderNo;
        }
        $query->andFilterWhere([
            'asset_item_no' => $this->item_no,
        ])
            ->andFilterWhere([
                'asset_item_no' => $itemNoList,
            ])
            ->andWhere(new Expression(sprintf('%d', empty($this->mobile) || !empty($itemNoList))))
            ->andFilterWhere([
                'and',
                ['>=', 'asset_create_at', $this->start_time],
                [
                    '<',
                    'asset_create_at',
                    $this->end_time ? Carbon::parse($this->end_time)->addDay()->toDateString()
                        : null,
                ],
            ]);

        return $dataProvider;
    }

    /**
     * {@inheritdoc}
     * The default implementation returns the names of the columns whose values have been populated into this record.
     */
    public function fields(): array
    {
        $fields = parent::fields();
        $data = [];
        $columns = [
            'id',
            'item_no',
            'period_count',
            'product_category',
            'status',
            'loan_channel',
            'type',
            'principal_amount',
            'granted_principal_amount',
            'from_app',
            'repayment_app',
            'grant_at',
            'actual_grant_at',
            'create_at',
            'total_amount',
            'balance_amount',
            'ref_order_no',
        ];

        foreach ($columns as $column) {
            $data[$column] = $fields[$column];
        }

        return $data;
    }
}
