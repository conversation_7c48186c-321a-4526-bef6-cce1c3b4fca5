<?php

namespace crm\actions;

use crm\models\AssetSearch;
use finance\models\Asset;
use grant\models\WithdrawOrder;
use Yii;
use yii\base\Action;
use yii\data\DataProviderInterface;
use yii\helpers\ArrayHelper;

class AssetList extends Action
{
    /**
     * @return DataProviderInterface
     * @throws \Exception
     */
    public function run(): DataProviderInterface
    {
        $dataProvider = (new AssetSearch())->search(Yii::$app->getRequest()->get());
        $itemNoList = array_column($dataProvider->models, 'asset_item_no');

        $withdrawInfo = WithdrawOrder::withdrawInfo($itemNoList);

        $models = array_map(function ($model) use ($withdrawInfo) {

            /**
             * @var AssetSearch|Asset $model
             */
            $item = $model->toArray([], [
                'create_at',
                'update_at',
                'actual_payoff_at',
                'payoff_at',
                'due_at',
                'actual_grant_at',
                'effect_at',
                'grant_at'
            ]);

            $withdrawRecordChannel = $withdrawInfo[$model->asset_item_no . WithdrawOrder::SPLICE_STR]['withdraw_record_channel'] ?? '';
            $withdrawRecordChannelKey = $withdrawInfo[$model->asset_item_no . WithdrawOrder::SPLICE_STR]['withdraw_record_channel_key'] ?? '';

            return ArrayHelper::merge($item, [
                'withdraw_record_channel' => $withdrawRecordChannel,
                'withdraw_record_channel_key' => $withdrawRecordChannelKey,
                'asset_borrower_mobile' => $model->borrower->asset_borrower_mobile ?? '',
                'is_delay' => $model->getAssetDelays()->exists(),
            ], $model->borrower->toArray(['此处返回空数组,请忽略！'], ['user_full_name', 'card_num']));

        }, $dataProvider->models);

        $dataProvider->models = $models;

        return $dataProvider;
    }
}
