<?php

namespace contract\components;

use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;
use Yii;
use yii\base\Exception;
use yii\base\UserException;

class ContractComponent extends RequestClient
{
    use ComponentTrait;

    public const FROM_SYSTEM = 'BIZ';

    /**
     * @param array $data
     *
     * @return bool
     * @throws Exception
     */
    public function batchSign(array $data): bool
    {
        return $this->post('contract/batchSign', [
            RequestOptions::JSON => [
                'from_system' => self::FROM_SYSTEM,
                'type' => 'ContractSign',
                'key' => md5(uniqid('ContractSign', true) . Yii::$app->security->generateRandomString()),
                'data' => $data,
            ],
        ]);
    }

    /**
     * @param string $idNumEncrypt
     *
     * @return bool
     */
    public function contractQuery(string $idNumEncrypt): bool
    {
        return $this->post('contract/query', [
            RequestOptions::JSON => [
                'item_no' => $idNumEncrypt,
                'need_sign' => false,
            ],
        ]);
    }

    /**
     * @param string $url
     *
     * @return string
     * @throws UserException
     */
    public function urlSign(string $url): string
    {
        $result = $this->get('contract/sign-url', [
            RequestOptions::QUERY => [
                'url' => $url,
            ],
        ]);

        if (!$result) {
            throw new UserException('签约系统：' . $this->getError());
        }

        return (string)$this->getData();
    }

    /**
     * @param array $data
     *
     * @return bool
     */
    public function msgUpdate(array $data): bool
    {
        return $this->post('msg/update', [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * 查询合同模板数据
     *
     * @param string $key
     *
     * @return bool
     * @see https://git.kuainiujinke.com/cd_biz/contract/-/wikis/%E6%9F%A5%E8%AF%A2%E5%90%88%E5%90%8C%E6%A8%A1%E6%9D%BF%E6%95%B0%E6%8D%AE
     */
    public function queryTemplateInfo(string $key): bool
    {
        return $this->get('contract-admin/query-template-info', [
            RequestOptions::QUERY => [
                'key' => $key,
            ],
        ]);
    }
}
