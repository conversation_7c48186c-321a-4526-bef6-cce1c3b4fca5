<?php

use contract\models\ContractConfigSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $searchModel ContractConfigSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '合同配置';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    //    'filterModel' => $searchModel,
    'columns' => [
        [
            'header' => '操作',
            'class' => DialogActionColumn::class,
            'template' => '{view} {update}',
        ],
        [
            'attribute' => 'contract_config_type',
        ],
        [
            'attribute' => 'contract_config_type_text',
            'format' => ['truncate', 15],
        ],
        [
            'attribute' => 'contract_config_tmp_key',
            'format' => static function ($key) {
                $key = trim((string)$key);
                if ($key) {
                    return Html::a($key, ['preview-template', 'key' => $key], [
                        'target' => '_blank',
                        'target_browser' => true,
                    ]);
                }

                return $key;
            },
        ],
        [
            'attribute' => 'contract_config_storage',
            'format' => ['truncate', 15],
        ],
        'contract_config_version',
        'contract_config_create_at',
    ],
]);
