<?php

use contract\models\ContractConfig;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model ContractConfig */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_type')->textInput([
                'disabled' => !$model->isNewRecord,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_type_text')->textInput([
                'maxlength' => true,
            ]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_operate_type')->widget(Select2::class, [
                'data' => ContractConfig::OPERATE_TYPE,
                'pluginOptions' => [
                    'allowClear' => true
                ],
                'options' => [
                    'prompt' => $model->getAttributeLabel('contract_config_operate_type'),
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_tmp_type')->widget(Select2::class, [
                'data' => ContractConfig::TMP_TYPE,
                'pluginOptions' => [
                    'allowClear' => true
                ],
                'options' => [
                    'prompt' => $model->getAttributeLabel('contract_config_tmp_type'),
                ],
            ]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_tmp_key', [
                'addon' => [
                    'append' => [
                        'content' => Html::a('预览合同文件', ['preview-template'], [
                            'class' => 'btn btn-info btn-flat contract-preview',
                            'target' => '_blank',
                            'target_browser' => true,
                            'data' => [
                                'href' => Url::to(['preview-template']),
                            ],
                        ]),
                        'asButton' => true,
                    ],
                ],
            ])->textInput([
                'disabled' => !$model->isNewRecord,
                'maxlength' => true,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_tmp_version')->textInput([
                'disabled' => !$model->isNewRecord,
            ]) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_version')->textInput([
                'disabled' => !$model->isNewRecord,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_config_storage')->textInput([
                'disabled' => !$model->isNewRecord,
                'maxlength' => true,
            ])->label('合同存储位置(spel表达式，例如：storage.getBucketByFromApp(#asset))') ?>
        </div>
    </div>

    <?= $form->field($model, 'contract_config_params')->widget(CodeEditor::class, [
        'clientOptions' => [
            'mode' => CodeEditor::MODE_JSON,
            'minLines' => 5,
            'maxLines' => 40,
        ],
    ])->label(
        vsprintf('%s %s', [
            $model->getAttributeLabel('contract_config_params'),
            Html::a('获取参数', ['template-query'], [
                'class' => 'btn btn-xs btn-twitter contract-params',
                'style' => 'display: none',
            ]),
        ])
    ) ?>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', ['class' => 'btn btn-primary',]) ?>
</div>

<?php ActiveForm::end(); ?>
<script>
    <?php $this->beginBlock('js') ?>
    ;(function () {
        const tmpKeyEl = $('#<?= Html::getInputId($model, 'contract_config_tmp_key') ?>'),
            paramsEl = aceInstance['<?= Html::getInputId($model, 'contract_config_params') ?>'],
            contractParamsEl = $('a.contract-params');

        let timer;
        tmpKeyEl.on('keyup change', function () {
            if (timer) {
                window.clearTimeout(timer);
            }
            timer = window.setTimeout(function () {
                if (tmpKeyEl.val()) {
                    contractParamsEl.show(0);
                }
            }, 100);
        }).trigger('keyup');

        contractParamsEl.on('click', function () {
            if (paramsEl.getSession().getValue().replace(/(^\s*|\s*$)/, '') === '') {
                $.post($(this).attr('href'), {
                    key: tmpKeyEl.val()
                }, function (res) {
                    if (res.code) {
                        layer.msg(res.message);
                    } else {
                        paramsEl.getSession().setValue(JSON.stringify(res.data.params, null, '    '));
                    }
                });
            }

            return false;
        });

        $('a.contract-preview').on('click', function () {
            const self = $(this);
            self.attr('href', self.data('href') + '?key=' + tmpKeyEl.val());
            return true;
        });


    })();
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>
