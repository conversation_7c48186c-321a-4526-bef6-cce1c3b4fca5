<?php

use Carbon\Carbon;
use cmdb\widgets\DetailView;
use contract\models\ContractBind;
use contract\models\ContractConfig;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ContractConfig */

$this->title = $model->contract_config_type_text;
$this->params['breadcrumbs'][] = ['label' => '合同配置', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="box box-info">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'krajeeDialogSettings' => [
                'overrideYiiConfirm' => false,
            ],
            'attributes' => [
                [
                    'columns' => [
                        'contract_config_type',
                        'contract_config_type_text',
                    ],
                ],
                [
                    'columns' => [
                        [
                            'attribute' => 'contract_config_tmp_key',
                            'format' => 'raw',
                            'value' => static function () use ($model) {
                                $key = trim((string)$model->contract_config_tmp_key);
                                if ($key) {
                                    return Html::a($key, ['preview-template', 'key' => $key], [
                                        'target' => '_blank',
                                        'target_browser' => true,
                                    ]);
                                }

                                return $key;
                            },
                        ],
                        'contract_config_tmp_version',
                    ],
                ],
                [
                    'columns' => [
                        'contract_config_version',
                        'contract_config_storage',
                    ],
                ],
                [
                    'columns' => [
                        [
                            'valueColOptions' => [
                                'style' => 'width: 90%',
                            ],
                            'attribute' => 'contract_config_params',
                            'format' => 'raw',
                            'value' => CodeEditor::widget([
                                'name' => '_view_params',
                                'value' => $model->contract_config_params,
                                'clientOptions' => [
                                    'model' => CodeEditor::MODE_JSON,
                                    'minLines' => 10,
                                    'maxLines' => 40,
                                    'readOnly' => true,
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ]) ?>
    </div>
</div>

<?= GridView::widget([
    'layout' => '<div class="box-header with-border"><div class="box-title">绑定信息</div></div><div class="box-body table-responsive no-padding">{items}</div><div class="box-footer">{pager}</div>',
    'dataProvider' => new ActiveDataProvider([
        'query' => $model->getBinds(),
        'pagination' => false,
        'sort' => [
            'attributes' => [
                'contract_bind_id',
            ],
            'defaultOrder' => [
                'contract_bind_id' => SORT_DESC,
            ],
        ],
    ]),
    'columns' => [
        [
            'attribute' => 'contract_bind_sign_type',
            'format' => ['in', ContractBind::signTypeList()],
        ],
        [
            'attribute' => 'contract_bind_sign_opportunity',
            'format' => ['in', ContractBind::oppList()],
        ],
        'contract_bind_rule',
        'contract_bind_valid_start_at',
        'contract_bind_valid_end_at',
        [
            'header' => Html::a('创建绑定信息', [
                'contract-bind/create',
                'configId' => $model->contract_config_id,
                'type' => $model->contract_config_type,
            ], [
                'class' => 'btn btn-xs btn-success layer-dialog',
                'data' => [
                    'layer-window' => 'self',
                ],
            ]),
            'class' => ActionColumn::class,
            'template' => '{update} {void}',
            'urlCreator' => static function ($action, ContractBind $model) {
                return [
                    'contract-bind/' . $action,
                    'id' => $model->contract_bind_id,
                    'configId' => $model->contract_bind_config_id,
                    'type' => $model->contract_bind_type,
                ];
            },
            'buttons' => [
                'update' => static function ($url) {
                    return ActionColumn::newButton('编辑', $url, [
                        'class' => 'btn-primary layer-dialog',
                        'data' => [
                            'layer-window' => 'self',
                        ],
                    ]);
                },
                'void' => static function ($url) {
                    return ActionColumn::newButton('失效', $url, [
                        'class' => 'btn-danger',
                        'data' => [
                            'method' => 'post',
                            'confirm' => '确定要设置为失效吗?',
                        ],
                    ]);
                },
            ],
            'visibleButtons' => [
                'update' => static function (ContractBind $bind) {
                    return Carbon::parse($bind->contract_bind_valid_end_at) > Carbon::now();
                },
                'void' => static function (ContractBind $bind) {
                    return Carbon::parse($bind->contract_bind_valid_end_at) > Carbon::now();
                },
            ],
        ],
    ],
]) ?>
<script>
    <?php $this->beginBlock('newBindJs') ?>
    ;(function () {
        $('a.bind-create, a.bind-update').on('click', function () {
            layer.open({
                type: 2,
                shadeClose: true,
                area: ['60%', '70%'],
                content: $(this).attr('href'),
            });
            return false;
        });
    })();
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['newBindJs']) ?>
</script>