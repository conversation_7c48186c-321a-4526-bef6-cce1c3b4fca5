<?php

use yii\web\View;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model \contract\models\ContractBind */

$this->title = $model->contract_bind_id;
$this->params['breadcrumbs'][] = ['label' => 'Contract Binds', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('Update', ['update', 'contract_bind_id' => $model->contract_bind_id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a('Delete', ['delete', 'contract_bind_id' => $model->contract_bind_id], [
        'class' => 'btn btn-danger',
        'data' => [
            'confirm' => Yii::t('yii', 'Are you sure you want to delete this item?'),
            'method' => 'post',
        ],
    ]) ?>
    <?= Html::a('Go Back', ['index'], ['class' => 'btn btn-default']) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title"><?= 'Detail' ?></div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options'    => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'contract_bind_id',
                'contract_bind_config_id',
                'contract_bind_type',
                'contract_bind_rule',
                'contract_bind_sign_type',
                'contract_bind_sign_opportunity',
                'contract_bind_create_at',
                'contract_bind_update_at',
                'contract_bind_valid_start_at',
                'contract_bind_valid_end_at',
                'contract_bind_config_version',
            ],
        ]) ?>
    </div>
</div>
