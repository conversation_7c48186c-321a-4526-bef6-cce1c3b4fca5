<?php

use contract\models\ContractBind;
use kartik\widgets\DateTimePicker;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ContractBind */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= Html::activeHiddenInput($model, 'contract_bind_config_id') ?>
    <?= Html::activeHiddenInput($model, 'contract_bind_type') ?>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_bind_sign_type')->widget(Select2::class, [
                'data' => ContractBind::signTypeList(),
                'hideSearch' => true,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_bind_sign_opportunity')->widget(Select2::class, [
                'data' => ContractBind::oppList(),
                'hideSearch' => true,
            ]) ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_bind_rule')->label(
                vsprintf('绑定规则%s', [
                    Html::button('<i class="fa fa-question-circle-o"></i>', [
                        'class' => 'btn-link',
                        'data' => [
                            'toggle' => 'popover',
                            'trigger' => 'focus',
                            'title' => 'spel表达式',
                            'html' => 'true',
                            'content' => '<b>可用变量如下:</b> asset、assetExtend、borrowerBindCard<br/>
<b>示例:</b> #asset.loanChannel == "xxxx"<br/>
<b>更多详见:</b> <a href="https://git.kuainiujinke.com/cd_biz/contract/-/wikis/%E5%90%88%E5%90%8C%E7%BB%91%E5%AE%9A%E8%A7%84%E5%88%99%E5%8F%AF%E7%94%A8%E5%8F%98%E9%87%8F" target="_blank">合同绑定规则可用变量</a>',
                        ],
                    ]),
                ])
            ) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_bind_config_version')->textInput() ?>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'contract_bind_valid_start_at')->widget(DateTimePicker::class, [
                'type' => DateTimePicker::TYPE_INPUT,
                'pluginOptions' => [
                    'todayBtn' => 'linked',
                    'format' => 'yyyy-mm-dd hh:ii:ss',
                    'todayHighlight' => true,
                    'autoclose' => true,
                ],
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'contract_bind_valid_end_at')->widget(DateTimePicker::class, [
                'type' => DateTimePicker::TYPE_INPUT,
                'pluginOptions' => [
                    'todayBtn' => 'linked',
                    'format' => 'yyyy-mm-dd hh:ii:ss',
                    'todayHighlight' => true,
                    'autoclose' => true,
                ],
            ]) ?>
        </div>
    </div>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>

<script>
    <?php $this->beginBlock('js') ?>
    $(document).ready(function () {
        $('[data-toggle="popover"]').popover();
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['js']) ?>
</script>