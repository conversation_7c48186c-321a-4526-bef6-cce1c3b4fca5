<?php

use yii\web\View;
use yii\data\ActiveDataProvider;
use xlerr\common\widgets\GridView;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */

$this->title = 'Contract Binds';
$this->params['breadcrumbs'][] = $this->title;
?>



<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        ['class' => 'yii\grid\ActionColumn'],

        'contract_bind_id',
        'contract_bind_config_id',
        'contract_bind_type',
        'contract_bind_rule',
        'contract_bind_sign_type',
        // 'contract_bind_sign_opportunity',
        // 'contract_bind_create_at',
        // 'contract_bind_update_at',
        // 'contract_bind_valid_start_at',
        // 'contract_bind_valid_end_at',
        // 'contract_bind_config_version',

    ],
]); ?>
