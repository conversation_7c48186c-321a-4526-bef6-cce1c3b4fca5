<?php

namespace contract\controllers;

use Carbon\Carbon;
use contract\models\ContractBind;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ContractBindController implements the CRUD actions for ContractBind model.
 */
class ContractBindController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'void' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Creates a new ContractBind model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $configId
     * @param int $type
     *
     * @return string
     */
    public function actionCreate(int $configId, int $type): string
    {
        $model = new ContractBind();
        $model->contract_bind_config_id = $configId;
        $model->contract_bind_type = $type;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ContractBind model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id 主键
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ContractBind model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id 主键
     * @param int $configId
     * @param int $type
     *
     * @return Response
     */
    public function actionVoid(int $id, int $configId, int $type): Response
    {
        $now = Carbon::now()->toDateTimeString();

        ContractBind::updateAll([
            'contract_bind_valid_end_at' => $now,
        ], [
            'and',
            ['=', 'contract_bind_id', $id],
            ['=', 'contract_bind_config_id', $configId],
            ['=', 'contract_bind_type', $type],
            ['>', 'contract_bind_valid_end_at', $now],
        ]);

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * Finds the ContractBind model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id 主键
     *
     * @return ContractBind the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ContractBind
    {
        if (($model = ContractBind::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
