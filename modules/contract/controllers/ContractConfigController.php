<?php

namespace contract\controllers;

use contract\components\ContractComponent;
use contract\models\ContractConfig;
use contract\models\ContractConfigSearch;
use Yii;
use yii\base\InvalidConfigException;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ContractConfigController implements the CRUD actions for ContractConfig model.
 */
class ContractConfigController extends Controller
{
    /**
     * @param string $key
     *
     * @return Response|string
     * @throws InvalidConfigException
     */
    public function actionPreviewTemplate(string $key)
    {
        $client = ContractComponent::instance();

        if (!$client->queryTemplateInfo($key)) {
            return 'ERROR: ' . $client->getError();
        }

        $data = $client->getData();

        return $this->redirect($data['viewUrl'] ?? ['index']);
    }

    /**
     * @return array
     * @throws InvalidConfigException
     */
    public function actionTemplateQuery(): array
    {
        $this->response->format = Response::FORMAT_JSON;

        $key = $this->request->post('key');
        if (!$key) {
            return [
                'code' => 1,
                'message' => '参数错误',
                'data' => null,
            ];
        }

        $client = ContractComponent::instance();

        $client->queryTemplateInfo($key);

        return $client->getResponse();
    }

    /**
     * Lists all ContractConfig models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new ContractConfigSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ContractConfig model.
     *
     * @param int $id
     *
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ContractConfig model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return Response|string
     */
    public function actionCreate()
    {
        $model = new ContractConfig();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->contract_config_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ContractConfig model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id
     *
     * @return Response|string
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->contract_config_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Finds the ContractConfig model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return ContractConfig the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ContractConfig
    {
        if (($model = ContractConfig::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
