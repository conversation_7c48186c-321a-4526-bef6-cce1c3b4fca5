<?php

namespace contract\models;

use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "contract_config".
 *
 * @property int $contract_config_id id
 * @property int $contract_config_type Contract type code
 * @property string $contract_config_type_text Contract type description
 * @property string|null $contract_config_operate_type operate type: silent_sign or interactive_sign
 * @property string $contract_config_tmp_key Contract system template key
 * @property string|null $contract_config_tmp_type template type: silent, interactive
 * @property int $contract_config_tmp_version Contract system template version number
 * @property string $contract_config_tmp_desc Contracting system template description
 * @property string|null $contract_config_params Contract parameters
 * @property string $contract_config_storage Contract storage location (storage rules)
 * @property string $contract_config_create_at
 * @property string $contract_config_update_at
 * @property int $contract_config_version Contract configuration version
 */
class ContractConfig extends \yii\db\ActiveRecord
{

    public const OPERATE_TYPE = [
        'silent_sign' => '静默签',
        'interactive_sign' => '交互签'
    ];

    public const  TMP_TYPE = [
        'silent' => '静默签模板',
        'interactive' => '交互签模板'
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'contract_config';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('contractDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['contract_config_version'], 'default', 'value' => 0],
            [
                [
                    'contract_config_type',
                    'contract_config_type_text',
                    'contract_config_tmp_version',
                    'contract_config_storage',
                    'contract_config_version',
                    'contract_config_tmp_type',
                    'contract_config_operate_type'
                ],
                'required',
            ],
            [
                ['contract_config_id'],
                'filter',
                'filter' => function () {
                    return $this->contract_config_type;
                },
            ],
            [
                [
                    'contract_config_id',
                    'contract_config_type',
                    'contract_config_tmp_version',
                    'contract_config_version',
                ],
                'integer',
            ],
            [['contract_config_create_at', 'contract_config_update_at'], 'safe'],
            [
                [
                    'contract_config_type_text',
                    'contract_config_tmp_key',
                    'contract_config_tmp_desc',
                ],
                'string',
                'max' => 64,
            ],
            [['contract_config_storage'], 'string', 'max' => 256],
            [['contract_config_params'], 'string'],
            [['contract_config_type'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'contract_config_id' => '主键',
            'contract_config_type' => '合同类型',
            'contract_config_type_text' => '合同名称',
            'contract_config_tmp_key' => '合同模板编码',
            'contract_config_tmp_version' => '合同模板版本号',
            'contract_config_tmp_desc' => '合同模板描述',
            'contract_config_params' => '合同参数',
            'contract_config_storage' => '合同存储位置',
            'contract_config_create_at' => '创建时间',
            'contract_config_update_at' => '更新时间',
            'contract_config_version' => '合同配置版本',
            'contract_config_operate_type' => '合同签约类型',
            'contract_config_tmp_type' => '合同模板类型',
        ];
    }

    public function transactions(): array
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    public function getBinds(): ActiveQuery
    {
        return $this->hasMany(ContractBind::class, ['contract_bind_config_id' => 'contract_config_id']);
    }
}
