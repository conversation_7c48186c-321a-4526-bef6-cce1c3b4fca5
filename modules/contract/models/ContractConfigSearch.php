<?php

namespace contract\models;

use yii\data\ActiveDataProvider;

class ContractConfigSearch extends ContractConfig
{
    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['contract_config_type'], 'integer'],
            [['contract_config_type_text', 'contract_config_tmp_key'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = ContractConfig::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'contract_config_create_at' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'contract_config_type' => $this->contract_config_type,
            'contract_config_tmp_key' => $this->contract_config_tmp_key,
        ])->andFilterWhere(['like', 'contract_config_type_text', $this->contract_config_type_text]);

        return $dataProvider;
    }
}
