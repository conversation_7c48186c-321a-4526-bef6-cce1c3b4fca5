<?php

namespace contract\models;

use kvmanager\models\KeyValue;
use Yii;

/**
 * This is the model class for table "contract_bind".
 *
 * @property int $contract_bind_id id
 * @property int $contract_bind_config_id Contract configuration ID
 * @property int $contract_bind_type Contract type code
 * @property string $contract_bind_rule Contract binding conditions (SpEl)
 * @property string $contract_bind_sign_type Signing method (OUR_SIGN-Only our side needs to sign, BOTH_SIGN-Both our and the capital need to sign, CAPITAL_DOWN-The capital downloads, IMPORT-Other systems Import, NO_SIGN-no signature)
 * @property string $contract_bind_sign_opportunity Signing timing (AssetImport-when the asset is generated, AssetWithdrawSuccess-when the loan to the card is successful, AssetReorganized-when the extension is extended, BeforeImport-sign before importing the goods, PayoffAsset- When settling, Diversion-when diversion, BeforeRegister-before opening an account, BeforeApply-before the apply, BindSuccess-the card is successfully bound, FoxContractSign-the post-loan legal reminder agreement is signed
 * @property string $contract_bind_create_at
 * @property string $contract_bind_update_at
 * @property string $contract_bind_valid_start_at Configuration validity start time
 * @property string $contract_bind_valid_end_at Configuration validity end time
 * @property int $contract_bind_config_version Contract configuration version
 */
class ContractBind extends \yii\db\ActiveRecord
{

    public static function oppList()
    {
        return KeyValue::take('contract_admin_config', 'contract')['signOpportuniy'] ?? [];
    }

    public static function signTypeList()
    {
        return KeyValue::take('contract_admin_config', 'contract')['signType'] ?? [];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'contract_bind';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('contractDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['contract_bind_config_id', 'contract_bind_type', 'contract_bind_config_version'], 'required'],
            [['contract_bind_config_id', 'contract_bind_type', 'contract_bind_config_version'], 'integer'],
            [['contract_bind_create_at', 'contract_bind_update_at', 'contract_bind_valid_start_at', 'contract_bind_valid_end_at'], 'safe'],
            [['contract_bind_rule'], 'string', 'max' => 1024],
            [['contract_bind_sign_type', 'contract_bind_sign_opportunity'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'contract_bind_id' => 'id',
            'contract_bind_config_id' => '合同配置ID',
            'contract_bind_type' => '合同类型编码',
            'contract_bind_rule' => '绑定规则',
            'contract_bind_sign_type' => '签章方式',
            'contract_bind_sign_opportunity' => '签约时机',
            'contract_bind_create_at' => '创建时间',
            'contract_bind_update_at' => '修改时间',
            'contract_bind_valid_start_at' => '生效时间',
            'contract_bind_valid_end_at' => '失效时间',
            'contract_bind_config_version' => '版本',
        ];
    }
}
