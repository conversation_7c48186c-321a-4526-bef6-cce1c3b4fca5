<?php

namespace payment\traits;

use Carbon\Carbon;
use payment\services\ChartDataProcessor;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

trait
RPCSChartTrait
{
    /**
     * 计算指标
     *
     * @param float $numerator 分子
     * @param float $denominator 分母
     * @param float $multiplier 乘数
     * @param int $precision 精度
     * @return float 计算结果
     */
    public function calculateMetric(float $numerator, float $denominator, float $multiplier = 1, int $precision = 2): float
    {
        return $denominator == 0 ? 0 : round(($numerator / $denominator) * $multiplier, $precision);
    }

    /**
     * 准备日期数据
     *
     * @param array $dailyData 每日数据
     * @return array 排序后的日期数组
     */
    protected function prepareDates(array $dailyData): array
    {
        $dates = array_unique(array_column($dailyData, 'date'));
        sort($dates);
        return $dates;
    }

    /**
     * 计算成功率
     *
     * @param int $total 总数
     * @param int $success 成功数
     * @return float 成功率
     */
    protected function calculateSuccessRate(int $total, int $success): float
    {
        return $total > 0 ? round(($success / $total) * 100, 2) : 0;
    }

    /**
     * 计算指标变化
     *
     * @param float $current 当前值
     * @param float $previous 前一个值
     * @return float|null 变化值
     */
    protected function calculateMetricChange(float $current, float $previous): ?float
    {
        if ($previous == 0) {
            return null;
        }

        return round(($current - $previous) / $previous * 100, 2);
    }

    /**
     * 获取按小时分组的数据
     *
     * @param array $types 需要查询的指标类型
     * @param array $selects 需要查询列
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @param array $groupBy 分组字段
     * @return array 查询结果
     */
    protected function fetchHourlyDataCommon(array $selects, array $types, Carbon $startDate, Carbon $endDate, array $groupBy = ['date_only', 'hour']): array
    {
        $selects = array_merge($selects, [
            'date_only' => new Expression('DATE(date)'),
            'hour' => new Expression('DATE_FORMAT(`date`, \'%H:%i\')'),
        ]);

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }

        $query = self::find()
            ->where(['type' => $types])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()]);

        $clone = clone $query;

        $data = $query->select($selects)->groupBy($groupBy)->asArray()->all();
        $maxDate = $clone->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();

        return [$data, $maxDate];
    }

    /**
     * @param ActiveQuery $query
     * @return false|int|string|null
     */
    public function queryMaxDate(ActiveQuery $query)
    {
        return $query->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();
    }

    /**
     * 根据日期范围获取数据
     *
     * @param array $types 需要查询的指标类型
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 查询结果
     */
    protected function fetchDataByDateRange(array $types, Carbon $startDate, Carbon $endDate): array
    {
        [$data, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        $rows = [];

        foreach ($types as $type) {
            $rows[$type] = array_sum(ArrayHelper::getColumn($data, $type));
        }

        $this->calculateDerivedMetrics($rows);

        return [$rows, $maxDate];
    }

    /**
     * 创建图表数据处理器
     *
     * @param string $type 图表类型
     * @param array $config 配置
     * @return ChartDataProcessor 图表数据处理器
     */
    protected function createChartProcessor(string $type, array $config = []): ChartDataProcessor
    {
        return new ChartDataProcessor($type, $config);
    }
}