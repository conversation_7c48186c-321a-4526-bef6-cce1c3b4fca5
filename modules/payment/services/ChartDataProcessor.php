<?php

namespace payment\services;

use Yii;

/**
 * 图表数据处理器
 *
 */
class ChartDataProcessor
{
    /**
     * 图表类型
     * @var string
     */
    protected $type;

    /**
     * 配置
     * @var array
     */
    protected $config;

    /**
     * 构造函数
     *
     * @param string $type 图表类型
     * @param array $config 配置
     */
    public function __construct(string $type, array $config = [])
    {
        $this->type = $type;
        $this->config = $config;
    }

    /**
     * 处理成功率图表数据
     *
     * @param array $dailyData 每日数据
     * @param array $dates 日期数组
     * @param string $totalField 总数字段
     * @param string $successField 成功数字段
     * @param string|null $groupField 分组字段
     * @return array 图表数据
     */
    public function processSuccessRateChart(array $dailyData, array $dates, string $totalField, string $successField, string $groupField = null): array
    {
        if ($groupField) {
            return $this->processGroupedSuccessRateChart($dailyData, $dates, $totalField, $successField, $groupField);
        }

        $totals = [];
        $successRates = [];

        foreach ($dailyData as $data) {
            $totals[] = (int)($data[$totalField] ?? 0);

            $total = (int)($data[$totalField] ?? 0);
            $success = (int)($data[$successField] ?? 0);

            $successRate = $total > 0 ? round(($success / $total) * 100, 2) : 0;
            $successRates[] = $successRate;
        }

        return [
            'dates' => $dates,
            'counts' => $totals,
            'rates' => $successRates
        ];
    }

    /**
     * 处理分组成功率图表数据
     *
     * @param array $dailyData 每日数据
     * @param array $dates 日期数组
     * @param string $totalField 总数字段
     * @param string $successField 成功数字段
     * @param string $groupField 分组字段
     * @return array 图表数据
     */
    protected function processGroupedSuccessRateChart(array $dailyData, array $dates, string $totalField, string $successField, string $groupField): array
    {
        $successRates = [];
        $groups = array_unique(array_column($dailyData, $groupField));

        // 初始化每个分组在每个日期的数据结构
        foreach ($groups as $group) {
            foreach ($dates as $date) {
                $successRates[$group][$date] = 0;
            }
        }

        // 按分组和日期聚合数据
        $aggregatedData = [];
        foreach ($dailyData as $data) {
            $group = $data[$groupField];
            $date = $data['date'];

            if (!isset($aggregatedData[$group][$date])) {
                $aggregatedData[$group][$date] = [
                    $totalField => 0,
                    $successField => 0
                ];
            }

            $aggregatedData[$group][$date][$totalField] += (int)($data[$totalField] ?? 0);
            $aggregatedData[$group][$date][$successField] += (int)($data[$successField] ?? 0);
        }

        // 计算每个分组每天的成功率
        foreach ($aggregatedData as $group => $dateData) {
            foreach ($dateData as $date => $metrics) {
                $total = $metrics[$totalField];
                $success = $metrics[$successField];
                $successRate = $total > 0 ? round(($success / $total) * 100, 2) : 0;
                $successRates[$group][$date] = $successRate;
            }
        }

        return [
            'dates' => $dates,
            'rates' => array_map(function ($v) {
                return array_values($v);
            }, $successRates)
        ];
    }

    /**
     * 处理平均值图表数据
     *
     * @param array $dailyData 每日数据
     * @param array $dates 日期数组
     * @param string $numeratorField 分子字段
     * @param string $denominatorField 分母字段
     * @return array 图表数据
     */
    public function processAverageChart(array $dailyData, array $dates, string $numeratorField, string $denominatorField): array
    {
        $avgValues = [];

        foreach ($dailyData as $data) {
            $numerator = (int)($data[$numeratorField] ?? 0);
            $denominator = (int)($data[$denominatorField] ?? 0);

            $avgValue = $denominator > 0 ? round($numerator / $denominator, 2) : 0;
            $avgValues[] = $avgValue;
        }

        return [
            'dates' => $dates,
            'avgValues' => $avgValues
        ];
    }

    /**
     * 处理千元成本图表数据
     *
     * @param array $dailyData 每日数据
     * @param array $dates 日期数组
     * @param string $feeField 费用字段
     * @param string $amountField 金额字段
     * @return array 图表数据
     */
    public function processThousandCostChart(array $dailyData, array $dates, string $feeField, string $amountField): array
    {
        $feeAmounts = [];
        $thousandCosts = [];

        foreach ($dailyData as $data) {
            // 总计手续费金额(K)
            $feeAmounts[] = convertFenToKiloYuan($data[$feeField] ?? 0, false);

            // 千元成本
            $fee = (float)($data[$feeField] ?? 0);
            $amount = (float)($data[$amountField] ?? 0);

            $thousandCost = $amount > 0 ? round(($fee / $amount) * 1000, 4) : 0;
            $thousandCosts[] = $thousandCost;
        }
        return [
            'dates' => $dates,
            'feeAmounts' => $feeAmounts,
            'thousandCosts' => $thousandCosts
        ];
    }

    /**
     * 处理小时图表数据
     *
     * @param array $hourlyData 小时数据
     * @param array $dates 日期数组
     * @param array $hours 小时数组
     * @param string $countField 计数字段
     * @param string $amountField 金额字段
     * @param string|null $groupField 分组字段
     * @return array 图表数据
     */
    public function processHourlyChart(array $hourlyData, array $dates, array $hours, string $countField, string $amountField, string $groupField = null): array
    {
        if ($groupField) {
            return $this->processGroupedHourlyChart($hourlyData, $dates, $hours, $countField, $amountField, $groupField);
        }

        $successData = array_fill_keys($dates, array_fill(0, count($hours), 0));
        $amountData = array_fill_keys($dates, array_fill(0, count($hours), 0));

        foreach ($hourlyData as $data) {
            $hourIndex = array_search($data['hour'], $hours);
            if ($hourIndex === false) continue;

            $date = $data['date_only'];
            $successData[$date][$hourIndex] = (int)($data[$countField] ?? 0);
            $amountData[$date][$hourIndex] = number_format((float)($data[$amountField] ?? 0) / (100 * 1000), 4);
        }

        return [
            'dates' => $dates,
            'hours' => $hours,
            'successData' => $successData,
            'amountData' => $amountData
        ];
    }

    /**
     * 处理分组小时图表数据
     *
     * @param array $hourlyData 小时数据
     * @param array $dates 日期数组
     * @param array $hours 小时数组
     * @param string $countField 计数字段
     * @param string $amountField 金额字段
     * @param string $groupField 分组字段
     * @return array 图表数据
     */
    public function processGroupedHourlyChart(array $hourlyData, array $dates, array $hours, string $countField, string $amountField, string $groupField): array
    {
        $groups = array_unique(array_column($hourlyData, $groupField));
        sort($groups);

        $groupData = [];
        foreach ($groups as $group) {
            $groupData[$group] = [
                'successData' => array_fill_keys($dates, array_fill(0, count($hours), 0)),
                'amountData' => array_fill_keys($dates, array_fill(0, count($hours), 0))
            ];
        }

        foreach ($hourlyData as $data) {
            $hourIndex = array_search($data['hour'], $hours);
            if ($hourIndex === false) continue;

            $date = $data['date_only'];
            $group = $data[$groupField];

            if (isset($groupData[$group])) {
                $groupData[$group]['successData'][$date][$hourIndex] = (int)($data[$countField] ?? 0);
                $groupData[$group]['amountData'][$date][$hourIndex] = (float)(($data[$amountField] ?? 0) / (100 * 1000));
            }
        }

        return [
            'dates' => $dates,
            'hours' => $hours,
            'groups' => $groups,
            'groupData' => $groupData
        ];
    }
}