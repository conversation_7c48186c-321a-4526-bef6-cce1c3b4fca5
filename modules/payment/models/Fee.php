<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "fee".
 *
 * @property int $fee_id
 * @property string $fee_type 费用类型：withhold-代扣；withdraw-代付；verify-四要素鉴权；register-开户;refund-退款
 * @property string $fee_ref_key 代扣对应withhold_receipt_channel_key,代付对应withdraw_receipt_channel_key,绑卡鉴权对应binding_request_merchant_key,开户对应channel_account_request_merchant_key
 * @property int $fee_order_amount 原始订单金额(分)
 * @property int $fee_amount 总成本(毫)=fee_service+fee_tax
 * @property int $fee_advance_amount 垫资费用(毫)
 * @property string $fee_provider_code 渠道
 * @property string $fee_provider_product_type 渠道产品
 * @property string $fee_channel_name 通道
 * @property string $fee_sign_company_code 签约主体
 * @property string $fee_merchant_name 业务方
 * @property int $fee_status 费用支付状态：0:未支付,1:已支付
 * @property string $fee_finish_at 交易完成时间
 * @property string $fee_create_at 创建时间
 * @property string $fee_update_at 更新时间
 * @property int $fee_service 手续费(毫)
 * @property int $fee_tax 税费(毫)
 */
class Fee extends \yii\db\ActiveRecord
{

    public const TYPE_LIST = [
        'withhold' => '代扣',
        'withdraw' => '代付',
        'refund' => '退款'
    ];

    public const STATUS_LIST = [
        '未支付',
        '已支付'
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'fee';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['fee_type'], 'string'],
            [['fee_order_amount', 'fee_amount', 'fee_advance_amount', 'fee_status', 'fee_service', 'fee_tax'], 'integer'],
            [['fee_finish_at', 'fee_create_at', 'fee_update_at'], 'safe'],
            [['fee_ref_key', 'fee_provider_code', 'fee_provider_product_type', 'fee_channel_name', 'fee_sign_company_code', 'fee_merchant_name'], 'string', 'max' => 64],
            [['fee_ref_key', 'fee_type'], 'unique', 'targetAttribute' => ['fee_ref_key', 'fee_type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'fee_id' => 'Fee ID',
            'fee_type' => '费用类型：withhold-代扣；withdraw-代付；verify-四要素鉴权；register-开户;refund-退款',
            'fee_ref_key' => '支付通道订单号',
            'fee_order_amount' => '原始订单金额(分)',
            'fee_amount' => '总成本(毫)=fee_service+fee_tax',
            'fee_advance_amount' => '垫资费用(毫)',
            'fee_provider_code' => '渠道',
            'fee_provider_product_type' => '渠道产品',
            'fee_channel_name' => '通道',
            'fee_sign_company_code' => '签约主体',
            'fee_merchant_name' => '业务方',
            'fee_status' => '费用支付状态：0:未支付,1:已支付',
            'fee_finish_at' => '交易完成时间',
            'fee_create_at' => '创建时间',
            'fee_update_at' => '更新时间',
            'fee_service' => '手续费(毫)',
            'fee_tax' => '税费(毫)',
        ];
    }


    public static function getSignCompany()
    {
        static $company;
        if (!$company) {
            $company = SignCompany::find()->select('sign_company_code')->indexBy('sign_company_code')->column();
        }
        return $company;
    }

    public static function getChannel()
    {
        static $channel;
        if (!$channel) {
            $channel = Channel::find()->select('channel_name')->indexBy('channel_name')->column();
        }
        return $channel;
    }
}
