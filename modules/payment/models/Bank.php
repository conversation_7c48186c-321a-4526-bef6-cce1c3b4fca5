<?php

namespace payment\models;

use Carbon\Carbon;
use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "{{%bank}}".
 *
 * @property int         $bank_id
 * @property string      $bank_code       银行缩写
 * @property string      $bank_name       银行名称
 * @property string|null $bank_serial     行号
 * @property string      $bank_created_at 创建时间
 * @property string      $bank_updated_at 更新时间
 * @property string      $bank_local_name 银行本地名称
 * @property string      $bank_short_name 银行简称
 * @property int         $bank_sort       排序
 */
class Bank extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%bank}}';
    }

    public function behaviors()
    {
        return [
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => 'bank_created_at',
                'updatedAtAttribute' => null,
                'value'              => function () {
                    return Carbon::now()->toDateTimeString();
                },
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['bank_sort'], 'default', 'value' => 0],
            [['bank_code', 'bank_name'], 'required'],
            [['bank_code'], 'string', 'max' => 32],
            [['bank_name', 'bank_local_name'], 'string', 'max' => 128],
            [['bank_serial'], 'string', 'max' => 20],
            [['bank_short_name'], 'string', 'max' => 64],
            [['bank_sort'], 'integer'],
            [['bank_code'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'bank_id'         => Yii::t('payment', 'Bank ID'),
            'bank_code'       => Yii::t('payment', '银行缩写'),
            'bank_name'       => Yii::t('payment', '银行名称'),
            'bank_serial'     => Yii::t('payment', '行号'),
            'bank_created_at' => Yii::t('payment', '创建时间'),
            'bank_updated_at' => Yii::t('payment', '更新时间'),
            'bank_sort'       => Yii::t('payment', '排序'),
            'bank_local_name' => Yii::t('payment', '银行本地名称'),
            'bank_short_name' => Yii::t('payment', '银行简称'),
        ];
    }
}
