<?php

namespace payment\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "channel_transaction_order".
 *
 * @property int $id
 * @property string $provider_code 渠道编码
 * @property string $contract_entity 签约主体
 * @property string $bill_date 订单账单日期
 * @property string $channel_key 商户订单号
 * @property string $channel_inner_key 通道平台订单号
 * @property string|null $request_at 通道订单创建时间
 * @property string|null $finished_at 通道订单完成时间
 * @property string $created_at 账单记录解析时间
 * @property string $updated_at 账单记录更新时间
 * @property string|null $channel_transaction_type 通道交易类型
 * @property int $amount 订单金额
 * @property string|null $currency 订单币种
 * @property int|null $fee_amount 费金额
 * @property int|null $tax_amount 税费
 * @property string $channel_status 通道订单状态
 * @property string|null $channel_status_mapping 通道订单状态映射结果
 * @property string|null $payment_method 支付方式
 * @property int|null $account_balance 余额
 * @property string|null $file_name 通道账单文件名称
 * @property string|null $source 账单记录来源MANUAL、BILL
 * @property string|null $channel_name 通道名称
 * @property string $transaction_type 订单类型，代收代付
 * @property string $reconci_at 账单对账时间
 * @property string $reconci_status 账单状态: open,process,pending,success
 * @property string|null $reconci_result 对账结果
 * @property string|null $reconci_remark 备注
 * @property string|null $channel_ext_info 扩展字段
 */
class ChannelTransactionOrder extends ActiveRecord
{
    public const RECONCI_STATUS_OPEN = 'open';
    public const RECONCI_STATUS_PROCESS = 'process';
    public const RECONCI_STATUS_PENDING = 'pending';
    public const RECONCI_STATUS_SUCCESS = 'success';
    public const RECONCI_STATUS_COMPLETE = 'complete';

    public const RECONCI_STATUS_MAP = [
        self::RECONCI_STATUS_OPEN    => '待对账',
        self::RECONCI_STATUS_PROCESS => '处理中',
        self::RECONCI_STATUS_PENDING => '对账失败',
        self::RECONCI_STATUS_SUCCESS => '对账成功',
        self::RECONCI_STATUS_COMPLETE => '无需对账',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'channel_transaction_order';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['provider_code', 'contract_entity', 'bill_date', 'channel_key', 'channel_inner_key', 'amount', 'channel_status'], 'required'],
            [['bill_date', 'request_at', 'finished_at', 'created_at', 'updated_at', 'reconci_at'], 'safe'],
            [['amount', 'fee_amount', 'tax_amount', 'account_balance'], 'integer'],
            [['provider_code', 'contract_entity', 'channel_transaction_type', 'currency', 'channel_status', 'channel_status_mapping', 'payment_method', 'source'], 'string', 'max' => 32],
            [['channel_key', 'channel_inner_key', 'file_name', 'channel_name', 'transaction_type', 'reconci_result'], 'string', 'max' => 64],
            [['reconci_status'], 'string', 'max' => 16],
            [['reconci_remark'], 'string', 'max' => 255],
            [['channel_ext_info'], 'string', 'max' => 1024],
            [['channel_inner_key', 'provider_code', 'contract_entity', 'bill_date'], 'unique', 'targetAttribute' => ['channel_inner_key', 'provider_code', 'contract_entity', 'bill_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'provider_code' => '渠道编码',
            'contract_entity' => '签约主体',
            'bill_date' => '订单账单日期',
            'channel_key' => '商户订单号',
            'channel_inner_key' => '通道平台订单号',
            'request_at' => '通道订单创建时间',
            'finished_at' => '通道订单完成时间',
            'created_at' => '账单记录解析时间',
            'updated_at' => '账单记录更新时间',
            'channel_transaction_type' => '通道交易类型',
            'amount' => '订单金额',
            'currency' => '订单币种',
            'fee_amount' => '费金额',
            'tax_amount' => '税费',
            'channel_status' => '通道订单状态',
            'channel_status_mapping' => '通道订单状态映射结果',
            'payment_method' => '支付方式',
            'account_balance' => '余额',
            'file_name' => '通道账单文件名称',
            'source' => '账单记录来源',
            'channel_name' => '通道名称',
            'transaction_type' => '订单类型',
            'reconci_at' => '账单对账时间',
            'reconci_status' => '对账状态',
            'reconci_result' => '对账结果',
            'reconci_remark' => '备注',
            'channel_ext_info' => '扩展字段',
        ];
    }
}
