<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class RPCSAllRepaySearch extends RPCSSearch
{
    public const METRICS_CONFIG = [
        'base_metrics' => [
            'repay_counts_day',
            'repay_success_counts_day',
            'repay_numbers',
            'repay_success_numbers',
            'repay_amount',
            'repay_success_amount',
            'repay_fee_amount',
        ],
        'chart' => [
            'repayCounts' => [
                'repay_counts_day',
                'repay_success_counts_day'
            ],
            'repayNumbers' => [
                'repay_numbers',
                'repay_success_numbers'
            ],
            'avgRepayNumbers' => [
                'repay_numbers',
                'repay_success_counts'
            ],
            'thousandRepayCost' => [
                'repay_fee_amount',
                'repay_success_amount'
            ]
        ],
        'derived_metrics' => [
            'repay_success_counts_rate',
            'repay_success_numbers_rate',
            'avg_repay_numbers_per_repay',
            'repay_success_amount_ratio',
            'thousand_repay_cost',
        ]
    ];


    /**
     * 获取当前时间段和前一个时间段的对比数据
     *
     * @param array $params 查询参数
     * @return array 包含当前时间段和前一个时间段数据的数组
     */
    public function getComparisonData(array $params = []): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        };

        $types = self::METRICS_CONFIG['base_metrics'];

        $currentEndDate = Carbon::parse($this->endDate);
        $currentStartDate = Carbon::parse($this->startDate);
        $dayDiff = $currentEndDate->diffInDays($currentStartDate) + 1;

        $previousEndDate = $currentStartDate->copy()->subDay();
        $previousStartDate = $previousEndDate->copy()->subDays($dayDiff - 1);

        [$currentData, $maxDate] = $this->fetchDataByDateRange(
            $types,
            $currentStartDate->clone(),
            $currentEndDate->clone()
        );

        [$previousData] = $this->fetchDataByDateRange(
            $types,
            $previousStartDate->clone(),
            $previousEndDate->clone()
        );

        $comparison = $this->calculateComparison($currentData, $previousData, []);

        $result = [
            [
                'type' => '数值',
                'period' => $currentStartDate->toDateString() . ' ~ ' . $currentEndDate->clone()->toDateString(),
            ],
            [
                'type' => '同比',
                'period' => $previousStartDate->toDateString() . ' ~ ' . $previousEndDate->clone()->toDateString(),
            ]
        ];

        $allMetrics = array_merge(
            $types,
            self::METRICS_CONFIG['derived_metrics']
        );

        foreach ($allMetrics as $metric) {
            $result[0][$metric] = $currentData[$metric] ?? 0;
            $result[1][$metric] = $comparison[$metric] ?? null;
        }

        // 获取图表数据
        $chartData = $this->getChartData($currentStartDate->clone(), $currentEndDate->clone());

        return [
            'tables' => [
                'data' => $result,
                'maxDate' => $maxDate
            ],
            'chart' => $chartData
        ];
    }

    public function getDetail(array $params = []): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }
        $types = self::METRICS_CONFIG['base_metrics'];
        $selects = ['date1' => new Expression('DATE(date)')];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $data = self::find()
            ->where(['type' => $types])
            ->andWhere(['>=', 'date', Carbon::parse($this->startDate)->toDateString()])
            ->andWhere(['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()])
            ->select($selects)
            ->groupBy(['date1'])
            ->orderBy(['date1' => SORT_DESC])
            ->asArray()
            ->all();


        foreach ($data as &$item) {
            $this->calculateDerivedMetrics($item);
        }
        unset($item);
        return $data;
    }

    /**
     * 获取图表数据
     *
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 图表数据
     */
    protected function getChartData(Carbon $startDate, Carbon $endDate): array
    {
        // 获取所有需要的指标类型
        $types = array_unique(array_merge(
            self::METRICS_CONFIG['chart']['repayCounts'],
            self::METRICS_CONFIG['chart']['repayNumbers'],
            self::METRICS_CONFIG['chart']['avgRepayNumbers'],
            self::METRICS_CONFIG['chart']['thousandRepayCost']
        ));

        // 获取按日期分组的数据
        [$dailyData, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        if (empty($dailyData)) {
            return [
                'repayCounts' => [],
                'repayNumbers' => [],
                'avgRepayNumbers' => [],
                'thousandRepayCost' => []
            ];
        }

        usort($dailyData, function ($a, $b) {
            return strtotime($a['date']) - strtotime($b['date']);
        });

        $dates = $this->prepareDates($dailyData);

        // 使用图表处理器处理数据
        $repayCountsProcessor = $this->createChartProcessor('repayCounts');
        $repayCountsChart = $repayCountsProcessor->processSuccessRateChart(
            $dailyData,
            $dates,
            'repay_counts_day',
            'repay_success_counts_day'
        );

        $repayNumbersProcessor = $this->createChartProcessor('repayNumbers');
        $repayNumbersChart = $repayNumbersProcessor->processSuccessRateChart(
            $dailyData,
            $dates,
            'repay_numbers',
            'repay_success_numbers'
        );
        $avgRepayNumbersProcessor = $this->createChartProcessor('avgRepayNumbers');
        $avgRepayNumbersChart = $avgRepayNumbersProcessor->processAverageChart(
            $dailyData,
            $dates,
            'repay_numbers',
            'repay_counts_day'
        );

        $thousandRepayCostProcessor = $this->createChartProcessor('thousandRepayCost');
        $thousandRepayCostChart = $thousandRepayCostProcessor->processThousandCostChart(
            $dailyData,
            $dates,
            'repay_fee_amount',
            'repay_success_amount'
        );

        return [
            'repayCounts' => $repayCountsChart,
            'repayNumbers' => $repayNumbersChart,
            'avgRepayNumbers' => $avgRepayNumbersChart,
            'thousandRepayCost' => $thousandRepayCostChart,
            'maxDate' => $maxDate
        ];
    }


    /**
     * 根据日期范围获取数据
     *
     * @param array $types 需要查询的指标类型
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 查询结果
     */
    protected function fetchDataByDateRange(array $types, Carbon $startDate, Carbon $endDate): array
    {
        [$data, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        $rows = [];

        foreach ($types as $type) {
            $rows[$type] = array_sum(ArrayHelper::getColumn($data, $type));
        }

        $this->calculateDerivedMetrics($rows);

        return [$rows, $maxDate];
    }

    /**
     * 计算衍生指标
     *
     * @param array &$data 数据行
     */
    protected function calculateDerivedMetrics(array &$data): void
    {
        $data['repay_success_numbers_rate'] = $this->calculateMetric($data['repay_success_numbers'] ?? 0, $data['repay_numbers'] ?? 0, 100);
        $data['avg_repay_numbers_per_repay'] = $this->calculateMetric($data['repay_numbers'] ?? 0, $data['repay_counts_day'] ?? 0);
        $data['repay_success_amount_ratio'] = $this->calculateMetric($data['repay_success_amount'] ?? 0, $data['repay_amount'] ?? 0, 100);
        $data['thousand_repay_cost'] = $this->calculateMetric($data['repay_fee_amount'] ?? 0, $data['repay_success_amount'] ?? 0, 1000, 4);
        $data['repay_success_counts_rate'] = $this->calculateMetric($data['repay_success_counts_day'] ?? 0, $data['repay_counts_day'] ?? 0, 100);
    }

    /**
     * 计算环比变化
     *
     * @param array $currentData
     * @param array $previousData
     * @param array $metrics
     * @return array 环比变化数据
     */
    protected function calculateComparison(array $currentData, array $previousData, array $metrics): array
    {

        $comparison = [];

        $allMetrics = array_merge(
            self::METRICS_CONFIG['base_metrics'],
            self::METRICS_CONFIG['derived_metrics']
        );

        foreach ($allMetrics as $metric) {
            $comparison[$metric] = $this->calculateMetricChange(
                $currentData[$metric] ?? 0,
                $previousData[$metric] ?? 0,
            );
        }

        return $comparison;
    }

    /**
     * 计算指标变化
     *
     * @param float $current 当前值
     * @param float $previous 前一个值
     * @return float|null 变化值
     */
    protected function calculateMetricChange(float $current, float $previous): ?float
    {
        if ($previous == 0) {
            return null;
        }

        return round(($current - $previous) / $previous * 100, 2);
    }
}
