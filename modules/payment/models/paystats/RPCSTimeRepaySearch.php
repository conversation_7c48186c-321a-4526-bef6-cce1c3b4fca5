<?php

namespace payment\models\paystats;

use Carbon\Carbon;

class RPCSTimeRepaySearch extends RPCSSearch
{

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => fn() => Carbon::now()->subDay()->toDateString()],
            [['endDate'], 'default', 'value' => fn() => Carbon::today()->toDateString()],
            [['startDate', 'endDate'], 'filter', 'filter' => fn($date) => Carbon::parse($date)->toDateString()],
        ];
    }

    public const METRIC_CONFIG = [
        'chart' => [
            'repayData' => [
                'repay_success_counts'
            ],
            'repayAmountData' => [
                'repay_success_amount'
            ]
        ]
    ];

    public function getChartData(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        };

        $currentEndDate = Carbon::now();
        $currentStartDate = Carbon::parse($this->startDate);

        $types = array_unique(array_merge(
            self::METRIC_CONFIG['chart']['repayData'],
            self::METRIC_CONFIG['chart']['repayAmountData'],
        ));

        // 获取按小时分组的数据
        [$hourlyData, $maxDate] = $this->fetchHourlyData([], $types, $currentStartDate, $currentEndDate);

        if (empty($hourlyData)) {
            return [
                'hourlyLoanData' => [
                    'dates' => [],
                    'hours' => [],
                    'successData' => [],
                    'amountData' => []
                ],
                'maxDate' => null
            ];
        }

        $dates = array_unique(array_column($hourlyData, 'date_only'));
        sort($dates);

        $hours = array_map(fn($i) => date('H:i', strtotime("00:00 +$i minutes")), range(0, 1410, 30));

        $successData = array_fill_keys($dates, array_fill(0, count($hours), 0));
        $amountData = array_fill_keys($dates, array_fill(0, count($hours), 0));

        foreach ($hourlyData as $data) {
            $hourIndex = array_search($data['hour'], $hours);
            if ($hourIndex === false) continue;

            $date = $data['date_only'];
            $successData[$date][$hourIndex] = (int)($data['repay_success_counts'] ?? 0);
            $amountData[$date][$hourIndex] = (float)(($data['repay_success_amount'] ?? 0) / (100 * 1000));
        }

        return [
            'hourlyLoanData' => [
                'dates' => $dates,
                'hours' => $hours,
                'successData' => $successData,
                'amountData' => $amountData
            ],
            'maxDate' => $maxDate
        ];
    }

    /**
     * 获取按小时分组的数据
     *
     * @param array $types 需要查询的指标类型
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 查询结果
     */
    protected function fetchHourlyData(array $selects, array $types, Carbon $startDate, Carbon $endDate): array
    {
        // 使用特性中的通用方法
        return $this->fetchHourlyDataCommon($selects, $types, $startDate, $endDate, ['date_only', 'hour']);
    }
}