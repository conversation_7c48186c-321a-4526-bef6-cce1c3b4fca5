<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use payment\models\ReportPaymentChannelStats;
use payment\traits\RPCSChartTrait;
use RuntimeException;
use yii\db\Expression;

class SummarizeByMonthSearch extends ReportPaymentChannelStats
{
    use RPCSChartTrait;

    public $startDate;
    public $endDate;

    // 放款相关指标
    public const LOAN_TYPE = [
        'import_success_counts',
        'import_counts',
        'pay_success_amount',
        'pay_fee_amount'
    ];

    // 还款相关指标
    public const REPAY_TYPE = [
        'repay_success_counts_day',
        'repay_counts_day',
        'repay_success_amount',
        'repay_fee_amount'
    ];

    public const PERCENTAGE_MULTIPLIER = 100;
    public const THOUSAND_MULTIPLIER = 1000;
    public const COST_PRECISION = 4;

    public function attributeLabels(): array
    {
        return [
            'startDate' => '开始日期',
            'endDate' => '结束日期'
        ];
    }

    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::now()->subMonths()->format('Y-m')],
            [['endDate'], 'default', 'value' => Carbon::now()->format('Y-m')],
            [['startDate', 'endDate'], 'safe'],
        ];
    }


    public function search(array $params): array
    {
        $this->load($params);

        if (!$this->validate()) {
            return [];
        }

        $monthsDiff = Carbon::parse($this->startDate)->diffInMonths(Carbon::parse($this->endDate));
        if ($monthsDiff > 12) {
            throw new RuntimeException('时间维度不能超过12个月!');
        }

        $rawData = $this->fetchMonthlyData();

        if (empty($rawData)) {
            return [];
        }

        return $this->processSearchResults($rawData);
    }


    protected function fetchMonthlyData(): array
    {
        $selects = ['month' => new Expression("DATE_FORMAT(`date`, '%Y-%m')")];
        $types = array_merge(self::LOAN_TYPE, self::REPAY_TYPE);

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }

        $query = self::find()
            ->select($selects)
            ->where(['type' => $types])
            ->andWhere(['>=', 'date', Carbon::parse($this->startDate)->toDateString()])
            ->andWhere(['<', 'date', Carbon::parse($this->endDate)->endOfMonth()->addDay()->toDateString()])
            ->groupBy(['month'])
            ->orderBy(['month' => SORT_ASC]);

        return $query->asArray()->all();
    }

    protected function processSearchResults(array $rawData): array
    {
        $months = array_column($rawData, 'month');
        $chartData = $this->buildChartData($rawData, $months);
        $monthlyData = $this->buildMonthlyData($rawData);

        return [
            'loanMonth' => $monthlyData['loan'],
            'repayMonth' => $monthlyData['repay'],
            'leftChart' => $chartData['left'],
            'rightChart' => $chartData['right']
        ];
    }

    protected function buildChartData(array $rawData, array $months): array
    {
        $leftChart = $this->initializeLeftChart($rawData, $months);
        $rightChart = $this->initializeRightChart($rawData, $months);

        foreach ($rawData as $item) {
            $metrics = $this->calculateMonthlyMetrics($item);

            $leftChart['loan_success_rate'][] = $metrics['loan_success_rate'];
            $leftChart['repay_success_rate'][] = $metrics['repay_success_rate'];
            $rightChart['thousand_loan_cost'][] = $metrics['thousand_loan_cost'];
            $rightChart['thousand_repay_cost'][] = $metrics['thousand_repay_cost'];
        }

        return ['left' => $leftChart, 'right' => $rightChart];
    }

    protected function initializeLeftChart(array $rawData, array $months): array
    {
        return [
            'month' => $months,
            'loan_success_rate' => [],
            'repay_success_rate' => [],
            'import_success_counts' => array_column($rawData, 'import_success_counts'),
            'repay_success_counts_day' => array_column($rawData, 'repay_success_counts_day')
        ];
    }


    protected function initializeRightChart(array $rawData, array $months): array
    {
        return [
            'month' => $months,
            'thousand_repay_cost' => [],
            'thousand_loan_cost' => [],
            'pay_success_amount' => array_map(static function ($val) {
                return convertFenToKiloYuan($val, false);
            }, array_column($rawData, 'pay_success_amount'),),
            'repay_success_amount' => array_map(static function ($val) {
                return convertFenToKiloYuan($val, false);
            }, array_column($rawData, 'repay_success_amount'))
        ];
    }


    protected function buildMonthlyData(array $rawData): array
    {
        $loanMonth = [];
        $repayMonth = [];

        foreach ($rawData as $item) {
            $metrics = $this->calculateMonthlyMetrics($item);

            $loanData = $this->extractLoanData($item, $metrics);
            $repayData = $this->extractRepayData($item, $metrics);

            $loanMonth[] = $loanData;
            $repayMonth[] = $repayData;
        }

        return ['loan' => $loanMonth, 'repay' => $repayMonth];
    }

    private function calculateMonthlyMetrics(array $item): array
    {
        return [
            'thousand_loan_cost' => $this->calculateMetric(
                $item['pay_fee_amount'] ?? 0,
                $item['pay_success_amount'] ?? 0,
                self::THOUSAND_MULTIPLIER,
                self::COST_PRECISION
            ),
            'thousand_repay_cost' => $this->calculateMetric(
                $item['repay_fee_amount'] ?? 0,
                $item['repay_success_amount'] ?? 0,
                self::THOUSAND_MULTIPLIER,
                self::COST_PRECISION
            ),
            'loan_success_rate' => $this->calculateMetric(
                $item['import_success_counts'] ?? 0,
                $item['import_counts'] ?? 0,
                self::PERCENTAGE_MULTIPLIER
            ),
            'repay_success_rate' => $this->calculateMetric(
                $item['repay_success_counts_day'] ?? 0,
                $item['repay_counts_day'] ?? 0,
                self::PERCENTAGE_MULTIPLIER
            )
        ];
    }


    private function extractLoanData(array $item, array $metrics): array
    {
        $loanData = ['month' => $item['month']];

        foreach (self::LOAN_TYPE as $type) {
            $loanData[$type] = $item[$type] ?? 0;
        }

        $loanData['thousand_loan_cost'] = $metrics['thousand_loan_cost'];
        $loanData['loan_success_rate'] = $metrics['loan_success_rate'];

        return $loanData;
    }

    private function extractRepayData(array $item, array $metrics): array
    {
        $repayData = ['month' => $item['month']];

        foreach (self::REPAY_TYPE as $type) {
            $repayData[$type] = $item[$type] ?? 0;
        }

        $repayData['thousand_repay_cost'] = $metrics['thousand_repay_cost'];
        $repayData['repay_success_rate'] = $metrics['repay_success_rate'];

        return $repayData;
    }
}