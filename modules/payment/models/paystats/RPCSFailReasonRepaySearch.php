<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use payment\models\ChannelErrorReason;
use yii\db\ActiveQuery;
use yii\db\Expression;

class RPCSFailReasonRepaySearch extends RPCSSearch
{
    public const METRIC_CONFIG = [
        'repay_fail_amount',
        'repay_fail_numbers',
        'repay_fail_counts',
        'repay_amount',
        'repay_numbers'
    ];

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['channel_name', 'payment_method', 'payment_mode', 'sign_company_name'], 'safe'],
            [['startDate'], 'default', 'value' => fn() => Carbon::now()->subDays(7)->toDateString()],
            [['endDate'], 'default', 'value' => fn() => Carbon::yesterday()->toDateString()],
            [['startDate', 'endDate'], 'filter', 'filter' => fn($date) => Carbon::parse($date)->toDateString()],
        ];
    }

    /**
     * 获取失败原因分布数据
     *
     * @param array $params 查询参数
     * @return array 失败原因分布数据
     */
    public function getFailReasonData(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'failure_reason',
            'count' => new Expression('COUNT(*)'),
            'channel_name',
            'payment_option',
            'sign_company_name',
            'payment_mode',
            'payment_method'
        ];

        foreach (self::METRIC_CONFIG as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $query = self::find()
            ->where(['type' => self::METRIC_CONFIG])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()])
            ->andWhere(['<>', 'failure_reason', ''])
            ->groupBy(['failure_reason', 'sign_company_name', 'channel_name', 'payment_option', 'payment_mode', 'payment_method']);
        $clone = clone $query;
        $maxDate = $this->queryMaxDate(clone $query);
        $totals = (clone $query)->select([
            'repay_fail_amount' => $selects['repay_fail_amount'],
            'repay_fail_numbers' => $selects['repay_fail_numbers'],
            'repay_fail_counts' => $selects['repay_fail_counts']
        ])->groupBy([])->asArray()->one();
        $data = $query->select($selects)->limit(10)->orderBy(['repay_fail_numbers' => SORT_DESC])->asArray()->all();

        $totalFailAmount = $totals['repay_fail_amount'] ?? 0;
        $totalFailNumbers = $totals['repay_fail_numbers'] ?? 0;
        $totalFailCounts = $totals['repay_fail_counts'] ?? 0;
        $rows = $chart = [];

        foreach ($data as &$item) {
            $channelErrorReason = ChannelErrorReason::findOne($item['failure_reason']);
            $item['total_fail_amount'] = $totalFailAmount;
            $item['total_fail_numbers'] = $totalFailNumbers;
            $item['total_fail_counts'] = $totalFailCounts;
            $this->calculateDerivedMetrics($item);
            $rows[] = array_merge($item, [
                'code' => $channelErrorReason->channel_error_reason_code ?? '',
                'msg' => $channelErrorReason->channel_error_reason_msg ?? '',
                'failure_reason' => $channelErrorReason->channel_error_reason_reason ?? '',
            ]);
        }

        $fetchSelect = ['repay_fail_numbers' => new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', 'repay_fail_numbers'))];

        $chart['failChannel'] = $this->fetch($clone, ['channel_name'], array_merge($fetchSelect, ['channel_name']));
        $chart['failCode'] = $this->fetch($clone, ['failure_reason'], array_merge($fetchSelect, ['failure_reason']));

        foreach ($chart['failCode'] as $k => &$code) {
            if (empty($code['failure_reason'])) {
                unset($chart['failCode'][$k]);
                continue;
            }
            $code['failure_reason'] = ChannelErrorReason::findOne($code['failure_reason'])->channel_error_reason_reason;
        }

        $chart['failCode'] = array_values($chart['failCode']);

        $chart['payMethod'] = $this->fetch($clone, ['payment_method'], array_merge($fetchSelect, ['payment_method']));
        $chart['payMode'] = $this->fetch($clone, ['payment_mode'], array_merge($fetchSelect, ['payment_mode']));
        return [
            'table' => $rows,
            'chart' => $chart,
            'maxDate' => $maxDate
        ];
    }

    protected function calculateDerivedMetrics(array &$data): void
    {
        $data['repay_fail_counts_ratio'] = $this->calculateMetric($data['repay_fail_counts'] ?? 0, $data['total_fail_counts'] ?? 0, 100);
        $data['repay_fail_numbers_ratio'] = $this->calculateMetric($data['repay_fail_numbers'] ?? 0, $data['total_fail_numbers'] ?? 0, 100);
        $data['repay_fail_amount_ratio'] = $this->calculateMetric($data['repay_fail_amount'] ?? 0, $data['total_fail_amount'] ?? 0, 100);
    }

    /**
     * @param ActiveQuery $query
     * @param array $groupBy
     * @param array $selects
     * @return array
     */
    protected function fetch(ActiveQuery $query, array $groupBy, array $selects): array
    {
        return (clone $query)->groupBy($groupBy)->select($selects)->asArray()->all();
    }

    public function search(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'date1' => new Expression('DATE(date)'),
            'failure_reason',
            'count' => new Expression('COUNT(*)'),
            'channel_name',
            'payment_option',
            'sign_company_name',
            'payment_mode',
            'payment_method'
        ];

        foreach (self::METRIC_CONFIG as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $data = self::find()
            ->select($selects)
            ->where(['type' => self::METRIC_CONFIG])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()])
            ->andFilterWhere([
                'channel_name' => $this->channel_name,
                'payment_method' => $this->payment_method,
                'sign_company_name' => $this->sign_company_name,
                'payment_mode' => $this->payment_mode
            ])
            ->andWhere(['<>', 'failure_reason', ''])
            ->groupBy(['date1', 'failure_reason', 'sign_company_name', 'channel_name', 'payment_option', 'payment_mode', 'payment_method'])
            ->asArray()->all();

        $totalFailAmount = array_sum(array_column($data, 'repay_fail_amount'));
        $totalFailNumbers = array_sum(array_column($data, 'repay_fail_numbers'));
        $totalFailCounts = array_sum(array_column($data, 'repay_fail_counts'));

        $rows = [];
        foreach ($data as &$item) {
            $channelErrorReason = ChannelErrorReason::findOne($item['failure_reason']);
            $item['total_fail_amount'] = $totalFailAmount;
            $item['total_fail_numbers'] = $totalFailNumbers;
            $item['total_fail_counts'] = $totalFailCounts;
            $this->calculateDerivedMetrics($item);
            $rows[] = array_merge($item, [
                'code' => $channelErrorReason->channel_error_reason_code ?? '',
                'msg' => $channelErrorReason->channel_error_reason_msg ?? '',
                'failure_reason' => $channelErrorReason->channel_error_reason_reason ?? '',
            ]);
        }

        return $rows;
    }
}