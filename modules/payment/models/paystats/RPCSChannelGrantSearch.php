<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use yii\db\Expression;

class RPCSChannelGrantSearch extends RPCSSearch
{
    public const METRIC_CONFIG = [
        'pay_numbers',
        'pay_amount',
        'pay_success_amount',
        'pay_success_numbers',
        'pay_fee_amount',
        'import_amount',
        'import_success_amount',
    ];

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['period', 'channel_name', 'payment_method', 'sign_company_name'], 'safe'],
            [['startDate'], 'default', 'value' => fn() => Carbon::now()->subDays(7)->toDateString()],
            [['endDate'], 'default', 'value' => fn() => Carbon::yesterday()->toDateString()],
            [['startDate', 'endDate'], 'filter', 'filter' => fn($date) => Carbon::parse($date)->toDateString()],
        ];
    }

    /**
     *
     *
     * @param array $params 查询参数
     * @return array
     */
    public function getChannelData(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'channel_name',
            'sign_company_name',
        ];

        foreach (self::METRIC_CONFIG as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $query = self::find()
            ->where(['type' => self::METRIC_CONFIG])
            ->andWhere(['<>', 'channel_name', ''])
            ->andWhere(['<>', 'sign_company_name', ''])
            ->andWhere(['>=', 'date', $startDate->clone()->toDateString()])
            ->andWhere(['<', 'date', $endDate->clone()->addDay()->toDateString()]);


        $data = $query->select($selects)->groupBy(['channel_name', 'sign_company_name'])->asArray()->all();

        $channelSummary = array_reduce($data, static function($carry, $item) {
            $channelName = $item['channel_name'];
            $carry[$channelName] = ($carry[$channelName] ?? 0) + $item['pay_success_numbers'];
            return $carry;
        }, []);

        $totalPayNumbers = array_sum(array_column($data, 'pay_numbers'));

        $rows = [];

        foreach ($data as &$item) {
            $item['total_pay_numbers'] = $totalPayNumbers;
            $this->calculateDerivedMetrics($item);
            $rows[] = $item;
        }

        $chart = $this->getChartData($startDate->clone(), $endDate->clone());

        return [
            'table' => $rows,
            'chart' => $chart
        ];
    }

    protected function calculateDerivedMetrics(array &$data): void
    {
        $data['pay_numbers_ratio'] = $this->calculateMetric($data['pay_numbers'] ?? 0, $data['total_pay_numbers'] ?? 0, 100);
        $data['pay_success_rate'] = $this->calculateMetric($data['pay_success_numbers'] ?? 0, $data['pay_numbers'] ?? 0, 100);
        $data['pay_success_amount_ratio'] = $this->calculateMetric($data['pay_success_amount'] ?? 0, $data['pay_amount'] ?? 0, 100);
        $data['thousand_loan_cost'] = $this->calculateMetric($data['pay_fee_amount'] ?? 0, $data['pay_success_amount'] ?? 0, 1000, 4);
    }

    protected function getChartData(Carbon $startDate, Carbon $endDate): array
    {
        $types = ['import_success_counts', 'import_counts', 'pay_numbers', 'pay_fee_amount', 'pay_success_numbers', 'import_success_amount', 'pay_success_amount'];

        [$dailyData, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        if (empty($dailyData)) {
            return [
                'channelSuccessRate' => [],
                'channelPayCount' => [],
                'singleChannelPayCount' => [],
                'payCostTrend' => []
            ];
        }

        $dates = array_unique(array_column($dailyData, 'day'));

        sort($dates);

        $channelSuccessRate = $this->prepareChannelSuccessRate($dailyData, $dates);
        $channelPayCount = $this->prepareChannelPayCount($dailyData, $dates);
        $singleChannelPayCount = $this->prepareSingleChannelPayCount($dailyData, $dates);
        $payCostTrend = $this->preparePayCostTrend($dailyData, $dates);

        return [
            'channelSuccessRate' => $channelSuccessRate,
            'channelPayCount' => $channelPayCount,
            'singleChannelPayCount' => $singleChannelPayCount,
            'payCostTrend' => $payCostTrend,
            'maxDate' => $maxDate
        ];
    }

    /**
     * 分通道成功率
     * @param array $dailyData
     * @param array $dates
     * @return array
     */
    protected function prepareChannelSuccessRate(array $dailyData, array $dates): array
    {
        $successRates = [];
        $channels = array_unique(array_column($dailyData, 'channel_name'));
        foreach ($channels as $channel) {
            foreach ($dates as $date) {
                $successRates[$channel][$date] = 0;
            }
        }

        foreach ($dailyData as $data) {
            $payNumbers = (int)($data['pay_numbers'] ?? 0);
            $paySuccessNumbers = (int)($data['pay_success_numbers'] ?? 0);
            $successRate = $payNumbers > 0
                ? round(($paySuccessNumbers / $payNumbers) * 100, 2)
                : 0;
            $successRates[$data['channel_name']][$data['day']] = $successRate;
        }

        return [
            'dates' => $dates,
            'rates' => array_map(function ($v) {
                return array_values($v);
            }, $successRates)
        ];
    }

    /**
     * 分通道的支付次数
     * @param array $dailyData
     * @param array $dates
     * @return array
     */
    protected function prepareChannelPayCount(array $dailyData, array $dates): array
    {
        $channelPayCount = [];
        $channels = array_unique(array_column($dailyData, 'channel_name'));
        foreach ($channels as $channel) {
            foreach ($dates as $date) {
                $channelPayCount[$channel][$date] = 0;
            }
        }
        foreach ($dailyData as $data) {
            $payNumbers = (int)($data['pay_numbers'] ?? 0);

            $channelPayCount[$data['channel_name']][$data['day']] = $payNumbers;
        }
        return [
            'dates' => $dates,
            'payCount' => array_map(function ($v) {
                return array_values($v);
            }, $channelPayCount)
        ];
    }

    /**
     * 通道的支付次数和其他占比优势
     * @param array $dailyData
     * @param array $dates
     * @return array
     */
    protected function prepareSingleChannelPayCount(array $dailyData, array $dates): array
    {
        // 初始化数据结构
        $ratio = [];
        $channelPayCount = [];
        $channels = array_unique(array_column($dailyData, 'channel_name'));
        $dailyTotals = []; // 存储每天的总支付次数

        foreach ($channels as $channel) {
            foreach ($dates as $date) {
                $channelPayCount[$channel][$date] = 0;
                $ratio[$channel][$date] = 0;
            }
        }

        foreach ($dailyData as $data) {
            $date = $data['day'];
            $payNumbers = (int)($data['pay_numbers'] ?? 0);
            $channelPayCount[$data['channel_name']][$date] = $payNumbers;

            // 累加每天的总支付次数
            if (!isset($dailyTotals[$date])) {
                $dailyTotals[$date] = 0;
            }
            $dailyTotals[$date] += $payNumbers;
        }

        foreach ($channels as $channel) {
            foreach ($dates as $date) {
                $payNumbers = $channelPayCount[$channel][$date];
                $dailyTotal = $dailyTotals[$date] ?? 0;
                $ratio[$channel][$date] = $dailyTotal > 0 ? round(($payNumbers / $dailyTotal) * 100, 2) : 0;
            }
        }

        return [
            'dates' => $dates,
            'payCount' => array_map(function ($v) {
                return array_values($v);
            }, $channelPayCount),
            'ratio' => array_map(function ($v) {
                return array_values($v);
            }, $ratio)
        ];
    }

    /**
     * 支付成本趋势
     * @param array $dailyData
     * @param array $dates
     * @return array
     */
    protected function preparePayCostTrend(array $dailyData, array $dates): array
    {
        $cost = [];
        $amount = [];

        $channels = array_unique(array_column($dailyData, 'channel_name'));

        foreach ($channels as $channel) {
            foreach ($dates as $date) {
                $cost[$channel][$date] = 0;
                $amount[$channel][$date] = 0;
            }
        }

        foreach ($dailyData as $data) {
            $payFeeAmount = (int)($data['pay_fee_amount'] ?? 0);
            $paySuccessAmount = (int)($data['pay_success_amount'] ?? 0);
            $cost[$data['channel_name']][$data['day']] = $paySuccessAmount > 0 ? round($payFeeAmount / $paySuccessAmount * 1000, 2) : 0;
            $amount[$data['channel_name']][$data['day']] = round($payFeeAmount / 100 / 1000, 2);
        }

        return [
            'dates' => $dates,
            'payFeeAmount' => array_map(function ($v) {
                return array_values($v);
            }, $amount),
            'cost' => array_map(function ($v) {
                return array_values($v);
            }, $cost),
        ];
    }


    public function search(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'date1' => new Expression('DATE(date)'),
            'channel_name',
            'sign_company_name',
        ];

        foreach (array_merge(self::METRIC_CONFIG, ['pay_fail_amount', 'pay_fail_numbers']) as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $query = self::find()
            ->select($selects)
            ->where(['type' => array_merge(self::METRIC_CONFIG, ['pay_fail_amount', 'pay_fail_numbers'])])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()])
            ->andWhere(['<>', 'channel_name', ''])
            ->andWhere(['<>', 'sign_company_name', ''])
            ->andFilterWhere([
                'channel_name' => $this->channel_name,
                'sign_company_name' => $this->sign_company_name
            ]);
        $clone = clone $query;
        $data = $query->groupBy(['date1', 'channel_name', 'sign_company_name'])
            ->asArray()->all();
        $totalPayNumbers = array_sum(array_column($data, 'pay_numbers'));
        $maxDate = $clone->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();

        $rows = [];
        foreach ($data as &$item) {
            $item['total_pay_numbers'] = $totalPayNumbers;
            $this->calculateDerivedMetrics($item);
            $rows[] = $item;
        }

        return [$rows, $maxDate];
    }

    public function fetchData(array $types, Carbon $startDate, Carbon $endDate): array
    {
        $selects = ['day' => new Expression('DATE(date)'), 'channel_name'];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }

        $query = self::find()
            ->where(['type' => $types])
            ->andWhere(['<>', 'channel_name', ''])
            ->andWhere(['<>', 'sign_company_name', ''])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()]);
        $clone = clone $query;

        $data = $query->select($selects)->groupBy(['day', 'channel_name'])->orderBy('day')->asArray()->all();
        $maxDate = $clone->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();
        return [$data, $maxDate];
    }
}