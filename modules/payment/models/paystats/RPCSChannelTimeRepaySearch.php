<?php

namespace payment\models\paystats;

use Carbon\Carbon;

class RPCSChannelTimeRepaySearch extends RPCSSearch
{

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => fn() => Carbon::now()->subDay()->toDateString()],
            [['endDate'], 'default', 'value' => fn() => Carbon::today()->toDateString()],
            [['startDate', 'endDate'], 'filter', 'filter' => fn($date) => Carbon::parse($date)->toDateString()],
        ];
    }


    public const METRIC_CONFIG = [
        'chart' => [
            'loanData' => [
                'repay_success_numbers'
            ],
            'loanAmountData' => [
                'repay_success_amount'
            ]
        ]
    ];

    public function getChartData(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $currentEndDate = Carbon::now();
        $currentStartDate = Carbon::parse($this->startDate);

        $types = array_unique(array_merge(
            self::METRIC_CONFIG['chart']['loanData'],
            self::METRIC_CONFIG['chart']['loanAmountData'],
        ));

        // 获取按小时分组的数据
        [$hourlyData, $maxDate] = $this->fetchHourlyData(['channel_name', 'sign_company_name'], $types, $currentStartDate, $currentEndDate);

        if (empty($hourlyData)) {
            return [
                'hourlyLoanData' => [
                    'dates' => [],
                    'hours' => [],
                    'channels' => [],
                    'channelData' => []
                ],
                'maxDate' => null
            ];
        }

        $dates = array_unique(array_column($hourlyData, 'date_only'));
        sort($dates);

        $hours = array_map(fn($i) => date('H:i', strtotime("00:00 +$i minutes")), range(0, 1410, 30));

        // 处理通道名称
        foreach ($hourlyData as &$data) {
            $data['channel'] = $data['channel_name'] . '*' . $data['sign_company_name'];
        }

        // 使用图表处理器处理数据
        $processor = $this->createChartProcessor('hourlyLoan');
        $chartData = $processor->processGroupedHourlyChart(
            $hourlyData,
            $dates,
            $hours,
            'repay_success_numbers',
            'repay_success_amount',
            'channel'
        );

        return [
            'hourlyLoanData' => $chartData,
            'maxDate' => $maxDate
        ];

    }


    /**
     * 获取按小时分组的数据
     *
     * @param array $types 需要查询的指标类型
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 查询结果
     */
    protected function fetchHourlyData(array $selects, array $types, Carbon $startDate, Carbon $endDate): array
    {
        // 使用特性中的通用方法，添加额外的分组字段
        return $this->fetchHourlyDataCommon($selects, $types, $startDate, $endDate, ['date_only', 'hour', 'channel_name', 'sign_company_name']);
    }
}