<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class RPCSAllGrantSearch extends RPCSSearch
{
    public const METRICS_CONFIG = [
        'base_metrics' => [
            'import_counts',
            'import_success_counts',
            'pay_numbers',
            'pay_success_numbers',
            'import_success_amount',
            'pay_amount',
            'pay_success_amount',
            'pay_fee_amount',
            'import_amount',
            'import_fail_counts',
            'import_fail_amount'
        ],
        'chart' => [
            //支付放款次数和成功率
            'importCounts' => [
                'import_counts',
                'import_success_counts'
            ],
            //放款笔数和成功率走势
            'payNumbers' => [
                'pay_numbers',
                'pay_success_numbers'
            ],
            //进件放款笔均支付次数
            'avgPayNumbers' => [
                'pay_numbers',
                'import_counts'
            ],
            //支付成本趋势
            'thousandLoanCost' => [
                'pay_fee_amount',
                'pay_success_amount'
            ]
        ],
        'derived_metrics' => [
            'import_success_rate',
            'pay_success_rate',
            'avg_pay_numbers_per_import',
            'pay_success_amount_ratio',
            'thousand_loan_cost',
        ]
    ];


    /**
     * 获取当前时间段和前一个时间段的对比数据
     *
     * @param array $params 查询参数
     * @return array 包含当前时间段和前一个时间段数据的数组
     */
    public function getComparisonData(array $params = []): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $types = self::METRICS_CONFIG['base_metrics'];

        $currentEndDate = Carbon::parse($this->endDate);
        $currentStartDate = Carbon::parse($this->startDate);
        $dayDiff = $currentEndDate->diffInDays($currentStartDate) + 1;

        $previousEndDate = $currentStartDate->copy()->subDay();
        $previousStartDate = $previousEndDate->copy()->subDays($dayDiff - 1);

        [$currentData, $maxDate] = $this->fetchDataByDateRange(
            $types,
            $currentStartDate->clone(),
            $currentEndDate->clone()
        );

        [$previousData] = $this->fetchDataByDateRange(
            $types,
            $previousStartDate->clone(),
            $previousEndDate->clone()
        );

        $allMetrics = array_merge(
            self::METRICS_CONFIG['base_metrics'],
            self::METRICS_CONFIG['derived_metrics']
        );
        $comparison = $this->calculateComparison($currentData, $previousData, $allMetrics);

        $result = [
            [
                'type' => '数值',
                'period' => $currentStartDate->toDateString() . ' ~ ' . $currentEndDate->clone()->toDateString(),
            ],
            [
                'type' => '同比',
                'period' => $previousStartDate->toDateString() . ' ~ ' . $previousEndDate->clone()->toDateString(),
            ]
        ];

        $allMetrics = array_merge(
            $types,
            self::METRICS_CONFIG['derived_metrics']
        );

        foreach ($allMetrics as $metric) {
            $result[0][$metric] = $currentData[$metric] ?? 0;
            $result[1][$metric] = $comparison[$metric] ?? null;
        }

        // 获取图表数据
        $chartData = $this->getChartData($currentStartDate->clone(), $currentEndDate->clone());

        return [
            'tables' => [
                'data' => $result,
                'maxDate' => $maxDate
            ],
            'chart' => $chartData
        ];
    }

    /**
     * 获取图表数据
     *
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 图表数据
     */
    protected function getChartData(Carbon $startDate, Carbon $endDate): array
    {
        // 获取所有需要的指标类型
        $types = array_unique(array_merge(
            self::METRICS_CONFIG['chart']['importCounts'],
            self::METRICS_CONFIG['chart']['payNumbers'],
            self::METRICS_CONFIG['chart']['avgPayNumbers'],
            self::METRICS_CONFIG['chart']['thousandLoanCost']
        ));

        // 获取按日期分组的数据
        [$dailyData, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        if (empty($dailyData)) {
            return [
                'importCounts' => [],
                'payNumbers' => [],
                'avgPayNumbers' => [],
                'thousandLoanCost' => []
            ];
        }

        usort($dailyData, function ($a, $b) {
            return strtotime($a['date']) - strtotime($b['date']);
        });

        $dates = array_column($dailyData, 'date');

        $importCountsChart = $this->prepareImportCountsChartData($dailyData, $dates);
        $payNumbersChart = $this->preparePayNumbersChartData($dailyData, $dates);
        $avgPayNumbersChart = $this->prepareAvgPayNumbersChartData($dailyData, $dates);
        $thousandLoanCostChart = $this->prepareThousandLoanCostChartData($dailyData, $dates);
        return [
            'importCounts' => $importCountsChart,
            'payNumbers' => $payNumbersChart,
            'avgPayNumbers' => $avgPayNumbersChart,
            'thousandLoanCost' => $thousandLoanCostChart,
            'maxDate' => $maxDate
        ];
    }


    protected function prepareImportCountsChartData(array $dailyData, array $dates): array
    {
        $processor = $this->createChartProcessor('importCounts');
        return $processor->processSuccessRateChart($dailyData, $dates, 'import_counts', 'import_success_counts');
    }

    protected function preparePayNumbersChartData(array $dailyData, array $dates): array
    {
        $processor = $this->createChartProcessor('payNumbers');
        $result = $processor->processSuccessRateChart($dailyData, $dates, 'pay_numbers', 'pay_success_numbers');
        // 保持与原来的键名一致
        $result['numbers'] = $result['counts'];
        unset($result['counts']);
        return $result;
    }


    protected function prepareAvgPayNumbersChartData(array $dailyData, array $dates): array
    {
        $processor = $this->createChartProcessor('avgPayNumbers');
        $result = $processor->processAverageChart($dailyData, $dates, 'pay_numbers', 'import_counts');
        // 保持与原来的键名一致
        $result['avgNumbers'] = $result['avgValues'];
        unset($result['avgValues']);
        return $result;
    }


    protected function prepareThousandLoanCostChartData(array $dailyData, array $dates): array
    {
        $processor = $this->createChartProcessor('thousandLoanCost');
        return $processor->processThousandCostChart($dailyData, $dates, 'pay_fee_amount', 'pay_success_amount');
    }

    /**
     * 根据日期范围获取数据
     *
     * @param array $types 需要查询的指标类型
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 查询结果
     */
    protected function fetchDataByDateRange(array $types, Carbon $startDate, Carbon $endDate): array
    {
        [$data, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        $rows = [];

        foreach ($types as $type) {
            $rows[$type] = array_sum(ArrayHelper::getColumn($data, $type));
        }

        $this->calculateDerivedMetrics($rows);

        return [$rows, $maxDate];
    }

    /**
     * 计算衍生指标
     *
     * @param array &$data 数据行
     */
    protected function calculateDerivedMetrics(array &$data): void
    {
        $data['import_success_rate'] = $this->calculateMetric($data['import_success_counts'] ?? 0, $data['import_counts'] ?? 0, 100);
        $data['pay_success_rate'] = $this->calculateMetric($data['pay_success_numbers'] ?? 0, $data['pay_numbers'] ?? 0, 100);
        $data['avg_pay_numbers_per_import'] = $this->calculateMetric($data['pay_numbers'] ?? 0, $data['import_counts'] ?? 0);
        $data['pay_success_amount_ratio'] = $this->calculateMetric($data['pay_success_amount'] ?? 0, $data['pay_amount'] ?? 0, 100);
        $data['thousand_loan_cost'] = $this->calculateMetric($data['pay_fee_amount'] ?? 0, $data['pay_success_amount'] ?? 0, 1000, 4);
    }

    /**
     * 计算环比变化
     *
     * @param array $currentData
     * @param array $previousData
     * @param array $metrics
     * @return array 环比变化数据
     */
    protected function calculateComparison(array $currentData, array $previousData, array $metrics): array
    {
        $allMetrics = array_merge(
            self::METRICS_CONFIG['base_metrics'],
            self::METRICS_CONFIG['derived_metrics']
        );

        return parent::calculateComparison($currentData, $previousData, $allMetrics);
    }

    public function getDetail(array $params = []): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }
        $types = self::METRICS_CONFIG['base_metrics'];
        $selects = ['date1' => new Expression('DATE(date)')];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $data = self::find()
            ->where(['type' => $types])
            ->andWhere(['>=', 'date', Carbon::parse($this->startDate)->toDateString()])
            ->andWhere(['<', 'date', Carbon::parse($this->endDate)->addDay()->toDateString()])
            ->select($selects)
            ->groupBy(['date1'])
            ->orderBy(['date1' => SORT_DESC])
            ->asArray()
            ->all();


        foreach ($data as &$item) {
            $this->calculateDerivedMetrics($item);
        }
        unset($item);
        return $data;
    }

}
