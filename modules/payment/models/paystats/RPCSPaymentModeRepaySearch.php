<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use yii\db\Expression;

class RPCSPaymentModeRepaySearch extends RPCSSearch
{
    public const METRIC_CONFIG = [
        'repay_numbers',
        'repay_amount',
        'repay_success_amount',
        'repay_fee_amount',
        'repay_success_amount',
        'repay_success_numbers'
    ];

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['payment_mode', 'payment_method', 'channel_name'], 'safe'],
            [['startDate'], 'default', 'value' => fn() => Carbon::now()->subDays(7)->toDateString()],
            [['endDate'], 'default', 'value' => fn() => Carbon::yesterday()->toDateString()],
            [['startDate', 'endDate'], 'filter', 'filter' => fn($date) => Carbon::parse($date)->toDateString()],
        ];
    }

    /**
     *
     *
     * @param array $params 查询参数
     * @return array
     */
    public function getPaymentModeData(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'payment_mode',
        ];

        foreach (self::METRIC_CONFIG as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $query = self::find()
            ->where(['type' => self::METRIC_CONFIG])
            ->andWhere(['>=', 'date', $startDate->clone()->toDateString()])
            ->andWhere(['<', 'date', $endDate->clone()->addDay()->toDateString()]);

        $data = $query->select($selects)->groupBy(['payment_mode'])->limit(10)->orderBy(['repay_numbers' => SORT_DESC])->asArray()->all();

        $totalPayNumbers = array_sum(array_column($data, 'repay_numbers'));

        $rows = [];

        foreach ($data as &$item) {
            $item['total_repay_numbers'] = $totalPayNumbers;
            $this->calculateDerivedMetrics($item);
            $rows[] = $item;
        }

        $chart = $this->getChartData($startDate->clone(), $endDate->clone());

        return [
            'table' => $rows,
            'chart' => $chart
        ];
    }

    protected function calculateDerivedMetrics(array &$data): void
    {
        $data['repay_numbers_ratio'] = $this->calculateMetric($data['repay_numbers'] ?? 0, $data['total_repay_numbers'] ?? 0, 100);
        $data['repay_success_rate'] = $this->calculateMetric($data['repay_success_numbers'] ?? 0, $data['repay_numbers'] ?? 0, 100);
        $data['repay_success_amount_ratio'] = $this->calculateMetric($data['repay_success_amount'] ?? 0, $data['repay_amount'] ?? 0, 100);
        $data['thousand_repay_cost'] = $this->calculateMetric($data['repay_fee_amount'] ?? 0, $data['repay_success_amount'] ?? 0, 1000, 4);
    }

    protected function getChartData(Carbon $startDate, Carbon $endDate): array
    {
        $types = ['repay_success_counts', 'repay_counts', 'repay_numbers', 'repay_fee_amount', 'repay_fail_numbers', 'repay_success_numbers'];

        [$dailyData, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        if (empty($dailyData)) {
            return [
                'paymentModeSuccessRate' => [],
                'paymentModePayCount' => [],
                'channelPayCount' => [],
                'channelSuccessRate' => [],
                'paymentMethodPayCount' => [],
                'methodSuccessRate' => [],
            ];
        }

        $dates = array_unique(array_column($dailyData, 'date'));
        $methods = array_unique(array_column($dailyData, 'payment_method'));
        $methods = array_combine($methods, $methods);
        $channels = array_unique(array_column($dailyData, 'channel_name'));
        $channels = array_combine($channels, $channels);

        $aggregated = [];
        foreach ($dailyData as $item) {
            $mode = $item['payment_mode'];
            $repayNumbers = (int)$item['repay_numbers'];

            if (!isset($aggregated[$mode])) {
                $aggregated[$mode] = 0;
            }
            $aggregated[$mode] += $repayNumbers;
        }
        arsort($aggregated, SORT_NUMERIC);
        $modes = array_keys($aggregated);
        $modes = array_combine($modes, $modes);

        sort($dates);

        $paymentModeSuccessRate = $this->preparePaymentModeSuccessRate($dailyData, $dates);
        $paymentModePayCount = $this->preparePaymentModePayCount($dailyData, $dates);
        $channelPayCount = $this->prepareChannelPayCount($dailyData, $dates);
        $channelSuccessRate = $this->prepareChannelSuccessRate($dailyData, $dates);
        $paymentMethodPayCount = $this->preparePaymentMethodPayCount($dailyData, $dates);
        $methodSuccessRate = $this->prepareMethodSuccessRate($dailyData, $dates);

        return [
            'paymentModeSuccessRate' => $paymentModeSuccessRate,
            'paymentModePayCount' => $paymentModePayCount,
            'channelPayCount' => $channelPayCount,
            'channelSuccessRate' => $channelSuccessRate,
            'paymentMethodPayCount' => $paymentMethodPayCount,
            'methodSuccessRate' => $methodSuccessRate,
            'maxDate' => $maxDate,
            'methods' => $methods,
            'channels' => $channels,
            'modes' => $modes,
        ];
    }


    /**
     * 分渠道成功率
     * @param array $dailyData
     * @param array $dates
     * @param string $indexKey
     * @return array
     */
    protected function preparePaymentModeSuccessRate(array $dailyData, array $dates, string $indexKey = 'payment_mode'): array
    {
        $successRates = [];
        $paymentModes = array_unique(array_column($dailyData, $indexKey));

        // 初始化每个渠道在每个日期的数据结构
        foreach ($paymentModes as $paymentMode) {
            foreach ($dates as $date) {
                $successRates[$paymentMode][$date] = 0;
                // 初始化每个渠道每天的支付次数和成功次数
                if (!isset($payNumbers[$paymentMode][$date])) {
                    $payNumbers[$paymentMode][$date] = 0;
                }
                if (!isset($paySuccessNumbers[$paymentMode][$date])) {
                    $paySuccessNumbers[$paymentMode][$date] = 0;
                }
            }
        }

        // 按payment_mode和date聚合数据
        $aggregatedData = [];
        foreach ($dailyData as $data) {
            $paymentMode = $data[$indexKey];
            $date = $data['date'];

            if (!isset($aggregatedData[$paymentMode][$date])) {
                $aggregatedData[$paymentMode][$date] = [
                    'repay_numbers' => 0,
                    'repay_success_numbers' => 0
                ];
            }

            $aggregatedData[$paymentMode][$date]['repay_numbers'] += (int)($data['repay_numbers'] ?? 0);
            $aggregatedData[$paymentMode][$date]['repay_success_numbers'] += (int)($data['repay_success_numbers'] ?? 0);
        }

        // 计算每个渠道每天的成功率
        foreach ($aggregatedData as $paymentMode => $dateData) {
            foreach ($dateData as $date => $metrics) {
                $payNumbers = $metrics['repay_numbers'];
                $paySuccessNumbers = $metrics['repay_success_numbers'];
                $successRate = $payNumbers > 0
                    ? round(($paySuccessNumbers / $payNumbers) * 100, 2)
                    : 0;
                $successRates[$paymentMode][$date] = $successRate;
            }
        }

        return [
            'dates' => $dates,
            'rates' => array_map(function ($v) {
                return array_values($v);
            }, $successRates)
        ];
    }

    /**
     * 分渠道的支付次数
     * @param array $dailyData
     * @param array $dates
     * @param string $indexKey
     * @return array
     */
    protected function preparePaymentModePayCount(array $dailyData, array $dates, string $indexKey = 'payment_mode'): array
    {
        $successPayCount = [];
        $failPayCount = [];
        $paymentModes = array_unique(array_column($dailyData, $indexKey));

        // 初始化每个渠道在每个日期的数据结构
        foreach ($paymentModes as $paymentMode) {
            foreach ($dates as $date) {
                $successPayCount[$paymentMode][$date] = 0;
                $failPayCount[$paymentMode][$date] = 0;
            }
        }

        // 按payment_mode和date聚合数据
        $aggregatedData = [];
        foreach ($dailyData as $data) {
            $paymentMode = $data[$indexKey];
            $date = $data['date'];

            if (!isset($aggregatedData[$paymentMode][$date])) {
                $aggregatedData[$paymentMode][$date] = [
                    'repay_success_numbers' => 0,
                    'repay_fail_numbers' => 0
                ];
            }

            $aggregatedData[$paymentMode][$date]['repay_success_numbers'] += (int)($data['repay_success_numbers'] ?? 0);
            $aggregatedData[$paymentMode][$date]['repay_fail_numbers'] += (int)($data['repay_fail_numbers'] ?? 0);
        }

        // 计算每个渠道每天的成功和失败次数
        foreach ($aggregatedData as $paymentMode => $dateData) {
            foreach ($dateData as $date => $metrics) {
                $successPayCount[$paymentMode][$date] = $metrics['repay_success_numbers'];
                $failPayCount[$paymentMode][$date] = $metrics['repay_fail_numbers'];
            }
        }

        return [
            'dates' => $dates,
            'successPayCount' => array_map(function ($v) {
                return array_values($v);
            }, $successPayCount),
            'failPayCount' => array_map(function ($v) {
                return array_values($v);
            }, $failPayCount)
        ];
    }

    /**
     * 通道的支付次数
     * @param array $dailyData
     * @param array $dates
     * @param string $indexKey
     * @return array
     */
    protected function prepareChannelPayCount(array $dailyData, array $dates, string $indexKey = 'payment_mode'): array
    {
        $channelPayCount = [];
        $paymentModes = array_unique(array_column($dailyData, $indexKey));
        $channels = array_unique(array_column($dailyData, 'channel_name'));

        foreach ($paymentModes as $paymentMode) {
            foreach ($channels as $channel) {
                $channelPayCount[$paymentMode][$channel] = 0;
            }
        }

        // 按payment_mode和channel_name聚合数据
        foreach ($dailyData as $data) {
            $paymentMode = $data[$indexKey];
            $channel = $data['channel_name'];

            if (!isset($channelPayCount[$paymentMode][$channel])) {
                $channelPayCount[$paymentMode][$channel] = 0;
            }

            $channelPayCount[$paymentMode][$channel] += (int)($data['repay_numbers'] ?? 0);
        }

        return [
            'dates' => $dates,
            'payCount' => $channelPayCount,
        ];
    }

    /**
     * 分通道成功率
     * @param array $dailyData
     * @param array $dates
     * @param string $indexKey
     * @return array
     */
    protected function prepareChannelSuccessRate(array $dailyData, array $dates, string $indexKey = 'payment_mode'): array
    {
        $rates = [];

        $paymentModes = array_unique(array_column($dailyData, $indexKey));
        $channels = array_unique(array_column($dailyData, 'channel_name'));

        // 初始化每个渠道在每个日期的数据结构
        foreach ($paymentModes as $paymentMode) {
            foreach ($channels as $channel) {
                foreach ($dates as $date) {
                    $rates[$paymentMode][$channel][$date] = 0;
                }
            }
        }

        // 按payment_mode和date聚合数据
        $aggregatedData = [];
        foreach ($dailyData as $data) {
            $paymentMode = $data[$indexKey];
            $date = $data['date'];
            $channel = $data['channel_name'];

            if (!isset($aggregatedData[$paymentMode][$channel][$date])) {
                $aggregatedData[$paymentMode][$channel][$date] = [
                    'repay_numbers' => 0,
                    'repay_success_numbers' => 0
                ];
            }

            $aggregatedData[$paymentMode][$channel][$date]['repay_numbers'] += (int)($data['repay_numbers'] ?? 0);
            $aggregatedData[$paymentMode][$channel][$date]['repay_success_numbers'] += (int)($data['repay_success_numbers'] ?? 0);
        }

        // 计算每个渠道每天的成本和金额
        foreach ($aggregatedData as $paymentMode => $channelData) {
            foreach ($channelData as $channel => $dateData) {
                foreach ($dateData as $date => $metrics) {
                    $payNumbers = $metrics['repay_numbers'];
                    $paySuccessNumbers = $metrics['repay_success_numbers'];
                    $successRate = $payNumbers > 0
                        ? round(($paySuccessNumbers / $payNumbers) * 100, 2)
                        : 0;
                    $rates[$paymentMode][$channel][$date] = $successRate;
                }
            }
        }

        return [
            'dates' => $dates,
            'rates' => $rates
        ];
    }

    /**
     * 按交易方式笔数
     * @param array $dailyData
     * @param array $dates
     * @return array
     */
    protected function preparePaymentMethodPayCount(array $dailyData, array $dates): array
    {
        $methodPayCount = [];
        $paymentModes = array_unique(array_column($dailyData, 'payment_mode'));
        $methods = array_unique(array_column($dailyData, 'payment_method'));

        foreach ($paymentModes as $paymentMode) {
            foreach ($methods as $method) {
                $methodPayCount[$paymentMode][$method] = 0;
            }
        }

        // 按payment_mode和payment_method聚合数据
        foreach ($dailyData as $data) {
            $paymentMode = $data['payment_mode'];
            $method = $data['payment_method'];

            if (!isset($methodPayCount[$paymentMode][$method])) {
                $methodPayCount[$paymentMode][$method] = 0;
            }

            $methodPayCount[$paymentMode][$method] += (int)($data['repay_numbers'] ?? 0);
        }

        return [
            'dates' => $dates,
            'payCount' => $methodPayCount,
        ];
    }

    /**
     * 分交易方式成功率
     * @param array $dailyData
     * @param array $dates
     * @return array
     */
    protected function prepareMethodSuccessRate(array $dailyData, array $dates): array
    {
        $rates = [];

        $paymentModes = array_unique(array_column($dailyData, 'payment_mode'));
        $methods = array_unique(array_column($dailyData, 'payment_method'));

        // 初始化每个渠道在每个日期的数据结构
        foreach ($paymentModes as $paymentMode) {
            foreach ($methods as $method) {
                foreach ($dates as $date) {
                    $rates[$paymentMode][$method][$date] = 0;
                }
            }
        }

        // 按payment_mode和date聚合数据
        $aggregatedData = [];
        foreach ($dailyData as $data) {
            $paymentMode = $data['payment_mode'];
            $date = $data['date'];
            $method = $data['payment_method'];

            if (!isset($aggregatedData[$paymentMode][$method][$date])) {
                $aggregatedData[$paymentMode][$method][$date] = [
                    'repay_numbers' => 0,
                    'repay_success_numbers' => 0
                ];
            }

            $aggregatedData[$paymentMode][$method][$date]['repay_numbers'] += (int)($data['repay_numbers'] ?? 0);
            $aggregatedData[$paymentMode][$method][$date]['repay_success_numbers'] += (int)($data['repay_success_numbers'] ?? 0);
        }

        // 计算每个渠道每天的成本和金额
        foreach ($aggregatedData as $paymentMode => $methodData) {
            foreach ($methodData as $method => $dateData) {
                foreach ($dateData as $date => $metrics) {
                    $payNumbers = $metrics['repay_numbers'];
                    $paySuccessNumbers = $metrics['repay_success_numbers'];
                    $successRate = $payNumbers > 0
                        ? round(($paySuccessNumbers / $payNumbers) * 100, 2)
                        : 0;
                    $rates[$paymentMode][$method][$date] = $successRate;
                }
            }
        }

        return [
            'dates' => $dates,
            'rates' => $rates
        ];
    }

    public function search(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'date1' => new Expression('DATE(date)'),
            'channel_name',
            'payment_method',
            'sign_company_name',
            'payment_mode'
        ];

        foreach (array_merge(self::METRIC_CONFIG, ['repay_fail_amount', 'repay_fail_numbers']) as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $query = self::find()
            ->select($selects)
            ->where(['type' => array_merge(self::METRIC_CONFIG, ['repay_fail_amount', 'repay_fail_numbers'])])
            ->andWhere(['<>', 'payment_mode', ''])
            ->andWhere(['<>', 'channel_name', ''])
            ->andWhere(['<>', 'sign_company_name', ''])
            ->andWhere(['<>', 'payment_method', ''])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()])
            ->andFilterWhere([
                'channel_name' => $this->channel_name,
                'payment_method' => $this->payment_method,
                'payment_mode' => $this->payment_mode
            ]);
        $clone = clone $query;
        $data = $query->groupBy(['date1', 'channel_name', 'sign_company_name', 'payment_mode', 'payment_method'])
            ->asArray()->all();

        $maxDate = $clone->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();

        $rows = [];
        foreach ($data as &$item) {
            $this->calculateDerivedMetrics($item);
            $rows[] = $item;
        }

        return [$rows, $maxDate];
    }


    public function fetchData(array $types, Carbon $startDate, Carbon $endDate): array
    {
        $selects = ['date' => new Expression('DATE(date)'), 'payment_mode', 'channel_name', 'payment_method'];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }

        $query = self::find()
            ->where(['type' => $types])
            ->andWhere(['<>', 'payment_mode', ''])
            ->andWhere(['<>', 'channel_name', ''])
            ->andWhere(['<>', 'sign_company_name', ''])
            ->andWhere(['<>', 'payment_method', ''])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()]);
        $clone = clone $query;

        $data = $query->select($selects)->groupBy(['date', 'payment_mode', 'channel_name', 'payment_method'])->orderBy('date')->asArray()->all();
        $maxDate = $clone->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();
        return [$data, $maxDate];
    }


    public function getPaymentMethodData(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        $selects = [
            'payment_mode',
            'payment_method'
        ];

        foreach (self::METRIC_CONFIG as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $query = self::find()
            ->where(['type' => self::METRIC_CONFIG])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()])
            ->andFilterWhere(['payment_mode' => $this->payment_mode]);


        $data = $query->select($selects)->groupBy(['payment_mode', 'payment_method'])->limit(10)->orderBy(['repay_numbers' => SORT_DESC])->asArray()->all();

        $totalPayNumbers = array_sum(array_column($data, 'repay_numbers'));

        $rows = [];

        foreach ($data as &$item) {
            $item['total_repay_numbers'] = $totalPayNumbers;
            $this->calculateDerivedMetrics($item);
            $rows[] = $item;
        }

        $chart = $this->getMethodChart($startDate, $endDate);

        return [
            'table' => $rows,
            'chart' => $chart
        ];
    }

    protected function getMethodChart($startDate, $endDate): array
    {
        $types = ['repay_success_counts', 'repay_counts', 'repay_numbers', 'repay_fee_amount', 'repay_fail_numbers', 'repay_success_numbers'];

        [$dailyData, $maxDate] = $this->fetchData($types, $startDate, $endDate);

        if (empty($dailyData)) {
            return [
                'paymentModeSuccessRate' => [],
                'paymentModePayCount' => [],
                'channelPayCount' => [],
                'channelSuccessRate' => [],
            ];
        }

        $dates = array_unique(array_column($dailyData, 'date'));
        $aggregated = [];
        foreach ($dailyData as $item) {
            $mode = $item['payment_method'];
            $repayNumbers = (int)$item['repay_numbers'];

            if (!isset($aggregated[$mode])) {
                $aggregated[$mode] = 0;
            }
            $aggregated[$mode] += $repayNumbers;
        }
        arsort($aggregated, SORT_NUMERIC);
        $methods = array_keys($aggregated);
        $methods = array_combine($methods, $methods);

        $modes = array_unique(array_column($dailyData, 'payment_mode'));
        $modes = array_combine($modes, $modes);
        $channels = array_unique(array_column($dailyData, 'channel_name'));
        $channels = array_combine($channels, $channels);



        sort($dates);

        $paymentMethodSuccessRate = $this->preparePaymentModeSuccessRate($dailyData, $dates, 'payment_method');
        $paymentMethodPayCount = $this->preparePaymentModePayCount($dailyData, $dates, 'payment_method');
        $paymentModePayCount = $this->prepareChannelPayCount($dailyData, $dates, 'payment_method');
        $paymentModeSuccessRate = $this->prepareChannelSuccessRate($dailyData, $dates, 'payment_method');

        return [
            'paymentMethodSuccessRate' => $paymentMethodSuccessRate,
            'paymentMethodPayCount' => $paymentMethodPayCount,
            'paymentModePayCount' => $paymentModePayCount,
            'paymentModeSuccessRate' => $paymentModeSuccessRate,
            'maxDate' => $maxDate,
            'methods' => $methods,
            'modes' => $modes,
            'channels' => $channels
        ];
    }

}