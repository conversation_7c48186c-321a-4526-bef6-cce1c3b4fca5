<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use payment\models\ReportPaymentChannelStats;
use payment\traits\RPCSChartTrait;
use yii\db\Expression;

/**
 * RPCS搜索基类
 *
 * 提供通用的数据处理方法，减少子类中的重复逻辑
 */
class RPCSSearch extends ReportPaymentChannelStats
{
    use RPCSChartTrait;

    public function formName(): string
    {
        return '';
    }

    public $startDate;
    public $endDate;
    public $period;

    public function attributeLabels()
    {
        return [
            'startDate' => '开始日期',
            'endDate' => '结束日期'
        ];
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => fn() => Carbon::now()->subDays(7)->toDateString()],
            [['endDate'], 'default', 'value' => fn() => Carbon::yesterday()->toDateString()],
            [['startDate', 'endDate'], 'filter', 'filter' => fn($date) => Carbon::parse($date)->toDateString()],
        ];
    }

    /**
     * @param array $types
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function fetchData(array $types, Carbon $startDate, Carbon $endDate): array
    {
        $selects = ['date' => new Expression('DISTINCT DATE(date)')];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) OVER(PARTITION BY DATE(date))', $type));
        }

        $query = self::find()
            ->where(['type' => $types])
            ->andWhere(['>=', 'date', $startDate->toDateString()])
            ->andWhere(['<', 'date', $endDate->addDay()->toDateString()]);
        $clone = clone $query;

        $data = $query->select($selects)->asArray()->all();
        $maxDate = $clone->select(['maxDate' => new Expression('MAX(updated_at)')])->scalar();
        return [$data, $maxDate];
    }

    /**
     * 计算环比变化
     *
     * @param array $currentData 当前时间段数据
     * @param array $previousData 前一个时间段数据
     * @param array $metrics 需要计算的指标
     * @return array 环比变化数据
     */
    protected function calculateComparison(array $currentData, array $previousData, array $metrics): array
    {
        $comparison = [];

        foreach ($metrics as $metric) {
            $comparison[$metric] = $this->calculateMetricChange(
                $currentData[$metric] ?? 0,
                $previousData[$metric] ?? 0
            );
        }

        return $comparison;
    }
}