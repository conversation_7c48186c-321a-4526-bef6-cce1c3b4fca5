<?php

namespace payment\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * WithholdReceiptSearch represents the model behind the search form about `payment\models\WithholdReceipt`.
 */
class WithholdReceiptSearch extends WithholdReceipt
{
    public $finishedAtStartDate;
    public $finishedAtEndDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'withhold_receipt_merchant_key',
                    'finishedAtStartDate',
                    'finishedAtEndDate',
                    'withhold_receipt_reconci_status',
                    'withhold_receipt_channel_name',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = WithholdReceipt::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['withhold_receipt_id'],
                'defaultOrder' => ['withhold_receipt_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'withhold_receipt_merchant_key' => $this->withhold_receipt_merchant_key,
            'withhold_receipt_reconci_status' => $this->withhold_receipt_reconci_status,
        ]);
        if ($this->finishedAtStartDate) {
            $query->andFilterWhere(['>=', 'withhold_receipt_finished_at', $this->finishedAtStartDate]);
        }
        if ($this->finishedAtEndDate) {
            $query->andFilterWhere(
                [
                    '<=',
                    'withhold_receipt_finished_at',
                    Carbon::parse($this->finishedAtEndDate)->endOfDay()->toDateTimeString(),
                ]
            );
        }

        return $dataProvider;
    }
}
