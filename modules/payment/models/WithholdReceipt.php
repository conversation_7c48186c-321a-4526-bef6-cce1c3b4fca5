<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "{{%withhold_receipt}}".
 *
 * @property int          $withhold_receipt_id
 * @property string       $withhold_receipt_merchant_name        商户名称
 * @property string       $withhold_receipt_merchant_key         商户订单号
 * @property string       $withhold_receipt_channel_name         支付通道
 * @property string       $withhold_receipt_channel_key          支付通道订单号
 * @property string       $withhold_receipt_channel_inner_key    支付通道内部订单号
 * @property string       $withhold_receipt_card_num             卡号
 * @property int          $withhold_receipt_amount               交易金额（分）
 * @property int          $withhold_receipt_status               交易状态： 0=新建，1=处理中，2=成功，3=失败
 * @property int          $withhold_receipt_status_stage         交易细分子状态：0=默认，1=缺少信息，2=余额不足
 * @property string       $withhold_receipt_channel_resp_code    支付通道返回码
 * @property string       $withhold_receipt_channel_resp_message 支付通道返回信息
 * @property string       $withhold_receipt_started_at           交易发起时间
 * @property string       $withhold_receipt_finished_at          交易完成时间
 * @property string       $withhold_receipt_created_at
 * @property string       $withhold_receipt_updated_at
 * @property string       $withhold_receipt_redirect             通道方跳转URL
 * @property string       $withhold_receipt_ruleset_code         支付路由规则集代码
 * @property string       $withhold_receipt_payment_option       Payment method type: wallet..
 * @property string       $withhold_receipt_payment_mode         支付方式，如cc,dc,nb,upi,paypal,wallet等
 * @property string|null  $withhold_receipt_payment_gateway      Channel Payment Gateway
 * @property string       $withhold_receipt_description          Remarks, such as product information, etc., json format
 * @property int          $withhold_receipt_service_charge       Service fee (sen)
 * @property int          $withhold_receipt_service_tax          Tax (sen)
 * @property string|null  $withhold_receipt_resp_payment_option  Payment type from channel notification
 * @property string|null  $withhold_receipt_resp_payment_mode    Payment type from channel notification
 * @property string       $withhold_receipt_expired_at           Order expiration time
 * @property string       $withhold_receipt_biz_type             Collection business type: ""=default gray=grey
 * @property string|null  $withhold_receipt_from_app             source app
 * @property string       $withhold_receipt_account_uuid         UUID of the virtual account
 * @property string       $withhold_receipt_reconci_status       对账状态: open,process,pending,success
 * @property string       $withhold_receipt_reconci_at           对账完成时间
 * @property string|null  $withhold_receipt_reconci_result       对账结果
 * @property-read Channel $channel
 */
class WithholdReceipt extends \yii\db\ActiveRecord
{
    public const RECONCI_STATUS_OPEN = 'open';
    public const RECONCI_STATUS_PROCESS = 'process';
    public const RECONCI_STATUS_PENDING = 'pending';
    public const RECONCI_STATUS_SUCCESS = 'success';
    public const RECONCI_STATUS_COMPLETE = 'complete';
    public const RECONCI_STATUS_MAP = [
        self::RECONCI_STATUS_OPEN    => '待对账',
        self::RECONCI_STATUS_PROCESS => '处理中',
        self::RECONCI_STATUS_PENDING => '对账失败',
        self::RECONCI_STATUS_SUCCESS => '对账成功',
        self::RECONCI_STATUS_COMPLETE => '无需对账',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%withhold_receipt}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'withhold_receipt_merchant_name',
                    'withhold_receipt_merchant_key',
                    'withhold_receipt_channel_name',
                    'withhold_receipt_amount',
                ],
                'required',
            ],
            [['withhold_receipt_amount', 'withhold_receipt_status', 'withhold_receipt_status_stage'], 'integer'],
            [
                [
                    'withhold_receipt_started_at',
                    'withhold_receipt_finished_at',
                    'withhold_receipt_created_at',
                    'withhold_receipt_updated_at',
                ],
                'safe',
            ],
            [
                [
                    'withhold_receipt_merchant_name',
                    'withhold_receipt_merchant_key',
                    'withhold_receipt_channel_name',
                    'withhold_receipt_channel_key',
                    'withhold_receipt_channel_inner_key',
                    'withhold_receipt_ruleset_code',
                ],
                'string',
                'max' => 64,
            ],
            [
                ['withhold_receipt_card_num', 'withhold_receipt_channel_resp_code', 'withhold_receipt_payment_mode'],
                'string',
                'max' => 32,
            ],
            [['withhold_receipt_channel_resp_message'], 'string', 'max' => 255],
            [['withhold_receipt_redirect'], 'string', 'max' => 512],
            [
                ['withhold_receipt_channel_key', 'withhold_receipt_channel_name'],
                'unique',
                'targetAttribute' => ['withhold_receipt_channel_key', 'withhold_receipt_channel_name'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withhold_receipt_id' => Yii::t('payment', 'ID'),
            'withhold_receipt_merchant_name' => Yii::t('payment', '商户名称'),
            'withhold_receipt_merchant_key' => Yii::t('payment', '商户订单号'),
            'withhold_receipt_channel_name' => Yii::t('payment', '支付通道'),
            'withhold_receipt_channel_key' => Yii::t('payment', '支付通道订单号'),
            'withhold_receipt_channel_inner_key' => Yii::t('payment', '支付通道内部订单号'),
            'withhold_receipt_card_num' => Yii::t('payment', '卡号'),
            'withhold_receipt_amount' => Yii::t('payment', '交易金额'),
            'withhold_receipt_status' => Yii::t('payment', '交易状态'),
            'withhold_receipt_status_stage' => Yii::t('payment', '交易细分子状态：0=默认，1=缺少信息，2=余额不足'),
            'withhold_receipt_channel_resp_code' => Yii::t('payment', '支付通道返回码'),
            'withhold_receipt_channel_resp_message' => Yii::t('payment', '支付通道返回信息'),
            'withhold_receipt_started_at' => Yii::t('payment', '交易发起时间'),
            'withhold_receipt_finished_at' => Yii::t('payment', '交易完成时间'),
            'withhold_receipt_created_at' => Yii::t('payment', '创建时间'),
            'withhold_receipt_updated_at' => Yii::t('payment', '更新时间'),
            'withhold_receipt_redirect' => Yii::t('payment', '通道方跳转URL'),
            'withhold_receipt_payment_option' => 'Withhold Receipt Payment Option',
            'withhold_receipt_ruleset_code' => Yii::t('payment', '支付路由规则集代码'),
            'withhold_receipt_payment_mode' => Yii::t('payment', '支付方式，如cc,dc,nb,upi,paypal,wallet等'),
            'withhold_receipt_payment_gateway' => 'Withhold Receipt Payment Gateway',
            'withhold_receipt_description' => 'Withhold Receipt Description',
            'withhold_receipt_service_charge' => '服务费',
            'withhold_receipt_service_tax' => '税费',
            'withhold_receipt_resp_payment_option' => '来自通道通知的支付类型',
            'withhold_receipt_resp_payment_mode' => '来自通道通知的支付方式',
            'withhold_receipt_expired_at' => 'Withhold Receipt Expired At',
            'withhold_receipt_biz_type' => '代收业务类型',
            'withhold_receipt_from_app' => 'Withhold Receipt From App',
            'withhold_receipt_account_uuid' => 'Withhold Receipt Account Uuid',
            'withhold_receipt_reconci_status' => '对账状态',
            'withhold_receipt_reconci_at' => '对账完成时间',
            'withhold_receipt_reconci_result' => '对账结果',
        ];
    }

    public function getChannel()
    {
        return $this->hasOne(Channel::class, ['channel_name' => 'withhold_receipt_channel_name']);
    }

    public const STATUS_LIST = [
        '新建',
        '处理中',
        '成功',
        '失败',
    ];
}
