<?php

namespace payment\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

class ChannelTransactionOrderSearch extends ChannelTransactionOrder
{
    public $finishedAtStartDate;
    public $finishedAtEndDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['finishedAtStartDate', 'finishedAtEndDate'], 'default', 'value' => Carbon::now()->toDateString()],
            [
                [
                    'provider_code',
                    'contract_entity',
                    'reconci_status',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['id'],
                'defaultOrder' => ['id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        $query->andFilterWhere([
            'provider_code' => $this->provider_code,
            'contract_entity' => $this->contract_entity,
            'reconci_status' => $this->reconci_status,
        ]);
        if ($this->finishedAtStartDate) {
            $query->andFilterWhere(['>=', 'finished_at', $this->finishedAtStartDate]);
        }
        if ($this->finishedAtEndDate) {
            $query->andFilterWhere(
                ['<=', 'finished_at', Carbon::parse($this->finishedAtEndDate)->endOfDay()->toDateTimeString()]
            );
        }

        return $dataProvider;
    }
}
