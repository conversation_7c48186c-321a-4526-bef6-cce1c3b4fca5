<?php

namespace payment\models;

use Carbon\Carbon;
use kvmanager\models\KeyValue;
use Yii;
use yii\behaviors\TimestampBehavior;


/**
 * This is the model class for table "{{%channel}}".
 *
 * @property int $channel_id
 * @property string $channel_name                       通道名称
 * @property string $channel_alias                      通道别名，中文名称
 * @property int $channel_type                       通道类型：0=鉴权 1=代扣 2=代付
 * @property int $channel_status                     通道状态：1=启用,0=未启用
 * @property int $channel_verify_priority            用于验证四要素/三要素的优先级：3=三要素，4=四要素
 * @property int $channel_need_charge_sms            是否需要支付验证码:1=需要，0=不需要
 * @property int $channel_need_binding               是否绑卡:1=需要，0=不需要
 * @property int $channel_need_binding_sms           是否需要绑卡验证码:1=需要，0=不需要
 * @property int $channel_provider_id                渠道
 * @property int $channel_provider_price_config_type 渠道产品
 * @property string $channel_created_at                 创建时间
 * @property string $channel_updated_at                 更新时间
 * @property string $channel_support_operator           USER：只支持主动还款，SYSTEM|MANAUL：只支持自动还款，USER|SYSTEM|MANAUL:支持主动自动操作
 * @property string $channel_sign_company_code          签约主体编号
 * @property string $channel_provider_code              渠道编号
 * @property string $channel_provider_product_type      产品类型
 * @property string $channel_merchant_no                渠道商户编号
 * @property-read SignCompany $signCompany
 * @property-read Provider $provider
 */
class Channel extends \yii\db\ActiveRecord
{
    public static function productTypeList()
    {
        return ProviderProduct::typeList();
    }

    public static function supportOperatorList()
    {
        return (array)KeyValue::take('channel_support_operator', 'paysvr');
    }

    public static function bindingSmsList()
    {
        return [
            '1' => '需要',
            '0' => '不需要',
        ];
    }

    public static function needBindingList()
    {
        return [
            '1' => '需要',
            '0' => '不需要',
        ];
    }

    public static function chargeSmsList()
    {
        return [
            '1' => '需要',
            '0' => '不需要',
        ];
    }

    public static function verifyPriorityList()
    {
        return [
            3 => '三要素',
            4 => '四要素',
        ];
    }

    public static function statusList()
    {
        return [
            '1' => '启用',
            '0' => '禁用',
        ];
    }

    public static function typeList()
    {
        return [
            0 => '鉴权',
            1 => '代扣',
            2 => '代付',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%channel}}';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'channel_created_at',
                'updatedAtAttribute' => null,
                'value' => Carbon::now()->toDateTimeString(),
            ],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'channel_type',
                    'channel_status',
                    'channel_verify_priority',
                    'channel_need_charge_sms',
                    'channel_need_binding',
                    'channel_need_binding_sms',
                    'channel_provider_id',
                    'channel_provider_price_config_type',
                ],
                'integer',
            ],
            [
                [
                    'channel_name',
                    'channel_alias',
                    'channel_sign_company_code',
                    'channel_provider_code',
                    'channel_provider_product_type',
                    'channel_merchant_no',
                ],
                'required',
            ],
            [
                [
                    'channel_provider_id',
                    'channel_provider_price_config_type',
                ],
                'default',
                'value' => 0,
            ],
            [['channel_created_at', 'channel_updated_at'], 'safe'],
            [['channel_name', 'channel_alias','channel_merchant_no'], 'string', 'max' => 64],
            [['channel_support_operator'], 'string', 'max' => 32],
            [
                ['channel_sign_company_code', 'channel_provider_code', 'channel_provider_product_type'],
                'string',
                'max' => 16,
            ],
            [['channel_name'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'channel_id' => Yii::t('payment', '主键'),
            'channel_name' => Yii::t('payment', '通道代码'),
            'channel_alias' => Yii::t('payment', '通道名称'),
            'channel_sign_company_code' => Yii::t('payment', '签约主体'),
            'channel_provider_code' => Yii::t('payment', '渠道'),
            'channel_provider_product_type' => Yii::t('payment', '产品类型'),
            'channel_status' => Yii::t('payment', '状态'),
            'channel_merchant_no' => Yii::t('payment', '渠道商户编号'),
            'channel_support_operator' => Yii::t('payment', '操作类型'),
            'channel_need_charge_sms' => Yii::t('payment', '支付验证码'),
            'channel_need_binding' => Yii::t('payment', '绑卡'),
            'channel_need_binding_sms' => Yii::t('payment', '绑卡验证码'),
            'channel_verify_priority' => Yii::t('payment', '验证优先级'),

            'channel_type' => Yii::t('payment', '通道类型'),
            'channel_provider_id' => Yii::t('payment', '渠道'),
            'channel_provider_price_config_type' => Yii::t('payment', '渠道产品'),
            'channel_created_at' => Yii::t('payment', '创建时间'),
            'channel_updated_at' => Yii::t('payment', '更新时间'),
        ];
    }

    public function getSignCompany()
    {
        return $this->hasOne(SignCompany::class, ['sign_company_code' => 'channel_sign_company_code']);
    }

    public function getProvider()
    {
        return $this->hasOne(Provider::class, ['provider_code' => 'channel_provider_code']);
    }
}
