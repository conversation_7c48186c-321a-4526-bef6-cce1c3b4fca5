<?php

namespace payment\models;

use yii\data\ActiveDataProvider;

class ChannelErrorReasonSearch extends ChannelErrorReason
{

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [

            [
                [
                    'channel_error_reason_provider_code',
                    'channel_error_reason_code',
                    'channel_error_reason_msg',
                    'channel_error_reason_reason'
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search(array $params): ActiveDataProvider
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => [
                    'channel_error_reason_id',
                ],
                'defaultOrder' => [
                    'channel_error_reason_id' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query
            ->andFilterWhere(['like', 'channel_error_reason_provider_code', $this->channel_error_reason_provider_code])
            ->andFilterWhere(['like', 'channel_error_reason_code', $this->channel_error_reason_code])
            ->andFilterWhere(['like', 'channel_error_reason_msg', $this->channel_error_reason_msg])
            ->andFilterWhere(['like', 'channel_error_reason_reason', $this->channel_error_reason_reason]);

        return $dataProvider;
    }
}
