<?php

namespace payment\models;

use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%withdraw}}".
 *
 * @property int $withdraw_id
 * @property string $withdraw_merchant_name           商户编号
 * @property string $withdraw_merchant_key            商户流水号
 * @property int $withdraw_status                  支付状态 0=新建, 1=处理中，2=成功，3=失败 4=冻结
 * @property int $withdraw_amount                  交易总金额(分)
 * @property string|null $withdraw_reason                  代付用途
 * @property string $withdraw_callback                业务方回调地址
 * @property string $withdraw_started_at              交易开始时间
 * @property string $withdraw_finished_at             交易结束时间
 * @property string $withdraw_created_at              创建时间
 * @property string $withdraw_updated_at              更新时间
 * @property string $withdraw_user_uuid               用户在用户中心ID，每个自然人在不通平台可能会有多个ID
 * @property int $withdraw_receiver_type           账户类型: 1=对私 2=对公
 * @property string $withdraw_receiver_name           收款户名
 * @property string $withdraw_receiver_name_mask
 * @property string $withdraw_receiver_no             收款帐号
 * @property string $withdraw_receiver_no_mask
 * @property string $withdraw_receiver_identity       收款人证件号
 * @property string $withdraw_receiver_identity_mask
 * @property string|null $withdraw_receiver_bank_no        收款人银行行号
 * @property string|null $withdraw_receiver_bank_name      收款人银行名称
 * @property string $withdraw_receiver_bank_code      收款帐号发卡行
 * @property string $withdraw_receiver_bank_branch    收款帐号发卡行分行
 * @property string $withdraw_receiver_bank_subbranch 收款帐号发卡行支行
 * @property string $withdraw_receiver_bank_province  收款帐号省份
 * @property string $withdraw_receiver_bank_city      收款帐号城市
 * @property array<WithdrawReceipt> $withdrawReceipts      收款帐号城市
 */
class Withdraw extends \yii\db\ActiveRecord
{
    public const STATUS_LIST = ['新建', '处理中', '成功', '失败', '冻结'];
    public const RECEIVER_TYPE_LIST = [
        1 => '对私',
        2 => '对公',
        3 => '转账',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%withdraw}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withdraw_merchant_name', 'withdraw_merchant_key'], 'required'],
            [['withdraw_status', 'withdraw_amount'], 'integer'],
            [['withdraw_started_at', 'withdraw_finished_at', 'withdraw_created_at', 'withdraw_updated_at'], 'safe'],
            [['withdraw_merchant_name', 'withdraw_merchant_key', 'withdraw_user_uuid'], 'string', 'max' => 64],
            [['withdraw_reason'], 'string', 'max' => 128],
            [['withdraw_callback'], 'string', 'max' => 1024],
            [
                ['withdraw_merchant_key', 'withdraw_merchant_name'],
                'unique',
                'targetAttribute' => ['withdraw_merchant_key', 'withdraw_merchant_name'],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_id' => 'ID',
            'withdraw_merchant_name' => '商户编号',
            'withdraw_merchant_key' => '商户流水号',
            'withdraw_status' => '支付状态',
            'withdraw_amount' => '交易总金额',
            'withdraw_reason' => '代付用途',
            'withdraw_callback' => '业务方回调地址',
            'withdraw_started_at' => '交易开始时间',
            'withdraw_finished_at' => '交易结束时间',
            'withdraw_created_at' => '创建时间',
            'withdraw_updated_at' => '更新时间',
            'withdraw_user_uuid' => '用户中心ID',
            'withdraw_receiver_type' => '账户类型',
            'withdraw_receiver_name' => '收款户名',
            'withdraw_receiver_name_mask' => 'Withdraw Receiver Name Mask',
            'withdraw_receiver_no' => '收款帐号',
            'withdraw_receiver_no_mask' => 'Withdraw Receiver No Mask',
            'withdraw_receiver_identity' => '收款证件号',
            'withdraw_receiver_identity_mask' => 'Withdraw Receiver Identity Mask',
            'withdraw_receiver_bank_no' => '收款人银行行号',
            'withdraw_receiver_bank_name' => '收款账号银行',
            'withdraw_receiver_bank_code' => '收款帐号发卡行',
            'withdraw_receiver_bank_branch' => '收款帐号发卡行分行',
            'withdraw_receiver_bank_subbranch' => '收款帐号发卡行支行',
            'withdraw_receiver_bank_province' => '收款帐号省份',
            'withdraw_receiver_bank_city' => '收款帐号城市',
            'withdraw_card_uuid' => 'CardUUID',
        ];
    }

    public function getWithdrawReceipts(): ActiveQuery
    {
        return $this->hasMany(WithdrawReceipt::class, [
            'withdraw_receipt_merchant_key' => 'withdraw_merchant_key',
            'withdraw_receipt_merchant_name' => 'withdraw_merchant_name',
        ]);
    }
}
