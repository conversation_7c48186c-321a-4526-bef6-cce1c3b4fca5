<?php

namespace payment\models;

use xlerr\desensitise\Desensitise;
use Yii;
use yii\base\Event;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

use yii\web\JsExpression;

use function xlerr\desensitise\encrypt;

/**
 * This is the model class for table "channel_account".
 *
 * @property int         $channel_account_id
 * @property string      $channel_account_account_uuid  银行账户编码
 * @property string      $channel_account_provider_code 渠道编码
 * @property string      $channel_account_user_name     用户名
 * @property string      $channel_account_account       银行账号
 * @property string      $channel_account_upi_id        UPI账户ID
 * @property string      $channel_account_bank_code     银行编码
 * @property string      $channel_account_bank_name     银行名称
 * @property string|null $channel_account_mobile        手机号
 * @property string|null $channel_account_device_id
 * @property string|null $channel_account_email         电子邮箱
 * @property int         $channel_account_status        状态 0 无效 1 有效
 * @property int         $channel_account_balance       余额
 * @property int         $channel_account_quota         日限额
 * @property int         $channel_account_max_balance   水位
 * @property string      $channel_account_auth_mode     卡类型 account, ups
 * @property int         $channel_account_purpose       卡用途,1:放款，2:收款，3:放款与收款
 * @property int         $channel_account_score         评分
 * @property int         $channel_account_route_wd_num  放款路由命中次数
 * @property int         $channel_account_route_wh_num  收款路由命中次数
 * @property string|null $channel_account_create_at     创建时间
 * @property string|null $channel_account_update_at     修改时间
 * @property string|null $channel_account_config        账户配置
 * @property-read Bank   $bank
 */
class ChannelAccount extends ActiveRecord
{
    public const STATUS_INVALID = 0;
    public const STATUS_VALID = 1;
    public const STATUS_LIST = [
        self::STATUS_INVALID => '禁用',
        self::STATUS_VALID => '启用',
    ];

    public const PURPOSE_GRANT = 1;
    public const PURPOSE_REPAY = 2;
    public const PURPOSE_GRANT_AND_REPAY = 3;
    public const PURPOSE_LIST = [
        self::PURPOSE_GRANT => '放款',
        self::PURPOSE_REPAY => '还款',
        self::PURPOSE_GRANT_AND_REPAY => '放款+还款',
    ];

    public const AUTH_MODE_LIST = [
        'account' => '银行卡',
        'upi' => 'UPI',
    ];

    /**
     * @return void
     */
    public function init(): void
    {
        parent::init();

        $encrypt = function (Event $event) {
            /** @var self $model */
            $model = $event->sender;

            $model->channel_account_user_name = encrypt(
                $model->channel_account_user_name,
                Desensitise::TYPE_NAME
            )->hash;
            $model->channel_account_account = encrypt(
                $model->channel_account_account,
                Desensitise::TYPE_BANK_CARD_NUMBER
            )->hash;

            if ($model->channel_account_mobile) {
                $model->channel_account_mobile = encrypt(
                    $model->channel_account_mobile,
                    Desensitise::TYPE_PHONE_NUMBER
                )->hash;
            }

            if ($model->channel_account_upi_id) {
                $model->channel_account_upi_id = encrypt(
                    $model->channel_account_upi_id,
                    Desensitise::TYPE_BANK_CARD_NUMBER
                )->hash;
            }
        };

        $this->on(self::EVENT_BEFORE_INSERT, $encrypt);
        $this->on(self::EVENT_BEFORE_UPDATE, $encrypt);
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'channel_account';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'channel_account_account_uuid',
                    'channel_account_provider_code',
                    'channel_account_account',
                    'channel_account_user_name',
                    'channel_account_bank_code',
                    'channel_account_purpose',
                    'channel_account_auth_mode',
                ],
                'required',
            ],
            [
                [
                    'channel_account_status',
                    'channel_account_balance',
                    'channel_account_quota',
                    'channel_account_max_balance',
                    'channel_account_purpose',
                    'channel_account_score',
                    'channel_account_route_wd_num',
                    'channel_account_route_wh_num',
                ],
                'integer',
            ],
            [['channel_account_create_at', 'channel_account_update_at'], 'safe'],
            [['channel_account_config'], 'string'],
            [
                ['channel_account_upi_id'],
                'required',
                'when'       => fn($model) => $model->channel_account_auth_mode === 'upi',
                'whenClient' => new JsExpression("() => \$('#channelaccount-channel_account_auth_mode').val() === 'upi'"),
            ],
            [
                [
                    'channel_account_account_uuid',
                    'channel_account_account',
                    'channel_account_upi_id',
                    'channel_account_device_id',
                ],
                'string',
                'max' => 32,
            ],
            [
                ['channel_account_provider_code', 'channel_account_bank_code', 'channel_account_auth_mode'],
                'string',
                'max' => 16,
            ],
            [
                [
                    'channel_account_user_name',
                    'channel_account_bank_name',
                    'channel_account_mobile',
                    'channel_account_email',
                ],
                'string',
                'max' => 64,
            ],
            [['channel_account_account_uuid'], 'unique'],
            [
                ['channel_account_account', 'channel_account_bank_code', 'channel_account_auth_mode'],
                'unique',
                'targetAttribute' => [
                    'channel_account_account',
                    'channel_account_bank_code',
                    'channel_account_auth_mode',
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'channel_account_id' => 'ID',
            'channel_account_account_uuid' => '银行账户编码',
            'channel_account_provider_code' => '渠道编码',
            'channel_account_user_name' => '账户名称',
            'channel_account_account' => '银行账号',
            'channel_account_upi_id' => 'UPI ID',
            'channel_account_bank_code' => 'IFSC Code',
            'channel_account_bank_name' => '银行名称',
            'channel_account_mobile' => '手机号',
            'channel_account_device_id' => 'Channel Account Device ID',
            'channel_account_email' => '电子邮箱',
            'channel_account_status' => '状态',
            'channel_account_balance' => '余额',
            'channel_account_quota' => '单笔转账日限额',
            'channel_account_max_balance' => '水位',
            'channel_account_auth_mode' => '收款账户类型',
            'channel_account_purpose' => '业务类型',
            'channel_account_score' => '评分',
            'channel_account_route_wd_num' => '放款路由命中次数',
            'channel_account_route_wh_num' => '收款路由命中次数',
            'channel_account_create_at' => '创建时间',
            'channel_account_update_at' => '修改时间',
            'channel_account_config' => '账户配置',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getBank(): ActiveQuery
    {
        return $this->hasOne(Bank::class, ['bank_code' => 'channel_account_provider_code']);
    }
}
