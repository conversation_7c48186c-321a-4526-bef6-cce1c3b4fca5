<?php

namespace payment\models;

use backend\behaviors\FormatterBehavior;
use common\models\User;
use kvmanager\models\KeyValue;
use xlerr\common\db\SaveTrait;
use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\BlameableBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "channel_offline_trade".
 *
 * @property int $channel_offline_trade_id  primary key
 * @property string $channel_offline_trade_order_no order no
 * @property string $channel_offline_trade_capital_order_no order no
 * @property string $channel_offline_trade_channel_name channel name
 * @property float $channel_offline_trade_amount trade amount
 * @property string $channel_offline_trade_busi_type business type
 * @property string $channel_offline_trade_pay_type pay type
 * @property string $channel_offline_trade_finish_at  finish time
 * @property string $channel_offline_trade_comment comment
 * @property int $channel_offline_trade_oa_tenant_id OA tenant ID
 * @property int $channel_offline_trade_oa_id OA ID
 * @property string $channel_offline_trade_create_user creator
 * @property string $channel_offline_trade_create_at create time
 * @property string $channel_offline_trade_update_at  update time
 * @property string $channel_offline_trade_order_from_type
 * @property string $channel_offline_trade_reconci_status
 * @property string $channel_offline_trade_reconci_at
 * @property string $channel_offline_trade_reconci_result
 * @property string $in_account_number
 * @property string $out_account_number
 * @property string $in_account_name
 * @property string $out_account_name
 * @property string $currency
 * @property User $createUser
 */
class ChannelOfflineTrade extends ActiveRecord
{
    use SaveTrait;

    public const RECONCI_STATUS_OPEN = 'open';
    public const RECONCI_STATUS_PROCESS = 'process';
    public const RECONCI_STATUS_PENDING = 'pending';
    public const RECONCI_STATUS_SUCCESS = 'success';
    public const RECONCI_STATUS_COMPLETE = 'complete';

    public const RECONCI_STATUS_MAP = [
        self::RECONCI_STATUS_OPEN => '待对账',
        self::RECONCI_STATUS_PROCESS => '处理中',
        self::RECONCI_STATUS_PENDING => '对账失败',
        self::RECONCI_STATUS_SUCCESS => '对账成功',
        self::RECONCI_STATUS_COMPLETE => '无需对账',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'channel_offline_trade';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * @inheritDoc
     * @return array[]
     */
    public function behaviors()
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'channel_offline_trade_create_user',
                'updatedByAttribute' => null,
                'value' => function () {
                    if (Yii::$app->has('user')) {
                        return (int)Yii::$app->user->id;
                    }

                    return 0;
                },
            ],
        ];
    }

    public static function getPaymentChannel()
    {
        $config = KeyValue::take('withdraw_channel_account_map');

        $paymentChannelList = array_keys($config);

        return array_combine($paymentChannelList, $paymentChannelList);
    }

    public function transactions()
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        /**
         * @var FormatterBehavior $formatter
         */
        $formatter = Yii::$app->getFormatter();

        return [
            [
                [
                    'channel_offline_trade_order_no',
                    'channel_offline_trade_amount',
                    'channel_offline_trade_finish_at',
                    'channel_offline_trade_busi_type',
                    'channel_offline_trade_channel_name',
                    'in_account_number',
                    'out_account_number',
                    'in_account_name',
                    'out_account_name',
                ],
                'required',
            ],
            [['channel_offline_trade_oa_tenant_id', 'channel_offline_trade_oa_id'], 'default', 'value' => 0],
            [['channel_offline_trade_amount'], 'number'],
            [
                [
                    'channel_offline_trade_finish_at',
                    'channel_offline_trade_create_at',
                    'channel_offline_trade_update_at',
                ],
                'safe',
            ],
            [['channel_offline_trade_oa_tenant_id', 'channel_offline_trade_oa_id'], 'integer'],
            [
                [
                    'channel_offline_trade_order_no',
                    'channel_offline_trade_capital_order_no',
                    'channel_offline_trade_channel_name',
                    'channel_offline_trade_busi_type',
                ],
                'string',
                'max' => 64,
            ],
            [['currency'], 'string', 'max' => 32],
            [['channel_offline_trade_create_user'], 'safe'],
            [['channel_offline_trade_comment'], 'string', 'max' => 256],
            [['channel_offline_trade_order_no'], 'unique'],
            [
                ['channel_offline_trade_amount'],
                'number',
                'min' => 1,
                'max' => 99999999900,
                'tooSmall' => vsprintf('金额不能低于%s%s', [
                    $formatter->asFormatAmount(1, true),
                    FormatterBehavior::currencyUnit(),
                ]),
                'tooBig' => vsprintf('金额不能超过%s%s', [
                    $formatter->asFormatAmount(99999999900, true),
                    FormatterBehavior::currencyUnit(),
                ]),
            ],
            [['channel_offline_trade_reconci_status'], 'string', 'max' => 16],
            [['channel_offline_trade_reconci_result'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'channel_offline_trade_id' => 'ID',
            'channel_offline_trade_order_no' => '订单号',
            'channel_offline_trade_capital_order_no' => '财务系统订单号',
            'channel_offline_trade_channel_name' => '通道名称',
            'channel_offline_trade_amount' => '金额',
            'channel_offline_trade_busi_type' => '业务类型',
            'channel_offline_trade_pay_type' => '付款类型',
            'channel_offline_trade_finish_at' => '完成时间',
            'channel_offline_trade_comment' => '备注',
            'channel_offline_trade_oa_tenant_id' => '租户ID',
            'channel_offline_trade_oa_id' => 'OA ID',
            'channel_offline_trade_create_user' => '创建人',
            'channel_offline_trade_create_at' => '创建时间',
            'channel_offline_trade_update_at' => '更新时间',
            'channel_offline_trade_order_from_type' => '交易类型',
            'channel_offline_trade_reconci_status' => '对账状态',
            'channel_offline_trade_reconci_at' => '对账完成时间',
            'channel_offline_trade_reconci_result' => '对账结果',
            'in_account_number' => '收款账户号',
            'out_account_number' => '出款账户号',
            'in_account_name' => '收款账户名称',
            'out_account_name' => '出款账户名称',
            'currency' => '币种',
        ];
    }

    public function getCreateUser(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'channel_offline_trade_create_user']);
    }
}
