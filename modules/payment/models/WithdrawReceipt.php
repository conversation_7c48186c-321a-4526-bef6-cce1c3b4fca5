<?php

namespace payment\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "{{%withdraw_receipt}}".
 *
 * @property int          $withdraw_receipt_id
 * @property string       $withdraw_receipt_merchant_name        商户编号
 * @property string       $withdraw_receipt_merchant_key         商户流水号
 * @property string       $withdraw_receipt_card_num             虚拟卡号
 * @property string       $withdraw_receipt_id_num               Certificate number
 * @property int          $withdraw_receipt_amount               付款金额（分）
 * @property int          $withdraw_receipt_status               支付状态 0=新建, 1=处理中，2=成功，3=失败 4=冻结
 * @property string       $withdraw_receipt_channel_name         渠道名称
 * @property string       $withdraw_receipt_channel_key          渠道订单号
 * @property string       $withdraw_receipt_channel_resp_code    渠道状态码
 * @property string       $withdraw_receipt_channel_resp_message 渠道错误消息
 * @property string       $withdraw_receipt_started_at           发起时间
 * @property string       $withdraw_receipt_finished_at          完成时间
 * @property string       $withdraw_receipt_created_at           创建时间
 * @property string       $withdraw_receipt_updated_at           更新时间
 * @property string       $withdraw_receipt_trade_no             请求流水号(业务幂等)
 * @property string       $withdraw_receipt_redirect             跳转URL
 * @property string|null  $withdraw_receipt_transfer_option      Transfer type
 * @property string       $withdraw_receipt_transfer_mode        转账方式，如banktransfer, upi, paytm, amazonpay等
 * @property string       $withdraw_receipt_channel_inner_key
 * @property int          $withdraw_receipt_service_charge       Service fee (sen)
 * @property int          $withdraw_receipt_service_tax          Tax (sen)
 * @property string       $withdraw_receipt_utr                  Bank statement
 * @property string       $withdraw_receipt_expired_at           Transaction expiration time
 * @property string|null  $withdraw_receipt_resp_transfer_mode   Loan type from channel notification
 * @property string       $withdraw_receipt_user_uuid            User center user unique id
 * @property string       $withdraw_receipt_card_uuid            User center card unique id
 * @property string       $withdraw_receipt_type                 Loan type: online=online, offline=offline
 * @property string       $withdraw_receipt_biz_type             Loan service type
 * @property string|null  $withdraw_receipt_from_app             source app
 * @property string       $withdraw_receipt_account_uuid         UUID of the lending account
 * @property int          $withdraw_receipt_retrytimes           Number of retries
 * @property int          $withdraw_receipt_balance              Balance
 * @property int|null     $withdraw_receipt_reverse_status       冲正状态
 * @property string       $withdraw_receipt_reverse_at           冲正时间
 * @property string       $withdraw_receipt_reconci_status       对账状态: open,process,pending,success
 * @property string       $withdraw_receipt_reconci_at           对账完成时间
 * @property string|null  $withdraw_receipt_reconci_result       对账结果
 * @property-read Channel $channel
 */
class WithdrawReceipt extends ActiveRecord
{
    public const RECONCI_STATUS_OPEN = 'open';
    public const RECONCI_STATUS_PROCESS = 'process';
    public const RECONCI_STATUS_PENDING = 'pending';
    public const RECONCI_STATUS_SUCCESS = 'success';
    public const RECONCI_STATUS_COMPLETE = 'complete';
    public const RECONCI_STATUS_IS_RECONCILED = 'is_reconciled';

    public const RECONCI_STATUS_MAP = [
        self::RECONCI_STATUS_OPEN    => '待对账',
        self::RECONCI_STATUS_PROCESS => '处理中',
        self::RECONCI_STATUS_PENDING => '对账失败',
        self::RECONCI_STATUS_SUCCESS => '对账成功',
        self::RECONCI_STATUS_COMPLETE => '无需对账',
    ];

    public const STATUS_LIST = ['新建', '处理中', '成功', '失败', '冻结'];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%withdraw_receipt}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'withdraw_receipt_merchant_name',
                    'withdraw_receipt_merchant_key',
                    'withdraw_receipt_amount',
                    'withdraw_receipt_channel_name',
                ],
                'required',
            ],
            [['withdraw_receipt_amount', 'withdraw_receipt_status'], 'integer'],
            [
                [
                    'withdraw_receipt_started_at',
                    'withdraw_receipt_finished_at',
                    'withdraw_receipt_created_at',
                    'withdraw_receipt_updated_at',
                ],
                'safe',
            ],
            [
                [
                    'withdraw_receipt_merchant_name',
                    'withdraw_receipt_merchant_key',
                    'withdraw_receipt_channel_name',
                    'withdraw_receipt_channel_key',
                    'withdraw_receipt_trade_no',
                ],
                'string',
                'max' => 64,
            ],
            [
                ['withdraw_receipt_card_num', 'withdraw_receipt_channel_resp_code', 'withdraw_receipt_transfer_mode'],
                'string',
                'max' => 32,
            ],
            [['withdraw_receipt_channel_resp_message'], 'string', 'max' => 255],
            [['withdraw_receipt_redirect'], 'string', 'max' => 512],
            [
                ['withdraw_receipt_channel_key', 'withdraw_receipt_channel_name'],
                'unique',
                'targetAttribute' => ['withdraw_receipt_channel_key', 'withdraw_receipt_channel_name'],
            ],
            [['withdraw_receipt_trade_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_receipt_id' => Yii::t('payment', 'ID'),
            'withdraw_receipt_merchant_name' => Yii::t('payment', '商户编号'),
            'withdraw_receipt_merchant_key' => Yii::t('payment', '商户流水号'),
            'withdraw_receipt_card_num' => Yii::t('payment', '虚拟卡号'),
            'withdraw_receipt_id_num' => 'Withdraw Receipt Id Num',
            'withdraw_receipt_amount' => Yii::t('payment', '付款金额'),
            'withdraw_receipt_status' => Yii::t('payment', '支付状态'),
            'withdraw_receipt_channel_name' => Yii::t('payment', '支付通道'),
            'withdraw_receipt_channel_key' => Yii::t('payment', '支付通道订单号'),
            'withdraw_receipt_channel_resp_code' => Yii::t('payment', '支付通道返回码'),
            'withdraw_receipt_channel_resp_message' => Yii::t('payment', '支付通道返回信息'),
            'withdraw_receipt_started_at' => Yii::t('payment', '发起时间'),
            'withdraw_receipt_finished_at' => Yii::t('payment', '完成时间'),
            'withdraw_receipt_created_at' => Yii::t('payment', '创建时间'),
            'withdraw_receipt_updated_at' => Yii::t('payment', '更新时间'),
            'withdraw_receipt_trade_no' => Yii::t('payment', '请求流水号(业务幂等)'),
            'withdraw_receipt_redirect' => Yii::t('payment', '跳转URL'),
            'withdraw_receipt_transfer_option' => 'Withdraw Receipt Transfer Option',
            'withdraw_receipt_transfer_mode' => Yii::t('payment', '转账方式'),
            'withdraw_receipt_channel_inner_key' => Yii::t('payment', '支付通道内部订单号'),
            'withdraw_receipt_service_charge' => '服务费',
            'withdraw_receipt_service_tax' => '税费',
            'withdraw_receipt_utr' => 'Withdraw Receipt Utr',
            'withdraw_receipt_expired_at' => 'Withdraw Receipt Expired At',
            'withdraw_receipt_resp_transfer_mode' => 'Withdraw Receipt Resp Transfer Mode',
            'withdraw_receipt_user_uuid' => 'Withdraw Receipt User Uuid',
            'withdraw_receipt_card_uuid' => Yii::t('payment', 'cardUUID'),
            'withdraw_receipt_type' => 'Withdraw Receipt Type',
            'withdraw_receipt_biz_type' => '代付业务类型',
            'withdraw_receipt_from_app' => 'Withdraw Receipt From App',
            'withdraw_receipt_account_uuid' => 'Withdraw Receipt Account Uuid',
            'withdraw_receipt_retrytimes' => 'Withdraw Receipt Retrytimes',
            'withdraw_receipt_balance' => 'Withdraw Receipt Balance',
            'withdraw_receipt_reverse_status' => '冲正状态',
            'withdraw_receipt_reverse_at' => '冲正时间',
            'withdraw_receipt_reconci_status' => '对账状态',
            'withdraw_receipt_reconci_at' => '对账完成时间',
            'withdraw_receipt_reconci_result' => '对账结果',
        ];
    }

    public function getChannel()
    {
        return $this->hasOne(Channel::class, ['channel_name' => 'withdraw_receipt_channel_name']);
    }
}
