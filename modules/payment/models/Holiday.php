<?php

namespace payment\models;

use Carbon\Carbon;
use Carbon\Traits\Creator;
use DateTimeInterface;
use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "holiday".
 *
 * @property int $holiday_id
 * @property string $holiday_date
 * @property int $holiday_status
 * @property string $holiday_create_at
 * @property string $holiday_update_at
 */
class Holiday extends ActiveRecord
{
    public const STATUS_WORKING = 0; // 上班
    public const STATUS_REST = 1; // 休假
    public const RESPONSE_SUCCESS = 0;    // 成功
    public const RESPONSE_FAIL = 1;    // 失败
    public const DATE_TYPE_WEEKDAY = 0;
    public const DATE_TYPE_WEEKEND = 1;
    public const DATE_TYPE_HOLIDAY = 2;


    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'holiday';
    }

    /**
     * @return Connection
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return instanceEnsureDb('paySvrDb');
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['holiday_date', 'holiday_status'], 'required'],
            [['holiday_date', 'holiday_create_at', 'holiday_update_at'], 'safe'],
            [['holiday_status'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'holiday_id' => '主键',
            'holiday_date' => '时间',
            'holiday_status' => '状态(0-工作，1-休息)',
            'holiday_create_at' => '创建时间',
            'holiday_update_at' => '更新时间',
        ];
    }

    /**
     * @param string|DateTimeInterface|Creator $date
     *
     * @return array
     */
    public static function holidayVerify($date): array
    {
        $date = Carbon::parse($date);

        $weekDay = $date->dayOfWeek;
        $holiday = self::findOne(['holiday_date' => $date->toDateString()]);
        if ($holiday) {
            if ((int)$holiday->holiday_status === self::STATUS_REST) {
                return [
                    'code' => self::RESPONSE_SUCCESS,
                    'message' => '法定节假日',
                    'data' => [
                        'type' => self::DATE_TYPE_HOLIDAY,
                        'week' => $weekDay,
                    ],
                ];
            }

            return [
                'code' => self::RESPONSE_SUCCESS,
                'message' => '工作日',
                'data' => [
                    'type' => self::DATE_TYPE_WEEKDAY,
                    'week' => $weekDay,
                ],
            ];
        }

        if ($weekDay > 0 && $weekDay < 6) {
            return [
                'code' => self::RESPONSE_SUCCESS,
                'message' => '工作日',
                'data' => [
                    'type' => self::DATE_TYPE_WEEKDAY,
                    'week' => $weekDay,
                ],
            ];
        }

        return [
            'code' => self::RESPONSE_SUCCESS,
            'message' => '周末',
            'data' => [
                'type' => self::DATE_TYPE_WEEKEND,
                'week' => $weekDay,
            ],
        ];
    }

    /**
     * 判断日期是否是周末和节假日
     *
     * @param string|DateTimeInterface|Creator $date
     *
     * @return bool
     */
    public static function isHoliday($date): bool
    {
        $result = self::holidayVerify($date);

        return $result['data']['type'] !== self::DATE_TYPE_WEEKDAY;
    }

    /**
     * 第n个工作日
     *
     * @param string|DateTimeInterface|Creator $date 日期
     * @param int $n
     *
     * @return string
     */
    public static function nthWorkingDay($date, int $n): string
    {
        $date = Carbon::parse($date);

        for ($i = 0; $i < $n; $i++) {
            $date = $date->addDay();
            if (self::isHoliday($date)) {
                ++$n;
            }
        }

        return $date->toDateString();
    }

    /**
     * @param $date
     *
     * @return array|Holiday[]|ActiveRecord[]
     */
    public static function queryFutureHoliday($date): array
    {
        return self::find()->where(['>=', 'holiday_date', $date])->all();
    }

    /**
     * @param $date
     *
     * @return array|Holiday[]|ActiveRecord[]
     */
    public static function queryUpdatedHoliday($date): array
    {
        return self::find()->where(['>', 'holiday_update_at', $date])->all();
    }
}
