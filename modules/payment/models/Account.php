<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "{{%account}}".
 *
 * @property int $account_id
 * @property string $account_card_uuid 账户UUID
 * @property string $account_card_num 虚拟卡号，系统唯一
 * @property string $account_created_at 创建时间
 * @property string $account_updated_at 更新时间
 * @property string $account_auth_mode 认证模式: account=银行账户 card=银行卡  upi=统一支付接口
 */
class Account extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%account}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['account_card_uuid', 'account_card_num'], 'required'],
            [['account_created_at', 'account_updated_at'], 'safe'],
            [['account_auth_mode'], 'string'],
            [['account_card_uuid', 'account_card_num'], 'string', 'max' => 32],
            [['account_card_uuid', 'account_card_num'], 'unique', 'targetAttribute' => ['account_card_uuid', 'account_card_num']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'account_id' => Yii::t('payment', 'Account ID'),
            'account_card_uuid' => Yii::t('payment', '账户UUID'),
            'account_card_num' => Yii::t('payment', '虚拟卡号，系统唯一'),
            'account_created_at' => Yii::t('payment', '创建时间'),
            'account_updated_at' => Yii::t('payment', '更新时间'),
            'account_auth_mode' => Yii::t('payment', '认证模式: account=银行账户 card=银行卡  upi=统一支付接口'),
        ];
    }
}
