<?php

namespace payment\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * WithdrawReceiptSearch represents the model behind the search form about `payment\models\WithdrawReceipt`.
 */
class WithdrawReceiptSearch extends WithdrawReceipt
{
    public $finishedAtStartDate;
    public $finishedAtEndDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['withdraw_receipt_merchant_key', 'finishedAtStartDate', 'finishedAtEndDate', 'withdraw_receipt_reconci_status', 'withdraw_receipt_channel_name'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = WithdrawReceipt::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['withdraw_receipt_id'],
                'defaultOrder' => ['withdraw_receipt_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'withdraw_receipt_merchant_key' => $this->withdraw_receipt_merchant_key,
            'withdraw_receipt_reconci_status' => $this->withdraw_receipt_reconci_status,
        ]);

        if ($this->finishedAtStartDate) {
            $query->andFilterWhere(['>=', 'withdraw_receipt_finished_at', $this->finishedAtStartDate]);
        }
        if ($this->finishedAtEndDate) {
            $query->andFilterWhere(
                [
                    '<=',
                    'withdraw_receipt_finished_at',
                    Carbon::parse($this->finishedAtEndDate)->endOfDay()->toDateTimeString(),
                ]
            );
        }

        return $dataProvider;
    }
}
