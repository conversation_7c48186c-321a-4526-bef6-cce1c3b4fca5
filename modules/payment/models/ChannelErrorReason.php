<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "channel_error_reason".
 *
 * @property int $channel_error_reason_id
 * @property string $channel_error_reason_provider_code 渠道编码
 * @property string $channel_error_reason_code 通道错误编码
 * @property string $channel_error_reason_msg 通道错误消息
 * @property string $channel_error_reason_created_at 创建时间
 * @property string $channel_error_reason_updated_at 更新时间
 * @property string $channel_error_reason_reason 失败理由
 */
class ChannelErrorReason extends \yii\db\ActiveRecord
{

    public const ERROR_STATUS_LIST = [
        0 => '成功',
        1 => '失败',
        2 => '处理中',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'channel_error_reason';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['channel_error_reason_code', 'channel_error_reason_msg', 'channel_error_reason_reason'], 'required'],
            [['channel_error_reason_created_at', 'channel_error_reason_provider_code', 'channel_error_reason_updated_at'], 'safe'],
            [['channel_error_reason_provider_code'], 'string', 'max' => 64],
            [['channel_error_reason_code'], 'string', 'max' => 190],
            [['channel_error_reason_msg'], 'string', 'max' => 200],
            [['channel_error_reason_reason'], 'string', 'max' => 512],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'channel_error_reason_id' => 'ID',
            'channel_error_reason_provider_code' => '渠道编码',
            'channel_error_reason_code' => '通道错误编码',
            'channel_error_reason_msg' => '通道错误消息',
            'channel_error_reason_created_at' => '创建时间',
            'channel_error_reason_updated_at' => '更新时间',
            'channel_error_reason_reason' => '失败理由',
        ];
    }
}
