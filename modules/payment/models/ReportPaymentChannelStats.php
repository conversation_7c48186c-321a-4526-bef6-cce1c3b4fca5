<?php

namespace payment\models;

use dashboard\traits\ReportModelTrait;
use Yii;

/**
 * This is the model class for table "report_payment_channel_stats".
 *
 * @property int $id ID
 * @property string $date 日期
 * @property string $type 类型
 * @property int $values 值
 * @property string $channel_name 通道名称
 * @property string $sign_company_name 主体名称
 * @property string $payment_option 支付渠道类型
 * @property string $payment_mode 支付渠道
 * @property string|null $payment_method 支付方式
 * @property string $failure_reason 失败理由
 * @property string $created_at 创建时间
 * @property string $updated_at 修改时间
 * @property int $data_source_id 数据源编号
 */
class ReportPaymentChannelStats extends \yii\db\ActiveRecord
{
    use ReportModelTrait;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'report_payment_channel_stats';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['date', 'created_at', 'updated_at'], 'safe'],
            [['values', 'data_source_id'], 'integer'],
            [['type', 'payment_option', 'payment_mode', 'payment_method'], 'string', 'max' => 64],
            [['channel_name', 'sign_company_name'], 'string', 'max' => 32],
            [['failure_reason'], 'string', 'max' => 128],
            [['date', 'type', 'channel_name', 'sign_company_name', 'payment_method', 'payment_option', 'payment_mode', 'failure_reason'], 'unique', 'targetAttribute' => ['date', 'type', 'channel_name', 'sign_company_name', 'payment_method', 'payment_option', 'payment_mode', 'failure_reason']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'type' => '类型',
            'values' => '值',
            'channel_name' => '通道名称',
            'sign_company_name' => '主体名称',
            'payment_option' => '支付渠道类型',
            'payment_mode' => '支付渠道',
            'payment_method' => '支付方式',
            'failure_reason' => '失败理由',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
            'data_source_id' => '数据源编号',
        ];
    }

    public static function getPaymentMethod(): array
    {
        return self::find()->select('payment_method')->indexBy('payment_method')->distinct()->column();
    }

    public static function getPaymentMode(): array
    {
        return self::find()->select('payment_mode')->indexBy('payment_mode')->distinct()->column();
    }

    public static function getChannel(): array
    {
        return self::find()->select('channel_name')->indexBy('channel_name')->distinct()->column();
    }

    public static function getSignCompany(): array
    {
        return self::find()->select('sign_company_name')->indexBy('sign_company_name')->distinct()->column();
    }
}
