<?php

namespace payment\models;

use Carbon\Carbon;
use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "{{%provider_bank}}".
 *
 * @property int    $provider_bank_id
 * @property string $provider_bank_code               渠道银行编码
 * @property string $provider_bank_name               渠道银行名称
 * @property string $provider_bank_provider_code      渠道编码
 * @property string $provider_bank_platform_bank_code paysvr银行编码
 * @property string $provider_bank_product_type       适用产品类型：withhold,quick,protocol,withdraw,web
 * @property string $provider_bank_created_at         创建时间
 * @property string $provider_bank_updated_at         更新时间
 * @property string $provider_bank_status             状态
 * @property string $provider_bank_score             渠道银行评分
 */
class ProviderBank extends \yii\db\ActiveRecord
{
    public const STATUS_OPEN  = 'open';
    public const STATUS_CLOSE = 'close';
    public const STATUS_LIST  = [
        self::STATUS_OPEN  => '启用',
        self::STATUS_CLOSE => '禁用',
    ];

    public function behaviors()
    {
        return [
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => 'provider_bank_created_at',
                'updatedAtAttribute' => 'provider_bank_updated_at',
                'value'              => function () {
                    return Carbon::now()->toDateTimeString();
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%provider_bank}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['provider_bank_created_at', 'provider_bank_updated_at'], 'safe'],
            [
                [
                    'provider_bank_code',
                    'provider_bank_name',
                    'provider_bank_provider_code',
                    'provider_bank_platform_bank_code',
                    'provider_bank_status',
                ],
                'string',
                'max' => 64,
            ],
            [['provider_bank_product_type'], 'string', 'max' => 128],
            [['provider_bank_score'],'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'provider_bank_id'                 => 'Provider Bank ID',
            'provider_bank_code'               => '渠道银行编码',
            'provider_bank_name'               => '渠道银行名称',
            'provider_bank_provider_code'      => '渠道编码',
            'provider_bank_platform_bank_code' => 'paysvr银行编码',
            'provider_bank_product_type'       => '适用产品类型',
            'provider_bank_created_at'         => '创建时间',
            'provider_bank_updated_at'         => '更新时间',
            'provider_bank_status'             => '状态',
            'provider_bank_score'             => '渠道银行评分',
        ];
    }
}
