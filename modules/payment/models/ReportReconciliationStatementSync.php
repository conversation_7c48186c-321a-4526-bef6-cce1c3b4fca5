<?php

namespace payment\models;

use datasource\DataSourceContext;
use datasource\interfaces\ReportMonitorInterface;
use datasource\models\DataSource;
use yii\base\UserException;

class ReportReconciliationStatementSync extends ReportReconciliationStatement implements ReportMonitorInterface
{
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        $currentCountry = DataSource::currentCountry();
        if ($currentCountry != $dataSourceContext->country) {
            return 0;
        }
        static::cleanWithDataSourceContext($config, $dataSourceContext);

        return parent::storage($data, $config, $dataSourceContext);
    }

    public static function clean($data): void
    {
    }

    /**
     * @param array $config
     * @param DataSourceContext $dataSourceContext
     *
     * @return void
     * @throws UserException
     */
    protected static function cleanWithDataSourceContext(array $config, DataSourceContext $dataSourceContext)
    {
        $mapping = $config['mapping'];
        unset($config['mapping']);
        $executeParams = $dataSourceContext->executeParams;
        if (empty($executeParams['startDate']) || empty($executeParams['endDate']) || empty($mapping)) {
            throw new UserException('开始时间和结束时间不能为空或者Type不能为空!');
        }
        $types = [];
        foreach ($mapping as $index => $map) {
            $rule = array_filter(explode(':', $index), 'trim');
            $type = array_shift($rule);
            $types[] = $type;
        }
        static::deleteAll([
            'id' => static::find()
                ->where([
                    'and',
                    ['>=', 'date', $executeParams['startDate']],
                    ['<', 'date', $executeParams['endDate']],
                    ['in', 'type', $types],
                ])
                ->select('id')
                ->column(),
        ]);
    }
}
