<?php

namespace payment\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

/**
 * WithdrawSearch represents the model behind the search form about `payment\models\Withdraw`.
 */
class WithdrawSearch extends Withdraw
{
    public $channelKey;
    public $channelName;
    public $createStartDate;
    public $createEndDate;
    public $finishStartDate;
    public $finishEndDate;
    public $channelInnerKey;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'createStartDate',
                    'createEndDate',
                ],
                'default',
                'value' => Carbon::now()->toDateString(),

            ],
            [
                [
                    'channelKey',
                    'channelName',
                    'createStartDate',
                    'createEndDate',
                    'finishStartDate',
                    'finishEndDate',
                    'withdraw_merchant_key',
                    'withdraw_status',
                    'channelInnerKey',
                    'withdraw_user_uuid',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Withdraw::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['withdraw_id'],
                'defaultOrder' => ['withdraw_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        if ($this->channelKey || $this->channelName || $this->channelInnerKey) {
            $query->innerJoinWith([
                'withdrawReceipts' => function (ActiveQuery $query) {
                    $query->andFilterWhere([
                        'withdraw_receipt_channel_key' => $this->channelKey,
                        'withdraw_receipt_channel_name' => $this->channelName,
                        'withdraw_receipt_channel_inner_key' => $this->channelInnerKey
                    ]);
                },
            ]);
        }


        // grid filtering conditions
        $query->andFilterWhere([
            'withdraw_merchant_key' => $this->withdraw_merchant_key,
            'withdraw_status' => $this->withdraw_status,
            'withdraw_user_uuid' => $this->withdraw_user_uuid,
        ]);

        $query->andFilterWhere([
            'and',
            ['>=', 'withdraw_created_at', $this->createStartDate],
            [
                '<',
                'withdraw_created_at',
                $this->createEndDate ? Carbon::parse($this->createEndDate)->addDay()->toDateString() : null,
            ],
            ['>=', 'withdraw_finished_at', $this->finishStartDate],
            [
                '<',
                'withdraw_finished_at',
                $this->finishEndDate ? Carbon::parse($this->finishEndDate)->addDay()->toDateString() : null,
            ],
        ]);

        return $dataProvider;
    }
}
