<?php

namespace payment\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;
use yii\db\Expression;
use yii\db\Query;

class ReportReconciliationSummarySearch extends ReportReconciliationSummary
{
    public $startDate;
    public $endDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['startDate', 'endDate'], 'default', 'value' => Carbon::now()->subdays()->toDateString()],
            [
                [
                    'provider_code',
                    'contract_entity',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @param array $types
     * @param $params
     * @return ActiveDataProvider
     */
    public function search(array $types, $params): ActiveDataProvider
    {
        $query = new Query();
        $query->from(self::tableName());

        $dataProvider = new ActiveDataProvider([
            'query'  => $query,
            'sort'       => [
                'attributes'   => ['date'],
                'defaultOrder' => ['date' => SORT_DESC],
            ],
        ]);
        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        $columns = [
            'date',
            'provider_code',
            'contract_entity'
        ];
        foreach ($types as $type) {
            $columns[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0))', $type));
        }
        $query->select($columns)
            ->groupBy(['date', 'provider_code', 'contract_entity']);

        $query->andFilterWhere([
            'provider_code' => $this->provider_code,
            'contract_entity' => $this->contract_entity,
        ]);
        if ($this->startDate) {
            $query->andFilterWhere(['>=', 'date', $this->startDate]);
        }
        if ($this->endDate) {
            $query->andFilterWhere(['<=', 'date', $this->endDate]);
        }
        $query->having('channel_transaction_order_total > 0');
        return $dataProvider;
    }
}
