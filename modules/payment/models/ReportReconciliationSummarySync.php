<?php

namespace payment\models;

use datasource\DataSourceContext;
use datasource\interfaces\ReportMonitorInterface;
use datasource\models\DataSource;
use yii\base\UserException;

class ReportReconciliationSummarySync extends ReportReconciliationSummary implements ReportMonitorInterface
{
    /**
     * @inheritDoc
     */
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        $currentCountry = DataSource::currentCountry();
        if ($currentCountry != $dataSourceContext->country) {
            return 0;
        }
        static::cleanDate($config, $dataSourceContext);

        return parent::storage($data, $config, $dataSourceContext);
    }

    /**
     * @inheritDoc
     */
    public static function clean($data): void
    {
    }

    /**
     * @param array $config
     * @param DataSourceContext $dataSourceContext
     *
     * @return void
     * @throws UserException
     */
    public static function cleanDate(array $config, DataSourceContext $dataSourceContext)
    {
        $executeParams = $dataSourceContext->executeParams;
        if (empty($executeParams['startDate']) || empty($executeParams['endDate']) || empty($config['mapping'])) {
            throw new UserException('开始时间和结束时间不能为空');
        }
        static::deleteAll([
            'id' => static::find()
                ->where([
                    'and',
                    ['>=', 'date', $executeParams['startDate']],
                    ['<', 'date', $executeParams['endDate']],
                    ['in', 'type', array_keys($config['mapping'])],
                ])
                ->select('id')
                ->column(),
        ]);
    }
}
