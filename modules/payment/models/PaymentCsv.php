<?php

namespace payment\models;

use common\models\User;
use kvmanager\models\KeyValue;
use payment\behaviors\PaymentCsvBehavior;
use payment\works\LoadPaymentCsvDataWorker;
use import\models\BizOssFile;
use Throwable;
use yii\base\UserException;
use yii\web\UploadedFile;


class PaymentCsv extends BizOssFile
{
    public const CHANNEL = 'payment';

    public const SUCCESS_STATUS = 'success';
    public const TODO_STATUS = 'todo';
    public const FAIL_STATUS = 'fail';
    public const ING_STATUS = 'ing';
    public const CLEAN_STATUS = 'clean';

    public static function statusList()
    {
        return [
            self::SUCCESS_STATUS => '成功',
            self::TODO_STATUS => '待处理',
            self::FAIL_STATUS => '失败',
            self::ING_STATUS => '处理中',
            self::CLEAN_STATUS => '清理中',
        ];
    }

    public static function config()
    {
        static $config;
        if (!isset($config)) {
            $config = array_values(KeyValue::take('channel_reconci_template', 'paysvr'));
        }

        return $config;
    }

    /**
     * @return array
     */
    public static function typeList()
    {
        return array_column(self::config(), 'name');
    }

    public function behaviors(): array
    {
        return [
            PaymentCsvBehavior::class,
        ];
    }

    public function transactions(): array
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'file'], 'required'],
            [['from_system'], 'default', 'value' => 'biz'],
            [['type'], 'in', 'range' => array_keys(self::config())],
            [
                ['file'],
                'file',
                'extensions' => ['csv', 'xls', 'xlsx', 'ods', 'xml', 'html', 'slk'],
                'maxSize' => 20 * 1024 * 1024,
                'checkExtensionByMimeType' => false,
                'uploadRequired' => true,
            ],
            [
                ['file'],
                'filter',
                'filter' => function (UploadedFile $file) {
                    $filename = $file->name;
                    if (mb_strlen($filename) > 64) {
                        $this->addError('file', '文件名不能超过64个字符');
                    }

                    $config = self::config();
                    $filenameRule = $config[$this->type]['complement']['channel_reconci_merchant_no'] ?? false;
                    if ($filenameRule && strpos($filename, $filenameRule) === false) {
                        $this->addError('file', sprintf('请确认上传的文件是否与选择的类型匹配，并且文件名中必须包含\'%s\'', $filenameRule));
                    }

                    return $file;
                },
            ],
//            [['file'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => '类型',
            'create_by' => '后台人员',
            'domain_url' => '静态地址',
            'oss_file_name' => 'OSS文件名称',
            'file' => '文件名称',
            'success_line' => '导入成功数',
            'error_message' => '错误信息',
            'create_at' => '创建时间',
            'update_at' => '更新时间',
            'status' => '状态',
            'search' => '搜索',
        ];
    }

    /**
     * @param $filename
     * @param $suffix
     *
     * @return string
     */
    public function download($filename = null, $suffix = null): string
    {
        /** @see PaymentCsvBehavior::doDownload() yii2 可以直接调用行为中的方法 */
        return $this->doDownload($filename, $suffix);
    }

    /**
     * @param int $id
     *
     * @return void
     * @throws Throwable
     * @throws UserException
     */
    public function updateMakePaymentCsvTask(int $id)
    {
        $model = self::findOne($id);

        if (!$model) {
            throw new UserException('数据不存在!');
        }

        if (!in_array($model->status, [self::SUCCESS_STATUS, self::FAIL_STATUS])) {
            throw new UserException('该数据不能被重新导入!');
        }

        $transaction = self::getDb()->beginTransaction();
        try {
            LoadPaymentCsvDataWorker::make(['dataSourceId' => $model->id]);

            $model->status = self::ING_STATUS;
            if (!$model->save(false)) {
                throw new UserException('修改状态失败');
            }
            $transaction->commit();
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    public function getCreator()
    {
        return $this->hasOne(User::class, ['id' => 'create_by']);
    }
}
