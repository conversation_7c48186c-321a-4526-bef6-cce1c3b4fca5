<?php

namespace payment\models;


use Carbon\Carbon;
use yii\data\ActiveDataProvider;
use yii\db\Expression;


class FeeSearch extends Fee
{
    public $startDate;
    public $endDate;
    public $company;
    public $channel;

    public $date;
    public $count;
    public $totalAmount;
    public $serviceAmount;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['startDate', 'endDate'], 'default', 'value' => Carbon::now()->toDateString()],
            [['fee_type', 'company', 'channel', 'fee_channel_name', 'fee_ref_key'], 'safe'],
        ];
    }


    /**
     * @param $params
     * @return ActiveDataProvider
     *
     */
    public function search($params): ActiveDataProvider
    {
        $query = self::find();
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['date'],
                'defaultOrder' => ['date' => SORT_DESC],
            ],
        ]);
        $this->load($params);
        if (!$this->validate()) {
            return $dataProvider;
        }
        $query->select([
            new Expression('date(fee_finish_at) as `date`'),
            'fee_sign_company_code',
            'fee_provider_code',
            'fee_channel_name',
            'fee_type',
            new Expression('count(*) as `count`'),
            new Expression('SUM(fee_order_amount) as `totalAmount`'),
            new Expression('SUM(fee_amount) as `serviceAmount`'),
        ]);
        $query->andWhere([
            'and',
            ['>=', 'fee_finish_at', $this->startDate],
            ['<', 'fee_finish_at', Carbon::parse($this->endDate)->addDay()->toDateString()],
        ])->andFilterWhere([
            'fee_sign_company_code' => $this->company,
        ])->andFilterWhere([
            'fee_channel_name' => $this->channel,
        ]);


        if (!$this->fee_type) {
            $query->andFilterWhere([
                'fee_type' => array_keys(self::TYPE_LIST),
            ]);
        } else {
            $query->andFilterWhere([
                'fee_type' => $this->fee_type,
            ]);
        }


        $query->groupBy([
            'date',
            'fee_channel_name',
            'fee_type'
        ]);

        return $dataProvider;
    }


    public function searchList(array $params = [])
    {
        $query = self::find();
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['fee_finish_at'],
                'defaultOrder' => ['fee_finish_at' => SORT_DESC],
            ],
        ]);
        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        $query->andWhere([
            'and',
            ['>=', 'fee_finish_at', $this->startDate],
            ['<', 'fee_finish_at', Carbon::parse($this->endDate)->addDay()->toDateString()],
        ])->andFilterWhere([
            'fee_channel_name' => $this->fee_channel_name,
            'fee_ref_key' => $this->fee_ref_key,
        ]);

        return $dataProvider;
    }
}
