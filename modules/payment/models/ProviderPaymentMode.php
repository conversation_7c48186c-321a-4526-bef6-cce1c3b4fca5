<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "{{%provider_payment_mode}}".
 *
 * @property int $provider_payment_id
 * @property string $provider_payment_provider_code   适用渠道编码，如flinpay
 * @property string $provider_payment_platform_option 支付方式类型，如VirtualAccount
 * @property string $provider_payment_platform_mode   平台支付方式编码，如BCA
 * @property string $provider_payment_provider_mode   渠道支付方式编码，如BCA
 * @property string $provider_payment_product_type    适用产品类型：paycode,withhold,withdraw,............
 * @property string $provider_payment_status          状态
 * @property string $provider_payment_created_at      创建时间
 * @property string $provider_payment_updated_at      更新时间
 * @property string $provider_payment_mode_config
 * @property string $provider_payment_mode_name       渠道mode的名称
 * @property int $provider_payment_mode_score       渠道mode评分
 * @property-read Provider $provider
 * @property-read PaymentMode $paymentMode
 */
class ProviderPaymentMode extends \yii\db\ActiveRecord
{
    public const STATUS_OPEN = 'open';
    public const STATUS_CLOSE = 'close';
    public const STATUS_LIST = [
        self::STATUS_OPEN => '正常',
        self::STATUS_CLOSE => '关闭',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%provider_payment_mode}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'provider_payment_provider_code',
                    'provider_payment_platform_option',
                    'provider_payment_platform_mode',
                    'provider_payment_provider_mode',
                    'provider_payment_status',
                ],
                'required',
            ],
            [
                [
                    'provider_payment_provider_code',
                    'provider_payment_platform_option',
                    'provider_payment_platform_mode',
                    'provider_payment_provider_mode',
                ],
                'string',
                'max' => 32,
            ],
            [['provider_payment_mode_score'], 'integer'],
            [['provider_payment_mode_config'], 'string', 'max' => 1024],
            [
                ['provider_payment_product_type'],
                'filter',
                'filter' => function ($val) {
                    return implode(',', (array)$val);
                },
            ],
            [
                [
                    'provider_payment_provider_code',
                    'provider_payment_platform_option',
                    'provider_payment_platform_mode',
                    'provider_payment_provider_mode',
                ],
                'unique',
                'targetAttribute' => [
                    'provider_payment_provider_code',
                    'provider_payment_platform_option',
                    'provider_payment_platform_mode',
                    'provider_payment_provider_mode',
                ],
            ],
            [
                ['productType', 'provider_payment_mode_name'],
                'safe',
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'provider_payment_id' => Yii::t('payment', 'ID'),
            'provider_payment_provider_code' => Yii::t('payment', '适用渠道编码'),
            'provider_payment_platform_option' => Yii::t('payment', '支付方式类型'),
            'provider_payment_platform_mode' => Yii::t('payment', '平台支付方式编码'),
            'provider_payment_provider_mode' => Yii::t('payment', '渠道支付方式编码'),
            'provider_payment_status' => Yii::t('payment', '状态'),
            'provider_payment_product_type' => Yii::t('payment', '适用产品类型'),
            'provider_payment_created_at' => Yii::t('payment', '创建时间'),
            'provider_payment_updated_at' => Yii::t('payment', '更新时间'),
            'provider_payment_mode_config' => Yii::t('payment', '配置'),
            'provider_payment_mode_name' => Yii::t('payment', '渠道mode名称'),
            'productType' => Yii::t('payment', '使用产品类型'),
            'provider_payment_mode_score' => Yii::t('payment', '渠道mode评分'),
        ];
    }

    public function getProvider()
    {
        return $this->hasOne(Provider::class, ['provider_code' => 'provider_payment_provider_code']);
    }

    public function getPaymentMode()
    {
        return $this->hasOne(PaymentMode::class, ['payment_mode_code' => 'provider_payment_platform_mode']);
    }

    public function getProductType(): array
    {
        return (array)preg_split('/\s*,\s*/', trim((string)$this->provider_payment_product_type), -1, PREG_SPLIT_NO_EMPTY);
    }

    public function setProductType($types)
    {
        $this->provider_payment_product_type = implode(',', array_filter((array)$types));
    }
}
