<?php

namespace payment\models;

use dashboard\traits\ReportModelTrait;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "report_reconciliation_statement".
 *
 * @property int $id ID
 * @property string $date 日期
 * @property string $provider_code 渠道
 * @property string $sign_company 主体
 * @property string $transaction_type 交易类型
 * @property string $type 类型
 * @property int $values 值
 * @property string $created_at 创建时间
 * @property string $updated_at 修改时间
 * @property int $data_job_id 数据任务编号
 */
class ReportReconciliationStatement extends ActiveRecord
{
    use ReportModelTrait;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'report_reconciliation_statement';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['date'], 'required'],
            [['date', 'created_at', 'updated_at'], 'safe'],
            [['values', 'data_job_id'], 'integer'],
            [['provider_code', 'sign_company', 'transaction_type', 'type'], 'string', 'max' => 64],
            [['provider_code', 'sign_company', 'transaction_type', 'type', 'date'], 'unique', 'targetAttribute' => ['provider_code', 'sign_company', 'transaction_type', 'type', 'date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'provider_code' => '渠道',
            'sign_company' => '主体',
            'transaction_type' => '交易类型',
            'type' => '类型',
            'values' => '值',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
            'data_job_id' => '数据任务编号',
        ];
    }
}
