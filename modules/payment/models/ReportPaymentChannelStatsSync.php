<?php

namespace payment\models;

use datasource\DataSourceContext;
use datasource\interfaces\ReportMonitorInterface;
use datasource\models\DataSource;
use yii\base\UserException;

class ReportPaymentChannelStatsSync extends ReportPaymentChannelStats implements ReportMonitorInterface
{
    public static function storage(array $data, array $config, DataSourceContext $dataSourceContext): int
    {
        $currentCountry = DataSource::currentCountry();
        if ($currentCountry != $dataSourceContext->country) {
            return 0;
        }
        static::cleanWithDataSourceContext($config, $dataSourceContext);

        return parent::storage($data, $config, $dataSourceContext);
    }

    public static function clean($data): void
    {
    }

    /**
     * @param array $config
     * @param DataSourceContext $dataSourceContext
     *
     * @return void
     * @throws UserException
     */
    protected static function cleanWithDataSourceContext(array $config, DataSourceContext $dataSourceContext)
    {
        $mapping = $config['mapping'];
        unset($config['mapping']);
        $executeParams = $dataSourceContext->executeParams;
        //@note 沟通过支付通道监控指标不会通过一条SQL刷全量数据
        if (empty($executeParams['startDate']) || empty($executeParams['endDate']) || empty($mapping)) {
            throw new UserException('开始时间和结束时间不能为空或者Type不能为空!');
        }
        $types = [];
        foreach ($mapping as $map) {
            $rule = array_filter(explode(':', $map), 'trim');
            $type = array_shift($rule);
            $types[] = $type;
        }
        //@note 2025-03-14:确认删除以日期和type全量删除,比如channel_name 为1 上次查询有值，而最新的一次查询没有值了，也需要删除历史的数据
        static::deleteAll([
            // 使用主键删除，防止出现死锁
            'id' => static::find()
                ->where([
                    'and',
                    ['>=', 'date', $executeParams['startDate']],
                    ['<', 'date', $executeParams['endDate']],
                    ['in', 'type', $types],
                ])
                ->select('id')
                ->column(),
        ]);
    }
}
