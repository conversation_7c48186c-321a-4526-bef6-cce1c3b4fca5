<?php

namespace payment\models;

use backend\behaviors\FormatterBehavior;
use Carbon\Carbon;
use kvmanager\models\KeyValue;
use system\components\PaymentHttpComponent;
use xlerr\desensitise\Desensitise;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\Model;
use yii\base\UserException;
use yii\caching\TagDependency;
use function xlerr\desensitise\decrypt;
use function xlerr\desensitise\encrypt;

class MemberCard extends Model
{
    public const MEMBER_TYPE_WITHDRAW = 'withdraw';
    public const MEMBER_TYPE_SALARY = 'salary';
    public const MEMBER_TYPE_GREY = 'grey';

    public const BANK_CODE_CLABE = 'clabe';
    public const BANK_CODE_ACCOUNT = 'account';
    public const BANK_CODE_WALLET = 'wallet';
    public const MEMBER_TYPE_LIST = [
        'salary' => '工资卡',
        'withdraw' => '提现卡',
        'grey' => '灰度卡',
    ];

    public static function accountTypeList()
    {
        return KeyValue::take('card_type_list', 'paysvr');
    }

    public static function idTypeList()
    {
        return KeyValue::take('card_id_type', 'paysvr');
    }

    public const STATUS_LIST = [
        '未验证/未生效',
        '已验证/有效',
    ];

    public const RECEIVER_TYPE_LIST = [
        1 => '对私',
        2 => '对公',
    ];

    public $user_uuid; // 用户的唯一id
    public $card_uuid; // 卡的唯一id
    public $member_type; // 白名单类型，salary=工资卡(默认)，withdraw=提现卡，grey=灰度卡
    public $account_type; // 账户类型 ，如 clabe=clabe账号，account=银行账号，wallet=电子钱包
    public $account_no; // 账户编码
    public $large_account; // 大额账户
    public $max_withdraw_amount; // 最大提现金额(分)
    public $user_name; // 用户名
    public $bank_code; // 银行编号
    public $mobile; // 手机号
    public $email; // 邮箱
    public $status; // true=已验证/有效，false=未验证/未生效
    public $id_num;  //证件号
    public $id_type; //证件类型
    public $address; //地址
    public $receiver_type; //账户类型

    public function formName()
    {
        return '';
    }

    public function rules()
    {
        return [
            [
                ['user_uuid', 'card_uuid'],
                'default',
                'value' => function ($model, $attribute) {
                    $countryCode = array_sum(array_map('ord', str_split(Yii::$app->params['logo']))) + [
                            'user_uuid' => 1,
                            'card_uuid' => 2,
                        ][$attribute];

                    return Carbon::now()->format('YmdHis') . $countryCode;
                },
            ],
            [['max_withdraw_amount'], 'default', 'value' => 0],
            [
                [
                    'account_type',
                    'bank_code',
                    'account_no',
                    'user_name',
                    'large_account',
                    'max_withdraw_amount',
                    'mobile',
                ],
                'required',
            ],
            [['status'], 'filter', 'filter' => 'intval'],
            [
                [
                    'user_name',
                    'mobile',
                    'email',
                    'member_type',
                    'account_type',
                    'account_no',
                    'bank_code',
                    'id_num',
                    'id_type',
                    'address',
                    'receiver_type',
                ],
                'safe',
            ],
            [['address'], 'string', 'max' => 128],
        ];
    }

    public function attributeLabels()
    {
        return [
            'user_uuid' => 'USER UUID',
            'card_uuid' => 'CARD UUID',
            'member_type' => '白名单类型',
            'account_type' => '账户类型',
            'account_no' => '账号',
            'large_account' => '大额账户',
            'max_withdraw_amount' => sprintf('最大提现金额(%s)', FormatterBehavior::currencyUnit()),
            'user_name' => '用户名',
            'bank_code' => '银行编号/账户渠道',
            'mobile' => '手机号',
            'email' => '邮箱',
            'status' => '状态',
            'id_num' => '证件号',
            'id_type' => '证件类型',
            'address' => '地址',
            'receiver_type' => '账户类型',
        ];
    }

    /**
     * @return array{}
     */
    public static function cpopSettlementAccounts(): array
    {
        $accounts = Yii::$app->cache->get('cpop_settlement_accounts');
        if ($accounts === false) {
            $rawAccounts = self::query(true);

            $accounts = [];
            foreach ($rawAccounts as $account) {
                if ($account['member_type'] === self::MEMBER_TYPE_GREY) {
                    continue;
                }

                $name = vsprintf('%s:%s', [
                    $account['user_uuid'],
                    decrypt($account['user_name'], true),
                ]);

                $accounts[$name] = [
                    'type' => 'member_card',
                    'name' => $name,
                    'account_no' => decrypt($account['account_no'], true),
                    'card_uuid' => $account['card_uuid'],
                    'user_uuid' => $account['user_uuid'],
                ];
            }

            Yii::$app->cache->set('cpop_settlement_accounts', $accounts, null, new TagDependency([
                'tags' => __CLASS__ . '::save',
            ]));
        }

        return $accounts;
    }

    /**
     * @return array<int, MemberCard>
     * @throws InvalidConfigException
     * @throws UserException
     */
    public static function query($raw = false): array
    {
        $client = PaymentHttpComponent::instance();
        if (!$client->queryMemberCards()) {
            throw new UserException($client->getError());
        }

        if ($raw) {
            return $client->getData();
        }

        $models = [];
        foreach ($client->getData() as $row) {
            $model = new self();
            $model->load($row, '');
            $model->validate();
            $models[] = $model;
        }

        return $models;
    }

    /**
     * @param string $id
     *
     * @return MemberCard|null
     * @throws InvalidConfigException
     * @throws UserException
     */
    public static function findOne(string $id): ?MemberCard
    {
        foreach (self::query() as $model) {
            if ($model->user_uuid === $id) {
                return $model;
            }
        }

        return null;
    }

    public function save(): bool
    {
        if (!$this->validate()) {
            return false;
        }

        //        $model = clone $this;
        //
        //        $model->account_no = $this->encrypt('account_no');
        //        $model->user_name  = $this->encrypt('user_name');
        //        $model->mobile     = $this->encrypt('mobile');
        //        $model->email      = $this->encrypt('email');

        $client = PaymentHttpComponent::instance();
        if (!$client->memberRegister($this)) {
            if (!Yii::$app->request->isConsoleRequest) {
                Yii::$app->session->setFlash('error', '接口:' . $client->getError());
            }
            return false;
        }

        TagDependency::invalidate(Yii::$app->cache, [
            __CLASS__ . '::save',
        ]);

        return true;
    }

    public function encrypt($attribute)
    {
        $val = $this->{$attribute} ?? null;
        if (!$val) {
            return $val;
        }
        $types = [
            'account_no' => Desensitise::TYPE_BANK_CARD_NUMBER,
            'user_name' => Desensitise::TYPE_NAME,
            'mobile' => Desensitise::TYPE_PHONE_NUMBER,
            'email' => Desensitise::TYPE_EMAIL,
        ];

        return encrypt($this->{$attribute}, $types[$attribute])->hash;
    }

    public function decrypt($attribute, $plain = false)
    {
        $val = $this->{$attribute} ?? null;
        if (!$val) {
            return $val;
        }

        return decrypt($this->{$attribute}, $plain);
    }

    public function getAccountKey(): string
    {
        return sprintf('%s:%s', $this->user_uuid, $this->user_name);
    }
}
