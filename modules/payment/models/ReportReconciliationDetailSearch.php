<?php

namespace payment\models;

use Carbon\Carbon;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\SqlDataProvider;
use yii\db\ActiveRecord;

class ReportReconciliationDetailSearch extends ActiveRecord
{
    public $startDate;
    public $endDate;

    public $providerCode;
    public $contractEntity;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['startDate', 'endDate'], 'default', 'value' => Carbon::now()->subdays()->toDateString()],
            [
                [
                    'providerCode',
                    'contractEntity',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @param $params
     * @return SqlDataProvider
     * @throws UserException
     * @throws KVException
     * @throws InvalidConfigException
     */
    public function search($params): SqlDataProvider
    {
        $this->load($params);

        if (!$this->validate()) {
            throw new UserException('必须输入开始时间和结束时间');
        }
        $startDateObj = Carbon::parse($this->startDate);
        $endDateObj = Carbon::parse($this->endDate);
        $startDate = $startDateObj->startOfDay()->toDateTimeString();
        $endDate = $endDateObj->endOfDay()->toDateTimeString();

        if ($startDateObj > $endDateObj) {
            throw new UserException('开始时间不能大于结束时间');
        }
        if ($startDateObj->addMonths() < $endDateObj) {
            throw new UserException('搜索范围最多支持1个月的数据');
        }
        $providerCodeWhere = $contractEntityWhere = '';
        if (!empty($this->providerCode)) {
            $providerCodeWhere = " AND provider_code = '{$this->providerCode}' ";
        }
        if (!empty($this->contractEntity)) {
            $contractEntityWhere = " AND contract_entity = '{$this->contractEntity}' ";
        }
        $withdrawReceiptChannelNameWhere = $withholdReceiptChannelNameWhere = $channelOfflineTradeChannelNameWhere = '';
        $code = '';
        if (!empty($this->providerCode) && !empty($this->contractEntity)) {
            $code = "{$this->providerCode}_{$this->contractEntity}%";
            $withdrawReceiptChannelNameWhere = " AND withdraw_receipt_channel_name like :code";
            $withholdReceiptChannelNameWhere = " AND withhold_receipt_channel_name like :code";
            $channelOfflineTradeChannelNameWhere = " AND channel_offline_trade_channel_name like :code";
        } elseif (!empty($this->providerCode)) {
            $code = "{$this->providerCode}%";
            $withdrawReceiptChannelNameWhere = " AND withdraw_receipt_channel_name like :code";
            $withholdReceiptChannelNameWhere = " AND withhold_receipt_channel_name like :code";
            $channelOfflineTradeChannelNameWhere = " AND channel_offline_trade_channel_name like :code";
        } elseif (!empty($this->contractEntity)) {
            $code = "%{$this->contractEntity}%";
            $withdrawReceiptChannelNameWhere = " AND withdraw_receipt_channel_name like :code";
            $withholdReceiptChannelNameWhere = " AND withhold_receipt_channel_name like :code";
            $channelOfflineTradeChannelNameWhere = " AND channel_offline_trade_channel_name like :code";
        }
        $template = KeyValue::takeAsRaw('reconci_result_sql_config', 'paysvr');
        $variables = [
            '{startDate}'                           => $startDate,
            '{endDate}'                             => $endDate,
            '{providerCodeWhere}'                   => $providerCodeWhere,
            '{contractEntityWhere}'                 => $contractEntityWhere,
            '{withdrawReceiptChannelNameWhere}'     => $withdrawReceiptChannelNameWhere,
            '{withholdReceiptChannelNameWhere}'     => $withholdReceiptChannelNameWhere,
            '{channelOfflineTradeChannelNameWhere}' => $channelOfflineTradeChannelNameWhere,
        ];
        $sql = strtr($template, $variables);
        return new SqlDataProvider([
            'sql' => $sql,
            'params' => [
                ':code' => $code,
            ],
            'db' => Yii::$app->get('paySvrDb')
        ]);
    }
}
