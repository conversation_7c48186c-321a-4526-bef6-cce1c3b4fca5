<?php

namespace payment\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;

/**
 * WithdrawalManualSearch represents the model behind the search form about `finance\models\WithdrawalManual`.
 */
class ChannelOfflineTradeSearch extends ChannelOfflineTrade
{
    public $finishedAtStartDate;
    public $finishedAtEndDate;

    public function formName()
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'channel_offline_trade_oa_id',
                    'channel_offline_trade_order_no',
                    'channel_offline_trade_busi_type',
                    'finishedAtStartDate',
                    'finishedAtEndDate',
                    'channel_offline_trade_channel_name',
                    'channel_offline_trade_reconci_status'
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['channel_offline_trade_id'],
                'defaultOrder' => ['channel_offline_trade_id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'channel_offline_trade_oa_id' => $this->channel_offline_trade_oa_id,
            'channel_offline_trade_busi_type' => $this->channel_offline_trade_busi_type,
            'channel_offline_trade_reconci_status' => $this->channel_offline_trade_reconci_status,
        ]);

        if ($this->channel_offline_trade_order_no) {
            $where = ['like', 'channel_offline_trade_order_no', $this->channel_offline_trade_order_no . '%', false];
            $query->andWhere($where);
        }

        if ($this->channel_offline_trade_channel_name) {
            $query->andFilterWhere(
                ['like', 'channel_offline_trade_channel_name', "{$this->channel_offline_trade_channel_name}%", false]
            );
        }

        if ($this->finishedAtStartDate) {
            $query->andFilterWhere(['>=', 'channel_offline_trade_finish_at', $this->finishedAtStartDate]);
        }
        if ($this->finishedAtEndDate) {
            $query->andFilterWhere(
                [
                    '<=',
                    'channel_offline_trade_finish_at',
                    Carbon::parse($this->finishedAtEndDate)->endOfDay()->toDateTimeString()
                ]
            );
        }

        return $dataProvider;
    }
}
