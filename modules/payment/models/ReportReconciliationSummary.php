<?php

namespace payment\models;

use dashboard\traits\ReportModelTrait;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "report_reconciliation_summary".
 *
 * @property int      $id
 * @property string   $date
 * @property string   $provider_code 渠道编号
 * @property string   $contract_entity 签约主体编号
 * @property string   $type 类型
 * @property int      $values 统计值
 * @property string   $created_at
 */
class ReportReconciliationSummary extends ActiveRecord
{
    use ReportModelTrait;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'report_reconciliation_summary';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['date', 'provider_code', 'contract_entity', 'type'], 'required'],
            [['date', 'created_at'], 'safe'],
            [['values'], 'integer'],
            [['provider_code', 'contract_entity'], 'string', 'max' => 16],
            [['type'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'provider_code' => '渠道编号',
            'contract_entity' => '签约主体编号',
            'type' => '类型',
            'values' => '统计值',
            'created_at' => '创建时间',
        ];
    }
}
