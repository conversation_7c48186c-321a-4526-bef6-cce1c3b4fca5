<?php

namespace payment\models;

use Yii;

/**
 * This is the model class for table "cost_calc_config".
 *
 * @property int $cost_calc_config_id               主键
 * @property string $cost_calc_config_provider_code    渠道编号
 * @property string|null $cost_calc_config_sign_company     支持的签约主体，使用逗号分隔
 * @property string|null $cost_calc_config_products         支持的产品，使用逗号分隔
 * @property string|null $cost_calc_config_fee_handler      费用特殊处理SPEL表达式，预留变量#fee和#calcDto，返回值保存为fee.fee_service
 * @property string $cost_calc_config_fee_type_handler SPEL表达式，用于费用类型判断，预设变量：#calcDto、#rule，返回值保存到fee.fee_type，默认withhold|withdraw
 * @property string|null $cost_calc_config_option           支持的paymentOption
 * @property string|null $cost_calc_config_mode             支持的paymentMode
 * @property string|null $cost_calc_config_remark           备注说明
 * @property string $cost_calc_config_source_condition Map<Status,Map<Code,Message>> 数据状态及Code Json数组，作为数据查询条件
 * @property string $cost_calc_config_ladder_type      阶梯类型，none，transferNumber，transferAmount，cycleTransferAmount
 * @property int $cost_calc_config_ladder_min       当前阶梯最小值
 * @property int $cost_calc_config_ladder_max       当前阶梯最大值（min<=value<max）
 * @property int $cost_calc_config_fee_type         服务费收费方式，0：固定金额，1：比例金额
 * @property string $cost_calc_config_fee              固定金额或比例，固定金额单位分（不能有小数），比例金额范围0.00~1.00
 * @property int $cost_calc_config_fee_min          服务费最小金额单位分
 * @property int $cost_calc_config_fee_max          服务费最大金额单位分
 * @property int $cost_calc_config_tax_type         税费收费方式，0：固定金额，1：比例金额
 * @property string $cost_calc_config_tax              固定金额或比例，固定金额单位分（不能有小数），比例金额范围0.00~1.00
 * @property int $cost_calc_config_tax_min          税费最小金额单位分
 * @property int $cost_calc_config_tax_max          税费最大金额单位分
 * @property int $cost_calc_config_rounding_mode    舍入模式，1：舍弃小数，保留整数，2：小数部分大于0进1，4：大于4进1，5：大于5进1
 * @property string|null $cost_calc_config_start            有效期开始时间
 * @property string|null $cost_calc_config_end              有效期结束时间
 * @property int $cost_calc_config_enable           是否启用
 * @property string $cost_calc_config_offline_fee_type 线下费用类型
 */
class CostCalcConfig extends \yii\db\ActiveRecord
{
    public const FEE_TYPE_LIST = [
        0 => '固定金额',
        1 => '比例金额',
    ];

    public const STATUS_LIST = [
        0 => '禁用',
        1 => '启用',
    ];

    public const ROUNDING_MODE_LIST = [
        4 => 'HALF_UP',
        5 => 'HALF_DOWN',
    ];

    public const  LADDER_TYPE_LIST = [
        'transferNumber' => 'transferNumber',
        'transferAmount' => 'transferAmount',
        'cycleTransferAmount' => 'cycleTransferAmount',
        'orderNumLastMonth' => 'orderNumLastMonth',
        'none' => 'none',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cost_calc_config';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paySvrDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'cost_calc_config_ladder_min',
                    'cost_calc_config_fee',
                    'cost_calc_config_fee_min',
                    'cost_calc_config_tax',
                    'cost_calc_config_tax_min',
                ],
                'default',
                'value' => 0,
            ],
            [
                [
                    'cost_calc_config_ladder_max',
                    'cost_calc_config_fee_max',
                    'cost_calc_config_tax_max',
                ],
                'default',
                'value' => **********,
            ],
            [
                [
                    'cost_calc_config_ladder_max',
                    'cost_calc_config_fee_max',
                    'cost_calc_config_tax_max',
                ],
                'integer',
                'min' => 0,
                'max' => **********,
            ],
            [
                [
                    'cost_calc_config_provider_code',
                    'cost_calc_config_remark',
                    'cost_calc_config_ladder_type',
                    'cost_calc_config_ladder_min',
                    'cost_calc_config_ladder_max',
                    'cost_calc_config_fee_type',
                    'cost_calc_config_fee',
                    'cost_calc_config_fee_min',
                    'cost_calc_config_fee_max',
                    'cost_calc_config_tax_type',
                    'cost_calc_config_tax',
                    'cost_calc_config_tax_min',
                    'cost_calc_config_tax_max',
                    'cost_calc_config_rounding_mode',
                    'cost_calc_config_start',
                    'cost_calc_config_end',
                ],
                'required',
            ],
            [
                [
                    'cost_calc_config_ladder_min',
                    'cost_calc_config_ladder_max',
                    'cost_calc_config_fee_type',
                    'cost_calc_config_fee_min',
                    'cost_calc_config_fee_max',
                    'cost_calc_config_tax_type',
                    'cost_calc_config_tax_min',
                    'cost_calc_config_tax_max',
                    'cost_calc_config_rounding_mode',
                    'cost_calc_config_enable',
                ],
                'integer',
            ],
            [['cost_calc_config_start', 'cost_calc_config_end', 'configSignCompany', 'configProducts', 'cost_calc_config_offline_fee_type'], 'safe'],
            [['cost_calc_config_provider_code', 'cost_calc_config_ladder_type'], 'string', 'max' => 32],
            [
                [
                    'cost_calc_config_fee_handler',
                    'cost_calc_config_fee_type_handler',
                    'cost_calc_config_option',
                    'cost_calc_config_mode',
                    'cost_calc_config_source_condition',
                ],
                'string',
                'max' => 256,
            ],
            [['cost_calc_config_remark'], 'string', 'max' => 128],
            [['cost_calc_config_fee', 'cost_calc_config_tax'], 'string', 'max' => 12],
        ];
    }


    public function getConfigSignCompany(): array
    {
        return (array)preg_split(
            '/\s*,\s*/',
            trim((string)$this->cost_calc_config_sign_company),
            -1,
            PREG_SPLIT_NO_EMPTY
        );
    }

    public function setConfigSignCompany($companies): void
    {
        $this->cost_calc_config_sign_company = implode(',', array_filter((array)$companies));
    }

    public function getConfigProducts(): array
    {
        return (array)preg_split(
            '/\s*,\s*/',
            trim((string)$this->cost_calc_config_products),
            -1,
            PREG_SPLIT_NO_EMPTY
        );
    }

    public function setConfigProducts($value): void
    {
        $this->cost_calc_config_products = implode(',', array_filter((array)$value));
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'cost_calc_config_id' => '主键',
            'cost_calc_config_provider_code' => '渠道',
            'cost_calc_config_sign_company' => '签约主体',
            'cost_calc_config_products' => '支付产品',
            'cost_calc_config_fee_handler' => 'FeeHandler',
            'cost_calc_config_fee_type_handler' => 'FeeTypeHandler',
            'cost_calc_config_option' => '支付方式类型',
            'cost_calc_config_mode' => '支付方式',
            'cost_calc_config_remark' => '备注',
            'cost_calc_config_source_condition' => '交易过滤条件',
            'cost_calc_config_ladder_type' => '阶梯类型',
            'cost_calc_config_ladder_min' => '阶梯下限',
            'cost_calc_config_ladder_max' => '阶梯上限',
            'cost_calc_config_fee_type' => '服务费计费类型',
            'cost_calc_config_fee' => '服务费金额(分)|比例',
            'cost_calc_config_fee_min' => '最小服务费(分)',
            'cost_calc_config_fee_max' => '	最大服务费(分)',
            'cost_calc_config_tax_type' => '税费计费类型',
            'cost_calc_config_tax' => '税费金额(分)|比例',
            'cost_calc_config_tax_min' => '最小税费(分)',
            'cost_calc_config_tax_max' => '最大税费(分)',
            'cost_calc_config_rounding_mode' => '舍入模式',
            'cost_calc_config_start' => '开始生效时间',
            'cost_calc_config_end' => '结束生效时间',
            'cost_calc_config_enable' => '是否启用',
            'configSignCompany' => '签约主体',
            'configProducts' => '支付产品',
            'cost_calc_config_offline_fee_type' => '线下费用类型'
        ];
    }


}
