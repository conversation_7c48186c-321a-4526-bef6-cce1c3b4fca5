<?php

namespace payment\controllers;

use payment\models\Fee;
use payment\models\FeeSearch;
use system\components\PaymentHttpComponent;
use Yii;
use yii\base\DynamicModel;
use yii\base\UserException;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use function xlerr\adminlte\userFullName;

class FeeController extends Controller
{
    public function actionIndex(): string
    {
        $searchModel = new FeeSearch();
        $dataProvider = $searchModel->searchList(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionUpdate(int $id, string $key)
    {
        $model = new class () extends DynamicModel {
            public $id;
            public $fee_ref_key;
            public $adjust_type;
            public $amount;
            public $fee_type;
            public $remark;

            public function formName()
            {
                return '';
            }

            public function rules()
            {
                return [
                    [
                        [
                            'id',
                            'fee_ref_key',
                            'adjust_type',
                            "amount",
                            'fee_type',
                        ],
                        'required',
                    ],
                    [['remark'], 'safe'],
                    [['amount'], 'integer'],
                ];
            }

            public function attributeLabels()
            {
                return [
                    'id' => 'ID',
                    "fee_ref_key" => '支付通道订单号',
                    "adjust_type" => '调整类型',
                    "amount" => "调整金额",
                    'fee_type' => '费用类型',
                    'remark' => '备注',
                ];
            }
        };
        $model->id = $id;
        $model->fee_ref_key = $key;
        $session = Yii::$app->getSession();
        if ($this->request->isPost) {
            try {
                if ($model->load($this->request->post()) && $model->validate()) {
                    $data = [
                        'id' => $model->id,
                        'adjust_type' => $model->adjust_type,
                        'amount' => $model->amount,
                        'fee_type' => $model->fee_type,
                        'remark' => $model->remark,
                        'operator' => userFullName()
                    ];
                    $client = PaymentHttpComponent::instance();
                    if (!$client->feeAdjust($data)) {
                        throw new UserException(sprintf('API 接口返回错误: %s', $client->getError()));
                    }
                    return Html::script('window.top.reloadCurrentTab()');
                }
            } catch (\Throwable $exception) {
                $session->setFlash('error', $exception->getMessage());
            }
        }
        return $this->render('update', [
            'model' => $model
        ]);
    }

    /**
     * @param $id
     * @return Fee|null
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        if (($model = Fee::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

}