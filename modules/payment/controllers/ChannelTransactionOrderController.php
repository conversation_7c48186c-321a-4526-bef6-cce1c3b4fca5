<?php

namespace payment\controllers;

use payment\models\ChannelTransactionOrder;
use payment\models\ChannelTransactionOrderSearch;
use Yii;
use yii\base\UserException;
use yii\web\Controller;

class ChannelTransactionOrderController extends Controller
{
    /**
     * Lists all WithdrawalManual models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new ChannelTransactionOrderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionUpdateStatus()
    {
        $session = Yii::$app->getSession();
        try {
            $reconciStatus = Yii::$app->request->post('reconci_status');
            $reconciRemark = Yii::$app->request->post('reconci_remark');

            $ids = Yii::$app->request->post('ids');

            $allowStatus = [
                ChannelTransactionOrder::RECONCI_STATUS_OPEN,
                ChannelTransactionOrder::RECONCI_STATUS_COMPLETE
            ];
            if (!in_array($reconciStatus, $allowStatus)) {
                throw new UserException('对账状态只能修改为open或complete');
            }
            if (empty($ids)) {
                throw new UserException('需要修改的id不能为空');
            }

            if ($reconciStatus === ChannelTransactionOrder::RECONCI_STATUS_COMPLETE && empty($reconciRemark)) {
                throw new UserException('修改为complete状态reconci_remark不能为空');
            }
            $count = (int)ChannelTransactionOrder::find()->where([
                'id' => $ids,
                'reconci_status' => ChannelTransactionOrder::RECONCI_STATUS_PENDING,
            ])->count();
            if ($count !== count($ids)) {
                throw new UserException('传递的id错误，请刷新后重试！');
            }

            $attributes['reconci_status'] = $reconciStatus;
            if ($reconciStatus === ChannelTransactionOrder::RECONCI_STATUS_COMPLETE) {
                $attributes['reconci_remark'] = $reconciRemark;
            }
            $affectedRows = ChannelTransactionOrder::updateAll(
                $attributes,
                [
                    'id' => $ids,
                    'reconci_status' => ChannelTransactionOrder::RECONCI_STATUS_PENDING,
                ]
            );
            $session->addFlash('success', sprintf('修改结果: %d条成功', $affectedRows));
        } catch (UserException $e) {
            $session->setFlash('error', $e->getMessage());
        }
        return $this->redirect($this->request->getReferrer());
    }
}
