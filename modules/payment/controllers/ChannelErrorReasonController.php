<?php

namespace payment\controllers;

use payment\models\ChannelErrorReason;
use payment\models\ChannelErrorReasonSearch;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ChannelErrorReasonController implements the CRUD actions for ChannelErrorReason model.
 */
class ChannelErrorReasonController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all ChannelErrorReason models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new ChannelErrorReasonSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new ChannelErrorReason model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new ChannelErrorReason();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ChannelErrorReason model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id Channel Error ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    public function actionView($id)
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ChannelErrorReason model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id Channel Error ID
     * @return Response
     * @throws NotFoundHttpException if the model cannot be found
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public function actionDelete($id): \yii\web\Response
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ChannelErrorReason model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id Channel Error ID
     * @return ChannelErrorReason the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): ChannelErrorReason
    {
        if (($model = ChannelErrorReason::findOne(['channel_error_reason_id' => $id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
