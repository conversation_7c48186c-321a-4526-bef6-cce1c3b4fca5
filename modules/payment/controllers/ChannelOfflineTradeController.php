<?php

namespace payment\controllers;

use finance\models\BusinessAssociationTable;
use finance\models\PaymentWithdrawalPendingTasks;
use kvmanager\KVException;
use payment\models\ChannelOfflineTrade;
use payment\models\ChannelOfflineTradeSearch;
use payment\models\ChannelTransactionOrder;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\db\AfterSaveEvent;
use yii\db\BaseActiveRecord;
use yii\db\Exception;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Request;

/**
 * AccountTurnoverSupplementController implements the CRUD actions for AccountTurnoverSupplement model.
 */
class ChannelOfflineTradeController extends Controller
{
    /**
     * Lists all WithdrawalManual models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new ChannelOfflineTradeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new WithdrawalManual model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param int|null $pendingTaskId
     * @param int|null $oaId
     * @param string|null $type
     *
     * @return string
     * @throws KVException
     * @throws Throwable
     */
    public function actionWithdraw(int $pendingTaskId = null, int $oaId = null, string $type = null): string
    {
        $model = new ChannelOfflineTrade();
        $model->channel_offline_trade_oa_id = $oaId;

        if ($type) {
            $model->channel_offline_trade_busi_type = $type;
        }
        if (Yii::$app->getRequest()->get('channel_offline_trade_amount')) {
            $model->channel_offline_trade_amount = Yii::$app->getRequest()->get('channel_offline_trade_amount');
        }
        if (Yii::$app->getRequest()->get('channel_offline_trade_order_no')) {
            $model->channel_offline_trade_order_no = Yii::$app->getRequest()->get('channel_offline_trade_order_no');
        }

        /** @var Request $request */
        $request = Yii::$app->getRequest();
        if ($request->isPost) {
            if ($pendingTaskId) {
                $model->on(BaseActiveRecord::EVENT_AFTER_INSERT, function (AfterSaveEvent $event) use ($pendingTaskId) {
                    /** @var ChannelOfflineTrade $model */
                    $model = $event->sender;
                    $association = new BusinessAssociationTable();
                    $association->main_id = $pendingTaskId;
                    $association->side_id = $model->channel_offline_trade_id;
                    $association->business_type = PaymentWithdrawalPendingTasks::BUSINESS_TYPE_CHANNEL_OFFLINE_TRADE;
                    if (!$association->insert()) {
                        throw new UserException('记录关联数据失败');
                    }
                });
            }
            if ($model->load($request->post()) && $model->insert()) {
                return Html::script('window.top.reloadCurrentTab()');
            }
        }

        return $this->render('withdraw', [
            'model' => $model,
        ]);
    }

    /**
     * @return string
     * @throws Exception
     */
    public function actionWithhold(): string
    {
        $model = new ChannelOfflineTrade();
        if (Yii::$app->getRequest()->get('channel_offline_trade_amount')) {
            $model->channel_offline_trade_amount = Yii::$app->getRequest()->get('channel_offline_trade_amount');
        }
        if (Yii::$app->getRequest()->get('channel_offline_trade_order_no')) {
            $model->channel_offline_trade_order_no = Yii::$app->getRequest()->get('channel_offline_trade_order_no');
        }

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('withhold', [
            'model' => $model,
        ]);
    }


    public function actionUpdateWithdraw(int $id): string
    {
        $model = $this->findModel($id);
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update-withdraw', [
            'model' => $model,
        ]);
    }

    public function actionUpdateWithhold(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return Html::script('window.top.reloadCurrentTab()');
        }

        return $this->render('update-withhold', [
            'model' => $model,
        ]);
    }

    public function actionGetFinishAt($orderNo)
    {
        /** @var ChannelTransactionOrder $channelTransactionOrder */
        $channelTransactionOrder = ChannelTransactionOrder::find()
            ->where(['channel_key' => $orderNo])
            ->orWhere(['channel_inner_key' => $orderNo])
            ->one();
        return $channelTransactionOrder->finished_at ?? '';
    }

    /**
     * @param int $id
     * @return ChannelOfflineTrade
     * @throws NotFoundHttpException
     */
    protected function findModel(int $id): ChannelOfflineTrade
    {
        if (($model = ChannelOfflineTrade::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
