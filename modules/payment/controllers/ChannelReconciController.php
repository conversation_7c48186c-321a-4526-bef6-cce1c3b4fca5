<?php

namespace payment\controllers;

use payment\models\ChannelReconciStatistics;
use system\components\PaymentHttpComponent;
use Yii;
use yii\base\InvalidConfigException;
use yii\web\Controller;
use yii\web\Request;
use yii\web\Response;

use function xlerr\adminlte\userFullName;

/**
 * @property-read Request $request
 */
class ChannelReconciController extends Controller
{
    /**
     * @return string
     */
    public function actionStatistics(): string
    {
        $searchModel = new ChannelReconciStatistics();
        [$dataProvider, $channels] = $searchModel->search($this->request->queryParams);

        return $this->render('statistics', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'channels' => $channels,
        ]);
    }

    /**
     * @return Response
     * @throws InvalidConfigException
     */
    public function actionFixFailedWithdrawReceipt(): Response
    {
        $session = Yii::$app->getSession();
        $client = PaymentHttpComponent::instance();

        $channel = $this->request->get('channel_name');
        $date = $this->request->get('date');

        if ($client->fixFailedWithdrawReceipt($channel, $date, userFullName())) {
            $session->setFlash('success', '操作成功');
        } else {
            $session->setFlash('error', $client->getError());
        }

        return $this->redirect($this->request->getReferrer());
    }
}
