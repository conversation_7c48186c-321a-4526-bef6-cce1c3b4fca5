<?php

namespace payment\controllers;

use kvmanager\KVException;
use payment\models\ReportReconciliationDetailSearch;
use Yii;
use yii\base\UserException;
use yii\data\ArrayDataProvider;
use yii\web\Controller;

class ReportReconciliationDetailController extends Controller
{
    /**
     * Lists all WithdrawalManual models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $session = Yii::$app->session;
        $searchModel = new ReportReconciliationDetailSearch();
        try {
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        } catch (UserException $e) {
            $session->setFlash('error', $e->getMessage());
            $dataProvider = new ArrayDataProvider([
                'allModels' => [], // 空数组表示没有数据
            ]);
        } catch (KVException $e) {
            $session->setFlash('error', $e->getMessage());
            $dataProvider = new ArrayDataProvider([
                'allModels' => [], // 空数组表示没有数据
            ]);
        }
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}
