<?php

namespace payment\controllers;

use payment\models\ReportReconciliationSummarySearch;
use Yii;
use yii\web\Controller;

class ReportReconciliationSummaryController extends Controller
{
    /**
     * Lists all WithdrawalManual models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $types = [
            'channel_transaction_order_total' => '通道总数',
            'channel_transaction_order_pending_reconciliation_total' => '通道未对账总数',
            'withdraw_receipt_total' => '放款总数据',
            'withdraw_receipt_pending_reconciliation_total' => '放款未对账总数',
            'withhold_receipt_total' => '还款总数据',
            'withhold_receipt_pending_reconciliation_total' => '还款未对账总数',
            'channel_offline_trade_total' => '交易补录总数据',
            'channel_offline_trade_pending_reconciliation_total' => '交易补录未对账总数',

        ];
        $searchModel = new ReportReconciliationSummarySearch();
        $dataProvider = $searchModel->search(array_keys($types), Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}