<?php

namespace payment\controllers;

use Carbon\Carbon;
use payment\models\ErrorReconciList;
use system\components\PaymentHttpComponent;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\data\SqlDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\Response;

use function xlerr\adminlte\userFullName;

class ErrorReconciListController extends Controller
{
    /**
     * @return string
     * @throws InvalidConfigException
     */
    public function actionIndex(): string
    {
        $searchModel = new ErrorReconciList();
        $dataProvider = $searchModel->search($this->request->get());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @return Response
     * @throws Throwable
     * @throws InvalidConfigException
     */
    public function actionUpdate(): Response
    {
        $session = Yii::$app->getSession();
        $selection = (array)$this->request->post('selection');
        $reconciType = $this->request->post('reconci_type');
        if (empty($selection)) {
            $session->addFlash('error', '请选择需要修复的数据');
        } else {
            $successTotal = 0;
            $failureTotal = 0;
            $client = PaymentHttpComponent::instance();
            $operator = Yii::$app->getUser()->getIdentity()->username;
            foreach ($selection as $item) {
                [$channelName, $channelKey] = json_decode($item, true);
                if ($reconciType === ErrorReconciList::RECONCI_TYPE_WITHHOLD) {
                    $result = $client->fixWithholdReceipt($channelName, $channelKey, $operator);
                } else {
                    $result = $client->fixWithdrawReceipt($channelName, $channelKey);
                }
                if (!$result) {
                    $failureTotal++;
                } else {
                    $successTotal++;
                }
            }

            $session->addFlash('success', sprintf('修复结果: %d条成功, %d条失败', $successTotal, $failureTotal));
        }

        return $this->redirect($this->request->getReferrer());
    }

    public function actionRepairSuccess()
    {
        if ($this->request->isPost) {
            $session = Yii::$app->getSession();
            $selection = (array)$this->request->post('selection');
            $operator = userFullName();
            $client = PaymentHttpComponent::instance();
            $successTotal = 0;
            $failureTotal = 0;
            foreach ($selection as $item) {
                $params = Json::decode($item);
                $result = $client->fixSuccessWithdrawReceipt(
                    $params['withdraw_receipt_channel_name'],
                    $params['withdraw_receipt_channel_key'],
                    $params['channel_reconci_channel_order_no'],
                    $operator
                );
                if (!$result) {
                    $failureTotal++;
                } else {
                    $successTotal++;
                }
            }
            $session->addFlash('success', sprintf('修复结果: %d条成功, %d条失败', $successTotal, $failureTotal));

            return $this->redirect($this->request->getReferrer());
        }

        $sql = <<<SQL
select withdraw_receipt_channel_name,          -- 通道名称 api:channel_name
       channel_reconci_order_finished_at,      -- 订单完成时间
       `withdraw_receipt_started_at`,          -- 订单开始时间
       withdraw_receipt_status,                -- 订单状态
       withdraw_receipt_channel_key,           -- 订单号 api:channel_key
       channel_reconci_channel_order_no,       -- 通道内部订单号 api:channel_inner_key
       `channel_reconci_account`,              -- 账单账号(显示密文)
       `card_account`,                         -- 账号
       channel_reconci_bank_code,              -- 账单银行编码
       `card_bank_code`,                       -- 银行编码
       channel_reconci_amount,                 -- 账单金额
       `withdraw_receipt_amount`,              -- 代付金额
       `withdraw_receipt_channel_resp_code`,   -- 通道编码
       `withdraw_receipt_channel_resp_message` -- 通道信息
from (select channel_reconci_order_finished_at,
             channel_reconci_channel_order_no,
             `channel_reconci_account`,
             IF(channel_reconci_bank_code = '', 'EasyPaisa', channel_reconci_bank_code) channel_reconci_bank_code,
             channel_reconci_amount
      from `withdraw_receipt`
               right join channel_reconci on `withdraw_receipt_channel_name` = `channel_reconci_channel_name` and
                                             `withdraw_receipt_channel_inner_key` = channel_reconci_channel_order_no
               inner join channel on channel_name = channel_reconci_channel_name
      where channel_reconci_status = 2
        and channel_reconci_type = 'withdraw'
        and channel_reconci_channel_name = :channelName
        and `channel_reconci_order_finished_at` >= :startDate
        and channel_reconci_order_finished_at < :endDate
        and withdraw_receipt_id is null) abc
         left join (select *
                    from withdraw_receipt
                             left join card on card_uuid = `withdraw_receipt_card_uuid` and
                                               `withdraw_receipt_user_uuid` = `card_user_uuid`
                    where withdraw_receipt_status in (1)
                      and withdraw_receipt_channel_name = :channelName
                      and `withdraw_receipt_started_at` >= :startDate
                      and withdraw_receipt_started_at < :endDate) abd
                   on abc.channel_reconci_account = abd.card_account and
                      abc.channel_reconci_bank_code = abd.`card_bank_code`
where date(abc.channel_reconci_order_finished_at) = date(abd.withdraw_receipt_started_at)
  and channel_reconci_account = card_account
  and channel_reconci_amount = withdraw_receipt_amount
SQL;

        $dataProvider = new SqlDataProvider([
            'db' => 'paySvrDb',
            'sql' => $sql,
            'params' => [
                'channelName' => trim((string)$this->request->get('channelName')),
                'startDate' => Carbon::parse($this->request->get('startDate', '4 days ago'))->toDateString(),
                'endDate' => Carbon::parse($this->request->get('endDate', 'today'))->addDay()->toDateString(),
            ],
            'key' => function ($model) {
                return ArrayHelper::filter($model, [
                    'withdraw_receipt_channel_name',
                    'withdraw_receipt_channel_key',
                    'channel_reconci_channel_order_no',
                ]);
            },
        ]);

        return $this->render('repair-success', [
            'dataProvider' => $dataProvider,
        ]);
    }
}
