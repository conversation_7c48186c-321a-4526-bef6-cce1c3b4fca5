<?php

use payment\models\Fee;
use payment\models\FeeSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;

/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel FeeSearch */


$this->title = '通道成本费用列表';

?>
    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'id' => 'asset-search-form',
                'type' => ActiveForm::TYPE_INLINE,
            ]) ?>

            <?= $form->field($searchModel, 'fee_ref_key')->textInput() ?>

            <?= $form->field($searchModel, 'startDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                    'placeholder' => '开始时间',
                ],
            ]) ?>
            <?= $form->field($searchModel, 'endDate')->widget(DatePicker::class, [
                'id' => 'startDate',
                'options' => [
                    'placeholder' => '结束时间',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'fee_channel_name', [
                'options' => [
                    'style' => 'min-width: 250px',
                ],
            ])->widget(Select2::class, [
                'data' => Fee::getChannel(),
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '请选择通道',
                ],
            ]) ?>
            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
            <?= Html::a('重置搜索条件', ['index'], [
                'class' => 'btn btn-default',
            ]) ?>
            <?php
            ActiveForm::end() ?>

        </div>
    </div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{update}',
            'buttons' => [
                'update' => static function ($url, Fee $model) {
                    return DialogActionColumn::newButton('修改', [
                        'update',
                        'id' => $model->fee_id,
                        'key' => $model->fee_ref_key,
                    ], [
                        'class' => 'btn-info layer-dialog',
                    ]);
                },
            ],
        ],
        [
            'label' => '支付通道订单号',
            'attribute' => 'fee_ref_key'
        ],
        [
            'label' => '费用类型',
            'value' => function ($item) {
                return Fee::TYPE_LIST[$item['fee_type']] ?? '-';
            },
        ],
        [
            'label' => '签约主体',
            'attribute' => 'fee_sign_company_code',
        ],
        [
            'label' => '通道名称',
            'attribute' => 'fee_channel_name',
        ],
        [
            'label' => '交易完成时间',
            'attribute' => 'fee_finish_at',
        ],
        [
            'label' => '交易订单金额',
            'attribute' => 'fee_order_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '总成本',
            'attribute' => 'fee_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '手续费',
            'attribute' => 'fee_service',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '税费',
            'attribute' => 'fee_tax',
            'class' => MoneyDataColumn::class,
        ],
        [
            'label' => '创建时间',
            'attribute' => 'fee_create_at',
        ],
    ],
]) ?>