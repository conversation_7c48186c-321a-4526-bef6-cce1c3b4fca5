<?php

use backend\widgets\MoneyInput;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\base\DynamicModel;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model DynamicModel */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'id')->textInput([
        'readOnly' => true
    ]) ?>

    <?= $form->field($model, 'fee_ref_key')->textInput([
        'readOnly' => true
    ]) ?>

    <?= $form->field($model, 'adjust_type')->widget(Select2::class, [
        'data' => [
            'ADD' => '增加',
            'SUB' => '减少'
        ],
        'hideSearch' => true,
        'options' => [
            'prompt' => '请选择调整类型',
        ],
    ]) ?>

    <?= $form->field($model, 'amount')->widget(MoneyInput::class) ?>

    <?= $form->field($model, 'fee_type')->widget(Select2::class, [
        'data' => [
            'SERVICE' => '服务费',
            'TAX' => '税费'
        ],
        'hideSearch' => true,
        'options' => [
            'prompt' => '请选择费用类型',
        ],
    ]) ?>

    <?= $form->field($model, 'remark')->textInput() ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('提交', ['class' => 'btn btn-success']) ?>
</div>

<?php ActiveForm::end(); ?>
