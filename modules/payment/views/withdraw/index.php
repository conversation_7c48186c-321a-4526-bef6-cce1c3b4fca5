<?php

use payment\models\Withdraw;
use payment\models\WithdrawSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithdrawSearch */

$this->title = '代付列表';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{detail} {callback-push}',
            'buttons' => [
                'detail' => static fn($url, Withdraw $model) => ActionColumn::newButton('收据详情', [
                    'withdraw-receipt/index',
                    'withdraw_receipt_merchant_key' => $model->withdraw_merchant_key,
                ], [
                    'class' => 'btn-primary layer-dialog',
                    'data' => [
                        'layer-dialog-width' => '98%',
                        'layer-dialog-height' => '96%',
                    ],
                ]),
                'callback-push' => static fn($url, Withdraw $model) => ActionColumn::newButton('回调补推', [
                    'callback-push',
                    'merchant_name' => $model->withdraw_merchant_name,
                    'merchant_key' => $model->withdraw_merchant_key,
                ], [
                    'class' => 'btn-danger',
                    'data' => [
                        'confirm' => '您确定要回调补推吗？',
                        'method' => 'post',
                    ],
                ]),
            ],
        ],

        'withdraw_id',
        'withdraw_merchant_name',
        'withdraw_merchant_key',
        [
            'label' => '支付通道内部订单号',
            'value' => function (Withdraw $model) {
                if ($model->withdrawReceipts) {
                    return implode(',', array_column($model->withdrawReceipts, 'withdraw_receipt_channel_inner_key'));
                }
                return '-';
            }
        ],
        [
            'attribute' => 'withdraw_status',
            'format' => ['in', Withdraw::STATUS_LIST],
        ],
        [
            'attribute' => 'withdraw_amount',
            'class' => MoneyDataColumn::class,
        ],
        'withdraw_reason',
        'withdraw_user_uuid',
        'withdraw_started_at',
        'withdraw_finished_at',
        'withdraw_created_at',
    ],
]);
