<?php

use payment\models\ChannelErrorReasonSearch;
use xlerr\common\widgets\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ChannelErrorReasonSearch */
/* @var $form ActiveForm */
?>

<div class="box search">
    <div class="box-header with-border">
        <div class="box-title">
            <i class="fa fa-search"></i>
            搜索
        </div>
    </div>
    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'channel_error_reason_provider_code') ?>

        <?= $form->field($model, 'channel_error_reason_code') ?>

        <?= $form->field($model, 'channel_error_reason_msg') ?>

        <?= $form->field($model, 'channel_error_reason_reason') ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

        <?= Html::a('重置搜索条件', ['index'], ['class' => 'btn btn-default']) ?>

        <?= Html::a('创建通道错误信息', ['create'], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end() ?>

    </div>
</div>
