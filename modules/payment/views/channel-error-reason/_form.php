<?php

use CommonPayment\models\PlatformError;
use payment\models\ChannelErrorReason;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\db\Expression;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ChannelErrorReason */
/* @var $form ActiveForm */
?>

<?php $form = ActiveForm::begin() ?>

<div class="box-body">

    <div class="row">
        <div class="col-md-6">

            <?= $form->field($model, 'channel_error_reason_provider_code')->textInput() ?>

            <?= $form->field($model, 'channel_error_reason_code')->textInput() ?>

            <?= $form->field($model, 'channel_error_reason_msg')->textInput() ?>

            <?= $form->field($model, 'channel_error_reason_reason')->textInput() ?>

        </div>

    </div>
</div>
<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
