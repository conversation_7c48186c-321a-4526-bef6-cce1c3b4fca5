<?php

use payment\models\ChannelErrorReason;
use payment\models\ChannelErrorReasonSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $searchModel ChannelErrorReasonSearch */
/* @var $dataProvider ActiveDataProvider */
/* @var $auditDataProvider ActiveDataProvider */

$this->title = '通道错误信息列表';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{view} {update} {delete}',
        ],
        [
            'attribute' => 'channel_error_reason_id',
        ],
        [
            'attribute' => 'channel_error_reason_provider_code',
        ],
        [
            'attribute' => 'channel_error_reason_code',
        ],
        [
            'attribute' => 'channel_error_reason_msg',
        ],
        [
            'attribute' => 'channel_error_reason_reason',
        ],
        [
            'attribute' => 'channel_error_reason_created_at',
        ],
        [
            'attribute' => 'channel_error_reason_updated_at',
        ],
    ],
]);
