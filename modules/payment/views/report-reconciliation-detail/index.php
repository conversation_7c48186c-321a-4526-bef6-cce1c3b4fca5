<?php

use payment\models\ChannelTransactionOrder;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\helpers\MoneyHelper;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel array */


$this->title = '对账异常明细';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

$layout = <<<HTML
<div class="box-header with-border">
    <button id="submit-open" type="submit" class="btn-twitter btn btn-xs btn-success">对账状态重置</button>
    <button id="submit-complete" type="submit" class="btn-primary btn btn-xs">无须对账</button>
    <input type="hidden" name="reconci_status" id="reconci_status" value="open">
    <input type="hidden" name="reconci_remark" id="reconci_remark" value="">
</div>
<div class="box-body table-responsive no-padding"> {items}</div>
<div class="box-footer">
    <div class="pull-left">{summary}</div>
    {pager}
</div>
HTML;

$js = <<<JS
    $('#submit-open').on('click', function (e) {
        e.preventDefault(); // 阻止默认提交行为
        if ($('input[name="ids[]"]:checked').length === 0) {
            layer.msg('请至少选中一条记录！', { icon: 2, time: 3000 });
            return;
        }

        layer.confirm('确定要重置对账状态?', function(index) {
            $('#reconci_remark').val("");
            $('#reconci_status').val("open");
            $('#data_list_form').submit();            
        })
    });
    $('#submit-complete').on('click', function (e) {
        e.preventDefault(); // 阻止默认提交行为
        
        if ($('input[name="ids[]"]:checked').length === 0) {
            layer.msg('请至少选中一条记录！', { icon: 2, time: 3000 });
            return;
        }

        //判断是否选择了数据
        layer.prompt({
            title: '请输入备注',
            formType: 2, // 多行文本输入框
            area: ['300px', '150px'],
            placeholder: '请输入备注',
            btn2: function (index, layero, that) {
                //点击取消按钮的逻辑
            }
        }, function (remark, index) {
            // 用户点击确认按钮时的回调
            if (!remark.trim()) { // 检查输入内容是否为空或仅包含空白字符
                layer.msg('输入内容不能为空！', { icon: 2, time: 2000 });
                return false; // 阻止关闭弹窗
            }
            $('#reconci_remark').val(remark);
            $('#reconci_status').val("complete");
            $('#data_list_form').submit();
        });
    });
JS;
$this->registerJs($js);

ActiveForm::begin([
    'id' => 'data_list_form',
    'action' => '/payment/channel-transaction-order/update-status',
]);
$formatter = Yii::$app->formatter;
$formatter->nullDisplay = '';

$transactionTypeFormat = [
    'withhold'      => '代扣',
    'WITHHOLD'      => '代扣',
    'withdraw'      => '代付',
    'WITHDRAW'      => '代付',
    'offline_trade' => '线下交易',
    'OFFLINE_TRADE' => '线下交易',
];

$statusFormat = [
    1 => '处理中',
    2 => '成功',
    3 => '失败',
];
$ourReverseStatus = [
    2 => '冲正成功'
];


echo GridView::widget([
    'dataProvider' => $dataProvider,
    'layout' => $layout,
    'formatter' => $formatter,
    'columns' => [
        [
            'class' => CheckboxColumn::class,
            'name' => 'ids',
            'checkboxOptions' => static function ($model) {
                return [
                    'disabled' => $model['reconci_status'] !== ChannelTransactionOrder::RECONCI_STATUS_PENDING,
                    'value' => $model['id'],
                ];
            },
        ],
        [
            'class' => DialogActionColumn::class,
            'template' => '{withhold} {withdraw}',
            'header'   => '操作',
            'buttons' => [
                'withhold' => static function ($url, $model) {
                    return DialogActionColumn::newButton(
                        '补录充值',
                        [
                            '/payment/channel-offline-trade/withhold',
                            'channel_offline_trade_amount' => $model['amount'],
                            'channel_offline_trade_order_no' => $model['channel_inner_key'],
                        ],
                        ['class' => 'btn-twitter btn btn-xs btn-success layer-dialog',]
                    );
                },
                'withdraw' => static function ($url, $model) {
                    return DialogActionColumn::newButton(
                        '补录提现',
                        [
                            '/payment/channel-offline-trade/withdraw',
                            'channel_offline_trade_amount' => $model['amount'],
                            'channel_offline_trade_order_no' => $model['channel_inner_key'],
                        ],
                        ['class' => 'btn-primary btn btn-xs layer-dialog',]
                    );
                },
            ],
            'visibleButtons' => [
                'withhold' => function ($model) {
                    $transactionType = strtolower($model['transaction_type'] ?? '');
                    $reconciStatus = strtolower($model['reconci_status'] ?? '');
                    if ($transactionType == 'offline_trade' && $reconciStatus == 'pending') {
                        return true;
                    }
                    return empty($model['our_inner_key']);
                },
                'withdraw' => function ($model) {
                    $transactionType = strtolower($model['transaction_type'] ?? '');
                    $reconciStatus = strtolower($model['reconci_status'] ?? '');
                    if ($transactionType == 'offline_trade' && $reconciStatus == 'pending') {
                        return true;
                    }
                    return empty($model['our_inner_key']);
                },
            ]
        ],
        [
            'label'     => '通道',
            'attribute' => 'provider_code',
        ],
        [
            'label'     => '主体',
            'attribute' => 'contract_entity',
        ],
        [
            'label'     => '通道交易单号',
            'attribute' => 'channel_inner_key',
        ],
        [
            'label'     => '通道交易金额',
            'value'     => function ($model) {
                if ($model['amount'] == '') {
                    return null;
                } else {
                    return MoneyHelper::f2y($model['amount'], true);
                }
            },
        ],
        [
            'label'     => '通道交易状态',
            'attribute' => 'channel_status',
        ],
        [
            'label'     => '通道交易完成时间',
            'attribute' => 'finished_at',
        ],
        [
            'label'     => '我方交易单号',
            'attribute' => 'our_inner_key',
        ],
        [
            'label'     => '我方交易金额',
            'value'     => function ($model) {
                if ($model['our_amount'] == '') {
                    return null;
                } else {
                    return MoneyHelper::f2y($model['our_amount'], true);
                }
            },
        ],
        [
            'label'     => '我方交易状态',
            'attribute' => 'our_status',
            'format' => ['in', $statusFormat],
        ],
        [
            'label'     => '我方交易完成时间',
            'attribute' => 'our_finish_at',
        ],

        [
            'label'     => '冲正状态',
            'attribute' => 'our_reverse_status',
            'format' => ['in', $ourReverseStatus],
        ],
        [
            'label'     => '冲正时间',
            'attribute' => 'our_reverse_at',
        ],
        [
            'label'     => '交易类型',
            'attribute' => 'transaction_type',
            'format' => ['in', $transactionTypeFormat],
        ],
        [
            'label'     => '失败原因',
            'attribute' => 'our_reconci_result',
        ],
    ],
]);
ActiveForm::end();
