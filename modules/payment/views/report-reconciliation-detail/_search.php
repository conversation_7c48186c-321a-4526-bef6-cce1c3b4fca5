<?php

use payment\models\Channel;
use payment\models\ReportReconciliationSummarySearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ReportReconciliationSummarySearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title"><?= Yii::t('finance', 'Search') ?></h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '结束时间',
            ],
        ]) ?>

        <?= $form->field($model, 'providerCode', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => Channel::find()
                ->select('channel_provider_code')
                ->where(['!=', 'channel_provider_code', 'empty'])
                ->distinct()
                ->indexBy('channel_provider_code')
                ->column(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '通道',
            ],
        ]) ?>

        <?= $form->field($model, 'contractEntity', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => Channel::find()
                ->select('channel_sign_company_code')
                ->where(['!=', 'channel_sign_company_code', 'empty'])
                ->distinct()
                ->indexBy('channel_sign_company_code')
                ->column(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => '主体',
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a(Yii::t('finance', 'Reset'), ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>
    </div>
</div>
