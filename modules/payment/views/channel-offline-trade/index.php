<?php

use backend\grid\MoneyDataColumn;
use finance\models\BizType;
use payment\models\ChannelOfflineTrade;
use payment\models\ChannelOfflineTradeSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\widgets\GridView;
use Xlerr\SettlementFlow\Models\Rule;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ChannelOfflineTradeSearch */

$this->title = Yii::t('finance', 'Account Turnover Supplements');
$this->params['breadcrumbs'][] = $this->title;

$outTypes = BizType::getBizTypeList(BizType::FUND_FLOW_OUT);
$InTypes = BizType::getBizTypeList();
$types = array_merge($outTypes, $InTypes);

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{update}',
            'buttons' => [
                'update' => static function ($url, ChannelOfflineTrade $model) use ($outTypes, $InTypes) {
                    if (array_key_exists($model->channel_offline_trade_busi_type, $outTypes)) {
                        $url = 'update-withdraw';
                    }
                    if (array_key_exists($model->channel_offline_trade_busi_type, $InTypes)) {
                        $url = 'update-withhold';
                    }

                    return ActionColumn::newButton('编辑', [
                        $url,
                        'id' => $model->channel_offline_trade_id,
                    ], [
                        'class' => 'btn-primary layer-dialog',
                    ]);
                },
            ],
            'visibleButtons' => [
                'update' => static function (ChannelOfflineTrade $model) use ($outTypes, $InTypes) {
                    return array_key_exists($model->channel_offline_trade_busi_type, $outTypes) || array_key_exists(
                            $model->channel_offline_trade_busi_type,
                            $InTypes
                        );
                },
            ],
        ],
        [
            'attribute' => 'channel_offline_trade_oa_id',
            'format' => static fn($id) => $id ?: '-',
        ],
        'channel_offline_trade_id',
        'channel_offline_trade_order_no',
        'channel_offline_trade_channel_name',
        [
            'attribute' => 'channel_offline_trade_busi_type',
            'format' => ['in', $types],
        ],
        [
            'attribute' => 'channel_offline_trade_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'currency',
            'format' => ['in', Rule::currencyList()],
        ],
        'channel_offline_trade_order_from_type',
        'in_account_name',
        'in_account_number',
        'out_account_name',
        'out_account_number',
        'channel_offline_trade_comment',
        'channel_offline_trade_finish_at',
        [
            'label' => '创建人',
            'attribute' => 'channel_offline_trade_create_user',
            'value' => function (ChannelOfflineTrade $trade) {
                return $trade->createUser->username ?? $trade->channel_offline_trade_create_user ?: '-';
            },
        ],
        'channel_offline_trade_create_at',
        'channel_offline_trade_update_at',
        [
            'attribute' => 'channel_offline_trade_reconci_status',
            'format' => ['in', ChannelOfflineTrade::RECONCI_STATUS_MAP],
        ],
        'channel_offline_trade_reconci_at',
        'channel_offline_trade_reconci_result',
    ],
]);
