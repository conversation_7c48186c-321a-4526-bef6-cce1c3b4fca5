<?php

use finance\models\BizType;
use payment\models\ChannelOfflineTrade;
use payment\models\ChannelOfflineTradeSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ChannelOfflineTradeSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title"><?= Yii::t('finance', 'Search') ?></h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'channel_offline_trade_channel_name')->hiddenInput([]) ?>
        <?= $form->field($model, 'finishedAtStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'finishedAtEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成结束时间',
            ],
        ]) ?>
        <?= $form->field($model, 'channel_offline_trade_oa_id') ?>

        <?= $form->field($model, 'channel_offline_trade_order_no') ?>

        <?= $form->field($model, 'channel_offline_trade_busi_type', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => BizType::getBizTypeList([BizType::FUND_FLOW_OUT, BizType::FUND_FLOW_IN]),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_offline_trade_busi_type'),
            ],
        ]) ?>

        <?= $form->field($model, 'channel_offline_trade_reconci_status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => ChannelOfflineTrade::RECONCI_STATUS_MAP,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
                'multiple' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_offline_trade_reconci_status'),
            ],
        ]) ?>


        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a(Yii::t('finance', 'Reset'), ['index'], ['class' => 'btn btn-default']) ?>
<!--        --><?php //= Html::a(Yii::t('finance', 'Withdraw'), ['withdraw'], ['class' => 'btn btn-success']) ?>
        <?= Html::a(Yii::t('finance', 'Withhold'), ['withhold'], ['class' => 'btn btn-success layer-dialog']) ?>

        <?php ActiveForm::end(); ?>

    </div>
</div>
