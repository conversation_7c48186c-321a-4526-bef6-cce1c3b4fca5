<?php

use backend\widgets\MoneyInput;
use finance\models\BizType;
use kartik\widgets\DateTimePicker;
use kvmanager\models\KeyValue;
use payment\models\ChannelOfflineTrade;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use Xlerr\SettlementFlow\Models\Rule;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/* @var $this View */
/* @var $model ChannelOfflineTrade */

$this->title = Yii::t('finance', 'Create Account Turnover Supplement');
$this->params['breadcrumbs'][] = ['label' => Yii::t('finance', 'Channel Offline Trade'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
$type = Yii::$app->request->get('type');
?>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title"><?= Yii::t('finance', 'Create') ?></div>
    </div>

    <?php
    $form = ActiveForm::begin([
        'id' => 'form',
    ]); ?>

    <div class="box-body">

        <?= $form->field($model, 'channel_offline_trade_oa_id') ?>

        <?= $form->field($model, 'channel_offline_trade_busi_type')->widget(Select2::class, [
            'data' => BizType::getBizTypeList(BizType::FUND_FLOW_OUT, '提现'),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'value' => $type ?? $model->channel_offline_trade_busi_type,
                'prompt' => $model->getAttributeLabel('channel_offline_trade_busi_type'),
            ],
        ]) ?>

        <?= $form->field($model, 'channel_offline_trade_amount')->widget(MoneyInput::class, [
            'options' => [
                'placeholder' => '提现金额',
            ],
        ]) ?>

        <?= $form->field($model, 'currency')->widget(Select2::class, [
            'data' => Rule::currencyList(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('currency'),
            ],
        ]) ?>

        <?= $form->field($model, 'channel_offline_trade_channel_name')->widget(Select2::class, [
            'data' => ChannelOfflineTrade::getPaymentChannel(),
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'hideSearch' => true,
            'options' => [
                'prompt' => $model->getAttributeLabel('channel_offline_trade_channel_name'),
            ],
        ]) ?>

        <div class="row">
            <div class="col-sm-6">
                <?= $form->field($model, 'out_account_number')->textInput(['readOnly' => true])->label('出款账户号') ?>
            </div>
            <div class="col-sm-6">
                <?= $form->field($model, 'out_account_name')->textInput(['readOnly' => true])->label('出款账户名称') ?>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <?= $form->field($model, 'in_account_number')->textInput(['maxlength' => true])->label('收款账户号') ?>
            </div>
            <div class="col-sm-6">
                <?= $form->field($model, 'in_account_name')->textInput(['maxlength' => true])->label('收款账户名称') ?>
            </div>
        </div>
        <?= $form->field($model, 'channel_offline_trade_order_no')->textInput([
                'id' => 'channel_offline_trade_order_no',
        ])
            ->label('订单号 (格式:YYYYMMDD_withdraw_XXXXX 例:20210827_withdraw_1)');
        ?>

        <?= $form->field($model, 'channel_offline_trade_finish_at')->widget(DateTimePicker::class, [
            'id' => 'channel_offline_trade_finish_at',
            'type' => DateTimePicker::TYPE_INPUT,
            'options' => [
                'id' => 'channel_offline_trade_finish_at',
            ],
            'pluginOptions' => [
                'todayBtn' => true,
                'todayHighlight' => true,
                'autoclose' => true,
                'format' => 'yyyy-mm-dd hh:ii:ss',
            ],
        ]) ?>
        <?= $form->field($model, 'channel_offline_trade_comment')->textInput(['maxlength' => true]) ?>


    </div>

    <div class="box-footer">
        <?= Html::submitButton('保存', [
            'class' => 'btn btn-primary',
        ]) ?>
    </div>

    <?php
    ActiveForm::end(); ?>

</div>
<script>
    <?php $this->beginBlock('jsBlock') ?>
    (function () {
        const channelSelect = $('#<?= Html::getInputId($model, 'channel_offline_trade_channel_name') ?>');
        const accountNumberInput = $('#<?= Html::getInputId($model, 'out_account_number') ?>');
        const accountNameInput = $('#<?= Html::getInputId($model, 'out_account_name') ?>');

        const channelAccounts = <?= Json::encode(KeyValue::take('withdraw_channel_account_map')) ?>;

        channelSelect.on('change', function () {
            const selectedChannel = $(this).val();
            if (selectedChannel in channelAccounts) {
                accountNumberInput.val(channelAccounts[selectedChannel].account_identify);
                accountNameInput.val(channelAccounts[selectedChannel].account_name);
            } else {
                accountNumberInput.val('');
                accountNameInput.val('');
            }
        });
    })();
    <?php $this->endBlock()?>
    <?php $this->registerJs($this->blocks['jsBlock'])?>
</script>
<?php
$js = <<<JS
    $('#channel_offline_trade_order_no').on('change', function () {
        let url = 'get-finish-at?orderNo=' + $(this).val();
        $.ajax({
            type: "POST",
            url: url,
            timeout: 30000,
            error: function(jqXHR, textStatus, errorThrown) {
               alert("网络错误或请求超时，请检查网络或稍后再试。");
           },
           success: function (response) {
                if (response.length > 0) {
                    $('#channel_offline_trade_finish_at').val(response);
                }
           }
        });
    });
JS;
//只有自动录入的才查询完成时间
if (strpos(strtolower($model->channel_offline_trade_order_no), 'null') === 0) {
    $this->registerJs($js);
}
?>

