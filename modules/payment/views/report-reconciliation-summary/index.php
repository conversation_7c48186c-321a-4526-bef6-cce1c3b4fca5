<?php

use payment\models\ReportReconciliationSummarySearch;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ReportReconciliationSummarySearch */

$this->title = '交易对账汇总';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

$sql = <<<sql
sum(channel_transaction_order_total) as channelTransactionOrderTotal, 
sum(withdraw_receipt_total + withhold_receipt_total + channel_offline_trade_total) as outTotal, 
sum(
    withdraw_receipt_pending_reconciliation_total
    + withhold_receipt_pending_reconciliation_total 
    + channel_offline_trade_pending_reconciliation_total
    + channel_transaction_order_pending_reconciliation_total
) as failTotal
sql;

$summary = (new Query())
    ->select(new Expression($sql))
    ->from(['sub' => $dataProvider->query])
    ->one();

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'showFooter' => true,
    'columns' => [
        [
            'label'     => '对账日期',
            'format'    => 'raw',
            'value'     => function ($model) {
                return Html::a(
                    $model['date'],
                    [
                        '/payment/report-reconciliation-detail/index',
                        'providerCode' => $model['provider_code'],
                        'contractEntity' => $model['contract_entity'],
                        'startDate' => $model['date'],
                        'endDate' => $model['date']
                    ],
                    ['target' => '_blank',]
                );
            },
            'footer' => '合计'
        ],
        [
            'label'     => '渠道编号',
            'attribute' => 'provider_code',
        ],
        [
            'label'     => '签约主体',
            'attribute' => 'contract_entity',
        ],
        [
            'label'     => '通道总数',
            'attribute' => 'channel_transaction_order_total',
            'footer'    => $summary['channelTransactionOrderTotal'] ?? 0,
        ],
        [
            'label'     => '我方交易总数',
            'value'     => function ($model) {
                return $model['withdraw_receipt_total']
                    + $model['withhold_receipt_total']
                    + $model['channel_offline_trade_total'];
            },
            'footer'    => $summary['outTotal'] ?? 0,
        ],
        [
            'label'     => '对账失败',
            'value'     => function ($model) {
                return $model['withdraw_receipt_pending_reconciliation_total']
                    + $model['withhold_receipt_pending_reconciliation_total']
                    + $model['channel_offline_trade_pending_reconciliation_total']
                    + $model['channel_transaction_order_pending_reconciliation_total'];
            },
            'footer'    => $summary['failTotal'] ?? 0,
        ]
    ],
]);
