<?php

use payment\models\WithdrawReceipt;
use payment\models\WithdrawReceiptSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithdrawReceiptSearch */

$this->title = '放款交易';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);
echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{order-query}',
            'buttons' => [
                'order-query' => static fn($_, WithdrawReceipt $model) => ActionColumn::newButton('通道订单查询', [
                    'order-query',
                    'channel_name' => $model->withdraw_receipt_channel_name,
                    'channel_key' => $model->withdraw_receipt_channel_key,
                ], [
                    'class' => 'btn-primary layer-dialog',
                    'data' => [
                        'layer-dialog-width' => '60%',
                        'layer-dialog-height' => '70%',
                    ],
                ]),
            ],
        ],
        'withdraw_receipt_id',
        'withdraw_receipt_merchant_name',
        'withdraw_receipt_merchant_key',
        [
            'attribute' => 'withdraw_receipt_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'withdraw_receipt_status',
            'format' => ['in', WithdrawReceipt::STATUS_LIST],
        ],
        'withdraw_receipt_channel_name',
        'withdraw_receipt_channel_key',
        'withdraw_receipt_channel_resp_code',
        'withdraw_receipt_channel_resp_message',
        'withdraw_receipt_started_at',
        'withdraw_receipt_finished_at',
        'withdraw_receipt_created_at',
        'withdraw_receipt_trade_no',
        'withdraw_receipt_channel_inner_key',
        [
            'attribute' => 'withdraw_receipt_service_charge',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'withdraw_receipt_service_tax',
            'class' => MoneyDataColumn::class,
        ],
        'withdraw_receipt_card_uuid',
        'withdraw_receipt_biz_type',
        'withdraw_receipt_reverse_status',
        'withdraw_receipt_reverse_at',
        [
            'attribute' => 'withdraw_receipt_reconci_status',
            'format' => ['in', WithdrawReceipt::RECONCI_STATUS_MAP],
        ],
        'withdraw_receipt_reconci_result'
    ],
]);
