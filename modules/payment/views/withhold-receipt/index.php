<?php

use payment\models\WithholdReceipt;
use payment\models\WithholdReceiptSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel WithholdReceiptSearch */

$this->title = '放款交易';
$this->params['breadcrumbs'][] = $this->title;
echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => ActionColumn::class,
            'template' => '{order-query} {update}',
            'buttons' => [
                'order-query' => static fn($_, WithholdReceipt $model) => ActionColumn::newButton('通道订单查询', [
                    'order-query',
                    'channel_name' => $model->withhold_receipt_channel_name,
                    'channel_key' => $model->withhold_receipt_channel_key,
                ], [
                    'class' => 'btn-primary layer-dialog',
                    'data' => [
                        'layer-dialog-width' => '60%',
                        'layer-dialog-height' => '70%',
                    ],
                ]),
                'update' => static fn($_, WithholdReceipt $model) => ActionColumn::newButton('修改', [
                    'update',
                    'id' => $model->withhold_receipt_id,
                ], [
                    'class' => 'btn-primary layer-dialog',
                    'data' => [
                        'layer-dialog-width' => '60%',
                        'layer-dialog-height' => '70%',
                    ],
                ]),
            ],
        ],
        'withhold_receipt_id',
        'withhold_receipt_merchant_name',
        'withhold_receipt_merchant_key',
        'withhold_receipt_channel_name',
        'withhold_receipt_channel_key',
        'withhold_receipt_channel_inner_key',
        [
            'attribute' => 'withhold_receipt_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'withhold_receipt_status',
            'format' => ['in', WithholdReceipt::STATUS_LIST],
        ],
        'withhold_receipt_channel_resp_code',
        'withhold_receipt_channel_resp_message',
        'withhold_receipt_started_at',
        'withhold_receipt_finished_at',
        'withhold_receipt_created_at',
        'withhold_receipt_service_charge',
        'withhold_receipt_service_tax',
        'withhold_receipt_resp_payment_option',
        'withhold_receipt_resp_payment_mode',
        'withhold_receipt_biz_type',
        'withhold_receipt_reconci_status',
        'withhold_receipt_reconci_at',
        'withhold_receipt_reconci_result'
    ],
]);
