<?php

use payment\models\WithholdReceipt;
use payment\models\WithholdReceiptSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model WithholdReceiptSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title"><?= Yii::t('finance', 'Search') ?></h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'withhold_receipt_channel_name')->hiddenInput([]) ?>
        <?= $form->field($model, 'finishedAtStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'finishedAtEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '完成结束时间',
            ],
        ]) ?>

        <?= $form->field($model, 'withhold_receipt_reconci_status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => WithholdReceipt::RECONCI_STATUS_MAP,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
                'multiple' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('withhold_receipt_reconci_status'),
            ],
        ]) ?>


        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a(Yii::t('finance', 'Reset'), ['index'], ['class' => 'btn btn-default']) ?>

        <?php ActiveForm::end(); ?>
    </div>
</div>
