<?php

use backend\assets\LayuiAsset;
use payment\models\ChannelTransactionOrder;
use payment\models\ChannelTransactionOrderSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel ChannelTransactionOrderSearch */

$this->title = '通道账单对账结果';
$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

$js = <<<JS
function setOpen(id) {
    var layer = layui.layer;
    layer.confirm('确定要重置对账状态?', function(index) {
        $('#ids').val(id);
        $('#reconciRemark').val("");
        $('#reconciStatus').val("open");
        $('#data_list_form').submit();            
    })
}

function setComplete(id) {
    var layer = layui.layer;
    layer.prompt({
            title: '请输入备注',
            formType: 2, // 多行文本输入框
            area: ['300px', '150px'],
            placeholder: '请输入备注',
            btn2: function (index, layero, that) {
                //点击取消按钮的逻辑
            }
        }, function (remark, index) {
            // 用户点击确认按钮时的回调
            if (!remark.trim()) { // 检查输入内容是否为空或仅包含空白字符
                layer.msg('输入内容不能为空！', { icon: 2, time: 2000 });
                return false; // 阻止关闭弹窗
            }
            $('#ids').val(id);
            $('#reconciRemark').val(remark);
            $('#reconciStatus').val("complete");
            $('#data_list_form').submit();
        });
}
JS;
LayuiAsset::register($this);
$this->registerJs($js, View::POS_HEAD);
ActiveForm::begin([
    'id' => 'data_list_form',
    'action' => '/payment/channel-transaction-order/update-status',
]);
echo <<<HTML
    <input type="hidden" name="ids[]" id="ids" value="">
    <input type="hidden" name="reconci_status" id="reconciStatus" value="">
    <input type="hidden" name="reconci_remark" id="reconciRemark" value="">
HTML;
ActiveForm::end();
echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => DialogActionColumn::class,
            'template' => '{set-open} {set-complete}',
            'buttons' => [
                'set-open' => static function ($url, ChannelTransactionOrder $model) {
                    return DialogActionColumn::newButton(
                        '对账状态重置',
                        null,
                        [
                            'class' => 'btn-danger',
                            'onclick' => "setOpen({$model->id});",
                        ],
                    );
                },
                'set-complete' => static function ($url, ChannelTransactionOrder $model) {
                    return DialogActionColumn::newButton(
                        '无须对账',
                        null,
                        [
                            'class' => 'btn btn-xs btn-danger',
                            'onclick' => "setComplete({$model->id});",
                        ]
                    );
                },
            ],
            'visibleButtons' => [
                'set-open' => function ($model) {
                    return $model->reconci_status === ChannelTransactionOrder::RECONCI_STATUS_PENDING;
                },
                'set-complete' => function ($model) {
                    return $model->reconci_status === ChannelTransactionOrder::RECONCI_STATUS_PENDING;
                },
            ]
        ],
        'provider_code',
        'contract_entity',
        'bill_date',
        'channel_key',
        'channel_inner_key',
        'request_at',
        'finished_at',
        'created_at',
        'updated_at',
        'channel_transaction_type',
        [
            'attribute' => 'amount',
            'class' => MoneyDataColumn::class,
        ],
        'currency',
        [
            'attribute' => 'fee_amount',
            'class' => MoneyDataColumn::class,
        ],
        [
            'attribute' => 'tax_amount',
            'class' => MoneyDataColumn::class,
        ],
        'channel_status',
        'payment_method',
        'source',
        'channel_name',
        'transaction_type',
        [
            'attribute' => 'reconci_status',
            'format' => ['in', ChannelTransactionOrder::RECONCI_STATUS_MAP],
        ],
        'reconci_result',
        'reconci_remark'
    ],
]);
