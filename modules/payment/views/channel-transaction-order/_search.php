<?php

use payment\models\Channel;
use payment\models\ChannelTransactionOrder;
use payment\models\ChannelTransactionOrderSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model ChannelTransactionOrderSearch */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <i class="fa fa-search"></i>
        <h3 class="box-title"><?= Yii::t('finance', 'Search') ?></h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>

    <div class="box-body">

        <?php $form = ActiveForm::begin([
            'action' => ['index'],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]); ?>
        <?= $form->field($model, 'finishedAtStartDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '通道订单完成开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'finishedAtEndDate')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '通道订单完成结束时间',
            ],
        ]) ?>

        <?= $form->field($model, 'provider_code', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => Channel::find()
                ->select('channel_provider_code')
                ->where(['!=', 'channel_provider_code', 'empty'])
                ->distinct()
                ->indexBy('channel_provider_code')
                ->column(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('provider_code'),
            ],
        ]) ?>

        <?= $form->field($model, 'contract_entity', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => Channel::find()
                ->select('channel_sign_company_code')
                ->where(['!=', 'channel_sign_company_code', 'empty'])
                ->distinct()
                ->indexBy('channel_sign_company_code')
                ->column(),
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('contract_entity'),
            ],
        ]) ?>

        <?= $form->field($model, 'reconci_status', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:120px',
            ],
        ])->widget(Select2::class, [
            'data' => ChannelTransactionOrder::RECONCI_STATUS_MAP,
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
                'multiple' => true,
            ],
            'options' => [
                'prompt' => $model->getAttributeLabel('reconci_status'),
            ],
        ]) ?>


        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a(Yii::t('finance', 'Reset'), ['index'], ['class' => 'btn btn-default']) ?>
        <?= Html::a('交易补录', ['/finance/payment-withdrawal-pending-tasks/index'], ['class' => 'btn btn-success']) ?>

        <?php ActiveForm::end(); ?>
    </div>
</div>
