<?php

use finance\models\BizType;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\base\DynamicModel;
use yii\helpers\Html;
use yii\web\View;

$this->title = '代付';

$this->params['breadcrumbs'][] = ['label' => '灰度测试'];
$this->params['breadcrumbs'][] = $this->title;

/* @var $this View */
/* @var $model DynamicModel */
/* @var $log string */
?>

<div class="row">
    <div class="col-md-6">
        <div class="box box-primary">
            <div class="box-header with-border">
                <div class="box-title">代付</div>
            </div>

            <?php $form = ActiveForm::begin(); ?>

            <div class="box-body">

                <?= $form->field($model, 'merchant_name')->textInput() ?>

                <?= $form->field($model, 'merchant_key')->textInput() ?>

                <?= $form->field($model, 'channel_name')->textInput() ?>

                <?= $form->field($model, 'trade_no')->textInput() ?>

                <?= $form->field($model, 'user_uuid')->textInput() ?>

                <?= $form->field($model, 'sign_company')->textInput() ?>

                <?= $form->field($model, 'card_uuid')->textInput() ?>

                <?= $form->field($model, 'amount')->textInput() ?>

                <?= $form->field($model, 'reason')->textInput() ?>

                <?= $form->field($model, 'biz_type')->widget(Select2::class, [
                    'data'       => BizType::getBizTypeList(BizType::FUND_FLOW_OUT,'代付','grey_withdraw'),
                    'hideSearch' => false,
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options'    => [
                        'prompt' => $model->getAttributeLabel('biz_type'),
                    ],
                ]) ?>

                <?= $form->field($model, 'from_app') ?>

                <?= $form->field($model, 'asset_due_at') ?>

                <?= $form->field($model, 'asset_interest_rate') ?>

            </div>

            <div class="box-footer">
                <?= Html::submitButton('提交', [
                    'class' => 'btn btn-success',
                ]) ?>
            </div>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
    <div class="col-md-6">
        <div class="box box-info">
            <div class="box-header with-border">
                <div class="box-title">请求信息</div>
            </div>

            <div class="box-body">
                <?= CodeEditor::widget([
                    'name'          => 'show1',
                    'value'         => $log,
                    'clientOptions' => [
                        'maxLines' => 34,
                        'readOnly' => true,
                    ],
                ]) ?>
            </div>
        </div>
    </div>
</div>
