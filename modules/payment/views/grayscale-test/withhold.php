<?php

use finance\models\BizType;
use kartik\widgets\DepDrop;
use kvmanager\models\KeyValue;
use payment\models\Channel;
use payment\models\PaymentMode;
use payment\models\SignCompany;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\base\DynamicModel;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

$this->title = '代扣';

$this->params['breadcrumbs'][] = ['label' => '灰度测试'];
$this->params['breadcrumbs'][] = $this->title;

/* @var $this View */
/* @var $model DynamicModel */
/* @var $log string */
?>

<div class="row">
    <div class="col-md-6">
        <div class="box box-primary">
            <div class="box-header with-border">
                <div class="box-title">代扣</div>
            </div>

            <?php $form = ActiveForm::begin(); ?>

            <div class="box-body">

                <?= $form->field($model, 'merchant_name')->textInput() ?>

                <?= $form->field($model, 'merchant_key')->textInput() ?>

                <?= $form->field($model, 'sign_company')->widget(Select2::class, [
                    'data' => SignCompany::find()->select('sign_company_full_name')
                        ->indexBy('sign_company_code')->column(),
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('sign_company'),
                    ],
                ]) ?>

                <?= $form->field($model, 'channel_name')->widget(Select2::class, [
                    'data' => Channel::find()->select('channel_alias')->indexBy('channel_name')->where([
                        'channel_type' => 1,
                    ])->column(),
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('channel_name'),
                    ],
                ]) ?>

                <?= $form->field($model, 'card_uuid')->textInput() ?>
                <?= $form->field($model, 'user_uuid')->textInput() ?>

                <?= $form->field($model, 'user_name_encrypt')->textInput() ?>
                <?= $form->field($model, 'email_encrypt')->textInput() ?>
                <?= $form->field($model, 'mobile_encrypt')->textInput() ?>
                <?= $form->field($model, 'card_num_encrypt')->textInput() ?>

                <?= $form->field($model, 'payment_type')->widget(Select2::class, [
                    'data' => KeyValue::take('provider_product_type', 'paysvr'),
                    'hideSearch' => true,
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('payment_type'),
                    ],
                ]) ?>
                <?= $form->field($model, 'payment_option')->widget(Select2::class, [
                    'data' => PaymentMode::find()->select('payment_mode_option')->distinct()
                        ->indexBy('payment_mode_option')->column(),
                    'hideSearch' => true,
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('payment_option'),
                    ],
                ]) ?>

                <?= $form->field($model, 'payment_mode')->widget(DepDrop::class, [
                    'type' => DepDrop::TYPE_SELECT2,
                    'options' => [
                        'prompt' => $model->getAttributeLabel('payment_mode'),
                    ],
                    'select2Options' => [
                        'theme' => Select2::THEME_DEFAULT,
                    ],
                    'pluginOptions' => [
                        'depends' => [Html::getInputId($model, 'payment_option')],
                        'initDepends' => [Html::getInputId($model, 'payment_option')],
                        'initialize' => false,
                        'url' => Url::to(['depDropPaymentMode', 'default' => $model->payment_mode]),
                    ],
                ]) ?>

                <?= $form->field($model, 'amount')->textInput() ?>

                <?= $form->field($model, 'redirect')->textInput() ?>

                <?= $form->field($model, 'callback')->textInput() ?>

                <?= $form->field($model, 'operator')->widget(Select2::class, [
                    'data' => Channel::supportOperatorList(),
                    'hideSearch' => true,
                ]) ?>

                <?= $form->field($model, 'biz_type')->widget(Select2::class, [
                    'data' => BizType::getBizTypeList(BizType::FUND_FLOW_IN, '代扣', 'grey_withhold'),
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => '业务类型',
                    ],
                ]) ?>

                <?= $form->field($model, 'from_app') ?>

            </div>

            <div class="box-footer">
                <?= Html::submitButton('提交', [
                    'class' => 'btn btn-success',
                ]) ?>
            </div>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
    <div class="col-md-6">
        <div class="box box-info">
            <div class="box-header with-border">
                <div class="box-title">请求信息</div>
            </div>

            <div class="box-body">
                <?= CodeEditor::widget([
                    'name' => 'show1',
                    'value' => $log,
                    'clientOptions' => [
                        'maxLines' => 38,
                        'readOnly' => true,
                    ],
                ]) ?>
            </div>
        </div>
    </div>
</div>
