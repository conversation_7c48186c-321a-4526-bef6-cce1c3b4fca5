<?php

use finance\models\BizType;
use payment\models\Channel;
use payment\models\SignCompany;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\base\DynamicModel;
use yii\helpers\Html;
use yii\web\View;

$this->title = '虚户开户';

$this->params['breadcrumbs'][] = ['label' => '灰度测试'];
$this->params['breadcrumbs'][] = $this->title;

/* @var $this View */
/* @var $model DynamicModel */
/* @var $log string */
?>

<div class="row">
    <div class="col-md-6">
        <div class="box box-primary">
            <div class="box-header with-border">
                <div class="box-title">虚户开户</div>
            </div>

            <?php $form = ActiveForm::begin(); ?>

            <div class="box-body">

                <?= Html::activeHiddenInput($model, 'merchant_name') ?>

                <?= Html::activeHiddenInput($model, 'merchant_key') ?>

                <?= $form->field($model, 'sign_company')->widget(Select2::class, [
                    'data' => SignCompany::find()->select('sign_company_full_name')
                        ->indexBy('sign_company_code')->column(),
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('sign_company'),
                    ],
                ]) ?>

                <?= $form->field($model, 'channel_name')->widget(Select2::class, [
                    'data' => Channel::find()->select('channel_alias')->indexBy('channel_name')->where([
                        'channel_type' => 1,
                    ])->column(),
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('channel_name'),
                    ],
                ]) ?>

                <?= $form->field($model, 'user_uuid')->textInput() ?>

                <?= $form->field($model, 'account_no')->textInput() ?>

                <?= $form->field($model, 'biz_type')->widget(Select2::class, [
                    'data' => BizType::getBizTypeList(BizType::FUND_FLOW_IN, '代扣', 'grey_withhold'),
                    'hideSearch' => false,
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('biz_type'),
                    ],
                ]) ?>
                <?= $form->field($model, 'bank_code')->widget(Select2::class, [
                    'data' => $model->getBankCode(),
                    'hideSearch' => false,
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                    'options' => [
                        'prompt' => $model->getAttributeLabel('bank_code'),
                    ],
                ]) ?>

                <?= $form->field($model, 'amount')->textInput() ?>

            </div>

            <div class="box-footer">
                <?= Html::submitButton('提交', [
                    'class' => 'btn btn-success',
                ]) ?>
            </div>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
    <div class="col-md-6">
        <div class="box box-info">
            <div class="box-header with-border">
                <div class="box-title">请求信息</div>
            </div>

            <div class="box-body">
                <?= CodeEditor::widget([
                    'name' => 'show1',
                    'value' => $log,
                    'clientOptions' => [
                        'maxLines' => 38,
                        'readOnly' => true,
                    ],
                ]) ?>
            </div>
        </div>
    </div>
</div>
