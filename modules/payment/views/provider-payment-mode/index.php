<?php

use payment\models\ProviderPaymentMode;
use payment\models\ProviderProduct;
use xlerr\common\widgets\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel payment\models\ProviderPaymentModeSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('payment', 'Provider Payment Modes');
$this->params['breadcrumbs'][] = $this->title;
?>


<?= $this->render('_search', ['model' => $searchModel]); ?>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{update} {delete}',
        ],

        [
            'attribute' => 'provider_payment_provider_code',
            'value' => 'provider.provider_name',
        ],
        'provider_payment_platform_option',
        [
            'attribute' => 'provider_payment_platform_mode',
            'value' => 'paymentMode.payment_mode_name',
        ],
        [
            'attribute' => 'provider_payment_status',
            'format' => ['in', ProviderPaymentMode::STATUS_LIST],
        ],
        'provider_payment_provider_mode',
        'provider_payment_mode_name',
        [
            'attribute' => 'provider_payment_product_type',
            'value' => function (ProviderPaymentMode $model) {
                $types = explode(',', $model->provider_payment_product_type);
                $types = array_filter($types);
                if ($types) {
                    return implode(',', ArrayHelper::filter(ProviderProduct::typeList(), $types));
                }

                return '-';
            },
        ],
        'provider_payment_mode_score',
        'provider_payment_created_at',
        'provider_payment_updated_at',
    ],
]); ?>
