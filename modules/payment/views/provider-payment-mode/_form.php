<?php

use kartik\widgets\DepDrop;
use payment\models\PaymentMode;
use payment\models\Provider;
use payment\models\ProviderPaymentMode;
use payment\models\ProviderProduct;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $model ProviderPaymentMode */

?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'provider_payment_provider_code')->widget(Select2::class, [
        'data' => Provider::find()
            ->select('provider_name')
            ->indexBy('provider_code')
            ->column(),
        'hideSearch' => true,
        'options' => [
            'prompt' => $model->getAttributeLabel('provider_payment_provider_code'),
        ],
    ]) ?>

    <?= $form->field($model, 'provider_payment_platform_option')->widget(Select2::class, [
        'data' => PaymentMode::find()
            ->select('payment_mode_option')
            ->indexBy('payment_mode_option')
            ->distinct()
            ->column(),
        'hideSearch' => true,
        'options' => [
            'prompt' => $model->getAttributeLabel('provider_payment_platform_option'),
        ],
    ]) ?>

    <?= $form->field($model, 'provider_payment_status')->widget(Select2::class, [
        'data' => ProviderPaymentMode::STATUS_LIST,
        'hideSearch' => true,
        'options' => [
            'prompt' => $model->getAttributeLabel('provider_payment_platform_option'),
        ],
    ]) ?>

    <?= $form->field($model, 'provider_payment_platform_mode')->widget(DepDrop::class, [
        'type' => DepDrop::TYPE_SELECT2,
        'options' => [
            'prompt' => $model->getAttributeLabel('provider_payment_platform_mode'),
        ],
        'select2Options' => [
            'theme' => Select2::THEME_DEFAULT,
        ],
        'pluginOptions' => [
            'depends' => [Html::getInputId($model, 'provider_payment_platform_option')],
            'initDepends' => [Html::getInputId($model, 'provider_payment_platform_option')],
            'initialize' => !$model->isNewRecord,
            'url' => Url::to(['depDropPaymentMode', 'default' => $model->provider_payment_platform_mode]),
        ],
    ]) ?>

    <?= $form->field($model, 'provider_payment_provider_mode')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'provider_payment_mode_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'productType')->widget(Select2::class, [
        'data' => ProviderProduct::typeList(),
        'hideSearch' => false,
        'pluginOptions' => [
            'multiple' => true,
        ],
    ]) ?>

    <?= $form->field($model, 'provider_payment_mode_config')->widget(CodeEditor::class, [
        'clientOptions' => [
            'minLines' => 6,
        ],
    ]) ?>

    <?= $form->field($model, 'provider_payment_mode_score')->textInput(['maxlength' => true]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton($model->isNewRecord ? Yii::t('payment', 'Create') : Yii::t('payment', 'Update'),
        ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
</div>

<?php ActiveForm::end(); ?>
