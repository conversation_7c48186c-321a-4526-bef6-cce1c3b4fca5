<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model payment\models\ProviderPaymentMode */

$this->title = $model->provider_payment_id;
$this->params['breadcrumbs'][] = ['label' => Yii::t('payment', 'Provider Payment Modes'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a(Yii::t('payment', 'Update'), ['update', 'id' => $model->provider_payment_id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a(Yii::t('payment', 'Delete'), ['delete', 'id' => $model->provider_payment_id], [
        'class' => 'btn btn-danger',
        'data' => [
            'confirm' => Yii::t('yii', 'Are you sure you want to delete this item?'),
            'method' => 'post',
        ],
    ]) ?>
    <?= Html::a(Yii::t('payment', 'Go Back'), ['index'], ['class' => 'btn btn-default']) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title"><?= Yii::t('payment', 'Detail') ?></div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'options'    => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'provider_payment_id',
                'provider_payment_provider_code',
                'provider_payment_platform_option',
                'provider_payment_platform_mode',
                'provider_payment_provider_mode',
                'provider_payment_product_type',
                'provider_payment_mode_score',
                'provider_payment_created_at',
                'provider_payment_updated_at',
            ],
        ]) ?>
    </div>
</div>
