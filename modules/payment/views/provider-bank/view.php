<?php

use kvmanager\models\KeyValue;
use payment\models\Provider;
use payment\models\ProviderBank;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\DetailView;

/* @var $this View */
/* @var $model \payment\models\ProviderBank */

$this->title                   = $model->provider_bank_id;
$this->params['breadcrumbs'][] = ['label' => '渠道银行配置', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<p>
    <?= Html::a('更新', ['update', 'id' => $model->provider_bank_id], ['class' => 'btn btn-primary']) ?>
    <?= Html::a('删除', ['delete', 'id' => $model->provider_bank_id], [
        'class' => 'btn btn-danger',
        'data'  => [
            'confirm' => Yii::t('yii', 'Are you sure you want to delete this item?'),
            'method'  => 'post',
        ],
    ]) ?>
    <?= Html::a('返回列表', ['index'], ['class' => 'btn btn-default']) ?>
</p>

<div class="box box-primary">
    <div class="box-header with-border">
        <div class="box-title">详情</div>
    </div>
    <div class="box-body no-padding">
        <?= DetailView::widget([
            'model'      => $model,
            'options'    => [
                'class' => 'table table-striped',
            ],
            'attributes' => [
                'provider_bank_id',
                'provider_bank_code',
                'provider_bank_name',
                [
                    'attribute' => 'provider_bank_provider_code',
                    'format'    => [
                        'in',
                        Provider::find()
                            ->select('provider_name')
                            ->indexBy('provider_code')
                            ->column(),
                    ],
                ],
                'provider_bank_platform_bank_code',
                [
                    'attribute' => 'provider_bank_product_type',
                    'format'    => ['in', KeyValue::take('provider_product_type', 'paysvr')],
                ],
                [
                    'attribute' => 'provider_bank_status',
                    'format'    => ['in', ProviderBank::STATUS_LIST],
                ],
                'provider_bank_score',
                'provider_bank_created_at',
                'provider_bank_updated_at',
            ],
        ]) ?>
    </div>
</div>
