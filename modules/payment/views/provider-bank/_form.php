<?php

use kvmanager\models\KeyValue;
use payment\models\Provider;
use payment\models\ProviderBank;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model \payment\models\ProviderBank */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <?= $form->field($model, 'provider_bank_code')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'provider_bank_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'provider_bank_provider_code')->widget(Select2::class, [
        'data'       => Provider::find()
            ->select('provider_name')
            ->indexBy('provider_code')
            ->column(),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'provider_bank_platform_bank_code')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'provider_bank_product_type')->widget(Select2::class, [
        'data'       => KeyValue::take('provider_product_type', 'paysvr'),
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'provider_bank_status')->widget(Select2::class, [
        'data'       => ProviderBank::STATUS_LIST,
        'hideSearch' => true,
    ]) ?>

    <?= $form->field($model, 'provider_bank_score')->textInput(['maxlength' => true]) ?>

</div>

<div class="box-footer">
    <?= Html::submitButton('保存', ['class' => 'btn btn-primary']) ?>
</div>

<?php ActiveForm::end(); ?>
