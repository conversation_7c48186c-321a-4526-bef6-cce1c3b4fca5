<?php

use kvmanager\models\KeyValue;
use payment\models\Provider;
use payment\models\ProviderBank;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\web\View;

/* @var $this View */
/* @var $dataProvider ActiveDataProvider */
/* @var $searchModel \payment\models\ProviderBankSearch */

$this->title = '渠道银行配置';

$this->params['breadcrumbs'][] = $this->title;

echo $this->render('_search', ['model' => $searchModel]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    // 'filterModel' => $searchModel,
    'columns'      => [
        ['class' => 'yii\grid\ActionColumn'],

        'provider_bank_code',
        'provider_bank_name',
        [
            'attribute' => 'provider_bank_provider_code',
            'format'    => [
                'in',
                Provider::find()
                    ->select('provider_name')
                    ->indexBy('provider_code')
                    ->column(),
            ],
        ],
        'provider_bank_platform_bank_code',
        [
            'attribute' => 'provider_bank_product_type',
            'format'    => ['in', KeyValue::take('provider_product_type', 'paysvr')],
        ],
        [
            'attribute' => 'provider_bank_status',
            'format'    => ['in', ProviderBank::STATUS_LIST],
        ],
        'provider_bank_score',
        'provider_bank_created_at',
        'provider_bank_updated_at',
    ],
]);
