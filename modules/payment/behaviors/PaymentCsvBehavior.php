<?php

namespace payment\behaviors;

use kvmanager\KVException;
use kvmanager\models\KeyValue;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use League\Flysystem\PhpseclibV3\SftpAdapter;
use League\Flysystem\PhpseclibV3\SftpConnectionProvider;
use OSS\Core\OssException;
use OSS\OssClient;
use payment\models\PaymentCsv;
use payment\works\LoadPaymentCsvDataWorker;
use system\components\AliOssComponent;
use Yii;
use yii\base\Behavior;
use yii\base\Event;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\BaseActiveRecord;
use yii\helpers\ArrayHelper;
use yii\web\UploadedFile;

class PaymentCsvBehavior extends Behavior
{
    public function events(): array
    {
        return [
            BaseActiveRecord::EVENT_BEFORE_INSERT => [$this, 'uploadFile'],
            BaseActiveRecord::EVENT_AFTER_INSERT => [$this, 'launchImport'],
            BaseActiveRecord::EVENT_BEFORE_DELETE => [$this, 'deleteFile'],
        ];
    }

    /**
     * @param array $config
     *
     * @return Filesystem
     */
    protected function initSftpClient(array $config): Filesystem
    {
        $provider = new SftpConnectionProvider(
            $config['host'] ?? '127.0.0.1',
            $config['username'] ?? 'test',
            $config['password'] ?? null,
            $config['privateKey'] ?? null,
            $config['passphrase'] ?? null,
            $config['port'] ?? 22,
            $config['useAgent'] ?? false,
            $config['timeout'] ?? 10,
            $config['maxTries'] ?? 4,
            $config['hostFingerprint'] ?? null
        );

        return new Filesystem(new SftpAdapter($provider, ''));
    }

    /**
     * @param array $config
     *
     * @return string
     * @throws UserException
     */
    protected function sftpStoragePath(array $config): string
    {
//        $config = [
//            'host'          => '**************',
//            'port'          => 22,
//            'username'      => 'cimbtest',
//            'password'      => 'srjMJkzl',
//            'root'          => '/reco/test',
//            'timeout'       => 10,
//            'directoryPerm' => 0755,
//        ];
        $path = $config['root'] ?? null;
        if (empty($path)) {
            throw new UserException('root路劲配置错误');
        }

        $data = [
            'date' => date('Ymd'),
            'yesterday' => date('Ymd', strtotime('-1 day')),
        ];

        return preg_replace_callback('/{([^}]+)}/', static function ($match) use ($data) {
            if (!isset($data[$match[1]])) {
                throw new UserException(
                    vsprintf('未知占位符: %s, 当前只支持: %s', [
                        $match[1],
                        implode(', ', array_keys($data)),
                    ])
                );
            }

            return $data[$match[1]];
        }, $path);
    }


    /**
     * @param Event $event
     *
     * @throws InvalidConfigException
     * @throws KVException
     * @throws OssException
     * @throws UserException
     * @throws FilesystemException
     */
    public function uploadFile(Event $event): void
    {
        /** @var PaymentCsv $sender */
        $sender = $event->sender;

        /** @var UploadedFile $file */
        $file = $sender->file;

        [$storage, $storageConfig] = self::storageConfig($sender->type);
        if (in_array($storage, ['sftp', 'ftp'], true)) {
            $filesystem = $this->initSftpClient($storageConfig);

            $fileUrl = $filepath = $this->sftpStoragePath($storageConfig) . '/' . $file->name;
            $filesystem->writeStream($filepath, fopen($file->tempName, 'rb'));
        } else {
            // oss
            $filepath = 'payment/csv/' . md5_file($file->tempName);

            $oldPaymentCsv = PaymentCsv::findOne(['oss_file_name' => $filepath]);
            if ($oldPaymentCsv) {
                $fileUrl = $oldPaymentCsv->domain_url;
            } else {
                $bucket = ArrayHelper::getValue(KeyValue::take('oss_config'), 'grantBucket');
                $result = AliOssComponent::instance()->uploadFile($bucket, $filepath, $file->tempName);
                Yii::debug($result, __METHOD__);
                $fileUrl = $result['oss-request-url'] ?? null;
            }
            if (!$fileUrl) {
                throw new UserException("上传文件错误!");
            }
        }

        $sender->oss_file_name = $filepath;
        $sender->domain_url = $fileUrl;
        $sender->file = $file->name;
        $sender->status = $this->needImport($sender->type) ? PaymentCsv::ING_STATUS : PaymentCsv::SUCCESS_STATUS;
        $sender->business_channel = PaymentCsv::CHANNEL;
    }

    public function launchImport(Event $event): void
    {
        /** @var PaymentCsv $sender */
        $sender = $event->sender;

        if ($this->needImport($sender->type)) {
            LoadPaymentCsvDataWorker::make(['dataSourceId' => $sender->id]);
        }
    }

    /**
     * @param $type
     *
     * @return bool
     */
    protected function needImport($type): bool
    {
        $config = PaymentCsv::config()[$type] ?? [];

        return (bool)($config['importTask'] ?? true);
    }

    /**
     * @param Event $event
     *
     * @throws FilesystemException
     * @throws InvalidConfigException
     * @throws KVException
     * @throws UserException
     */
    public function deleteFile(Event $event): void
    {
        /** @var PaymentCsv $sender */
        $sender = $event->sender;

        $multipleReferences = PaymentCsv::find()->where([
            'and',
            ['<>', 'id', $sender->id],
            ['=', 'oss_file_name', $sender->oss_file_name],
        ])->exists();
        if (!$multipleReferences) {
            [$storage, $storageConfig] = self::storageConfig($sender->type);
            if (in_array($storage, ['sftp', 'ftp'], true)) {
                $this->initSftpClient($storageConfig)->delete($sender->oss_file_name);
            } else {
                $bucket = ArrayHelper::getValue(KeyValue::take('oss_config'), 'grantBucket');
                AliOssComponent::instance()->deleteObject($bucket, $sender->oss_file_name);
            }
        }
    }

    /**
     * @param null $filename
     * @param null $suffix
     *
     * @return false|string
     * @throws FilesystemException
     * @throws InvalidConfigException
     * @throws KVException
     * @throws UserException
     */
    public function doDownload($filename = null, $suffix = null): string
    {
        /** @var PaymentCsv $model */
        $model = $this->owner;

        if (null === $filename) {
            $filename = tempnam(sys_get_temp_dir(), 'payment_csv_');
        }

        if (null !== $suffix) {
            $filename .= '.' . $suffix;
        }

        [$storage, $storageConfig] = self::storageConfig($model->type);

        if (in_array($storage, ['sftp', 'ftp'], true)) {
            $filesystem = $this->initSftpClient($storageConfig);
            file_put_contents($filename, $filesystem->read($model->oss_file_name));
        } else {
            $bucket = ArrayHelper::getValue(KeyValue::take('oss_config'), 'grantBucket');

            AliOssComponent::instance()->getObject($bucket, $model->oss_file_name, [
                OssClient::OSS_FILE_DOWNLOAD => $filename,
            ]);
        }

        return $filename;
    }

    /**
     * @param $type
     *
     * @return array
     * @throws UserException
     */
    public static function storageConfig($type): array
    {
        $config = PaymentCsv::config()[$type] ?? [];
        $storage = $config['storage'] ?? '';

        if (in_array($storage, ['sftp', 'ftp'], true)) {
            $storageConfig = (array)($config[$storage] ?? []);
            if (empty($storageConfig)) {
                throw new UserException('存储服务配置错误');
            }
        } else {
            $storageConfig = null;
        }

        return [$storage, $storageConfig];
    }
}
