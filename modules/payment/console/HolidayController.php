<?php

namespace payment\console;

use Carbon\Carbon;
use Carbon\Traits\Creator;
use common\helpers\ArrayHelper;
use DateTime;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use payment\models\Holiday;
use RuntimeException;
use waterank\audit\provider\AuditProvider;
use yii\base\InvalidConfigException;
use yii\console\Controller;
use yii\console\ExitCode;
use yii\db\Exception;
use yii\helpers\Json;

use function waterank\audit\authClient;

class HolidayController extends Controller
{
    /**
     * @param string $country
     * @param string|Creator|DateTime $startDate
     * @param string|Creator|DateTime $endDate
     *
     * @return int
     * @throws Exception
     * @throws IdentityProviderException
     * @throws InvalidConfigException
     */
    public function actionSync(string $country, string $startDate = '10 days ago', string $endDate = '10 days'): int
    {
        /** @var AuditProvider $client */
        $client = authClient('audit');

        $startDate = Carbon::parse($startDate)->toDateString();
        $endDate = Carbon::parse($endDate)->toDateString();

        $result = $client->holidays($country, $startDate, $endDate);
        $code = ArrayHelper::getValue($result, ['code'], false);
        if ($code !== 0) {
            throw new RuntimeException('请求接口出错: ' . Json::encode($result));
        }

        $data = (array)($result['data'] ?? []);

        echo 'API: ', Json::encode($result), "\n\n";

        $query = Holiday::find()->where([
            'and',
            ['>=', 'holiday_date', $startDate],
            ['<', 'holiday_date', Carbon::parse($endDate)->addDay()->toDateString()],
        ]);

        $deleteIds = [];
        $restIds = [];
        $workIds = [];

        /** @var Holiday $holiday */
        foreach ($query->each() as $holiday) {
            $date = Carbon::parse($holiday->holiday_date)->toDateString();

            $status = ArrayHelper::remove($data, $date);

            if (null === $status) {
                $deleteIds[] = $holiday->holiday_id;
                continue;
            }

            if ((int)$status === 1) {
                // 接口返回工作日，但是本地库不是工作日
                if ($holiday->holiday_status !== Holiday::STATUS_WORKING) {
                    $workIds[] = $holiday->holiday_id;
                }
            } elseif ($holiday->holiday_status !== Holiday::STATUS_REST) {
                // 接口返回节假日，但是本地库不是节假日
                $restIds[] = $holiday->holiday_id;
            }
        }

        $new = [];
        foreach ($data as $date => $status) {
            $new[] = [
                $date,
                1 === (int)$status ? Holiday::STATUS_WORKING : Holiday::STATUS_REST,
            ];
        }

        // 删除
        if (!empty($deleteIds)) {
            Holiday::deleteAll([
                'holiday_id' => $deleteIds,
            ]);
        }

        // 更新节假日数据
        if (!empty($restIds)) {
            $affected = Holiday::updateAll([
                'holiday_status' => Holiday::STATUS_REST,
            ], [
                'holiday_id' => $restIds,
            ]);

            echo vsprintf("更新节假日数据: %s, 成功: %s.\n", [
                Json::encode($restIds),
                $affected,
            ]);
        }

        // 更新工作日数据
        if (!empty($workIds)) {
            $affected = Holiday::updateAll([
                'holiday_status' => Holiday::STATUS_WORKING,
            ], [
                'holiday_id' => $workIds,
            ]);
            echo vsprintf("更新工作日数据: %s, 成功: %s.\n", [
                Json::encode($workIds),
                $affected,
            ]);
        }

        // 添加新数据
        if (!empty($new)) {
            $affected = Holiday::getDb()
                ->createCommand()
                ->batchInsert(Holiday::tableName(), ['holiday_date', 'holiday_status'], $new)
                ->execute();

            echo vsprintf("新增数据: %s, 成功: %s.\n", [
                Json::encode($new),
                $affected,
            ]);
        }

        return ExitCode::OK;
    }
}
