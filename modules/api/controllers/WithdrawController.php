<?php

namespace api\controllers;

use Carbon\Carbon;
use finance\models\Withdraw;
use finance\worker\AccountSupplementWorker;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\helpers\Json;
use yii\rest\Controller;
use yii\web\Response;

class WithdrawController extends Controller
{
    /**
     * 代付callback回调接口
     *
     * @return Response
     * @throws Throwable
     */
    public function actionPaymentCallback(): Response
    {
        $data = (array)$this->request->post('data');

        $withdraw = Withdraw::findOne([
            'withdraw_merchant_key' => $data['merchant_key'] ?? '',
            'withdraw_trade_no'     => $data['trade_no'] ?? '',
        ]);
        if (!$withdraw) {
            throw new UserException("数据不存在!");
        }
        if (!in_array($withdraw->withdraw_status,
            [Withdraw::STATUS_PAYMENT_ING, Withdraw::STATUS_SUCCESS, Withdraw::STATUS_PAYMENT_FAILED])
        ) {
            throw new UserException('手动提现数据状态错误:' . $withdraw->withdraw_status);
        }

        switch ($data['status'] ?? 0) {
            case 0:
                throw new UserException('状态错误:' . $this->request->rawBody);
            case 2://成功
                $status = Withdraw::STATUS_SUCCESS;
                break;
            default:
                $status = Withdraw::STATUS_PAYMENT_FAILED;
                break;
        }

        $transaction = Withdraw::getDb()->beginTransaction();
        try {
            $result = (int)Withdraw::updateAll([
                'withdraw_status'      => $status,
                'withdraw_finished_at' => Carbon::parse($data['finished_at'])
                    ->setTimezone(Yii::$app->timeZone)
                    ->toDateTimeString(),
            ], [
                'withdraw_id'     => $withdraw->withdraw_id,
                'withdraw_status' => $withdraw->withdraw_status,
            ]);
            if ($result !== 1) {
                throw new UserException('修改代付状态失败: ' . Json::encode($data));
            }

            $transaction->commit();

            return $this->asJson([
                'code' => 0,
            ]);
        } catch (Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }
}
