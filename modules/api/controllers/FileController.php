<?php

namespace api\controllers;

use api\tasks\FileHandler;
use Throwable;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\rest\Controller;
use yii\web\Request;

/**
 * @property-read Request $request
 */
class FileController extends Controller
{
    protected function verbs(): array
    {
        return [
            'analysis' => ['post'],
        ];
    }

    /**
     * @return array|TaskResult
     * @throws Throwable
     */
    public function actionAnalysis()
    {
        $data = $this->request->post('data');

        try {
            $task = FileHandler::make($data, [
                'task_from_system' => $this->request->post('from_system'),
            ]);

            $task->refresh();
            $result = TaskHandler::execute($task);
            if ($result['result'] instanceof TaskResult) {
                return $result['result']->toArray();
            }
            //TaskHandler::run 其实已经做了处理,这里只是做个兼容处理，防止意外情况
            return ['code' => 1, 'message' => '未知错误!', 'data' => []];
        } catch (Throwable $e) {
            return ['code' => 1, 'message' => $e->getMessage(), 'data' => []];
        }
    }
}
