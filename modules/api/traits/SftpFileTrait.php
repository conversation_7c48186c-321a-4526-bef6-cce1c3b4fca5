<?php

namespace api\traits;

use PhpOffice\PhpSpreadsheet\IOFactory;
use Yii;
use yii\base\UserException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

trait SftpFileTrait
{
    /**
     * @param string $content
     * @return array
     * @throws UserException
     */
    public function parseData(string $content): array
    {
        $rows = $this->processFileData($content);
        if ($this->rowToColumn) {
            $rows = $this->rowToColumn($rows);
        }
        return $this->resetData($rows);
    }


    /**
     * @param $rows
     * @return array|string[]
     * @throws UserException
     */
    protected function rowToColumn($rows): array
    {
        if (!$this->appendTitle) {
            throw new UserException('该资方需要处理行转列,需配置标题!');
        }
        [$filter, $indexBy, $map] = array_values($this->rowColumnConfig);
        preg_match('/^expression:(.*+)$/', $filter, $matches);
        $formatter = Yii::$app->getFormatter();
        $toRows = [];
        $toRowsPeriod = [];
        foreach ($rows as $row) {
            if (!$formatter->format(null, ['expression', $matches[1], $row])) {
                continue;
            } else {
                $item = [];
                foreach ($map as $key => $config) {
                    if ($row[$config['index']] === $key) {
                        $item[$config['to']] = $row[$config['value']] ?? 0;
                        //解决罚息期次和本金期次不一致的问题，最后落库的期次需要以本金期次为准
                        if (isset($config['period'])) {
                            $toRowsPeriod[$row[$indexBy]][$config['period']['to']] = $row[$config['period']['index']];
                        }
                        unset($row[$config['index']], $row[$config['value']]);
                        break;
                    }
                }
                if (isset($toRows[$row[$indexBy]])) {
                    $toRows[$row[$indexBy]] = array_replace($item, $toRows[$row[$indexBy]]);
                } else {
                    $toRows[$row[$indexBy]] = array_replace($item, $row);
                }
                unset($item, $row);
            }
        }

        $assets = [];
        $count = count(explode($this->rowSeparator, $this->appendTitle));
        $toKeyArr = [];

        foreach ($map as $key => $item) {
            $value = ArrayHelper::getValue($item, 'to');
            $toKeyArr[$value] = $key;
        }

        foreach ($toRows as &$row) {
            //"@index@" => array:1 [
            //    2 => "@9@"
            //  ]
            if (isset($toRowsPeriod[$row[$indexBy]])) {
                $row[key($toRowsPeriod[$row[$indexBy]])] = current($toRowsPeriod[$row[$indexBy]]);
            }
            //如果当前行列数和标题对应不上
            if (count($row) !== $count) {
                $needIndexArr = array_column($map, 'to', '');
                $diff = array_diff($needIndexArr, array_keys($row));
                foreach ($diff as $index) {
                    $toKey = $toKeyArr[$index];
                    $row[$index] = $map[$toKey]['defaultValue'] ?? null;
                }
            }
            ksort($row);
            $assets[] = array_values($row);
        }

        return $assets;
    }


    protected function resetData(array $rows): array
    {
        $assets = [];
        if (!empty($this->appendTitle)) {
            $headerArray = explode($this->rowSeparator, $this->appendTitle);
        } else {
            $headerArray = $rows[0];
        }

        foreach ($rows as $row) {
            //过滤和无效的数据 处理标题列数和数据行列数对不上的问题
            if (count($headerArray) !== count($row)) {
                continue;
            }
            $combine = array_combine($headerArray, $row);
            $assetKey = '';
            if (is_array($this->itemNoColumn)) {
                array_walk($this->itemNoColumn, static function ($value, $key) use (&$assetKey, $combine) {
                    $assetKey .= $combine[$value] . ':';
                });
                $assetKey = rtrim($assetKey, ':');
            } else {
                $assetKey = $combine[$this->itemNoColumn];
            }

            $assets[$assetKey][] = $combine;
        }
        return [$assets, $headerArray];
    }


    protected function processFileData(string $content): array
    {
        if ($this->csvFile) {
            return $this->csvConversion($content);
        }
        return $this->normalConversion($content);
    }

    protected function csvConversion(string $content): array
    {
        $file = sys_get_temp_dir() . '/' . uuid_create() . 'csv';
        file_put_contents($file, $content);
        $spreadsheet = IOFactory::load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = [];
        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(FALSE);
            $cells = [];
            foreach ($cellIterator as $cell) {
                $cells[] = $cell->getValue();
            }
            $rows[] = $cells;
        }
        unlink($file);
        return $rows;
    }

    /**
     * @param string $content
     * @return array|string[]
     */
    private function normalConversion(string $content): array
    {
        $data = [];
//        $rows = preg_split('/\s*\r?\n\s*/', trim($content));
        $rows = preg_split($this->pregSplitPattern, trim($content));
        foreach ($rows as $row) {
            if ($this->jsonFile) {
                $rowToArray = array_values(Json::decode($row));
            } else {
                $rowToArray = (array)explode($this->rowSeparator, $row);
            }
            $data[] = $rowToArray;
        }
        return $data;
    }
}