<?php

namespace api\traits;

use Carbon\Carbon;
use Generator;

trait SftpPathTrait
{
    /**
     * @return Generator
     */
    public function pathList(): Generator
    {
        if ($this->pathDateFormat === null) {
            yield $this->path;
        } else {
            $startDate = Carbon::parse($this->params[0] ?? $this->startDate)->setTime(0, 0);
            $endDate = Carbon::parse($this->params[1] ?? $this->endDate)->setTime(0, 0);
            while ($startDate <= $endDate) {
                $date = $startDate->format($this->pathDateFormat);
                yield sprintf($this->path, $date);
                $startDate->addDay();
            }
        }
    }

    /**
     * @param array $list
     * [
     * {
     * "name": "compensatory_20230228.txt",
     * "size": 72,
     * "isDir": false
     * },
     * {
     * "name": "compensatory_20230228.ok",
     * "size": 0,
     * "isDir": false
     * },
     * {
     * "name": "compensatory_check_20230228.ok",
     * "size": 0,
     * "isDir": false
     * }
     * ]
     *
     * @return Generator
     */
    public function filter(array $list): Generator
    {
        $date = $this->params[0] ?? null;
        if ($date) {
            $date = Carbon::parse($date);
        }

        $fileNameList = array_column($list, 'name');

        foreach (parent::filter($list) as $fileInfo) {
            if ($date && !$date->equalTo(Carbon::parse($fileInfo['matches']['date']))) {
                continue;
            }
            if ($this->checkExtension === null) {
                yield $fileInfo;
            } elseif ($this->checkExtension && in_array(
                    $fileInfo['matches']['basename'] . '.' . $this->checkExtension,
                    $fileNameList,
                    true
                )) {
                yield $fileInfo;
            }
        }
    }
}