<?php

namespace api;

use datasource\JsonGzipParser;
use Yii;
use yii\web\JsonParser;
use yii\web\JsonResponseFormatter;
use yii\web\Request;
use yii\web\Response;

class Module extends \yii\base\Module
{
    public function init()
    {
        parent::init();

        /** @var Request $request */
        $request = Yii::$app->getRequest();

        if (!$request->isConsoleRequest && str_starts_with($request->getPathInfo(), 'api/')) {
            self::apiModule($request, Yii::$app->getResponse());
        }
    }

    public static function apiModule(Request $request, Response $response): void
    {
        $request->parsers = [
            'application/json-gzip' => JsonGzipParser::class,
            'application/json' => JsonParser::class,
            '*' => JsonParser::class,
        ];

//        $response->format = Response::FORMAT_JSON;
//        $response->formatters = [
//            Response::FORMAT_HTML => [
//                'class' => JsonResponseFormatter::class,
//                'prettyPrint' => YII_DEBUG,
//            ],
//            Response::FORMAT_XML => [
//                'class' => JsonResponseFormatter::class,
//                'prettyPrint' => YII_DEBUG,
//            ],
//            Response::FORMAT_JSON => [
//                'class' => JsonResponseFormatter::class,
//                'prettyPrint' => YII_DEBUG,
//            ],
//        ];

        $response->on(Response::EVENT_BEFORE_SEND, function () use ($response) {
            if ($response->format === Response::FORMAT_JSON) {
                if ($response->statusCode === 500) {
                    if (isset($response->data['code']) && $response->data['code'] === 0) {
                        $response->data['code'] = 1;
                    }
                } elseif (!isset($response->data['code'])) {
                    $response->data['code'] = 0;
                }
            }
        });
    }
}
