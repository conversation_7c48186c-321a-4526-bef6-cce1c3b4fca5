<?php

namespace Codingheping\StatementComponent\component;

use Codingheping\StatementComponent\models\CopyConfigFrom;
use GuzzleHttp\RequestOptions;
use xlerr\httpca\ComponentTrait;
use xlerr\httpca\RequestClient;
use Yii;

/**
 * @TODO 待开发
 * 创建对账任务
 */
class StatementGateComponent extends RequestClient
{
    use ComponentTrait;

    public $fromSystem = 'BIZ';

    /**
     * 创建对账任务
     *
     * @param array $params
     *
     * @return bool
     */
    public function createTask(array $params): bool
    {
        return $this->post('task/manual-create-task', [
            RequestOptions::JSON => $params,
        ]);
    }


    /**
     * 发送通知
     *
     * @param string $batchNo
     *
     * @return bool
     */
    public function notify(string $batchNo)
    {
        return $this->post('reconcile/result-reNotify', [
            RequestOptions::JSON => [
                'batch_no' => $batchNo,
            ],
        ]);
    }

    /**
     * 复制对账配置
     *
     * @param CopyConfigFrom $configFrom
     *
     * @return bool
     */
    public function statementConfigCopy(CopyConfigFrom $configFrom): bool
    {
        return $this->post('config/statement-config-copy', [
            RequestOptions::JSON => [
                'config_code' => $configFrom->statement_code,
                'config_name' => $configFrom->statement_name,
                'group' => $configFrom->statement_group,
                'leader' => is_array($configFrom->statement_leader) ? implode(
                    ',',
                    $configFrom->statement_leader
                ) : $configFrom->statement_leader,
                'origin_config_code' => $configFrom->source_code,
                'operator' => Yii::$app->user->identity->getId(),
            ],
        ]);
    }

    /**
     * 重新对账
     *
     * @param array $params
     *
     * @return bool
     */
    public function retryStatement(array $params = []): bool
    {
        return $this->post('statement/reStatement', [
            RequestOptions::JSON => $params,
        ]);
    }

    public function original(array $params = [])
    {
        return $this->post('originalData/query', [
            RequestOptions::JSON => $params,
        ]);
    }


    public function transform(array $params = [])
    {
        return $this->post('transformData/query', [
            RequestOptions::JSON => $params,
        ]);
    }
}
