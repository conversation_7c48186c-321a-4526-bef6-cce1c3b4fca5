<?php

namespace Codingheping\StatementComponent\console;

use Codingheping\StatementComponent\models\StatementConfig;
use yii\base\UserException;
use yii\console\Controller;
use yii\db\Query;
use yii\helpers\Json;

/**
 * 更新对账系统库用户信息
 */
class MigrateController extends Controller
{
    public function actionMigrate()
    {
        $this->statementConfig();
    }

    //statement_config_leader statement_config_operator statement_config_updator
    protected function statementConfig()
    {
        $leaders = StatementConfig::find()->select([
            'statement_config_leader',
        ])->where(['status' => StatementConfig::STATEMENT_CONFIG_STATUS_VALID])->indexBy('id')->column();
        $newLeaders = [];
        $statementUsers = $this->query('dbStatement');
        $bizUsers = $this->query();
        foreach ($leaders as $l_k => $l_v) {
            if (!empty($l_v)) {
                //过滤中文
                if (preg_match('/[\x{4e00}-\x{9fa5}]+/u', (string)$l_v)) {
                    $this->stdout(sprintf('statement_config: 主键ID为[%s]存在statement_config_leader为[%s]中文的字符', $l_k, $l_v)
                        . PHP_EOL);
                    continue;
                }
                $vArr = explode(',', (string)$l_v);
                $new_v = [];//存储在biz的uid
                foreach ($vArr as $value) {
                    $email = array_search($value, $statementUsers, true);
                    //查询出在对账系统的邮箱,利用邮箱作为对账系统和biz系统的唯一关联关系
                    if ($email) {
                        $newUid = $bizUsers[strtolower((string)$email)] ?? 0;
                        $new_v[] = $newUid;
                    }
                }
                //存储了字符串更新原始数据(id1,id2,id3)
                $newVal = implode(',', $new_v);
                if (empty($newVal)) { //过滤为空的数据
                    continue;
                }
                $newLeaders[] = [
                    'id' => $l_k,
                    'statement_config_leader' => $newVal,
                ];
            }
        }
        $db = StatementConfig::getDb();
        /** @var \yii\db\Transaction $transaction */
        $transaction = $db->beginTransaction();

        try {
            $this->stdout(sprintf('需要更新的数据有[%s]条', count($newLeaders)) . PHP_EOL);
            $this->stdout('更新的数据为:' . PHP_EOL);
            $this->stdout(Json::encode($newLeaders, JSON_UNESCAPED_UNICODE) . PHP_EOL);
            $sql = $this->batchUpdate(StatementConfig::tableName(), 'id', 'statement_config_leader', $newLeaders);
            $res = $db->createCommand($sql)->execute();
            $this->stdout(sprintf('更新成功的条数为[%s]条' . PHP_EOL, $res));
            $transaction->commit();
        } catch (\Exception $exception) {
            $transaction->rollBack();
            $this->stdout(sprintf('更新失败,错误原因为:{%s}', $exception->getMessage()));
        }
    }

    protected function batchUpdate($table, $key, $val, $data)
    {
        if (empty($data)) {
            throw new UserException('需要更新的数据为空');
        }
        $ids = implode(',', array_column($data, $key));
        $condition = ' ';
        foreach ($data as $v) {
            $condition .= "WHEN {$v[$key]} THEN '{$v[$val]}' ";
        }

        return "UPDATE `{$table}` SET  {$val} = CASE {$key} {$condition} END WHERE {$key} in ({$ids})";
    }

    private function query(string $dbName = 'db'): array
    {
        /** @var  \yii\db\Connection $db */
        $db = \Yii::$app->get($dbName);
        $query = new Query();
        $data =
            $query->select(['id', 'email'])->where(['or', ['<>', 'email', ''], ['<>', 'email', null]])->from('user')
                ->createCommand($db)->queryAll();

        return array_column($data, 'id', 'email');
    }
}
