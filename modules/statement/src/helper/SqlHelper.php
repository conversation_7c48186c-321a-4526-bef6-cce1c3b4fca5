<?php

namespace Codingheping\StatementComponent\helper;

class SqlHelper
{
    /**
     * @param        $field
     * @param        $params
     * @param string $cond
     *
     * @return string
     */
    public static function makeFindInSetWhere($field, $params, string $cond = 'OR'): string
    {
        $temp      = "FIND_IN_SET('%s',`{$field}`)";
        $condition = '';
        foreach ($params as $i => $param) {
            if ($i) {
                $condition .= " $cond " . sprintf($temp, $param);
            } else {
                $condition = sprintf($temp, $param);
            }
        }

        return $condition;
    }
}
