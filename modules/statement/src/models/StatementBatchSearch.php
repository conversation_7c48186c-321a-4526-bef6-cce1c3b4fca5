<?php

namespace Codingheping\StatementComponent\models;

use Carbon\Carbon;
use Codingheping\StatementComponent\helper\SqlHelper;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;
use yii\db\Expression;

class StatementBatchSearch extends StatementBatch
{
    public string $startDate = '';
    public string $endDate = '';
    public string $status = '';
    public $group = '';
    public $operator = '';

    public static array $queryStatusAndWhere = [
        'status' => [
            'all' => '全部',
            'success' => '成功',
            'fail' => '失败',
            'unfinished' => '未完成',
            'unprocessed' => '未处理',
        ],
        'where' => [
            'success' => ['reconcile_result_fail_count' => 0],
            'fail' => ['>', 'reconcile_result_fail_count', 0],
            'unfinished' => ['is', 'reconcile_result.id', null],
            'unprocessed' => ['>', 'reconcile_result.reconcile_result_untreated_count', 0],
            'all' => '1 = 1',
        ],
    ];

    public function attributeLabels()
    {
        return array_merge([
            'startDate' => '创建开始时间',
            'endDate' => '创建结束时间',
        ], parent::attributeLabels());
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'statement_batch_version'], 'integer'],
            [['startDate',], 'default', 'value' => Carbon::now()->subDay()->toDateString()],
            [['endDate',], 'default', 'value' => Carbon::now()->toDateString()],
            [['status',], 'default', 'value' => 'fail'],
            [
                [
                    'statement_batch_no',
                    'statement_batch_task_order_no',
                    'statement_batch_status',
                    'statement_batch_date',
                    'statement_batch_config_no',
                    'statement_batch_create_at',
                    'startDate',
                    'endDate',
                    'statement_batch_update_at',
                    'status',
                    'group',
                    'operator',
                ],
                'safe',
            ],
        ];
    }

    public function search(array $params = [])
    {
        $query = self::find();
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['id'],
                'defaultOrder' => ['id' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            $dataProvider->setModels([]);

            return $dataProvider;
        }

        $group = $this->group;
        if (!$group || !array_key_exists($group, UserGroup::getAvailableGroup())) {
            $group = array_keys(UserGroup::getAvailableGroup());
        }

        $query->innerJoinWith([
            'statementConfig' => function (ActiveQuery $query) use ($group) {
                $conditions = [
                    'and',
                    '1=1',
                    [
                        'statement_config_group' => $group,
                    ],
                ];
                if ($this->operator) {
                    $conditions[] =
                        new Expression(SqlHelper::makeFindInSetWhere('statement_config_leader', $this->operator));
                }
                $query->andOnCondition($conditions);
            },
        ])->joinWith('reconcileResult');

        $query->andFilterWhere([
            '>=',
            'statement_batch_date',
            $this->startDate,
        ])->andFilterWhere([
            '<',
            'statement_batch_date',
            Carbon::parse($this->endDate)->addDay()->toDateString(),
        ]);
        if ($this->status) {
            $query->andWhere(self::$queryStatusAndWhere['where'][$this->status]);
        }

        $query->andFilterWhere([
            'statement_batch_no' => $this->statement_batch_no,
            'statement_batch_config_no' => $this->statement_batch_config_no,
        ]);

        return $dataProvider;
    }
}
