<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Expression;

/**
 * This is the model class for table "trouble_types".
 *
 * @property int $trouble_types_id        主键
 * @property string $trouble_types_desc      描述
 * @property string $trouble_types_create_at 创建时间
 * @property string $trouble_types_update_at 更新时间
 */
class TroubleTypes extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'trouble_types';
    }

    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['trouble_types_create_at', 'trouble_types_update_at'], 'safe'],
            [['trouble_types_desc'], 'string', 'max' => 64],
            ['trouble_types_desc', 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'trouble_types_id' => 'ID',
            'trouble_types_desc' => '描述',
            'trouble_types_create_at' => '创建时间',
            'trouble_types_update_at' => '更新时间',
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => [
                        'trouble_types_create_at',
                        'trouble_types_update_at',
                    ],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['trouble_types_update_at'],
                ],
                'value' => new Expression('NOW()'),
            ],
        ];
    }


    public static function getTroubleType(string $code)
    {
        $config = StatementConfig::getVal($code);

        return $config['troubleTypes'] ?? self::find()->select('trouble_types_desc')->column();
    }
}
