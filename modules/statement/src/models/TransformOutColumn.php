<?php

namespace Codingheping\StatementComponent\models;

use Yii;

/**
 * This is the model class for table "transform_out_columns".
 *
 * @property int $transform_out_columns_id              主键
 * @property string $transform_out_columns_output_key      对账列名
 * @property string $transform_out_columns_output_type     对账列类型
 * @property string $transform_out_columns_is_business_key 是否为业务主键
 * @property int $transform_out_columns_business_index  业务主键的顺序
 * @property string $transform_out_columns_statement_code  对账配置code
 */
class TransformOutColumn extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'transform_out_columns';
    }


    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     * @noinspection PhpVariableVariableInspection
     */
    public function rules()
    {
        return [
            [['transform_out_columns_is_business_key'], 'string'],
            [['transform_out_columns_business_index'], 'integer'],
            [['transform_out_columns_output_key'], 'string', 'max' => 100],
            [
                'transform_out_columns_output_key',
                function ($attribute, $params) {
                    $exists = self::find()->where([
                        'and',
                        ['transform_out_columns_statement_code' => $this->transform_out_columns_statement_code],
                        ['!=', 'transform_out_columns_id', $this->transform_out_columns_id],
                        ['transform_out_columns_output_key' => $this->$attribute],
                    ])->exists();
                    if ($exists) {
                        $this->addError($attribute, '对账列名重复！');

                        return false;
                    }

                    return true;
                },
            ],
            [['transform_out_columns_output_type', 'transform_out_columns_statement_code'], 'string', 'max' => 50],
            [
                ['transform_out_columns_output_key', 'transform_out_columns_statement_code'],
                'unique',
                'targetAttribute' => ['transform_out_columns_output_key', 'transform_out_columns_statement_code'],
            ],
            ['transform_out_columns_business_index', 'default', 'value' => 0],
            [
                'transform_out_columns_business_index',
                function ($attribute, $params) {
                    if ($this->$attribute) {
                        $exists = self::find()->where([
                            'and',
                            [
                                'transform_out_columns_statement_code' => $this->transform_out_columns_statement_code,
                                'transform_out_columns_business_index' => $this->$attribute,
                                'transform_out_columns_is_business_key' => TransformDef::DATA_SOURCE_DESENSITIZATION_YES,
                            ],
                            ['!=', 'transform_out_columns_id', $this->transform_out_columns_id],
                        ])->exists();
                        if ($exists) {
                            $this->addError($attribute, '主键顺序已存在！');

                            return false;
                        }

                        return true;
                    }

                    $this->addError($attribute, '主键顺序不能为0！');

                    return false;
                },
                'when' => function ($model) {
                    return $model->transform_out_columns_is_business_key
                        === TransformDef::DATA_SOURCE_DESENSITIZATION_YES;
                },
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'transform_out_columns_id' => '对账列ID',
            'transform_out_columns_output_key' => '对账列名',
            'transform_out_columns_output_type' => '对账列类型',
            'transform_out_columns_is_business_key' => '是否为主键',
            'transform_out_columns_business_index' => '业务主键顺序',
            'transform_out_columns_statement_code' => '所属对账配置',
        ];
    }

    public function getStatementConfig()
    {
        return $this->hasOne(StatementConfig::class, [
            'statement_config_code' => 'transform_out_columns_statement_code',
        ]);
    }

    public static function getTransOutColumnMap($statement_code)
    {
        return self::find()
            ->where(['transform_out_columns_statement_code' => $statement_code])
            ->indexBy('transform_out_columns_id')
            ->select(['transform_out_columns_output_key', 'transform_out_columns_id'])
            ->column();
    }

    public function beforeSave($insert)
    {
        $this->statementConfig->statement_config_updator = Yii::$app->user->identity->getId();
        $this->statementConfig->update();

        return parent::beforeSave($insert);
    }

    public static function getTransOutColumns($statement_code)
    {
        return self::find()
            ->where(['transform_out_columns_statement_code' => $statement_code])
            ->indexBy('transform_def_transform_out_columns_id')
            ->select([
                'transform_def_transform_out_columns_id' => 'transform_out_columns_id',
                'transform_def_output_key' => 'transform_out_columns_output_key',
                'transform_def_output_type' => 'transform_out_columns_output_type',
                'transform_def_is_business_key' => 'transform_out_columns_is_business_key',
                'transform_def_business_index' => 'transform_out_columns_business_index',
            ])
            ->asArray()
            ->all();
    }
}
