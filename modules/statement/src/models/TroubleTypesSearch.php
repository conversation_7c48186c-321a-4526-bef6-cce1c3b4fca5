<?php

namespace Codingheping\StatementComponent\models;

use yii\data\ActiveDataProvider;

/**
 * TroubleTypesSearch represents the model behind the search form of `common\models\TroubleTypes`.
 */
class TroubleTypesSearch extends TroubleTypes
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['trouble_types_id'], 'integer'],
            [['trouble_types_desc', 'trouble_types_create_at', 'trouble_types_update_at'], 'safe'],
        ];
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = TroubleTypes::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'trouble_types_id' => $this->trouble_types_id,
            'trouble_types_create_at' => $this->trouble_types_create_at,
            'trouble_types_update_at' => $this->trouble_types_update_at,
        ]);

        $query->andFilterWhere(['like', 'trouble_types_desc', $this->trouble_types_desc]);

        return $dataProvider;
    }
}
