<?php

namespace Codingheping\StatementComponent\models;

use common\models\User;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use mdm\admin\components\UserStatus;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use function xlerr\adminlte\userFullName;

/**
 * This is the model class for table "statement_config".
 *
 * @property int         $id
 * @property string      $statement_config_code           对账code
 * @property string|null $statement_config_name           对账名称
 * @property string      $statement_config_conf           配置值
 * @property string      $statement_config_create_at      创建时间
 * @property string      $statement_config_update_at      更新时间
 * @property string|null $status                          状态
 * @property int|null    $statement_config_cycle          对账周期
 * @property string|null $statement_config_reconcile_type 对账方式 SINGLE  SUMMARY
 * @property string|null $statement_config_summary_type   汇总方式  COUNT  SUM
 * @property string|null $statement_config_exec_time      执行时间
 * @property string|null $statement_config_group          对账组别
 * @property string|null $statement_config_leader         负责人
 * @property string|null $statement_config_dynamic_params 动态参数模板
 * @property string|null $statement_config_summary_info   其它汇总信息
 * @property string|null $statement_config_count_key      汇总字段
 * @property int|null    $statement_config_operator       创建用户
 * @property int         $statement_config_updator        更新人
 */
class StatementConfig extends ActiveRecord
{
    public const STATEMENT_CONFIG_STATUS_VALID = 'valid';
    public const STATEMENT_CONFIG_STATUS_INVALID = 'invalid';

    public const SCENARIO_CREATE = 'create';
    public const SCENARIO_UPDATE = 'update';
    public const SCENARIO_COUNT_CONFIG = 'count_config';

    public const STATEMENT_CONFIG_STATUS = [
        self::STATEMENT_CONFIG_STATUS_VALID => '有效',
        self::STATEMENT_CONFIG_STATUS_INVALID => '无效',
    ];

    public const RECONCILE_TYPE_SINGLE = 'SINGLE';
    public const RECONCILE_TYPE_SUMMARY = 'SUMMARY';

    public const RECONCILE_TYPE_MAP = [
        self::RECONCILE_TYPE_SINGLE => '对明细',
        self::RECONCILE_TYPE_SUMMARY => '对汇总',
    ];

    public const SUMMARY_TYPE_COUNT = 'COUNT';
    public const SUMMARY_TYPE_SUM = 'SUM';

    public const SUMMARY_TYPE_MAP = [
        self::SUMMARY_TYPE_COUNT => '计数',
        self::SUMMARY_TYPE_SUM => '总和',
    ];

    //KV 相关
    public const  KV_STATEMENT_CONFIG_GROUP = 'statement_group_config';
    public const  KV_STATEMENT_CONFIG_COMPATIBLE = 'statement_config_compatible';
    public const  KV_STATEMENT_CONFIG_UPLOAD = 'statement_config_upload_config';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'statement_config';
    }

    /*
    * 对账时间
    */
    public $date;

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => [
                        'statement_config_create_at',
                        'statement_config_update_at',
                    ],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['statement_config_update_at'],
                ],
                'value' => new Expression('NOW()'),
            ],
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'statement_config_operator',
                'updatedByAttribute' => 'statement_config_updator',
                'value' => function () {
                    return Yii::$app->getUser()->getId();
                },
            ],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_COUNT_CONFIG] = [
            'statement_config_reconcile_type',
            'statement_config_summary_type',
            'statement_config_count_key',
            'statement_config_summary_info',
        ];

        return $scenarios;
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'status',
                    'statement_config_exec_time',
                    'statement_config_cycle',
                    'statementConfigLeader',
                    'statement_config_group',
                ],
                'required',
                'on' => [$this::SCENARIO_CREATE],
            ],
            [
                ['statement_config_code', 'statementConfigLeader', 'statement_config_name'],
                'required',
                'on' => [$this::SCENARIO_CREATE, $this::SCENARIO_UPDATE],
            ],
            [
                ['statement_config_code'],
                'match',
                'pattern' => '/^[a-zA-Z0-9_\-]+$/',
                'message' => '只能输入字母、数字和-或_',
                'on' => [$this::SCENARIO_CREATE],
            ],
            [
                [
                    'statement_config_conf',
                    'statement_config_dynamic_params',
                    'statement_config_summary_info',
                    'statement_config_count_key',
                ],
                'string',
            ],
            [['statement_config_create_at', 'statement_config_update_at'], 'safe'],
            [['statement_config_cycle', 'statement_config_operator', 'statement_config_updator'], 'integer'],
            [['statement_config_code', 'status'], 'string', 'max' => 32],
            [['statement_config_name'], 'string', 'max' => 64],
            [
                [
                    'statement_config_reconcile_type',
                    'statement_config_summary_type',
                    'statement_config_exec_time',
                ],
                'string',
                'max' => 16,
            ],
            [['date', 'statement_config_leader'], 'safe'],
            [['statement_config_group'], 'string', 'max' => 12],
            [['statement_config_code'], 'unique'],
            [['statement_config_conf', 'statement_config_dynamic_params'], 'default', 'value' => '{}'],
            [
                'statement_config_operator',
                'default',
                'value' => function ($model, $attribute) {
                    return Yii::$app->user->identity->getId();
                },
            ],
            [
                'statement_config_count_key',
                'filter',
                'filter' => function ($value) {
                    if ($this->statement_config_reconcile_type === self::RECONCILE_TYPE_SUMMARY
                        && $this->statement_config_summary_type === self::SUMMARY_TYPE_SUM
                    ) {
                        return implode(',', json_decode($value, true));
                    }

                    return '';
                },
                'on' => self::SCENARIO_COUNT_CONFIG,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'statement_config_code' => '对账code',
            'statement_config_name' => '对账名称',
            'statement_config_conf' => '配置值',
            'statement_config_create_at' => '创建时间',
            'statement_config_update_at' => '更新时间',
            'status' => '状态',
            'statement_config_cycle' => '对账周期',
            'statement_config_reconcile_type' => '对账方式 SINGLE  SUMMARY',
            'statement_config_summary_type' => '汇总方式  COUNT  SUM',
            'statement_config_exec_time' => '执行时间',
            'statement_config_group' => '对账组别',
            'statement_config_leader' => '负责人',
            'statementConfigLeader' => '负责人',
            'statement_config_dynamic_params' => '动态参数模板',
            'statement_config_summary_info' => '其它汇总信息',
            'statement_config_count_key' => '汇总字段',
            'statement_config_operator' => '创建用户',
            'statement_config_updator' => '更新人',
        ];
    }

    /**
     * 获取对账组别
     *
     * @return string[]
     * @throws KVException
     */
    public static function getKvConfigGroup(): array
    {
        return KeyValue::take(self::KV_STATEMENT_CONFIG_GROUP, 'statement');
    }

    /**
     * 获取对账组负责人
     *
     * @return array|string[]
     */
    public static function getLeader(): array
    {
        $query = User::find()
            ->where([
                'status' => UserStatus::ACTIVE,
            ]);
        $users = [];

        /** @var User $user */
        foreach ($query->each() as $user) {
            $users[$user->id] = sprintf('%s(%s)', $user->username, userFullName($user));
        }

        return $users;
    }

    public static function getTabsViewParams(
        string $code = '',
        string $action = '',
        int $index = 0,
        bool $isNew = false,
        bool $readonly = false
    ): array {
        return [
            'statement_config_code' => $code,
            'sub_data' => [
                'one' => FeedConfig::checkSourceByCode($code, 1),
                'two' => FeedConfig::checkSourceByCode($code, 2),
            ],
            'trans_def' => [
                1 => TransformDef::getTranCountByFeedId(FeedConfig::getFeedIdByCode($code, 1)),
                2 => TransformDef::getTranCountByFeedId(FeedConfig::getFeedIdByCode($code, 2)),
            ],
            'trans_count' => [
                1 => TransformDef::getPrimaryTranCountByFeedId(FeedConfig::getFeedIdByCode($code, 1)),
                2 => TransformDef::getPrimaryTranCountByFeedId(FeedConfig::getFeedIdByCode($code, 2)),
            ],
            'show' => $index,
            'action' => $action,
            'is_new' => $isNew,
            'readonly' => $readonly,
            'summary' => self::RECONCILE_TYPE_SUMMARY,
        ];
    }

    public static function updateCountKey(string $code, string $newColName, $oldColName = null): void
    {
        $model = self::findOne(['statement_config_code' => $code]);
        if ($model && $oldColName) {
            $keys = explode(',', (string)$model->statement_config_count_key);
            if (in_array($oldColName, $keys, true)) {
                array_walk($keys, static function (&$v) use ($oldColName, $keys, $newColName) {
                    return $v = $v === $oldColName ? $newColName : $v;
                });
                $model->statement_config_count_key = implode(',', $keys);
                $model->save(false, ['statement_config_count_key']);
            }
        }
    }

    /**
     * @param $keys
     *
     * @return string[]
     */
    public function getCountKeyFilter($keys): array
    {
        $key = explode(',', (string)$this->statement_config_count_key);
        if (!empty($key)) {
            $keys = array_diff($keys, $key);
        }
        $data = [];
        foreach ($keys as $item) {
            $data[$item] = $item;
        }

        return $data;
    }

    public function countKeyFilter($countKeys): array
    {
        $key = explode(',', (string)$this->statement_config_count_key);

        $keys = array_diff($key, array_diff($key, $countKeys));
        $data = [];
        foreach ($keys as $item) {
            $data[$item] = $item;
        }

        return $data;
    }

    public static function getStatementCodeMap()
    {
        return self::find()
            ->select(['statement_config_name', 'statement_config_code'])
            ->where([
                'statement_config_conf' => '{}',
                'status' => self::STATEMENT_CONFIG_STATUS_VALID,
            ])
            ->indexBy('statement_config_code')
            ->column();
    }

    public function getOperator()
    {
        return $this->hasOne(User::class, ['id' => 'statement_config_operator']);
    }


    public function getGroup()
    {
        //TODO 这里记录一下如何获取分组信息
        return $this->hasOne(UserGroup::class, ['user_group_code' => 'statement_config_group']);
    }

    public function getLeaders()
    {
        $fullNameField = ArrayHelper::getValue(Yii::$app->params, 'adminlte.user.full_name_field', 'fullname');

        $leaders =
            User::find()->where(['in', 'id', explode(',', (string)$this->statement_config_leader)])->asArray()
                ->all();

        return array_column($leaders, $fullNameField, 'id');
    }

    public static function getLeaderNames($uids)
    {
        $fullNameField = ArrayHelper::getValue(Yii::$app->params, 'adminlte.user.full_name_field', 'fullname');
        $leaders =
            User::find()->select($fullNameField)->where(['in', 'id', explode(',', (string)$uids)])->asArray()
                ->column();

        return empty($leaders) ? '-' : implode(',', $leaders);
    }

    /**
     * @return array
     */
    public static function getConfigMappings(): array
    {
        $mappings = [];
        $query = self::find()
            ->select([
                'statement_config_code',
                'statement_config_conf',
            ])
            ->where(['status' => self::STATEMENT_CONFIG_STATUS_VALID]);

        /** @var StatementConfig $sec */
        foreach ($query->each() as $sec) {
            $mappings[$sec->statement_config_code] = json_decode($sec->statement_config_conf, true);
        }

        return $mappings;
    }

    /**
     * @param string $code
     *
     * @return mixed|string
     * @throws UserException
     */
    public static function getVal(string $code)
    {
        $config = self::findOne([
            'statement_config_code' => $code,
            'status' => self::STATEMENT_CONFIG_STATUS_VALID,
        ]);
        if (!$config) {
            throw new UserException(sprintf('配置[%s]不存在', $code));
        }
        $val = json_decode($config->statement_config_conf, true);

        return $val ? $config->statement_config_conf : $val;
    }

    /**
     * @return string[]
     */
    public function getStatementConfigLeader()
    {
        return array_filter(explode(',', (string)$this->statement_config_leader), static function ($val) {
            return $val !== '';
        });
    }

    /**
     * @param $statementConfigLeader
     */
    public function setStatementConfigLeader($statementConfigLeader): void
    {
        $this->statement_config_leader = implode(',', (array)$statementConfigLeader);
    }
}
