<?php

namespace Codingheping\StatementComponent\models;

use yii\base\Model;

/**
 * This is the model class for table "user_group".
 *
 * @property int $user_group_id
 * @property string $user_group_code      组编码
 * @property string $user_group_name      组名
 * @property int|null $user_group_status    状态
 * @property string $user_group_create_at 创建时间
 * @property string|null $user_group_update_at 更新时间
 */
class UserGroup extends Model
{

    public const USER_GROUP_PERMISSION_SUFFIX = '_statement';

    /**
     * @var array
     */
    protected static $availableGroup;

    /**
     * @return array
     */
    public static function getAvailableGroup(): array
    {
        return self::$availableGroup;
    }

    /**
     * @param array $availableGroup
     */
    public static function setAvailableGroup(array $availableGroup): void
    {
        self::$availableGroup = $availableGroup;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_group_code', 'user_group_name'], 'required'],
            [['user_group_status'], 'integer'],
            [['user_group_create_at', 'user_group_update_at'], 'safe'],
            [['user_group_code', 'user_group_name'], 'string', 'max' => 32],
            [['user_group_code'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'user_group_id' => 'User Group ID',
            'user_group_code' => '组编码',
            'user_group_name' => '组名',
            'user_group_status' => '状态',
            'user_group_create_at' => '创建时间',
            'user_group_update_at' => '更新时间',
        ];
    }
}
