<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;

/**
 * This is the model class for table "notify_config".
 *
 * @property int $notify_config_id             主键
 * @property string $notify_config_statement_code 对账配置code
 * @property string $notify_config_need_notify    通知开关
 * @property string $notify_config_message_type   消息类型
 * @property string $notify_config_message_theme  消息主题
 * @property string $notify_config_notify_obj     通知对象
 * @property string $notify_config_data           默认通知数据
 * @property string $notify_config_batch_count    批量通知数量
 * @property string $notify_config_use_group      通知是否使用通知组
 * @property string $notify_config_group_id       通知组ID
 * @property string $notify_config_create_at      创建时间
 * @property string $notify_config_update_at      更新时间
 * @property StatementConfig $statementCode
 */
class NotifyConfig extends ActiveRecord
{
    public const MESSAGE_TYPE_EMAIL = 'EMAIL';
    public const MESSAGE_TYPE_MQ = 'MQ';
    public const MESSAGE_TYPE_WEIXIN = 'WEIXIN';
    public const MESSAGE_TYPE_SMS = 'SMS';

    public const MESSAGE_TYPE_MAP = [
        self::MESSAGE_TYPE_EMAIL => self::MESSAGE_TYPE_EMAIL,
        self::MESSAGE_TYPE_MQ => self::MESSAGE_TYPE_MQ,
        self::MESSAGE_TYPE_WEIXIN => self::MESSAGE_TYPE_WEIXIN,
        self::MESSAGE_TYPE_SMS => self::MESSAGE_TYPE_SMS,
    ];

    public const MESSAGE_NEED_YES = 1;
    public const MESSAGE_NEED_NO = 0;
    public const MESSAGE_NEED_MAP = [
        self::MESSAGE_NEED_YES => 'YES',
        self::MESSAGE_NEED_NO => 'NO',
    ];
    public const NOTIFY_GROUP_YES = 'YES';
    public const NOTIFY_GROUP_NO = 'NO';
    public const NOTIFY_GROUP_MAP = [
        self::NOTIFY_GROUP_NO => '单个通知',
        self::NOTIFY_GROUP_YES => '通知邮件组',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'notify_config';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                'notify_config_notify_obj',
                'required',
                'when' => function ($model) {
                    if ($model->notify_config_use_group == self::NOTIFY_GROUP_NO) {
                        return true;
                    }

                    return false;
                },
                'whenClient' => "function (attribute, value) {
                if($('input[name*=notify_config_use_group]:radio:checked').val()=='YES'){
                    return false;
                }else{
                    return true;
                }
            }",
            ],
            [
                'notify_config_group_id',
                'required',
                'when' => function ($model) {
                    if ($model->notify_config_use_group === self::NOTIFY_GROUP_YES) {
                        return true;
                    }

                    return false;
                },
                'whenClient' => "function (attribute, value) {
                if($('input[name*=notify_config_use_group]:radio:checked').val()=='YES'){
                    return true;
                }else{
                    return false;
                }
            }",
            ],
            [['notify_config_notify_obj', 'notify_config_data'], 'string'],
            [
                [
                    'notify_config_create_at',
                    'notify_config_update_at',
                    'notify_config_use_group',
                    'notify_config_group_id',
                ],
                'safe',
            ],
            [['notify_config_statement_code'], 'string', 'max' => 32],
            [['notify_config_need_notify'], 'string', 'max' => 1],
            [['notify_config_message_type'], 'string', 'max' => 12],
            [['notify_config_message_theme'], 'string', 'max' => 64],
            ['notify_config_batch_count', 'default', 'value' => 10],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'notify_config_id' => ' ID',
            'notify_config_statement_code' => '对账 Code',
            'notify_config_need_notify' => '是否通知',
            'notify_config_message_type' => '消息类型',
            'notify_config_message_theme' => '消息主体',
            'notify_config_notify_obj' => '通知对象',
            'notify_config_group_id' => '通知邮件组',
            'notify_config_batch_count' => '通知数量',
            'notify_config_data' => '通知内容',
            'notify_config_use_group' => '通知邮件类型',
            'notify_config_create_at' => '创建时间',
            'notify_config_update_at' => '更新时间',
        ];
    }

    public static function list($params)
    {
        $query = self::find();
        $query->where(['notify_config_statement_code' => $params['statement_code']]);
        $query->indexBy('notify_config_id');

        return new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 10,
            ],
        ]);
    }

    public static function getNotifyConfigByStateCode($code)
    {
        return self::find()->where(['notify_config_statement_code' => $code])->all();
    }

    public function getNotifyGroupConfig()
    {
        return $this->hasOne(NotifyGroupConfig::class, ['notify_group_config_id' => 'notify_config_group_id']);
    }

    public function getStatementCode()
    {
        return $this->hasOne(StatementConfig::class, ['statement_config_code' => 'notify_config_statement_code']);
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => [
                        'notify_config_create_at',
                        'notify_config_update_at',
                    ],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['notify_config_update_at'],
                ],
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    public function beforeSave($insert)
    {
        $this->statementCode->statement_config_updator = Yii::$app->user->identity->getId();
        $this->statementCode->update();

        return parent::beforeSave($insert);
    }

    public function getNotifyGroup(): ActiveQuery
    {
        return $this->hasOne(NotifyGroupConfig::class, ['notify_group_config_id' => 'notify_config_group_id']);
    }
}
