<?php

namespace Codingheping\StatementComponent\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

class TransformDefSearch extends TransformDef
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['transform_def_id', 'transform_def_index', 'transform_def_feed_config_id'], 'integer'],
            [
                [
                    'transform_def_statement_code',
                    'transform_def_input_key',
                    'transform_def_input_type',
                    'transform_def_output_key',
                    'transform_def_output_type',
                    'transform_def_create_at',
                    'transform_def_update_at',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = TransformDef::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => false,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'transform_def_id' => $this->transform_def_id,
            'transform_def_index' => $this->transform_def_index,
            'transform_def_feed_config_id' => $this->transform_def_feed_config_id,
            'transform_def_create_at' => $this->transform_def_create_at,
            'transform_def_update_at' => $this->transform_def_update_at,
        ]);

        $query->andFilterWhere(['like', 'transform_def_statement_code', $this->transform_def_statement_code])
            ->andFilterWhere(['like', 'transform_def_input_key', $this->transform_def_input_key])
            ->andFilterWhere(['like', 'transform_def_input_type', $this->transform_def_input_type])
            ->andFilterWhere(['like', 'transform_def_output_key', $this->transform_def_output_key])
            ->andFilterWhere(['like', 'transform_def_output_type', $this->transform_def_output_type]);

        return $dataProvider;
    }
}
