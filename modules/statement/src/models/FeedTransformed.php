<?php

namespace Codingheping\StatementComponent\models;

use Yii;

/**
 * This is the model class for table "feed_transformed".
 *
 * @property int $feed_transformed_id
 * @property string $feed_transformed_batch_no
 * @property string $feed_transformed_system_code
 * @property string $feed_transformed_primarykey_value
 * @property string $feed_transformed_content
 * @property string $feed_transformed_created_at
 * @property string $feed_transformed_updated_at
 */
class FeedTransformed extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'feed_transformed';
    }


    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['feed_transformed_content'], 'string'],
            [['feed_transformed_created_at', 'feed_transformed_updated_at'], 'safe'],
            [['feed_transformed_batch_no', 'feed_transformed_system_code'], 'string', 'max' => 64],
            [['feed_transformed_primarykey_value'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'feed_transformed_id' => 'ID',
            'feed_transformed_batch_no' => '批次号',
            'feed_transformed_system_code' => '系统 CODE',
            'feed_transformed_primarykey_value' => '主键值',
            'feed_transformed_content' => '转换后内容',
            'feed_transformed_created_at' => '创建时间',
            'feed_transformed_updated_at' => '更新时间',
        ];
    }
}
