<?php

namespace Codingheping\StatementComponent\models;

use yii\data\ActiveDataProvider;

/**
 * NotifyGroupConfigSearch represents the model behind the search form of `common\models\NotifyGroupConfig`.
 */
class NotifyGroupConfigSearch extends NotifyGroupConfig
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['notify_group_config_id'], 'integer'],
            [
                [
                    'notify_group_config_name',
                    'notify_group_config_notify_obj',
                    'notify_group_config_statement_group',
                    'notify_group_config_status',
                    'notify_group_config_create_at',
                    'notify_group_config_update_at',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = NotifyGroupConfig::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'notify_group_config_id' => $this->notify_group_config_id,
            'notify_group_config_create_at' => $this->notify_group_config_create_at,
            'notify_group_config_update_at' => $this->notify_group_config_update_at,
        ]);
        $group = array_keys(UserGroup::getAvailableGroup());
        $query->andFilterWhere(['like', 'notify_group_config_name', $this->notify_group_config_name])
            ->andFilterWhere(['like', 'notify_group_config_notify_obj', $this->notify_group_config_notify_obj])
            ->andFilterWhere(['notify_group_config_statement_group' => $group])
            ->andFilterWhere(['like', 'notify_group_config_status', $this->notify_group_config_status]);

        return $dataProvider;
    }
}
