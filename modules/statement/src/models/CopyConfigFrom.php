<?php

namespace Codingheping\StatementComponent\models;

use Codingheping\StatementComponent\component\StatementGateComponent;
use yii\base\InvalidConfigException;
use yii\base\Model;
use yii\base\UserException;

class CopyConfigFrom extends Model
{
    public $statement_code;
    public $statement_name;
    public $statement_group;
    public $statement_leader;
    public $source_code;

    public function rules()
    {
        return [
            [
                ['statement_code', 'statement_name', 'statement_group', 'statement_leader', 'statement_group'],
                'required',
            ],
            [['statement_code'], 'string', 'max' => 32],
            [
                ['statement_code'],
                'match',
                'pattern' => '/^[a-zA-Z0-9_\-]+$/',
                'message' => '只能输入字母、数字和-或_',
            ],
            [
                'statement_group',
                'in',
                'range' => array_keys(UserGroup::getAvailableGroup()),
            ],
            [['statement_name'], 'string', 'max' => 64],
            [
                'source_code',
                'exist',
                'targetClass' => StatementConfig::class,
                'targetAttribute' => ['source_code' => 'statement_config_code'],
                'message' => '所选对账配置不存在！',
            ],
            [
                'statement_code',
                function ($attribute, $params) {
                    if (StatementConfig::find()->where(['statement_config_code' => $attribute])->exists()) {
                        $this->addError($attribute, '对账配置code已存在！');

                        return false;
                    }

                    return true;
                },
            ],
        ];
    }

    /**
     * @return mixed|null
     * @throws UserException
     * @throws InvalidConfigException
     */
    public function copyConfig()
    {
        $client = StatementGateComponent::instance();
        $ret = $client->statementConfigCopy($this);
        if ($ret) {
            return $client->getData();
        }

        throw new UserException($client->getHasError() ? $client->getError() : '未知异常');
    }

    public function attributeLabels(): array
    {
        return [
            'statement_code' => '对账CODE',
            'statement_name' => '对账名称',
            'statement_group' => '所属组别',
            'statement_leader' => '对账负责人',
            'source_code' => '源对账配置',
        ];
    }
}
