<?php

namespace Codingheping\StatementComponent\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * TransformOutColumnSearch represents the model behind the search form of `common\models\TransformOutColumn`.
 */
class TransformOutColumnSearch extends TransformOutColumn
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['transform_out_columns_id', 'transform_out_columns_business_index'], 'integer'],
            [
                [
                    'transform_out_columns_output_key',
                    'transform_out_columns_output_type',
                    'transform_out_columns_is_business_key',
                    'transform_out_columns_statement_code',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = TransformOutColumn::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => false,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'transform_out_columns_id' => $this->transform_out_columns_id,
            'transform_out_columns_business_index' => $this->transform_out_columns_business_index,
        ]);

        $query->andFilterWhere([
            'like',
            'transform_out_columns_output_key',
            $this->transform_out_columns_output_key,
        ])->andFilterWhere([
            'like',
            'transform_out_columns_output_type',
            $this->transform_out_columns_output_type,
        ])->andFilterWhere([
            'like',
            'transform_out_columns_is_business_key',
            $this->transform_out_columns_is_business_key,
        ])->andFilterWhere(['transform_out_columns_statement_code' => $this->transform_out_columns_statement_code]);
        $query->orderBy(['transform_out_columns_id' => SORT_DESC]);

        return $dataProvider;
    }
}
