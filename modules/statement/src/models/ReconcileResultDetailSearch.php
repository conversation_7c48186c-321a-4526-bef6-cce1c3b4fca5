<?php

namespace Codingheping\StatementComponent\models;

use yii\data\ActiveDataProvider;

/**
 * ReconcileResultDetailSearch represents the model behind the search form of `common\models\ReconcileResultDetail`.
 */
class ReconcileResultDetailSearch extends ReconcileResultDetail
{

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'reconcile_result_detail_version'], 'integer'],
            [
                [
                    'reconcile_result_detail_batch_no',
                    'reconcile_result_detail_primary_key_value',
                    'reconcile_result_detail_main_system_code',
                    'reconcile_result_detail_main_feed_content',
                    'reconcile_result_detail_other_system_code',
                    'reconcile_result_detail_other_feed_content',
                    'reconcile_result_detail_result_status',
                    'reconcile_result_detail_fail_cause',
                    'reconcile_result_detail_create_at',
                    'reconcile_result_detail_update_at',
                    'reconcile_result_detail_processing_status',
                ],
                'safe',
            ],
        ];
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find();

        // add conditions that should always apply here
        $pageSize = isset($params['per-page']) ? intval($params['per-page']) : 30;
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'reconcile_result_detail_result_status' => SORT_ASC,
                    'reconcile_result_detail_processing_status' => SORT_ASC,
                    'id' => SORT_DESC,
                ],
            ],
            'pagination' => ['pageSize' => $pageSize],
        ]);
        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        if (array_key_exists($this->reconcile_result_detail_processing_status, self::$processing_status_maps)) {
            $this->reconcile_result_detail_result_status = 'fail';
        }
        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'reconcile_result_detail_version' => $this->reconcile_result_detail_version,
            'reconcile_result_detail_result_status' => $this->reconcile_result_detail_result_status,
            'reconcile_result_detail_batch_no' => $this->reconcile_result_detail_batch_no,
            'reconcile_result_detail_primary_key_value' => $this->reconcile_result_detail_primary_key_value,
            'reconcile_result_detail_processing_status' => $this->reconcile_result_detail_processing_status,
        ]);

        $query->andFilterWhere([
            'like',
            'reconcile_result_detail_main_system_code',
            $this->reconcile_result_detail_main_system_code,
        ])
            ->andFilterWhere([
                'like',
                'reconcile_result_detail_main_feed_content',
                $this->reconcile_result_detail_main_feed_content,
            ])
            ->andFilterWhere([
                'like',
                'reconcile_result_detail_other_system_code',
                $this->reconcile_result_detail_other_system_code,
            ])
            ->andFilterWhere([
                'like',
                'reconcile_result_detail_other_feed_content',
                $this->reconcile_result_detail_other_feed_content,
            ])
            ->andFilterWhere(['like', 'reconcile_result_detail_fail_cause', $this->reconcile_result_detail_fail_cause]);

        return $dataProvider;
    }
}
