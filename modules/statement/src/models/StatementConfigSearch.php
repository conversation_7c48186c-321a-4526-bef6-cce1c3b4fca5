<?php

namespace Codingheping\StatementComponent\models;

use Codingheping\StatementComponent\helper\SqlHelper;
use yii\data\ActiveDataProvider;

/**
 * StatementConfigSearch represents the model behind the search form of `\modules\models\StatementConfig`.
 */
class StatementConfigSearch extends StatementConfig
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'integer'],
            [['statement_config_code', 'statement_config_name'], 'trim'],
            [
                [
                    'statement_config_code',
                    'statement_config_name',
                    'statement_config_conf',
                    'statement_config_group',
                    'statement_config_leader',
                    'statement_config_create_at',
                    'statement_config_update_at',
                    'status',
                ],
                'safe',
            ],
        ];
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = StatementConfig::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'statement_config_update_at' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'statement_config_create_at' => $this->statement_config_create_at,
            'statement_config_update_at' => $this->statement_config_update_at,
            'status' => $this->status,
        ]);
        $query->with('operator');

        $statement_config_group = $this->statement_config_group;
        if (!$statement_config_group || !array_key_exists($statement_config_group, UserGroup::getAvailableGroup())) {
            $statement_config_group = array_keys(UserGroup::getAvailableGroup());
        }

        $query->andFilterWhere(['like', 'statement_config_code', $this->statement_config_code])
            ->andFilterWhere(['like', 'statement_config_name', $this->statement_config_name])
            ->andFilterWhere(['statement_config_group' => $statement_config_group])
            ->andFilterWhere(['like', 'statement_config_conf', $this->statement_config_conf]);
        if (!empty($this->statement_config_leader)) {
            $query->andWhere(SqlHelper::makeFindInSetWhere('statement_config_leader', $this->statement_config_leader));
        }

        return $dataProvider;
    }
}
