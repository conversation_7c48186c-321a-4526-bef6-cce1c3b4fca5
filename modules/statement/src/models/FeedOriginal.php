<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "feed_original".
 *
 * @property int $feed_original_id
 * @property string $feed_original_batch_no
 * @property string $feed_original_system_code
 * @property string $feed_original_primarykey_value
 * @property string $feed_original_column_name
 * @property string $feed_original_column_value
 * @property string $feed_original_column_type
 * @property string $feed_original_created_at
 * @property string $feed_original_updated_at
 */
class FeedOriginal extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'feed_original';
    }


    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['feed_original_column_value'], 'string'],
            [['feed_original_created_at', 'feed_original_updated_at'], 'safe'],
            [['feed_original_batch_no'], 'string', 'max' => 64],
            [['feed_original_system_code'], 'string', 'max' => 128],
            [['feed_original_primarykey_value', 'feed_original_column_name'], 'string', 'max' => 255],
            [['feed_original_column_type'], 'string', 'max' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'feed_original_id' => 'ID',
            'feed_original_batch_no' => '批次号',
            'feed_original_system_code' => '系统CODE',
            'feed_original_primarykey_value' => '主键值',
            'feed_original_column_name' => '字段名称',
            'feed_original_column_value' => '字段值',
            'feed_original_column_type' => '字段类型',
            'feed_original_created_at' => '创建时间',
            'feed_original_updated_at' => '更新时间',
        ];
    }
}
