<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;

/**
 * This is the model class for table "notify_group_config".
 *
 * @property int $notify_group_config_id
 * @property string $notify_group_config_name            组名称
 * @property string $notify_group_config_notify_obj      通知对象，多个以分号分割
 * @property string $notify_group_config_statement_group 对账组别
 * @property string $notify_group_config_status          状态:valid 有效，无效：invalid
 * @property string $notify_group_config_create_at       创建时间
 * @property string $notify_group_config_update_at       更新时间
 */
class NotifyGroupConfig extends ActiveRecord
{

    public const NOTIFY_STATUS_VALID = 'valid';
    public const NOTIFY_STATUS_INVALID = 'invalid';

    public const NOTIFY_STATUS_MAP = [
        self::NOTIFY_STATUS_VALID => '有效',
        self::NOTIFY_STATUS_INVALID => '无效',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'notify_group_config';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                ['notify_group_config_name', 'notify_group_config_notify_obj', 'notify_group_config_statement_group'],
                'required',
            ],
            [['notify_group_config_notify_obj'], 'string'],
            [['notify_group_config_create_at', 'notify_group_config_update_at'], 'safe'],
            [['notify_group_config_name'], 'string', 'max' => 128],
            [['notify_group_config_statement_group'], 'string', 'max' => 52],
            [
                'notify_group_config_statement_group',
                function ($attribute, $params) {
                    if (!$this->isNewRecord) {
                        $group = self::find()
                            ->select('statement_config_group')
                            ->innerJoin(
                                'notify_config',
                                'notify_config_use_group="YES" and notify_group_config_id=notify_config_group_id'
                            )
                            ->innerJoin('statement_config', 'statement_config_code=notify_config_statement_code')
                            ->column();
                        if (!in_array($this->$attribute, $group, true)) {
                            $this->addError('notify_group_config_statement_group', '邮件组已被相应组使用，不能再变更所属组');

                            return false;
                        }
                    }

                    return true;
                },
            ],
            ['notify_group_config_status', 'in', 'range' => array_keys(self::NOTIFY_STATUS_MAP)],
            [
                'notify_group_config_status',
                function ($attribute, $params) {
                    if (!$this->isNewRecord && ($this->$attribute === self::NOTIFY_STATUS_INVALID)
                        && NotifyConfig::find()
                            ->where([
                                'notify_config_use_group' => NotifyConfig::NOTIFY_GROUP_YES,
                                'notify_config_group_id' => $this->notify_group_config_id,
                            ])->exists()
                    ) {
                        $this->addError('notify_group_config_status', '对账配置使用中，不能被禁用');

                        return false;
                    }

                    return true;
                },
            ],
            [
                ['notify_group_config_name', 'notify_group_config_statement_group'],
                'unique',
                'targetAttribute' => ['notify_group_config_name', 'notify_group_config_statement_group'],
                'message' => '该组下已存在该邮件组！',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'notify_group_config_id' => '邮件组配置 ID',
            'notify_group_config_name' => '邮件组名称',
            'notify_group_config_notify_obj' => '邮件组通知对象',
            'notify_group_config_statement_group' => '所属组',
            'notify_group_config_status' => '邮件组配置状态',
            'notify_group_config_create_at' => '创建时间',
            'notify_group_config_update_at' => '更新时间',
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => [
                        'notify_group_config_create_at',
                        'notify_group_config_update_at',
                    ],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['notify_group_config_update_at'],
                ],
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    public static function getNotifyGroupConfigMap($status = null)
    {
        return self::find()
            ->select('notify_group_config_name,notify_group_config_id')
            ->where(['notify_group_config_statement_group' => array_keys(UserGroup::getAvailableGroup())])
            ->andFilterWhere(['notify_group_config_status' => $status])
            ->indexBy('notify_group_config_id')
            ->column();
    }
}
