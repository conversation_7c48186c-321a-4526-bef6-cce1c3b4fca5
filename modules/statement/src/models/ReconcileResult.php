<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "reconcile_result".
 *
 * @property int $id
 * @property string|null $reconcile_result_batch_no 批次号
 * @property string|null $reconcile_result_main_system_code 主系统code
 * @property string|null $reconcile_result_other_system_code 次系统code
 * @property string|null $reconcile_result_config_no 对账配置编码
 * @property int|null $reconcile_result_total_count 总对比数
 * @property int|null $reconcile_result_success_count 对比成功条数
 * @property int|null $reconcile_result_fail_count 对比失败的条数
 * @property string $reconcile_result_create_at 创建时间
 * @property string $reconcile_result_update_at 更新时间
 * @property int $reconcile_result_untreated_count 未处理条数
 * @property string|null $reconcile_result_summary_content 对账汇总信息
 */
class ReconcileResult extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'reconcile_result';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'reconcile_result_total_count',
                    'reconcile_result_success_count',
                    'reconcile_result_fail_count',
                    'reconcile_result_untreated_count',
                ],
                'integer',
            ],
            [['reconcile_result_create_at', 'reconcile_result_update_at'], 'safe'],
            [
                [
                    'reconcile_result_batch_no',
                    'reconcile_result_main_system_code',
                    'reconcile_result_other_system_code',
                ],
                'string',
                'max' => 64,
            ],
            [['reconcile_result_config_no', 'reconcile_result_summary_content'], 'string', 'max' => 256],
            [['reconcile_result_batch_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'reconcile_result_batch_no' => '批次号',
            'reconcile_result_main_system_code' => '主系统code',
            'reconcile_result_other_system_code' => '次系统code',
            'reconcile_result_config_no' => '对账配置编码',
            'reconcile_result_total_count' => '总对比数',
            'reconcile_result_success_count' => '对比成功条数',
            'reconcile_result_fail_count' => '对比失败的条数',
            'reconcile_result_create_at' => '创建时间',
            'reconcile_result_update_at' => '更新时间',
            'reconcile_result_untreated_count' => '未处理条数',
            'reconcile_result_summary_content' => '对账汇总信息',
        ];
    }
}
