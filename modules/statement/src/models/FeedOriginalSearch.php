<?php

namespace Codingheping\StatementComponent\models;

use Codingheping\StatementComponent\component\StatementGateComponent;
use Codingheping\StatementComponent\data\ArrayDataProvider;
use Exception;
use xlerr\common\models\Settings;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\data\Pagination;

/**
 * FeedOriginalSearch represents the model behind the search form of `common\models\FeedOriginal`.
 */
class FeedOriginalSearch extends FeedOriginal
{
    public $configCode;
    public $batchNo;
    public $businessKey;
    public $fieldName;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                ['batchNo'],
                function () {
                    if ($this->batchNo) {
                        return true;
                    }
                    $this->addError('batchNo', '当前查询依赖对账批次');
                    Yii::$app->session->setFlash('error', '当前查询依赖对账批次');

                    return false;
                },
                'skipOnEmpty' => false,
            ],
            [
                [
                    'configCode',
                    'batchNo',
                    'businessKey',
                    'fieldName',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ArrayDataProvider
     * @throws InvalidConfigException
     */
    public function search(array $params): ArrayDataProvider
    {
        $pagination = new Pagination([
            'pageSizeLimit' => [10, 1000],
        ]);
        $pagination->setPageSize($this->getPageSetting($pagination));
        [$data, $totalCount] = $this->queryData($params, $pagination);

        return new ArrayDataProvider([
            'totalCount' => $totalCount,
            'allModels' => $data,
            'pagination' => $pagination,
        ]);
    }

    /**
     * @param                      $params
     * @param Pagination $pagination
     *
     * @return array|null
     */
    protected function queryData($params, Pagination $pagination): ?array
    {
        $this->load($params, '');
        if ($this->validate()) {
            $queryParams = [];
            $queryParams['page_index'] = $pagination->getPage();
            $queryParams['page_size'] = $pagination->getPageSize();
            $queryParams['config_code'] = $this->configCode;
            $queryParams['batch_no'] = $this->batchNo;
            $queryParams['business_key'] = $this->businessKey;
            $queryParams['field_name'] = $this->fieldName;
            try {
                $client = StatementGateComponent::instance();
                if (!$client->original($queryParams) && $client->getHasError()) {
                    throw new UserException($client->getError());
                }
                $data = $client->getData();

                return [$data['dataList'], $data['totalCount']];
            } catch (Exception $e) {
                Yii::$app->session->setFlash('error', $e->getMessage());

                return [[], 0];
            }
        }

        return [[], 0];
    }

    /**
     * @param Pagination $pagination
     *
     * @return int|mixed
     * @throws InvalidConfigException
     */
    protected function getPageSetting(Pagination $pagination)
    {
        $page = Yii::$app->request->getQueryParam($pagination->pageSizeParam);
        if ($page === null) {
            $path = '/' . Yii::$app->getRequest()->getPathInfo();
            $settings = Settings::getSettings(Yii::$app->getUser()->getId(), $path, $pagination->pageSizeParam);

            return $settings[$pagination->pageSizeParam] ?? 20;
        }

        return $page;
    }
}
