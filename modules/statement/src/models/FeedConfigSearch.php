<?php


namespace Codingheping\StatementComponent\models;

use yii\data\ActiveDataProvider;

class FeedConfigSearch extends FeedConfig
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['feed_config_id', 'feed_config_seq'], 'integer'],
            [
                [
                    'feed_config_statement_code',
                    'feed_config_fetch_type',
                    'feed_config_system_code',
                    'feed_config_system_name',
                    'feed_config_data_format_type',
                    'feed_config_primary_key',
                    'feed_config_user_name',
                    'feed_config_password',
                    'feed_config_driver_class',
                    'feed_config_url',
                    'feed_config_sql',
                    'feed_config_request_method',
                    'feed_config_path_to_data',
                    'feed_config_request_params',
                    'feed_config_trigger_time',
                    'feed_config_create_at',
                    'feed_config_update_at',
                ],
                'safe',
            ],
        ];
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = FeedConfig::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'feed_config_id' => $this->feed_config_id,
            'feed_config_seq' => $this->feed_config_seq,
            'feed_config_create_at' => $this->feed_config_create_at,
            'feed_config_update_at' => $this->feed_config_update_at,
        ]);

        $query->andFilterWhere(['like', 'feed_config_statement_code', $this->feed_config_statement_code])
            ->andFilterWhere(['like', 'feed_config_fetch_type', $this->feed_config_fetch_type])
            ->andFilterWhere(['like', 'feed_config_system_code', $this->feed_config_system_code])
            ->andFilterWhere(['like', 'feed_config_system_name', $this->feed_config_system_name])
            ->andFilterWhere(['like', 'feed_config_data_format_type', $this->feed_config_data_format_type])
            ->andFilterWhere(['like', 'feed_config_primary_key', $this->feed_config_primary_key])
            ->andFilterWhere(['like', 'feed_config_user_name', $this->feed_config_user_name])
            ->andFilterWhere(['like', 'feed_config_password', $this->feed_config_password])
            ->andFilterWhere(['like', 'feed_config_driver_class', $this->feed_config_driver_class])
            ->andFilterWhere(['like', 'feed_config_url', $this->feed_config_url])
            ->andFilterWhere(['like', 'feed_config_sql', $this->feed_config_sql])
            ->andFilterWhere(['like', 'feed_config_request_method', $this->feed_config_request_method])
            ->andFilterWhere(['like', 'feed_config_path_to_data', $this->feed_config_path_to_data])
            ->andFilterWhere(['like', 'feed_config_request_params', $this->feed_config_request_params])
            ->andFilterWhere(['like', 'feed_config_trigger_time', $this->feed_config_trigger_time]);

        return $dataProvider;
    }
}
