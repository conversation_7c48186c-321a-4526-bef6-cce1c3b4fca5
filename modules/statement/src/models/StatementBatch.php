<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "statement_batch".
 *
 * @property int $id
 * @property string $statementBatchNo                         批次编号
 * @property string $statement_batch_no                       批次编号
 * @property string|null $statement_batch_task_order_no            对应task表的order_no
 * @property string|null $statement_batch_status                   状态  feeding 数据获取中 feedsuccess 数据获取成功  reconciling 数据对账中  reconcilesuccess 对账成功 notifying   通知中  notifysuccess 通知成功
 * @property string|null $statement_batch_date                     对账时间
 * @property string|null $statement_batch_config_no                statement_config中的对账编号
 * @property string|null $statement_batch_create_at                创建时间
 * @property string|null $statement_batch_update_at                修改时间
 * @property int|null $statement_batch_version                  版本号
 * @property string $statement_batch_type                     类型 auto :自动 、manual：手动
 * @property string|null $statement_batch_params_content           任务参数
 * @property string|null $statement_batch_main_system_file_path    第一个数据源临时文件路劲
 * @property string|null $statement_batch_other_system_file_path   第二个数据源临时文件路劲
 * @property int|null $statement_batch_operator
 */
class StatementBatch extends ActiveRecord
{
    public const STATUS_INIT = 'init';
    public const STATUS_FEEDING = 'feeding';
    public const STATUS_FEEDSUCCESS = 'feed_success';
    public const STATUS_RECONCILING = 'reconciling';
    public const STATUS_RECONCILESUCCESS = 'reconcile_success';
    public const STATUS_NOTIFYING = 'notifying';
    public const STATUS_NOTIFYSUCCESS = 'notify_success';

    public static array $statusText = [
        self::STATUS_INIT => '初始化',
        self::STATUS_FEEDING => '数据获取中',
        self::STATUS_FEEDSUCCESS => '数据获取成功',
        self::STATUS_RECONCILING => '数据对账中',
        self::STATUS_RECONCILESUCCESS => '对账成功',
        self::STATUS_NOTIFYING => '通知中',
        self::STATUS_NOTIFYSUCCESS => '通知成功',
    ];

    /**
     * @var array|Closure $kvConfig
     */
    public static $kvConfig = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'statement_batch';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['statement_batch_no'], 'required'],
            [['statement_batch_date', 'statement_batch_create_at', 'statement_batch_update_at'], 'safe'],
            [['statement_batch_version', 'statement_batch_operator'], 'integer'],
            [['statement_batch_type', 'statement_batch_params_content'], 'string'],
            [['statement_batch_no', 'statement_batch_status'], 'string', 'max' => 32],
            [['statement_batch_task_order_no', 'statement_batch_config_no'], 'string', 'max' => 64],
            [
                ['statement_batch_main_system_file_path', 'statement_batch_other_system_file_path'],
                'string',
                'max' => 256,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'statement_batch_no' => '批次编号',
            'statement_batch_task_order_no' => '对应task表的order_no',
            'statement_batch_status' => '状态  feeding 数据获取中 feedsuccess 数据获取成功  reconciling 数据对账中  reconcilesuccess 对账成功 notifying   通知中  notifysuccess 通知成功',
            'statement_batch_date' => '对账时间',
            'statement_batch_config_no' => 'statement_config中的对账编号',
            'statement_batch_create_at' => '创建时间',
            'statement_batch_update_at' => '修改时间',
            'statement_batch_version' => '版本号',
            'statement_batch_type' => ' 类型 auto :自动 、manual：手动',
            'statement_batch_params_content' => '任务参数',
            'statement_batch_main_system_file_path' => '第一个数据源临时文件路劲',
            'statement_batch_other_system_file_path' => '第二个数据源临时文件路劲',
            'statement_batch_operator' => 'Statement Batch Operator',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getStatementConfig()
    {
        return $this->hasOne(StatementConfig::class, ['statement_config_code' => 'statement_batch_config_no']);
    }

    /**
     * @return ActiveQuery
     */
    public function getTask()
    {
        return $this->hasOne(Task::class, ['task_order_no' => 'statement_batch_task_order_no']);
    }

    /**
     * @return ActiveQuery
     */
    public function getReconcileResult()
    {
        return $this->hasOne(ReconcileResult::class, ['reconcile_result_batch_no' => 'statement_batch_no']);
    }


    public static function getBatchNoMap(string $code = null, string $date = ''): array
    {
        $group = array_keys(UserGroup::getAvailableGroup());

        return self::find()
            ->innerJoinWith([
                'statementConfig' => function (ActiveQuery $query) use ($group) {
                    return $query->andOnCondition(['statement_config_group' => $group]);
                },
            ], false)
            ->filterWhere(['statement_batch_config_no' => $code])
            ->andFilterWhere(['>', 'statement_batch_date', $date])
            ->select(['id' => 'statement_batch_no', 'name' => 'statement_batch_no'])
            ->indexBy('id')
            ->orderBy(['statement_batch_no' => SORT_DESC])
            ->asArray()->all();
    }
}
