<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Exception;
use yii\helpers\Json;

/**
 * This is the model class for table "statement_mistake_solution".
 *
 * @property int $statement_mistake_solution_id                         主键
 * @property int $statement_mistake_solution_reconcile_result_detail_id 差错结果ID
 * @property string $statement_mistake_solution_trouble_type               问题类型
 * @property string $statement_mistake_solution_is_need_solve              是否需要处理：Y-需要，N-不需要
 * @property string $statement_mistake_solution_result                     处理方案
 * @property int $statement_mistake_solution_user_id                    处理人ID
 * @property string $statement_mistake_solution_user_name                  处理人
 * @property string $statement_mistake_solution_create_at                  创建时间
 * @property string $statement_mistake_solution_update_at                  更新时间
 */
class StatementMistakeSolution extends ActiveRecord
{

    public const NEED_SOLVE = 'Y';
    public const NOT_NEED_SOLVE = 'N';
    public const SOLVE_STATUS = [
        self::NEED_SOLVE => '需要处理',
        self::NOT_NEED_SOLVE => '无需处理',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'statement_mistake_solution';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'statement_mistake_solution_reconcile_result_detail_id',
                    'statement_mistake_solution_is_need_solve',
                    'statement_mistake_solution_user_id',
                    'statement_mistake_solution_user_name',
                ],
                'required',
            ],
            [
                ['statement_mistake_solution_result', 'statement_mistake_solution_trouble_type'],
                'required',
                'when' => function ($model) {
                    return $model->statement_mistake_solution_is_need_solve === self::NEED_SOLVE;
                },
            ],
            [
                ['statement_mistake_solution_reconcile_result_detail_id', 'statement_mistake_solution_user_id'],
                'integer',
            ],
            [['statement_mistake_solution_is_need_solve'], 'string'],
            [['statement_mistake_solution_create_at', 'statement_mistake_solution_update_at'], 'safe'],
            [
                [
                    'statement_mistake_solution_trouble_type',
                    'statement_mistake_solution_result',
                    'statement_mistake_solution_user_name',
                ],
                'string',
                'max' => 255,
            ],
            ['statement_mistake_solution_trouble_type', 'default', 'value' => '无需处理'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'statement_mistake_solution_id' => '主键',
            'statement_mistake_solution_reconcile_result_detail_id' => '差错结果ID',
            'statement_mistake_solution_trouble_type' => '问题类型',
            'statement_mistake_solution_is_need_solve' => '是否需要处理：Y-需要，N-不需要',
            'statement_mistake_solution_result' => '处理方案',
            'statement_mistake_solution_user_id' => '处理人ID',
            'statement_mistake_solution_user_name' => '处理人',
            'statement_mistake_solution_create_at' => '创建时间',
            'statement_mistake_solution_update_at' => '更新时间',
        ];
    }

    /**
     * 批量添加处理记录
     *
     * @param $data
     * @param $ids
     *
     * @return int
     * @throws Exception
     */
    public static function batchAdd($data, $ids): int
    {
        /* @var $data BatchProcessing */
        $params = [];
        foreach ($ids as $id) {
            $params[] = [
                'statement_mistake_solution_reconcile_result_detail_id' => $id,
                'statement_mistake_solution_trouble_type' => $data->troubleType,
                'statement_mistake_solution_is_need_solve' => $data->isNeedSolve,
                'statement_mistake_solution_result' => $data->result,
                'statement_mistake_solution_user_id' => Yii::$app->getUser()->id,
                'statement_mistake_solution_user_name' => Yii::$app->getUser()->id,
            ];
        }

        return self::getDb()->createCommand()
            ->batchInsert(self::tableName(), [
                'statement_mistake_solution_reconcile_result_detail_id',
                'statement_mistake_solution_trouble_type',
                'statement_mistake_solution_is_need_solve',
                'statement_mistake_solution_result',
                'statement_mistake_solution_user_id',
                'statement_mistake_solution_user_name',
            ], $params)
            ->execute();
    }

    /**
     * 单条添加
     *
     * @param $params
     *
     * @return StatementMistakeSolution
     * @throws UserException
     */
    public static function import($params): StatementMistakeSolution
    {
        $statementMistakeSolution = new self();
        $statementMistakeSolution->statement_mistake_solution_reconcile_result_detail_id = $params['id'];
        $statementMistakeSolution->statement_mistake_solution_trouble_type =
            $params['statement_mistake_solution_trouble_type'];
        $statementMistakeSolution->statement_mistake_solution_is_need_solve =
            $params['statement_mistake_solution_is_need_solve'];
        $statementMistakeSolution->statement_mistake_solution_result =
            $params['statement_mistake_solution_result'];
        $statementMistakeSolution->statement_mistake_solution_user_id = $params['user_id'];
        $statementMistakeSolution->statement_mistake_solution_user_name = $params['user_name'];
        if (!$statementMistakeSolution->save()) {
            throw new UserException(Json::encode(
                $statementMistakeSolution->getErrors(),
                JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
            ));
        }

        return $statementMistakeSolution;
    }
}
