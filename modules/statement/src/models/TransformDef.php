<?php /** @noinspection PhpConstantNamingConventionInspection */

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;

/**
 * This is the model class for table "transform_def".
 *
 * @property int $transform_def_id                           主键
 * @property string $transform_def_statement_code               对账配置code
 * @property int $transform_def_index                        索引位置
 * @property string $transform_def_input_key                    输入列名
 * @property string $transform_def_input_type                   输入列的数据类型
 * @property string $transform_def_output_key                   对账列名
 * @property string $transform_def_output_type                  对账列类型
 * @property int $transform_def_feed_config_id               数据源Id
 * @property int $transform_def_is_desensitization           是否脱敏
 * @property int $transform_def_is_business_key              是否业务主键
 * @property int $transform_def_business_index               业务主键顺序
 * @property int $transform_def_transform_out_columns_id     所属对账列ID
 * @property string $transform_def_create_at                    创建时间
 * @property string $transform_def_update_at                    更新时间
 */
class TransformDef extends ActiveRecord
{
    public const INPUT_TYPE_STRING = 'String';
    public const INPUT_TYPE_INTEGER = 'Integer';
    public const INPUT_TYPE_TIMESTAMP = 'Timestamp';
    public const INPUT_TYPE_DATE = 'Date';
    public const INPUT_TYPE_BIGDECIMAL = 'BigDecimal';
    public const INPUT_TYPE_DOUBLE = 'Double';
    public const INPUT_TYPE_SHORT = 'Short';
    public const INPUT_TYPE_NUMBER = 'Number';
    public const INPUT_TYPE_DATEWITHOUTTIME = 'DateWithOutTime';

    public const INPUT_TYPE_MAP = [
        self::INPUT_TYPE_STRING => self::INPUT_TYPE_STRING,
        self::INPUT_TYPE_INTEGER => self::INPUT_TYPE_INTEGER,
        self::INPUT_TYPE_TIMESTAMP => self::INPUT_TYPE_TIMESTAMP,
        self::INPUT_TYPE_DATE => self::INPUT_TYPE_DATE,
        self::INPUT_TYPE_BIGDECIMAL => self::INPUT_TYPE_BIGDECIMAL,
        self::INPUT_TYPE_DOUBLE => self::INPUT_TYPE_DOUBLE,
        self::INPUT_TYPE_SHORT => self::INPUT_TYPE_SHORT,
        self::INPUT_TYPE_NUMBER => self::INPUT_TYPE_NUMBER,
        self::INPUT_TYPE_DATEWITHOUTTIME => self::INPUT_TYPE_DATEWITHOUTTIME,
    ];

    public const DATA_SOURCE_DESENSITIZATION_TYPE_MOBILE = 'phone';
    public const DATA_SOURCE_DESENSITIZATION_TYPE_USER_NAME = 'user_name';
    public const DATA_SOURCE_DESENSITIZATION_TYPE_BANK_CARD = 'bank_card';
    public const DATA_SOURCE_DESENSITIZATION_TYPE_ID_CARD = 'id_card';

    public const DATA_SOURCE_DESENSITIZATION_TYPE_MAP = [
        self::DATA_SOURCE_DESENSITIZATION_TYPE_MOBILE => '手机号',
        self::DATA_SOURCE_DESENSITIZATION_TYPE_USER_NAME => '姓名',
        self::DATA_SOURCE_DESENSITIZATION_TYPE_BANK_CARD => '银行卡号',
        self::DATA_SOURCE_DESENSITIZATION_TYPE_ID_CARD => '身份证',
    ];

    public const DATA_SOURCE_DESENSITIZATION_YES = 'YES';
    public const DATA_SOURCE_DESENSITIZATION_NO = 'NO';

    public const DATA_SOURCE_DESENSITIZATION_MAP = [
        self::DATA_SOURCE_DESENSITIZATION_YES => '是',
        self::DATA_SOURCE_DESENSITIZATION_NO => '否',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'transform_def';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['transform_def_index', 'transform_def_feed_config_id'], 'integer'],
            [
                [
                    'transform_def_feed_config_id',
                    'transform_def_is_desensitization',
                    'transform_def_input_key',
                    'transform_def_input_type',
                    'transform_def_output_key',
                    'transform_def_output_type',
                ],
                'required',
            ],
            [
                [
                    'transform_def_create_at',
                    'transform_def_update_at',
                    'transform_def_desensitization_type',
                    'transform_def_transform_out_columns_id',
                ],
                'safe',
            ],
            [
                ['transform_def_statement_code', 'transform_def_input_type', 'transform_def_output_type'],
                'string',
                'max' => 50,
            ],
            [['transform_def_input_key', 'transform_def_output_key'], 'string', 'max' => 100],
            [
                'transform_def_statement_code',
                'default',
                'value' => function ($model, $attribute) {
                    return $model->$attribute =
                        FeedConfig::getStatementCodeByFeedId($model->transform_def_feed_config_id);
                },
            ],
            [
                ['transform_def_is_desensitization', 'transform_def_is_business_key'],
                'default',
                'value' => function ($model) {
                    return self::DATA_SOURCE_DESENSITIZATION_NO;
                },
            ],
            ['transform_def_business_index', 'integer'],
            ['transform_def_business_index', 'default', 'value' => 0],
            [
                'transform_def_transform_out_columns_id',
                function ($attribute, $params) {
                    $exists = TransformDef::find()->where([
                        'and',
                        [
                            'transform_def_feed_config_id' => $this->transform_def_feed_config_id,
                            'transform_def_statement_code' => $this->transform_def_statement_code,
                            'transform_def_transform_out_columns_id' => $this->transform_def_transform_out_columns_id,
                        ],
                        ['!=', 'transform_def_id', $this->transform_def_id],
                    ])->exists();
                    if ($exists) {
                        $this->addError($attribute, '对账列配置已使用，请检查！');

                        return false;
                    }

                    return true;
                },
            ],

        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'transform_def_id' => 'ID',
            'transform_def_statement_code' => '对账 Code',
            'transform_def_index' => '索引位置',
            'transform_def_input_key' => '输入列名',
            'transform_def_input_type' => '输入列类型',
            'transform_def_output_key' => '对账列名',
            'transform_def_output_type' => '对账列类型',
            'transform_def_feed_config_id' => '数据源 ID',
            'transform_def_is_desensitization' => '是否脱敏',
            'transform_def_desensitization_type' => '脱敏类型',
            'transform_def_is_business_key' => '是否业务主键',
            'transform_def_business_index' => '业务主键顺序',
            'transform_def_create_at' => '创建时间',
            'transform_def_update_at' => '更新时间',
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => [
                        'transform_def_create_at',
                        'transform_def_update_at',
                    ],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['transform_def_update_at'],
                ],
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    public static function getTranCountByFeedId($id)
    {
        return self::find()->where(['transform_def_feed_config_id' => $id])->count();
    }

    public static function getPrimaryTranCountByFeedId($id): bool
    {
        return self::find()->where([
            'transform_def_feed_config_id' => $id,
            'transform_def_is_business_key' => self::DATA_SOURCE_DESENSITIZATION_YES,
        ])->exists();
    }

    public static function getTranDefsByStatementCode($code): array
    {
        return self::find()
            ->select('transform_def_output_key')
            ->where(['transform_def_statement_code' => $code])
            ->distinct()
            ->column();
    }

    public function getStatementConfig()
    {
        return $this->hasOne(StatementConfig::class, ['statement_config_code' => 'transform_def_statement_code']);
    }

    public function beforeSave($insert)
    {
        $this->statementConfig->statement_config_updator = Yii::$app->user->identity->getId();
        $this->statementConfig->update();

        return parent::beforeSave($insert);
    }
}
