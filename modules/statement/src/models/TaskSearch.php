<?php

namespace Codingheping\StatementComponent\models;

use Carbon\Carbon;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;

/**
 * TaskSearch represents the model behind the search form of `\common\models\Task`.
 */
class TaskSearch extends Task
{

    public $startDate;
    public $endDate;
    public $group;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['startDate', 'endDate'], 'default', 'value' => Carbon::now()->toDateString()],
            [['task_order_no'], 'filter', 'filter' => 'trim'],
            [
                [
                    'task_type',
                    'task_request_data',
                    'task_response_data',
                    'task_memo',
                    'task_status',
                    'task_next_run_at',
                    'task_create_at',
                    'task_update_at',
                    'startDate',
                    'endDate',
                    'group',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Task::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'defaultPageSize' => 20,
            ],
            'sort' => [
                'defaultOrder' => [
                    'task_id' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }
        $query->where([
            'and',
            ['>=', 'task_create_at', $this->startDate],
            ['<', 'task_create_at', Carbon::parse($this->endDate)->addDay()->toDateString()],
        ]);
        // grid filtering conditions
        $query->andFilterWhere([
            'task_id' => $this->task_id,
            'task_next_run_at' => $this->task_next_run_at,
            'task_version' => $this->task_version,
            'task_priority' => $this->task_priority,
            'task_retrytimes' => $this->task_retrytimes,
            'task_type' => $this->task_type,
            'task_status' => $this->task_status,
        ]);

        $query->andFilterWhere(['like', 'task_request_data', $this->task_request_data])
            ->andFilterWhere(['like', 'task_response_data', $this->task_response_data])
            ->andFilterWhere(['like', 'task_order_no', $this->task_order_no])
            ->andFilterWhere(['like', 'task_memo', $this->task_memo]);

        if (!empty($this->group)) {
            $group = $this->group;
            $query->innerJoinWith([
                'statement' => function (ActiveQuery $query) use ($group) {
                    $query->andFilterWhere(['statement_config_group' => $group]);
                },
            ], false);
        }

        return $dataProvider;
    }
}
