<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "task".
 *
 * @property int $task_id
 * @property string $task_order_no      任务编号
 * @property string $task_type          任务类型
 * @property string $task_request_data  任务接收参数
 * @property string $task_response_data 任务执行返回数据
 * @property string $task_memo          备注
 * @property string $task_status        状态
 * @property string $task_next_run_at   下次执行时间
 * @property string $task_create_at     创建时间
 * @property string $task_update_at     更新时间
 * @property int $task_version       版本
 * @property int $task_priority      优先级
 * @property int $task_retrytimes    重试次数
 */
class Task extends ActiveRecord
{
    public const TASK_PRIORITY_1 = 1;
    public const TASK_PRIORITY_2 = 2;
    public const TASK_PRIORITY_3 = 3;
    public const TASK_PRIORITY_4 = 4;
    public const TASK_PRIORITY_5 = 5;
    public const TASK_PRIORITY_6 = 6;
    public const TASK_PRIORITY_7 = 7;         // 恒丰转账专用，请勿使用
    public const TASK_PRIORITY_8 = 8;         // 恒丰提现专用，请勿使用
    public const TASK_PRIORITY_9 = 9;
    public const TASK_PRIORITY_10 = 10;        // 对账平台专用，请勿使用
    public const TASK_PRIORITY_11 = 11;        // 备用


    public const TASK_STATUS_OPEN = 'open';
    public const TASK_STATUS_RUNNING = 'running';
    public const TASK_STATUS_CLOSE = 'close';
    public const TASK_STATUS_TERMINATED = 'terminated';
    public const TASK_STATUS_ERROR = 'error';

    public const TASK_STATUS = [
        self::TASK_STATUS_OPEN => '待处理',
        self::TASK_STATUS_RUNNING => '处理中',
        self::TASK_STATUS_CLOSE => '处理成功',
        self::TASK_STATUS_TERMINATED => '终止处理',
        self::TASK_STATUS_ERROR => '处理失败',
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'task';
    }

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['task_type', 'task_request_data', 'task_response_data', 'task_status'], 'required'],
            [['task_request_data', 'task_response_data', 'task_status'], 'string'],
            [['task_next_run_at', 'task_create_at', 'task_update_at'], 'safe'],
            [['task_version', 'task_priority', 'task_retrytimes'], 'integer'],
            [['task_order_no'], 'string', 'max' => 64],
            [['task_type'], 'string', 'max' => 45],
            [['task_memo'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'task_id' => '任务ID',
            'task_order_no' => '任务编号',
            'task_type' => '任务类型',
            'task_request_data' => '任务接收参数',
            'task_response_data' => '任务执行返回数据',
            'task_memo' => '备注',
            'task_status' => '状态',
            'task_next_run_at' => '下次执行时间',
            'task_create_at' => '创建时间',
            'task_update_at' => '更新时间',
            'task_version' => '版本',
            'task_priority' => '优先级',
            'task_retrytimes' => '重试次数',
        ];
    }

    public function getBatch()
    {
        return $this->hasOne(StatementBatch::class, ['statement_batch_task_order_no' => 'task_order_no']);
    }

    public function getStatement()
    {
        return $this->hasOne(StatementConfig::class, ['statement_config_code' => 'statement_batch_config_no'])
            ->via('batch');
    }
}
