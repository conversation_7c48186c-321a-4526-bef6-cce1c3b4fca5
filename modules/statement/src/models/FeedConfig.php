<?php

namespace Codingheping\StatementComponent\models;

use kvmanager\models\KeyValue;
use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\db\Expression;

/**
 * This is the model class for table "feed_config".
 *
 * @property int    $feed_config_id                   主键
 * @property string $feed_config_statement_code       对账配置code
 * @property string $feed_config_fetch_type           抓取方式
 * @property string $feed_config_system_code          系统code
 * @property string $feed_config_system_name          系统名称
 * @property string $feed_config_data_format_type     数据格式类型
 * @property string $feed_config_primary_key          唯一业务主键
 * @property string $feed_config_user_name            用户名
 * @property string $feed_config_password             密码
 * @property string $feed_config_driver_class         驱动类型
 * @property string $feed_config_url                  数据获取地址，SQL方式就是填写数据库连接池，其他方式填写具体的地址
 * @property string $feed_config_sql                  执行SQL
 * @property string $feed_config_request_method       请求方式
 * @property string $feed_config_path_to_data         数据在接口返回的位置
 * @property string $feed_config_request_params       API请求参数
 * @property string $feed_config_trigger_time         抓取数据执行时间
 * @property int    $feed_config_seq                  数据源顺序
 * @property int    $feed_config_is_desensitization   是否脱敏
 * @property int    $feed_config_feed_file_type       文件类型
 * @property int    $feed_config_file_name            文件路径
 * @property int    $feed_config_ignore_first_row     跳过文本首行
 * @property int    $feed_config_ignore_end_row       跳过文本尾行
 * @property string $feed_config_create_at            创建时间
 * @property string $feed_config_update_at            更新时间
 * @property string $feed_config_file_split_char      文件分隔符
 * @property string $feed_config_secret_key           秘钥
 * @property string $feed_config_db_name              数据库名
 * @property string $feed_config_extra_show_column    额外显示列
 * @property string $feed_config_callback_url         对账对调地址
 * @property string $feed_config_callback_method      对账回调请求方式
 * @property string $feed_config_file_skip_char       过滤指定值
 */
class FeedConfig extends ActiveRecord
{

    public const DATA_FORMAT_JSON = 'JSON';
    public const DATA_FORMAT_TXT = 'TXT';
    public const DATA_FORMAT_CSV = 'CSV';
    public const DATA_FORMAT_EXCEL_XLS = 'EXCEL_XLS';
    public const DATA_FORMAT_EXCEL_XLSX = 'EXCEL_XLSX';
    public const DATA_FORMAT_JSON_FILE = 'JSON_FILE';
    public const DATA_FORMAT_WHOLE_JSON_FILE = 'WHOLE_JSON_FILE';

    public const DATA_FORMAT_MAP = [
        self::DATA_FORMAT_JSON => 'JSON',
        self::DATA_FORMAT_TXT => 'TXT',
        self::DATA_FORMAT_CSV => 'CSV',
        self::DATA_FORMAT_EXCEL_XLS => 'EXCEL_XLS',
        self::DATA_FORMAT_EXCEL_XLSX => 'EXCEL_XLSX',
        self::DATA_FORMAT_JSON_FILE => 'JSON_FILE',
        self::DATA_FORMAT_WHOLE_JSON_FILE => 'WHOLE_JSON_FILE',
    ];

    public const FETCH_TYPE_SQL = 'SQL';
    public const FETCH_TYPE_API = 'API';
    public const FETCH_TYPE_FILE = 'FILE';
    public const FETCH_TYPE_FTP = 'FTP';
    public const FETCH_TYPE_SFTP = 'SFTP';
    public const FETCH_TYPE_URL_FILE = 'URL_FILE';

    public const FETCH_TYPE_MAP = [
        self::FETCH_TYPE_SQL => self::FETCH_TYPE_SQL,
        self::FETCH_TYPE_API => self::FETCH_TYPE_API,
        self::FETCH_TYPE_FILE => self::FETCH_TYPE_FILE,
        self::FETCH_TYPE_FTP => self::FETCH_TYPE_FTP,
        self::FETCH_TYPE_SFTP => self::FETCH_TYPE_SFTP,
        self::FETCH_TYPE_URL_FILE => self::FETCH_TYPE_URL_FILE,
    ];

    public const REQUEST_METHOD_POST = 'POST';
    public const REQUEST_METHOD_GET = 'GET';

    public const REQUEST_METHOD_MAP = [
        self::REQUEST_METHOD_POST => self::REQUEST_METHOD_POST,
        self::REQUEST_METHOD_GET => self::REQUEST_METHOD_GET,
    ];

    public const DRIVER_CLASS_MYSQL = 'MYSQL';
    public const DRIVER_CLASS_ORACLE = 'ORACLE';

    public const DRIVER_CLASS_MAP = [
        self::DRIVER_CLASS_MYSQL => self::DRIVER_CLASS_MYSQL,
        self::DRIVER_CLASS_ORACLE => self::DRIVER_CLASS_ORACLE,
    ];

    //数据源顺序
    public const DATA_SOURCE_SEQ_1 = 1;

    public const DATA_SOURCE_SEQ_2 = 2;

    public const FILE_READ_ROW_1 = 1;
    public const FILE_READ_ROW_0 = 0;

    public const FILE_READ_ROW_MAP = [
        self::FILE_READ_ROW_0 => '否',
        self::FILE_READ_ROW_1 => '是',
    ];
    public const CALLBACK_GET = 'GET';
    public const CALLBACK_POST = 'POST';

    public const CALLBACK_MAP = [
        self::CALLBACK_GET => self::CALLBACK_GET,
        self::CALLBACK_POST => self::CALLBACK_POST,
    ];
    /**
     * 手动创建对账允许上传的文件类型
     */
    public const ALLOW_UPLOAD_FORMAT = [
        self::DATA_FORMAT_EXCEL_XLS => 'application/vnd.ms-excel',
        self::DATA_FORMAT_EXCEL_XLSX => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        self::DATA_FORMAT_TXT => 'text/plain',
        self::DATA_FORMAT_CSV => 'text/csv,application/vnd.ms-excel',
        self::DATA_FORMAT_JSON => 'application/json,text/json',
    ];

    public const ALLOW_UPLOAD_SUFFIX = [
        self::DATA_FORMAT_EXCEL_XLS => 'xls',
        self::DATA_FORMAT_EXCEL_XLSX => 'xlsx',
        self::DATA_FORMAT_TXT => 'txt',
        self::DATA_FORMAT_CSV => 'csv',
        self::DATA_FORMAT_JSON => 'json',
    ];

    /**
     * @return Connection the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb(): Connection
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'feed_config';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                ['feed_config_trigger_time', 'feed_config_system_code', 'feed_config_system_name'],
                'required',
            ],
            [['feed_config_url', 'feed_config_sql', 'feed_config_file_skip_char','feed_config_request_params'], 'string'],
            [
                'feed_config_statement_code',
                'unique',
                'message' => '系统Code另一个数据源重复',
                'targetAttribute' => ['feed_config_system_code', 'feed_config_statement_code'],
            ],
            [['feed_config_seq'], 'integer'],
            [
                ['feed_config_db_name', 'feed_config_sql'],
                'required',
                'when' => function ($model) {
                    return $model->feed_config_fetch_type == self::FETCH_TYPE_SQL;
                },
            ],
            [
                ['feed_config_url'],
                'required',
                'when' => function ($model) {
                    return in_array($model->feed_config_fetch_type, [
                        FeedConfig::FETCH_TYPE_API,
                        FeedConfig::FETCH_TYPE_FTP,
                        FeedConfig::FETCH_TYPE_SFTP,
                        FeedConfig::FETCH_TYPE_URL_FILE,
                    ], true);
                },
            ],
            [
                'feed_config_primary_key',
                function ($attribute, $params) {
                    $primary_keys = explode('|', (string)$this->$attribute);
                    if (is_array($primary_keys) && !empty($primary_keys)) {
                        return true;
                    }

                    $this->addError($attribute, '业务主键不合法');

                    return false;
                },
                'skipOnEmpty' => true,
            ],
            [
                ['feed_config_request_method'],
                'required',
                'when' => function ($model) {
                    return $model->feed_config_fetch_type === self::FETCH_TYPE_API;
                },
            ],
            [['feed_config_ignore_first_row', 'feed_config_ignore_end_row'], 'integer'],
            [
                ['feed_config_ignore_first_row', 'feed_config_ignore_end_row'],
                'required',
                'when' => function ($model) {
                    return in_array($model->feed_config_data_format_type, [
                        self::DATA_FORMAT_EXCEL_XLSX,
                        self::DATA_FORMAT_EXCEL_XLS,
                        self::DATA_FORMAT_TXT,
                        self::DATA_FORMAT_CSV,
                    ], true);
                },
            ],
            [
                ['feed_config_file_name'],
                'required',
                'when' => function ($model) {
                    return in_array($model->feed_config_fetch_type, [
                        self::FETCH_TYPE_FILE,
                        self::FETCH_TYPE_FTP,
                        self::FETCH_TYPE_SFTP,
                    ], true);
                },
            ],
            [['feed_config_create_at', 'feed_config_update_at', 'feed_config_extra_show_column'], 'safe'],
            [['feed_config_statement_code'], 'string', 'max' => 32],
            [['feed_config_fetch_type', 'feed_config_data_format_type'], 'string', 'max' => 32],
            [['feed_config_system_code'], 'string', 'max' => 32],
            [
                'feed_config_system_code',
                function ($attribute, $params) {
                    $check_res = self::find()->where([
                        'and',
                        ['!=', 'feed_config_seq', $this->feed_config_seq],
                        ['feed_config_statement_code' => $this->feed_config_statement_code],
                        ['feed_config_system_code' => $this->feed_config_system_code],
                    ])->exists();
                    if ($check_res) {
                        $this->addError($attribute, '系统code已存在');

                        return false;
                    }

                    return true;
                },
            ],
            [
                'feed_config_user_name',
                'filter',
                'filter' => function ($value) {
                    if ($this->beforeUserFilter()) {
                        return base64_encode(trim((string)$value));
                    }

                    return '';
                },
            ],
            [
                'feed_config_password',
                'filter',
                'filter' => function ($value) {
                    if ($this->beforeUserFilter()) {
                        return base64_encode(trim((string)$value));
                    }

                    return '';
                },
            ],
            ['feed_config_callback_url', 'url'],
            ['feed_config_callback_method', 'in', 'range' => [self::CALLBACK_GET, self::CALLBACK_POST]],
            [
                ['feed_config_system_name', 'feed_config_request_method', 'feed_config_trigger_time'],
                'string',
                'max' => 50,
            ],
            [
                ['feed_config_primary_key', 'feed_config_user_name', 'feed_config_password', 'feed_config_secret_key'],
                'string',
                'max' => 255,
            ],
            [['feed_config_driver_class', 'feed_config_path_to_data'], 'string', 'max' => 60],
            [
                ['feed_config_ignore_first_row', 'feed_config_ignore_end_row'],
                'default',
                'value' => self::FILE_READ_ROW_0,
            ],
            [
                ['feed_config_fetch_type'],
                'default',
                'value' => self::FETCH_TYPE_SQL,
            ],
            [
                ['feed_config_data_format_type',],
                'default',
                'value' => self::DATA_FORMAT_JSON,
            ],
            [
                ['feed_config_callback_method',],
                'default',
                'value' => self::CALLBACK_POST,
            ],
            [['feed_config_file_split_char'], 'string', 'max' => 16],
            [
                'feed_config_data_format_type',
                'required',
                'when' => function ($model) {
                    return $model->feed_config_fetch_type == self::FETCH_TYPE_FILE;
                },
                'whenClient' => "function (attribute, value) {
                    return $('#feed_config_fetch_type_1').val()=='FILE'
            }",
            ],
//            [
//                'feed_config_path_to_data',
//                'required',
//                'when'       => function ($model) {
//                    return $this->feed_config_fetch_type === self::FETCH_TYPE_URL_FILE
//                        || ($this->feed_config_fetch_type === self::FETCH_TYPE_API
//                            && in_array(
//                                $this->feed_config_data_format_type,
//                                [self::DATA_FORMAT_JSON, self::DATA_FORMAT_WHOLE_JSON_FILE]
//                            ));
//                },
//                'whenClient' => "function (attribute, value) {
//                  let fetch_type = $('#feed_config_fetch_type').val();
//                 let format_type=$('#feed_config_data_format_type').val();
//                 if(fetch_type=='URL_FILE' || (fetch_type=='API' && (format_type=='JSON' || format_type == 'WHOLE_JSON_FILE' ))){
//                    return true;
//                 } else{
//                    return false;
//                 }
//            }",
//            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'feed_config_id' => 'ID',
            'feed_config_statement_code' => '对账 Code',
            'feed_config_fetch_type' => '抓取方式',
            'feed_config_system_code' => '系统 Code',
            'feed_config_system_name' => '系统名称',
            'feed_config_data_format_type' => '数据格式',
            'feed_config_primary_key' => '业务主键',
            'feed_config_is_desensitization' => '是否脱敏',
            'feed_config_user_name' => '用户名',
            'feed_config_password' => '用户密码',
            'feed_config_driver_class' => '驱动类型',
            'feed_config_url' => 'URL',
            'feed_config_sql' => '执行 SQL',
            'feed_config_request_method' => '请求方式',
            'feed_config_path_to_data' => '数据节点',
            'feed_config_request_params' => '请求参数',
            'feed_config_trigger_time' => '抓取时间',
            'feed_config_seq' => '数据源顺序',
            'feed_config_feed_file_type' => '文件类型',
            'feed_config_file_name' => '文件名称',
            'feed_config_ignore_first_row' => '跳过开始行数',
            'feed_config_ignore_end_row' => '跳过结束行数',
            'feed_config_create_at' => '创建时间',
            'feed_config_update_at' => '更新时间',
            'feed_config_file_split_char' => '文件分隔符',
            'feed_config_secret_key' => '秘钥',
            'feed_config_db_name' => '数据库',
            'feed_config_extra_show_column' => '额外显示列',
            'feed_config_callback_url' => '回调地址',
            'feed_config_callback_method' => '回调请求方式',
            'feed_config_file_skip_char' => '过滤指定值',
            ['feed_config_callback_method', 'default', 'value' => self::CALLBACK_POST],
        ];
    }


    public function getStatementConfig()
    {
        return $this->hasOne(StatementConfig::class, ['statement_config_code' => 'feed_config_statement_code']);
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => [
                        'feed_config_create_at',
                        'feed_config_update_at',
                    ],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['feed_config_update_at'],
                ],
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    private function beforeUserFilter()
    {
        return in_array($this->feed_config_fetch_type, [
            self::FETCH_TYPE_API,
            self::FETCH_TYPE_FTP,
            self::FETCH_TYPE_SFTP,
        ]);
    }

    public static function checkSourceByCode($code, $seq)
    {
        return self::find()->where(['feed_config_statement_code' => $code, 'feed_config_seq' => $seq])->exists();
    }

    public static function getFeedIdByCode($code, $seq)
    {
        return self::find()
            ->where(['feed_config_statement_code' => $code, 'feed_config_seq' => $seq])
            ->select('feed_config_id')
            ->scalar();
    }

    public static function getStatementCodeByFeedId($FeedConfigId)
    {
        return self::find()->where(['feed_config_id' => $FeedConfigId])->select('feed_config_statement_code')->scalar();
    }

    public function getFeedConfigUserName()
    {
        return base64_decode((string)$this->feed_config_user_name);
    }

    public function getFeedConfigPassword()
    {
        return base64_decode((string)$this->feed_config_password);
    }

    public static function getUseDbNames()
    {
        return self::find()->select('feed_config_db_name')->groupBy('feed_config_db_name')->column();
    }

    public function beforeSave($insert)
    {
        $this->statementConfig->statement_config_updator = Yii::$app->user->identity->getId();
        $this->statementConfig->update();

        return parent::beforeSave($insert);
    }


    public static function getDbSource(): array
    {
        return KeyValue::take('db_source_config', 'statement');
    }
}
