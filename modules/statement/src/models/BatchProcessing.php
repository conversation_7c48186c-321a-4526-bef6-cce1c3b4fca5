<?php

namespace Codingheping\StatementComponent\models;

use yii\base\Model;

class BatchProcessing extends Model
{
    /**
     * 问题类型
     */
    public $troubleType;

    /**
     * 是否需要处理
     */
    public $isNeedSolve;

    /**
     * 处理方案
     */
    public $result;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['troubleType', 'result', 'isNeedSolve'], 'required'],
            [['troubleType', 'isNeedSolve', 'result'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'troubleType' => '问题类型',
            'result' => '处理方案',
            'isNeedSolve' => '是否需要处理',
        ];
    }
}
