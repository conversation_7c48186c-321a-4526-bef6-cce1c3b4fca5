<?php

namespace Codingheping\StatementComponent\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;

class NotifyConfigSearch extends NotifyConfig
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['notify_config_id'], 'integer'],
            [
                [
                    'notify_config_statement_code',
                    'notify_config_need_notify',
                    'notify_config_message_type',
                    'notify_config_message_theme',
                    'notify_config_notify_obj',
                    'notify_config_data',
                    'notify_config_create_at',
                    'notify_config_update_at',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = NotifyConfig::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }
        $query->joinWith('notifyGroupConfig');
        // grid filtering conditions
        $query->andFilterWhere([
            'notify_config_id' => $this->notify_config_id,
            'notify_config_create_at' => $this->notify_config_create_at,
            'notify_config_update_at' => $this->notify_config_update_at,
        ]);

        $query->andFilterWhere(['=', 'notify_config_statement_code', $this->notify_config_statement_code])
            ->andFilterWhere(['like', 'notify_config_need_notify', $this->notify_config_need_notify])
            ->andFilterWhere(['like', 'notify_config_message_type', $this->notify_config_message_type])
            ->andFilterWhere(['like', 'notify_config_message_theme', $this->notify_config_message_theme])
            ->andFilterWhere(['like', 'notify_config_notify_obj', $this->notify_config_notify_obj])
            ->andFilterWhere(['like', 'notify_config_data', $this->notify_config_data]);

        $sql = $query->createCommand()->getRawSql();

        return $dataProvider;
    }
}
