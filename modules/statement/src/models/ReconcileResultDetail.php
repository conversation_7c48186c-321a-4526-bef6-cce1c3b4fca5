<?php

namespace Codingheping\StatementComponent\models;

use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "reconcile_result_detail".
 *
 * @property int $id                                         主键
 * @property string $reconcile_result_detail_batch_no           批次号
 * @property string $reconcile_result_detail_primary_key_value  业务主键值
 * @property string $reconcile_result_detail_main_system_code   主系统code
 * @property string $reconcile_result_detail_main_feed_content  主系统数据明细
 * @property string $reconcile_result_detail_other_system_code  次系统code
 * @property string $reconcile_result_detail_other_feed_content 次系统数据明细
 * @property string $reconcile_result_detail_result_status      对账结果：success 成功  fail 失败
 * @property string $reconcile_result_detail_fail_cause         失败原因
 * @property string $reconcile_result_detail_create_at          创建时间
 * @property string $reconcile_result_detail_update_at          更新时间
 * @property int $reconcile_result_detail_version            版本号
 * @property int $reconcile_result_detail_processing_status  处理结果
 * @property StatementBatch $statementBatch
 * @property StatementMistakeSolution $statementMistakeSolution
 */
class ReconcileResultDetail extends ActiveRecord
{
    public const PROCESSING_STATUS_WAIT = 0;        //待处理
    public const PROCESSING_STATUS_END = 1;         //已处理

    public static $processing_status_maps = [
        self::PROCESSING_STATUS_WAIT => '待处理',
        self::PROCESSING_STATUS_END => '已处理',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'reconcile_result_detail';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['reconcile_result_detail_batch_no'], 'required'],
            [['reconcile_result_detail_main_feed_content', 'reconcile_result_detail_other_feed_content'], 'string'],
            [['reconcile_result_detail_create_at', 'reconcile_result_detail_update_at'], 'safe'],
            [['reconcile_result_detail_version', 'reconcile_result_detail_processing_status'], 'integer'],
            [
                [
                    'reconcile_result_detail_batch_no',
                    'reconcile_result_detail_primary_key_value',
                    'reconcile_result_detail_main_system_code',
                    'reconcile_result_detail_other_system_code',
                ],
                'string',
                'max' => 64,
            ],
            [['reconcile_result_detail_result_status'], 'string', 'max' => 32],
            [['reconcile_result_detail_fail_cause'], 'string', 'max' => 256],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'reconcile_result_detail_batch_no' => '批次号',
            'reconcile_result_detail_primary_key_value' => '业务主键值',
            'reconcile_result_detail_main_system_code' => '来源系统',
            'reconcile_result_detail_main_feed_content' => '主系统数据明细',
            'reconcile_result_detail_other_system_code' => '目标系统',
            'reconcile_result_detail_other_feed_content' => '次系统数据明细',
            'reconcile_result_detail_result_status' => '对账结果',
            'reconcile_result_detail_fail_cause' => '失败原因',
            'reconcile_result_detail_create_at' => '创建时间',
            'reconcile_result_detail_update_at' => '更新时间',
            'reconcile_result_detail_version' => '版本号',
            'reconcile_result_detail_processing_status' => '处理结果',
        ];
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('dbStatement');
    }

    /**
     * @return ActiveQuery
     */
    public function getStatementBatch()
    {
        return $this->hasOne(StatementBatch::class, ['statement_batch_no' => 'reconcile_result_detail_batch_no']);
    }

    /**
     * @return ActiveQuery
     */
    public function getReconcileResult()
    {
        return $this->hasOne(
            ReconcileResult::class,
            ['reconcile_result_batch_no' => 'reconcile_result_detail_batch_no']
        );
    }

    /**
     * @return ActiveQuery
     */
    public function getStatementMistakeSolution()
    {
        return $this->hasMany(
            StatementMistakeSolution::class,
            ['statement_mistake_solution_reconcile_result_detail_id' => 'id']
        );
    }

    /**
     * 获取格式化后的值
     *
     * @return string
     */
    public function getFormattedValue($param)
    {
        $data = json_decode($this->$param, true);
        if ($data === null) {
            return "'" . $this->$param . "'";
        }

        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
}
