<?php

namespace Codingheping\StatementComponent\controllers;

use Codingheping\StatementComponent\models\FeedConfig;
use Codingheping\StatementComponent\models\FeedConfigSearch;
use Codingheping\StatementComponent\models\StatementConfig;
use Exception;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class FeedConfigController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function actionIndex(): string
    {
        $searchModel = new FeedConfigSearch();
        $dataProvider = $searchModel->search($this->request->getQueryParams());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single FeedConfig model.
     *
     * @param int $id
     *
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView(int $id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the FeedConfig model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return FeedConfig the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): FeedConfig
    {
        if (($model = FeedConfig::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * @return string|Response
     */
    public function actionCreate()
    {
        $model = new FeedConfig();
        $code = $this->request->getQueryParam('statement_config_code');
        $index = (int)$this->request->getQueryParam('index', 0);
        if ($this->request->isPost) {
            $post = $this->request->post();
            $onlySave = $post['only_save'] ?? null;
            if ($model->load($post) && $model->save()) {
                //仅保存
                if ($onlySave) {
                    $url = 'feed-config/update';
                } elseif ($index === 4) {
                    $url = 'feed-config/update';
                } elseif ($index === 5) {
                    $url = 'notify-config/index';
                } else {
                    $url = 'view';
                }
                Yii::$app->session->setFlash('success', sprintf('数据源%s配置成功!', ($index - 1)));

                return $this->redirect([
                    $url,
                    'statement_config_code' => $code,
                    'index' => $onlySave ? $index : $index + 1,
                ]);
            }
        }
        $model->feed_config_statement_code = $code;
        $model->feed_config_seq = $index - 1;
        $model->feed_config_fetch_type = FeedConfig::FETCH_TYPE_SQL;
        $model->feed_config_data_format_type = FeedConfig::DATA_FORMAT_JSON;
        $model->feed_config_ignore_first_row = FeedConfig::FILE_READ_ROW_0;
        $model->feed_config_ignore_end_row = FeedConfig::FILE_READ_ROW_0;
        $model->feed_config_callback_method = FeedConfig::CALLBACK_POST;

        if ($model->hasErrors()) {
            Yii::$app->session->setFlash('error', json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
        }

        return $this->render('create', [
            'model' => $model,
            'tabParams' => StatementConfig::getTabsViewParams($code, 'feed_config', $index),
        ]);
    }

    /**
     * @return string|Response
     */
    public function actionUpdate()
    {
        $code = $this->request->getQueryParam('statement_config_code');
        $index = (int)$this->request->getQueryParam('index', 0);
        $model = FeedConfig::find()
            ->where([
                'feed_config_statement_code' => $code,
                'feed_config_seq' => $index - 2,
            ])
            ->one();
        if ($this->request->isPost) {
            $post = $this->request->post();
            $onlySave = $post['only_save'] ?? null;
            try {
                if ($model->load($post) && $model->save()) {
                    if ($onlySave) {
                        $url = 'feed-config/update';
                    } elseif ($index === 3) {
                        if (FeedConfig::checkSourceByCode($code, 2)) {
                            $url = 'feed-config/update';
                        } else {
                            $url = 'feed-config/create';
                        }
                    } elseif ($index === 4) {
                        $url = 'statement-config/update-count';
                    } else {
                        $url = 'view';
                    }
                    Yii::$app->session->setFlash('success', sprintf('数据源%s配置成功!', ($index - 1)));

                    return $this->redirect([
                        $url,
                        'statement_config_code' => $code,
                        'index' => $onlySave ? $index : $index + 1,
                    ]);
                }

                if ($model->hasErrors()) {
                    Yii::$app->session->setFlash(
                        'error',
                        json_encode($model->getErrors(), JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE)
                    );
                }
            } catch (Exception $exception) {
                Yii::$app->session->setFlash('error', $exception->getMessage());
            }
        }

        return $this->render('update', [
            'model' => $model,
            'tabParams' => StatementConfig::getTabsViewParams($code, 'feed_config', $index),
        ]);
    }

    /**
     * Deletes an existing FeedConfig model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id
     *
     * @return Response
     * @throws NotFoundHttpException if the model cannot be found
     * @throws StaleObjectException
     */
    public function actionDelete(int $id): Response
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }
}
