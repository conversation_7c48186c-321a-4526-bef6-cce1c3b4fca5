<?php

namespace Codingheping\StatementComponent\controllers;

use Carbon\Carbon;
use Codingheping\StatementComponent\component\StatementGateComponent;
use Codingheping\StatementComponent\models\CopyConfigFrom;
use Codingheping\StatementComponent\models\FeedConfig;
use Codingheping\StatementComponent\models\NotifyConfigSearch;
use Codingheping\StatementComponent\models\StatementConfig;
use Codingheping\StatementComponent\models\StatementConfigSearch;
use Codingheping\StatementComponent\models\TransformDef;
use Exception;
use Yii;
use yii\base\UserException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class StatementConfigController extends Controller
{
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actionIndex(): string
    {
        $searchModel = new StatementConfigSearch();
        $queryParams = Yii::$app->request->queryParams;
        if (!isset($queryParams['StatementConfigSearch']['status'])) {
            $queryParams['StatementConfigSearch']['status'] = $searchModel::STATEMENT_CONFIG_STATUS_VALID;
        }

        $dataProvider = $searchModel->search($queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate()
    {
        $model = new StatementConfig();
        $tabIndex = $this->request->getQueryParam('index', 1);
        if ($this->request->isPost) {
            $model->scenario = $model::SCENARIO_CREATE;
            if ($model->load($this->request->post()) && $model->save()) {
                Yii::$app->session->setFlash('success', '对账配置已保存！');

                return $this->redirect([
                    (int)$tabIndex === 1 ? 'transform-out-column/index' : 'create',
                    'statement_config_code' => $model->statement_config_code,
                    'index' => 2,
                ]);
            }

            Yii::$app->session->setFlash('error', '保存失败');
        }

        return $this->render('create', [
            'model' => $model,
            'tabParams' => StatementConfig::getTabsViewParams('', 'statement_config', 1, true),
        ]);
    }

    public function actionUpdateCount()
    {
        $code = $this->request->getQueryParam('statement_config_code');
        $index = (int)$this->request->getQueryParam('index', 0);
        /** @var StatementConfig $model */
        $model = StatementConfig::find()->where([
            'statement_config_code' => $code,
            'statement_config_conf' => '{}',
        ])->one();
        if ($this->request->isPost) {
            $post = $this->request->post();
            $model->scenario = $model::SCENARIO_COUNT_CONFIG;
            if ($model->load($post) && $model->save()) {
                Yii::$app->getSession()->setFlash('success', '对账汇总信息配置已保存！');

                return $this->redirect([
                    $index === 5 ? 'notify-config/index' : 'update-count',
                    'statement_config_code' => $model->statement_config_code,
                    'index' => 6,
                ]);
            }
        }
        !$model->status && $model->status = StatementConfig::STATEMENT_CONFIG_STATUS_VALID;

        return $this->render('update-count', [
            'model' => $model,
            'transFormOoutputTypes' => $code ? TransformDef::getTranDefsByStatementCode($code) : [],
            'tabParams' => StatementConfig::getTabsViewParams($code, 'update-count', 5),
        ]);
    }


    public function actionCopyConfig()
    {
        $copyConfig = new CopyConfigFrom();
        if (Yii::$app->request->isPost) {
            try {
                $post = Yii::$app->request->post();
                if ($copyConfig->load($post)) {
                    if ($copyConfig->validate()) {
                        $response = $copyConfig->copyConfig();
                        Yii::$app->session->setFlash('success', '复制对账配置成功！');

                        return $this->redirect([
                            'update',
                            'id' => $response['id'],
                            'index' => 1,
                            'statement_config_code' => $post['CopyConfigFrom']['statement_code'],
                        ]);
                    }

                    throw new UserException(json_encode($copyConfig->getErrors(), JSON_UNESCAPED_UNICODE));
                }

                throw new UserException('复制对账配置数据不合法！');
            } catch (Exception $e) {
                Yii::$app->session->setFlash('error', '复制失败：' . $e->getMessage());

                return $this->redirect($this->request->getReferrer());
            }
        }

        return $this->renderAjax('_copy_form', [
            'model' => $copyConfig,
        ]);
    }


    public function actionUpdate()
    {
        $code = $this->request->getQueryParam('statement_config_code');
        $index = (int)$this->request->getQueryParam('index', 0);
        /** @var StatementConfig $model */
        $model = StatementConfig::find()->where([
            'statement_config_code' => $code,
            'statement_config_conf' => '{}',
        ])->one();
        if ($this->request->isPost) {
            $model->scenario = $model::SCENARIO_UPDATE;
            if ($model->load($this->request->post())) {
                if (!$model->save()) {
                    Yii::$app->session->setFlash('error', json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
                } else {
                    $uri = 'view';
                    if ($index === 1) {
                        $uri = 'transform-out-column/index';
                    }
                    Yii::$app->getSession()->setFlash('success', '对账配置已保存！');

                    return $this->redirect([
                        $uri,
                        'statement_config_code' => $model->statement_config_code,
                        'index' => 2,
                    ]);
                }
            }
        }

        return $this->render('update', [
            'model' => $model,
            'tabParams' => StatementConfig::getTabsViewParams($code, 'statement_config', 1),
        ]);
    }

    /**
     * @param $id
     *
     * @return Response
     */
    public function actionDelete($id): Response
    {
        /** @var StatementConfig $model */
        $model = StatementConfig::findOne($id);
        $model->status = $model->status === StatementConfig::STATEMENT_CONFIG_STATUS_VALID ?
            StatementConfig::STATEMENT_CONFIG_STATUS_INVALID : StatementConfig::STATEMENT_CONFIG_STATUS_VALID;
        $model->save();

        return $this->redirect(['index']);
    }

    /**
     * Finds the StatementConfig model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int   $id
     * @param array $condition
     *
     * @return StatementConfig the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id, $condition = []): StatementConfig
    {
        $condition['id'] = $id;

        if (($model = StatementConfig::findOne($condition)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }


    public function actionCreateTask($id): string
    {
        $model = $this->findModel($id);
        $model->date = Carbon::now()->subDay()->toDateString();
        $model->statement_config_conf = json_decode($model->statement_config_conf);
        if ($this->request->isPost) {
            $post = $this->request->post('StatementConfig');
            try {
                $date = $post['date'] ?? Carbon::now()->toDateString();
                if (Carbon::parse($date)->toDateString() > Carbon::now()->toDateString()) {
                    throw new UserException('日期只能选择小于或等于今天的日期!');
                }
                $statementPlatformConfig = StatementConfig::getConfigMappings();
                $type = $model->statement_config_code;
                if (!array_key_exists($type, $statementPlatformConfig)) {
                    throw new UserException("不存在该类型'' . $type . ''配置");
                }
                $arr = ['statement_config_code', 'date'];
                $keys = array_keys($post);
                $dynamicParamsKeys = array_diff($keys, $arr);
                $maps = [];
                foreach ($dynamicParamsKeys as $key) {
                    $maps[$key] = $post[$key];
                }
                $data['statement_config_code'] = $post['statement_config_code'];
                $data['statement_date'] = $post['date'];
                $data['dynamic_params'] = $maps;
                $data['operator'] = Yii::$app->user->identity->getId();
                $client = StatementGateComponent::instance();
                if (!$client->createTask($data) && $client->getHasError()) {
                    throw new UserException($client->getError());
                }
                Yii::$app->session->setFlash('success', '创建成功');
                $this->redirect(['index'])->send();
            } catch (Exception $exception) {
                Yii::$app->session->setFlash('error', '创建任务失败，请重试，原因：' . $exception->getMessage());
            }
        }

        return $this->render('create_task', [
            'model' => $model,
        ]);
    }

    public function actionRedoReconciliation()
    {
        $params = Yii::$app->request->getQueryParams();

        try {
            if ($params['originBatchNo'] && $params['statementConfigCode']) {
                $data = [
                    'origin_batch_no' => $params['originBatchNo'],
                    'statement_config_code' => $params['statementConfigCode'],
                    'operator' => Yii::$app->user->identity->getId(),
                ];
                $client = StatementGateComponent::instance();
                if (!$client->retryStatement($data) && $client->getHasError()) {
                    throw new UserException($client->getError());
                }
                Yii::$app->session->setFlash('success', '重新对账任务创建成功！');
            } else {
                throw new UserException('参数错误，重新对账任务创建失败！');
            }
        } catch (Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        } finally {
            $this->redirect($this->request->getReferrer());
        }
    }

    public function actionView($id): string
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'StatementModel' => $model,
            'FeedConfig1Model' => FeedConfig::findOne([
                'feed_config_statement_code' => $model->statement_config_code,
                'feed_config_seq' => FeedConfig::DATA_SOURCE_SEQ_1,
            ]),
            'FeedConfig2Model' => FeedConfig::findOne([
                'feed_config_statement_code' => $model->statement_config_code,
                'feed_config_seq' => FeedConfig::DATA_SOURCE_SEQ_2,
            ]),
            'NotifyConfigModel' => (new NotifyConfigSearch())->search([
                'NotifyConfigSearch' => [
                    'notify_config_statement_code' => $model->statement_config_code,
                ],
            ]),
            'statementCode' => $model->statement_config_code,
            'initParams' => StatementConfig::getTabsViewParams($model->statement_config_code),
        ]);
    }
}
