<?php

namespace Codingheping\StatementComponent\controllers;

use Codingheping\StatementComponent\models\NotifyConfig;
use Codingheping\StatementComponent\models\NotifyConfigSearch;
use Codingheping\StatementComponent\models\StatementConfig;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class NotifyConfigController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function actionIndex(): string
    {
        $params = $this->request->getQueryParams();
        $code = $params['statement_config_code'];
        $params['NotifyConfigSearch']['notify_config_statement_code'] = $code;
        $index = $params['index'];
        $search = new NotifyConfigSearch();
        $dataProvider = $search->search($params);

        return $this->render('/common/_common', [
            'searchModel' => $search,
            'model' => $dataProvider,
            'tabParams' => StatementConfig::getTabsViewParams($code, 'notify_config', $index),
        ]);
    }


    public function actionUpdate($id): string
    {
        $model = $this->findModel($id);
        if ($model->notify_config_use_group === NotifyConfig::NOTIFY_GROUP_NO) {
            $model->notify_config_group_id = 0;
        }
        if ($this->request->isPost) {
            $model->load(Yii::$app->request->post());
            if ($model->save()) {
                return Html::tag('script', 'var index = parent.layer.getFrameIndex(window.name); 
            parent.layer.close(index); ');
            }
        }
        !$model->notify_config_use_group && $model->notify_config_use_group = NotifyConfig::NOTIFY_GROUP_NO;

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    public function actionCreate(): string
    {
        $code = Yii::$app->request->getQueryParam('statement_config_code');
        $model = new NotifyConfig();
        if ($this->request->isPost) {
            $model->load(Yii::$app->request->post());
            if ($model->save()) {
                return Html::tag('script', 'var index = parent.layer.getFrameIndex(window.name); 
            parent.layer.close(index); ');
            }
        } else {
            $model->notify_config_statement_code = $code;
            $model->notify_config_need_notify = NotifyConfig::MESSAGE_NEED_YES;
            $model->notify_config_message_type = NotifyConfig::MESSAGE_TYPE_MQ;
            $model->notify_config_use_group = NotifyConfig::NOTIFY_GROUP_NO;
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing NotifyConfig model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id
     *
     * @return Response
     * @throws NotFoundHttpException if the model cannot be found
     * @throws StaleObjectException
     */
    public function actionDelete(int $id): Response
    {
        $this->findModel($id)->delete();

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * @param $id
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id): string
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the NotifyConfig model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return NotifyConfig the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): NotifyConfig
    {
        if (($model = NotifyConfig::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
