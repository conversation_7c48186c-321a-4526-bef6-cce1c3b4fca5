<?php

namespace Codingheping\StatementComponent\controllers;

use Carbon\Carbon;
use Codingheping\StatementComponent\models\FeedOriginalSearch;
use Codingheping\StatementComponent\models\StatementBatch;
use kartik\depdrop\DepDropAction;
use Yii;
use yii\filters\VerbFilter;
use yii\web\Controller;

/**
 * FeedOriginalController implements the CRUD actions for FeedOriginal model.
 */
class FeedOriginalController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actions(): array
    {
        return [
            'dep-drop-list' => [
                'class' => DepDropAction::class,
                'outputCallback' => function ($code) {
                    return StatementBatch::getBatchNoMap($code, Carbon::now()->subMonths(30)->toDateString());
                },
                'selectedCallback' => function () {
                    return Yii::$app->request->get('batchNo');
                },
            ],
        ];
    }


    /**
     * Lists all FeedOriginal models.
     */
    public function actionIndex(): string
    {
        $searchModel = new FeedOriginalSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->getQueryParams());

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
}
