<?php

namespace Codingheping\StatementComponent\controllers;

use Codingheping\StatementComponent\models\StatementConfig;
use Codingheping\StatementComponent\models\TransformDef;
use Codingheping\StatementComponent\models\TransformOutColumn;
use Codingheping\StatementComponent\models\TransformOutColumnSearch;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class TransformOutColumnController extends Controller
{
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actionIndex()
    {
        $code = $this->request->getQueryParam('statement_config_code');
        $index = $this->request->getQueryParam('index');
        $searchModel = new TransformOutColumnSearch();
        $queryParams = Yii::$app->request->queryParams;
        $queryParams['TransformOutColumnSearch']['transform_out_columns_statement_code'] = $code;
        $dataProvider = $searchModel->search($queryParams);

        return $this->render('/common/_common', [
            'searchModel' => $searchModel,
            'model' => $dataProvider,
            'tabParams' => StatementConfig::getTabsViewParams($code, 'transform', $index),
        ]);
    }

    /**
     * Creates a new TransformOutColumn model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return Response|string
     */
    public function actionCreate()
    {
        $model = new TransformOutColumn();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->transform_out_columns_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * Deletes an existing TransformOutColumn model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @return array
     * @throws StaleObjectException
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $result = ['code' => 0, 'msg' => 'ok'];
        $id = Yii::$app->request->post('id');
        if (TransformDef::find()->where(['transform_def_transform_out_columns_id' => $id])->exists()) {
            $result['code'] = -1;
            $result['msg'] = '该对账列已经在使用中，不可删除！';
        } else {
            $model = $this->findModel($id);
            if ($model) {
                $model->delete();
                $result['data']['id'] = $id;
            } else {
                $result['code'] = -1;
                $result['msg'] = '操作数据不存在！';
            }
        }

        return $result;
    }

    /**
     * Finds the TransformOutColumn model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return TransformOutColumn the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): TransformOutColumn
    {
        if (($model = TransformOutColumn::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * @return array
     * @throws NotFoundHttpException
     */
    public function actionSave(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $params = $this->request->post();
        $result = ['code' => 0, 'msg' => 'ok'];
        if ($params['transform_out_columns_id']) {
            $model = $this->findModel($params['transform_out_columns_id']);
        } else {
            $model = new TransformOutColumn();
        }
        try {
            if ($model->load($params, '') && $model->validate()) {
                Yii::$app->db->transaction(function () use ($model, $params, &$result) {
                    if ($model->transform_out_columns_output_key
                        !== $model->getOldAttribute('transform_out_columns_output_key')
                    ) {
                        StatementConfig::updateCountKey(
                            $model->transform_out_columns_statement_code,
                            $model->transform_out_columns_output_key,
                            $model->getOldAttribute('transform_out_columns_output_key')
                        );
                    }
                    if (!$model->save()) {
                        throw new UserException(json_encode($model->getErrors()));
                    }
                    if ($params['transform_out_columns_id']) {
                        TransformDef::updateAll([
                            'transform_def_output_key' => $model->transform_out_columns_output_key,
                            'transform_def_output_type' => $model->transform_out_columns_output_type,
                            'transform_def_is_business_key' => $model->transform_out_columns_is_business_key,
                            'transform_def_business_index' => $model->transform_out_columns_business_index,
                        ], ['transform_def_transform_out_columns_id' => $params['transform_out_columns_id']]);
                    }

                    $result['data']['transform_out_columns_id'] = $model->transform_out_columns_id;
                });
            } else {
                throw new UserException(json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
            }
        } catch (Throwable $throwable) {
            $result['code'] = -1;
            $result['msg'] = $throwable->getMessage();
        }

        return $result;
    }
}
