<?php /** @noinspection JsonEncodingApiUsageInspection */

namespace Codingheping\StatementComponent\controllers;

use Codingheping\StatementComponent\component\StatementGateComponent;
use Codingheping\StatementComponent\models\BatchProcessing;
use Codingheping\StatementComponent\models\ReconcileResult;
use Codingheping\StatementComponent\models\ReconcileResultDetail;
use Codingheping\StatementComponent\models\ReconcileResultDetailSearch;
use Codingheping\StatementComponent\models\StatementBatchSearch;
use Codingheping\StatementComponent\models\StatementConfig;
use Codingheping\StatementComponent\models\StatementMistakeSolution;
use Codingheping\StatementComponent\models\TroubleTypes;
use Exception;
use Throwable;
use Yii;
use yii\base\UserException;
use yii\data\ActiveDataProvider;
use yii\db\Transaction;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use function xlerr\adminlte\userFullName;

class StatementBatchController extends Controller
{
    public function actionIndex(): string
    {
        $params = $this->request->getQueryParams();
        $searchModel = new StatementBatchSearch();
        $dataProvider = $searchModel->search($params);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
            'searchModel' => $searchModel,
        ]);
    }

    /**
     * @param $id
     * @return string
     * @throws NotFoundHttpException
     */
    public function actionView($id): string
    {
        /* @var $model ReconcileResultDetail */
        $model = ReconcileResultDetail::find()
            ->with([
                'statementBatch' => function ($query) {
                    $query->with(['statementConfig', 'task']);
                },
            ])
            ->where(['id' => $id])
            ->one();
        if (!$model) {
            throw new NotFoundHttpException('请求内容不存在！');
        }

        return $this->render('view', [
            'model' => $model,
            'solutionDataProvider' => new ActiveDataProvider([
                'query' => $model->getStatementMistakeSolution()->select([
                    'statement_mistake_solution_id',
                    'statement_mistake_solution_trouble_type',
                    'statement_mistake_solution_result',
                    'statement_mistake_solution_user_name',
                    'statement_mistake_solution_create_at',
                ])->asArray(),
            ]),
        ]);
    }

    /**
     * @param $id
     *
     * @return ReconcileResultDetail|null
     * @throws NotFoundHttpException
     */
    protected function findDetailModel($id): ?ReconcileResultDetail
    {
        if (($model = ReconcileResultDetail::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionMistakeSolve($id): string
    {
        $trans = ReconcileResultDetail::getDb()->beginTransaction();
        try {
            /** @var $model ReconcileResultDetail* */
            $model = $this->findDetailModel($id);
            if (!$this->request->isPost) {
                throw new UserException('非法请求');
            }
            $params = $this->request->post('StatementMistakeSolution');
            $statementMistakeSolution = new StatementMistakeSolution();
            if (!$statementMistakeSolution->load($params, '')
                || !$statementMistakeSolution->validate([
                    'statement_mistake_solution_is_need_solve',
                    'statement_mistake_solution_trouble_type',
                    'StatementMistakeSolution[statement_mistake_solution_result]: 324234324',
                ])
            ) {
                return Json::encode([
                    'code' => 1,
                    'message' => Json::encode($statementMistakeSolution->getErrors(), JSON_UNESCAPED_UNICODE),
                ]);
            }

            $oldStatus = $model->reconcile_result_detail_processing_status;
            $model->reconcile_result_detail_processing_status = ReconcileResultDetail::PROCESSING_STATUS_END;
            if (!$model->save(false)) {
                throw new UserException('处理失败，原因：' . Json::encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            if ($model->reconcileResult->reconcile_result_untreated_count > 0
                && $oldStatus !== ReconcileResultDetail::PROCESSING_STATUS_END
            ) {
                ReconcileResult::updateAllCounters(
                    ['reconcile_result_untreated_count' => -1],
                    ['reconcile_result_batch_no' => $model->reconcile_result_detail_batch_no]
                );
            }

            //新增处理记录
            StatementMistakeSolution::import(array_merge($params, [
                'user_id' => Yii::$app->user->id,
                'user_name' => userFullName(),
                'id' => $id,
            ]));
            $trans->commit();
        } catch (Exception $exception) {
            $trans->rollBack();

            return Json::encode([
                'code' => 1,
                'message' => $exception->getMessage(),
            ]);
        }

        return Json::encode([
            'code' => 0,
            'message' => '处理成功！',
        ]);
    }

    public function actionMistakeIndex($type, $title, $code): string
    {
        $searchModel = new ReconcileResultDetailSearch();
        $searchModel->reconcile_result_detail_batch_no = $type;
        $dataProvider = $searchModel->search($this->request->getQueryParams());
        $statementConfigId =
            StatementConfig::find()->where(['statement_config_code' => $code])->select('id')->scalar();

        return $this->render('mistake_index', [
            'dataProvider' => $dataProvider,
            'searchModel' => $searchModel,
            'title' => $title,
            'code' => $code,
            'type' => $type,
            'statementConfigId' => $statementConfigId,
        ]);
    }


    public function actionBatchProcessing($ids, $code)
    {
        $model = new BatchProcessing();
        $troubleTypes = TroubleTypes::getTroubleType($code);
        $troubleTypes = array_combine($troubleTypes, $troubleTypes);
        if ($this->request->isPost) {
            $ids = explode(',', (string)$ids);
            $post = $this->request->post();
            if ($ids && $model->load($post)) {
                /** @var  $transaction  Transaction */
                $transaction = ReconcileResultDetail::getDb()->beginTransaction();
                try {
                    if (!$model->validate()) {
                        throw new UserException(Json::encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
                    }
                    ReconcileResultDetail::updateAll(
                        ['reconcile_result_detail_processing_status' => ReconcileResultDetail::PROCESSING_STATUS_END],
                        ['id' => $ids]
                    );
                    //修改状态
                    $detailModel = ReconcileResultDetail::find()->where(['id' => $ids])->asArray()->all();
                    $batchNos = array_column($detailModel, 'reconcile_result_detail_batch_no');
                    foreach ($batchNos as $batchNo) {
                        /** @var  $resultModel ReconcileResult */
                        $resultModel = ReconcileResult::findOne(['reconcile_result_batch_no' => $batchNo]);
                        if ($resultModel->reconcile_result_untreated_count > 0) {
                            ReconcileResult::updateAllCounters(
                                ['reconcile_result_untreated_count' => -1],
                                ['reconcile_result_batch_no' => $batchNo]
                            );
                        }
                    }

                    //新增处理记录
                    StatementMistakeSolution::batchAdd($model, $ids);
                    $transaction->commit();

                    return Html::tag(
                        'script',
                        'var index = parent.layer.getFrameIndex(window.name); parent.layer.close(index); '
                    );
                } catch (Exception $e) {
                    $transaction->rollBack();
                    Yii::$app->session->setFlash('error', '处理失败，原因：' . $e->getMessage());
                }

                return $this->redirect($this->request->getReferrer());
            }
        }

        return $this->render('_batch_processing', [
            'model' => $model,
            'troubleTypes' => $troubleTypes,
        ]);
    }


    public function actionBatchProcessAll($code, $batchNo)
    {
        $model = new BatchProcessing();
        $troubleTypes = TroubleTypes::getTroubleType($code);
        $troubleTypes = array_combine($troubleTypes, $troubleTypes);
        if ($this->request->isPost) {
            /** @var  $transaction Transaction */
            $transaction = ReconcileResultDetail::getDb()->beginTransaction();
            $post = $this->request->post();
            try {
                if ($model->load($post)) {
                    if (!$model->validate()) {
                        throw new UserException(Json::encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
                    }
                    $ids = ReconcileResultDetail::find()->where([
                        'reconcile_result_detail_batch_no' => $batchNo,
                        'reconcile_result_detail_processing_status' => ReconcileResultDetail::PROCESSING_STATUS_WAIT,
                        'reconcile_result_detail_result_status' => 'fail',
                    ])->select('id')->column();
                    if ($ids) {
                        ReconcileResultDetail::updateAll([
                            'reconcile_result_detail_processing_status' => ReconcileResultDetail::PROCESSING_STATUS_END,
                        ], [
                            'id' => $ids,
                        ]);
                        /** @var  $resultModel ReconcileResult */
                        $resultModel = ReconcileResult::findOne(['reconcile_result_batch_no' => $batchNo]);
                        if ($resultModel->reconcile_result_untreated_count > 0) {
                            $resultModel->updateCounters(['reconcile_result_untreated_count' => 0 - count($ids)]);
                        }
                        //新增处理记录
                        StatementMistakeSolution::batchAdd($model, $ids);
                        $transaction->commit();

                        return Html::tag(
                            'script',
                            'var index = parent.layer.getFrameIndex(window.name); parent.layer.close(index); '
                        );
                    }
                    Yii::$app->session->setFlash('error', '全量处理失败，原因：无对账结果需要处理');
                }
            } catch (Exception $exception) {
                $transaction->rollBack();
                Yii::$app->session->setFlash('error', '全量处理失败，原因：' . $exception->getMessage());
            }

            return $this->redirect($this->request->getReferrer());
        }

        return $this->render('_batch_processing', [
            'model' => $model,
            'troubleTypes' => $troubleTypes,
        ]);
    }


    public function actionReNotify()
    {
        try {
            $batchNo = Yii::$app->request->getQueryParam('batch_no');
            if (!$batchNo) {
                throw new UserException('批次号不合法！');
            }
            $client = StatementGateComponent::instance();
            $response = $client->notify($batchNo);
            if (!$response && $client->getHasError()) {
                throw new UserException($client->getError());
            }
            Yii::$app->session->setFlash('success', '通知对账结果成功！');
        } catch (Throwable $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        } finally {
            $this->redirect($this->request->getReferrer());
        }
    }
}
