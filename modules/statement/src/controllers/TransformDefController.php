<?php

namespace Codingheping\StatementComponent\controllers;

use Codingheping\StatementComponent\models\TransformDef;
use Codingheping\StatementComponent\models\TransformDefSearch;
use Codingheping\StatementComponent\models\TransformOutColumn;
use Exception;
use Yii;
use yii\base\UserException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class TransformDefController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all TransformDef models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new TransformDefSearch();
        $queryParams = $this->request->getQueryParams();
        $dataProvider = $searchModel->search($queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'feedcConfigId' => $queryParams['TransformDefSearch']['transform_def_feed_config_id'],
            'statementCode' => $queryParams['TransformDefSearch']['transform_def_statement_code'],
        ]);
    }

    /**
     * Finds the TransformDef model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return TransformDef the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): TransformDef
    {
        if (($model = TransformDef::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionSave(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $result = ['code' => 0, 'msg' => 'ok'];
        $post = $this->request->post();
        if ($post['transform_def_id']) {
            $model = $this->findModel($post['transform_def_id']);
        } else {
            $model = new TransformDef();
        }
        try {
            /**
             * @var $transformObj TransformOutColumn
             */
            $transformObj =
                TransformOutColumn::findOne($post['transform_def_transform_out_columns_id']);
            if (!$transformObj) {
                throw new UserException('参数错误!');
            }
            $post['transform_def_output_key'] = $transformObj->transform_out_columns_output_key;
            $post['transform_def_output_type'] = $transformObj->transform_out_columns_output_type;
            $post['transform_def_is_business_key'] = $transformObj->transform_out_columns_is_business_key;
            $post['transform_def_business_index'] = $transformObj->transform_out_columns_business_index;
            $model->setAttributes($post);
            if (!$model->save()) {
                throw new UserException(json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
            }
            $result['data']['transform_def_id'] = $model->transform_def_id;
        } catch (Exception $e) {
            $result['code'] = -1;
            $result['msg'] = $e->getMessage();
        }

        return $result;
    }


    public function actionDelete(): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $result = ['code' => 0, 'msg' => 'ok'];
        $id = $this->request->post('id');
        $model = $this->findModel($id);
        if ($model) {
            $model->delete();
            $result['data']['id'] = $id;
        } else {
            $result['code'] = -1;
            $result['msg'] = '操作数据不存在！';
        }

        return $result;
    }
}
