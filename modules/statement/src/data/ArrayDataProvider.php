<?php

namespace Codingheping\StatementComponent\data;

class ArrayDataProvider extends \yii\data\ArrayDataProvider
{
    protected function prepareModels(): array
    {
        if (($models = $this->allModels) === null) {
            return [];
        }

        if (($sort = $this->getSort()) !== false) {
            $models = $this->sortModels($models, $sort);
        }

        if (($pagination = $this->getPagination()) !== false) {
            $pagination->totalCount = $this->getTotalCount();
        }

        return $models;
    }

    protected function prepareTotalCount(): int
    {
        return $this->getTotalCount();
    }
}
