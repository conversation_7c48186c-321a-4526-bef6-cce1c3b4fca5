<?php

namespace Codingheping\StatementComponent;

use Codingheping\StatementComponent\models\StatementConfig;
use Codingheping\StatementComponent\models\UserGroup;
use Exception;
use Yii;
use yii\base\BootstrapInterface;
use yii\base\Event;
use yii\i18n\PhpMessageSource;
use yii\web\Application;
use yii\web\UrlRule;

class Module extends \yii\base\Module implements BootstrapInterface
{
    public $controllerNamespace = __NAMESPACE__ . '\\controllers';

    public $defaultRoute = 'statement-config';

    public const EVENT_INIT = 'init';

    /**
     * {@inheritdoc}
     */
    public function bootstrap($app): void
    {
        /** @var $app Application */
        $app->getUrlManager()->addRules([
            [
                'class' => UrlRule::class,
                'route' => $this->id . '/statement/<action>',
                'pattern' => $this->id . '/<action:[\w\-]+>',
            ],
        ], false);
    }

    public function init(): void
    {
        if (!Yii::$app->has('dbStatement')) {
            throw new Exception('未配置对账平台数据库信息');
        }
        if (!isset(Yii::$app->i18n->translations['statement'])) {
            Yii::$app->i18n->translations['statement'] = [
                'class' => PhpMessageSource::class,
                'forceTranslation' => true,
                'basePath' => '@Codingheping/StatementComponent/messages',
            ];
        }
        $event = new Event();
        $this->trigger(self::EVENT_INIT, $event);
        $this->loadUserGroup();
    }

    protected function loadUserGroup(): void
    {
        $config = StatementConfig::getKvConfigGroup();
        UserGroup::setAvailableGroup($config);
    }
}
