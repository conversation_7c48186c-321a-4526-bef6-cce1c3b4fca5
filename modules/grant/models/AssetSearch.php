<?php

namespace grant\models;

use Carbon\Carbon;
use payment\models\WithdrawReceipt;
use xlerr\desensitise\Desensitise;
use yii\base\InvalidConfigException;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\db\Expression;

use function xlerr\desensitise\encrypt;

/**
 * AssetSearch represents the model behind the search form about `grant\models\Asset`.
 */
class AssetSearch extends Asset
{
    public $startTimeOfCreatedAt;
    public $endTimeOfCreatedAt;
    public $startTimeOfActualGrantAt;
    public $endTimeOfActualGrantAt;
    public $mobile;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['startTimeOfCreatedAt', 'endTimeOfCreatedAt'], 'default', 'value' => Carbon::now()->toDateString()],
            [['asset_item_no', 'mobile'], 'trim'],
            [
                [
                    'asset_status',
                    'asset_loan_channel',
                    'startTimeOfActualGrantAt',
                    'endTimeOfActualGrantAt',
                    'pCategory',
                    'asset_period_count',
                    'asset_sub_type',
                ],
                'safe',
            ],
        ];
    }

    /**
     * @return string
     */
    public function getPCategory()
    {
        if ($this->asset_period_count && $this->asset_product_category) {
            return vsprintf('%s.%s', [
                $this->asset_period_count,
                $this->asset_product_category,
            ]);
        }

        return '';
    }

    /**
     * @param string $val
     */
    public function setPCategory(string $val)
    {
        if (empty($val)) {
            $val = '.';
        }
        [$periodCount, $productCategory] = explode('.', $val);
        $this->asset_period_count = (int)$periodCount;
        $this->asset_product_category = $productCategory;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Asset::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'attributes' => ['asset_create_at'],
                'defaultOrder' => ['asset_create_at' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $itemNoList = null;
        if ($this->mobile) {
            $itemNoList = AssetBorrower::find()
                ->where([
                    'asset_borrower_mobile' => encrypt($this->mobile, Desensitise::TYPE_PHONE_NUMBER)->hash,
                ])
                ->select('asset_borrower_item_no')
                ->column();
        }

        if (!empty($this->asset_item_no . $this->mobile)) {
            $this->startTimeOfCreatedAt = null;
            $this->endTimeOfCreatedAt = null;
        }

        // grid filtering conditions
        $query
            ->andFilterWhere([
                'asset_item_no' => $this->asset_item_no,
                'asset_status' => $this->asset_status,
                'asset_period_count' => $this->asset_period_count,
                'asset_loan_channel' => $this->asset_loan_channel,
                'asset_product_category' => $this->asset_product_category,
                'asset_sub_type' => $this->asset_sub_type,
            ])
            ->andFilterWhere([
                'asset_item_no' => $itemNoList,

            ])
            ->andWhere(new Expression(sprintf('%d', empty($this->mobile) || !empty($itemNoList))))
            ->andFilterWhere([
                'and',
                ['>=', 'asset_create_at', $this->startTimeOfCreatedAt],
                [
                    '<',
                    'asset_create_at',
                    $this->endTimeOfCreatedAt ? Carbon::parse($this->endTimeOfCreatedAt)->addDay()->toDateString(
                    ) : null,
                ],
                ['>=', 'asset_actual_grant_at', $this->startTimeOfActualGrantAt],
                [
                    '<',
                    'asset_actual_grant_at',
                    $this->endTimeOfActualGrantAt ? Carbon::parse($this->endTimeOfActualGrantAt)->addDay(
                    )->toDateString()
                        : null,
                ],
            ]);

        return $dataProvider;
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'mobile' => '手机号',
        ]);
    }


    public function searchFailAsset(array $params)
    {
        $query = Asset::find()->select(
            ['asset_loan_channel', 'asset_loan_record_memo', new Expression('count(*) as cnt')]
        )->where([
            'asset_status' => Asset::STATUS_VOID,
        ]);
        $this->load($params);
        $dataProvider = new ArrayDataProvider([
            'pagination' => false,
            'sort' => [
                'attributes' => ['cnt'],
                'defaultOrder' => ['cnt' => SORT_DESC],
            ],
        ]);
        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            $dataProvider->allModels = [];

            return $dataProvider;
        }
        $defaultDate = Carbon::now()->toDateString();
        if (!$this->startTimeOfCreatedAt) {
            $this->startTimeOfCreatedAt = $defaultDate;
        }
        if (!$this->endTimeOfCreatedAt) {
            $this->endTimeOfCreatedAt = $defaultDate;
        }
        $query->innerJoin(AssetLoanRecord::tableName(), 'asset_item_no = asset_loan_record_asset_item_no')
            ->andFilterWhere([
                'and',
                ['>=', 'asset_create_at', $this->startTimeOfCreatedAt],
                [
                    '<',
                    'asset_create_at',
                    $this->endTimeOfCreatedAt ? Carbon::parse($this->endTimeOfCreatedAt)->addDay()->toDateString(
                    ) : null,
                ],
            ])
            ->andFilterWhere([
                'asset_loan_channel' => $this->asset_loan_channel,
            ]);
        $query->groupBy(['asset_loan_record_memo', 'asset_loan_channel']);

        $dataProvider->allModels = $query->asArray()->all();

        return $dataProvider;
    }


    /**
     * @param array $params
     *
     * @return ArrayDataProvider
     */
    public function searchFailWithdrawReceipt(array $params): ArrayDataProvider
    {
        $query = WithdrawReceipt::find()->select(
            [
                'withdraw_receipt_channel_resp_code',
                'withdraw_receipt_channel_resp_message',
                new Expression('count(*) as cnt'),
            ]
        )->where([
            'withdraw_receipt_merchant_name' => 'gbiz',
            'withdraw_receipt_status' => 3,
        ]);
        $this->load($params);
        $dataProvider = new ArrayDataProvider([
            'pagination' => false,
            'sort' => [
                'attributes' => ['cnt'],
                'defaultOrder' => ['cnt' => SORT_DESC],
            ],
        ]);
        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            $dataProvider->allModels = [];

            return $dataProvider;
        }
        $defaultDate = Carbon::now()->toDateString();
        if (!$this->startTimeOfCreatedAt) {
            $this->startTimeOfCreatedAt = $defaultDate;
        }
        if (!$this->endTimeOfCreatedAt) {
            $this->endTimeOfCreatedAt = $defaultDate;
        }
        $query
            ->andFilterWhere([
                'and',
                ['>=', 'withdraw_receipt_finished_at', $this->startTimeOfCreatedAt],
                [
                    '<',
                    'withdraw_receipt_finished_at',
                    $this->endTimeOfCreatedAt ? Carbon::parse($this->endTimeOfCreatedAt)->addDay()->toDateString(
                    ) : null,
                ],
            ]);
        $query->groupBy(['withdraw_receipt_channel_resp_code', 'withdraw_receipt_channel_resp_message']);
        $dataProvider->allModels = $query->asArray()->all();

        return $dataProvider;
    }
}
