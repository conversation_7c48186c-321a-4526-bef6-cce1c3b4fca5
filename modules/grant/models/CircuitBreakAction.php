<?php

namespace grant\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\Connection as DbConnection;

/**
 * This is the model class for table "{{%circuit_break_action}}".
 *
 * @property int $circuit_break_action_id               主键
 * @property int $circuit_break_action_circuit_break_id 熔断记录id
 * @property string $circuit_break_action_memo             备注
 * @property string $circuit_break_action_status           action 状态:unfinished/finished
 * @property string $circuit_break_action_type             action类型
 * @property string|null $circuit_break_action_data             挂起条件/告警内容
 * @property string $circuit_break_action_create_at        创建时间
 * @property string $circuit_break_action_update_at        更新时间
 */
class CircuitBreakAction extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%circuit_break_action}}';
    }

    /**
     * @return DbConnection|object the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                [
                    'circuit_break_action_circuit_break_id',
                    'circuit_break_action_memo',
                    'circuit_break_action_status',
                    'circuit_break_action_type',
                ],
                'required',
            ],
            [['circuit_break_action_circuit_break_id'], 'integer'],
            [['circuit_break_action_create_at', 'circuit_break_action_update_at'], 'safe'],
            [['circuit_break_action_memo'], 'string', 'max' => 200],
            [['circuit_break_action_status'], 'string', 'max' => 20],
            [['circuit_break_action_type'], 'string', 'max' => 30],
            [['circuit_break_action_data'], 'string', 'max' => 1000],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'circuit_break_action_id' => '主键',
            'circuit_break_action_circuit_break_id' => '熔断记录ID',
            'circuit_break_action_memo' => '备注',
            'circuit_break_action_status' => '状态',
            'circuit_break_action_type' => '类型',
            'circuit_break_action_data' => '挂起条件/告警内容',
            'circuit_break_action_create_at' => '创建时间',
            'circuit_break_action_update_at' => '更新时间',
            'circuit_break_action_config' => '熔断Action配置',
        ];
    }
}
