<?php

namespace grant\models;

use Yii;

/**
 * This is the model class for table "{{%asset_attachment}}".
 *
 * @property int         $asset_attachment_id
 * @property int         $asset_attachment_type              附件类型
 * @property string      $asset_attachment_type_text         附件类型描述
 * @property string      $asset_attachment_url               附件URL链接
 * @property int         $asset_attachment_status            状态：1:正常 0:作废 2:生成中
 * @property string      $asset_attachment_from_system       附件来源：进件为来源系统dsq/hxyl，合同下载对应资方渠道
 * @property string      $asset_attachment_create_at         创建时间
 * @property string      $asset_attachment_update_at         更新时间
 * @property string|null $asset_attachment_contract_code
 * @property string      $asset_attachment_contract_sign_at  合同签约时间
 * @property string|null $asset_attachment_asset_item_no
 */
class AssetAttachment extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset_attachment}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['asset_attachment_type', 'asset_attachment_status'], 'required'],
            [['asset_attachment_type', 'asset_attachment_status'], 'integer'],
            [['asset_attachment_create_at', 'asset_attachment_update_at', 'asset_attachment_contract_sign_at'], 'safe'],
            [['asset_attachment_type_text'], 'string', 'max' => 255],
            [['asset_attachment_url'], 'string', 'max' => 1000],
            [['asset_attachment_from_system'], 'string', 'max' => 24],
            [['asset_attachment_contract_code'], 'string', 'max' => 25],
            [['asset_attachment_asset_item_no'], 'string', 'max' => 48],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_attachment_id'               => Yii::t('grant', 'Asset Attachment ID'),
            'asset_attachment_type'             => Yii::t('grant', '附件类型'),
            'asset_attachment_type_text'        => Yii::t('grant', '附件类型描述'),
            'asset_attachment_url'              => Yii::t('grant', '附件URL链接'),
            'asset_attachment_status'           => Yii::t('grant', '状态：1:正常 0:作废 2:生成中'),
            'asset_attachment_from_system'      => Yii::t('grant', '附件来源：进件为来源系统dsq/hxyl，合同下载对应资方渠道'),
            'asset_attachment_create_at'        => Yii::t('grant', '创建时间'),
            'asset_attachment_update_at'        => Yii::t('grant', '更新时间'),
            'asset_attachment_contract_code'    => Yii::t('grant', 'Asset Attachment Contract Code'),
            'asset_attachment_contract_sign_at' => Yii::t('grant', ' 合同签约时间'),
            'asset_attachment_asset_item_no'    => Yii::t('grant', 'Asset Attachment Asset Item No'),
        ];
    }
}
