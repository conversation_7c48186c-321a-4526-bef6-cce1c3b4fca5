<?php

namespace grant\models;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\Connection as DbConnection;

/**
 * This is the model class for table "{{%circuit_break_record}}".
 *
 * @property int $circuit_break_record_id           主键
 * @property string $circuit_break_record_name         熔断场景名称
 * @property string $circuit_break_record_status       熔断状态
 * @property string $circuit_break_record_trigger_rule 熔断触发条件
 * @property string $circuit_break_record_create_at    创建时间
 * @property string $circuit_break_record_update_at    更新时间
 * @property string $circuit_break_record_data_snapshot    熔断数据快照
 * @property-read CircuitBreakAction[] $actions
 */
class CircuitBreakRecord extends \yii\db\ActiveRecord
{
    public $cleanCache = true;

    public function formName(): string
    {
        return '';
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%circuit_break_record}}';
    }

    /**
     * @return DbConnection|object the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [
                ['circuit_break_record_name', 'circuit_break_record_status', 'circuit_break_record_trigger_rule'],
                'required',
            ],
            [['circuit_break_record_create_at', 'circuit_break_record_update_at'], 'safe'],
            [['circuit_break_record_name'], 'string', 'max' => 50],
            [['circuit_break_record_status'], 'string', 'max' => 10],
            [['circuit_break_record_trigger_rule'], 'string', 'max' => 1000],
            [['cleanCache'], 'boolean'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'circuit_break_record_id' => '熔断ID',
            'circuit_break_record_name' => '熔断名称',
            'circuit_break_record_status' => '熔断状态',
            'circuit_break_record_trigger_rule' => '熔断触发条件',
            'circuit_break_record_create_at' => '创建时间',
            'circuit_break_record_update_at' => '更新时间',
            'circuit_break_record_data_snapshot' => '熔断数据快照'
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getActions(): ActiveQuery
    {
        return $this->hasMany(CircuitBreakAction::class, [
            'circuit_break_action_circuit_break_id' => 'circuit_break_record_id',
        ]);
    }
}
