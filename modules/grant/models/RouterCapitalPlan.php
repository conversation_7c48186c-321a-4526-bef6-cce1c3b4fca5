<?php

namespace grant\models;

use backend\behaviors\FormatterBehavior;
use common\models\User;
use grant\components\GrantHttpComponent;
use grant\components\RouterHttpComponent;
use grant\exceptions\RouterException;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\UserException;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\helpers\Json;

/**
 * This is the model class for table "{{%router_capital_plan}}".
 *
 * @property integer                $router_capital_plan_id
 * @property string                 $router_capital_plan_date
 * @property string                 $router_capital_plan_label
 * @property string                 $router_capital_plan_desc
 * @property integer                $router_capital_plan_amount
 * @property string                 $router_capital_plan_update_memo
 * @property string                 $router_capital_plan_create_at
 * @property string                 $router_capital_plan_update_at
 * @property-read RouterCapitalRule $rule
 */
class RouterCapitalPlan extends ActiveRecord
{
    /**
     * @var User
     */
    public $operator;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%router_capital_plan}}';
    }

    public function behaviors()
    {
        return [
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => 'router_capital_plan_create_at',
                'updatedAtAttribute' => 'router_capital_plan_update_at',
                'value'              => function () {
                    return date('Y-m-d H:i:s');
                },
            ],
            [
                'class'              => BlameableBehavior::class,
                'createdByAttribute' => null,
                'updatedByAttribute' => 'operator',
                'value'              => function () {
                    return Yii::$app->getUser()->getIdentity();
                },
            ],
        ];
    }

    /**
     * @return Connection|object the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'router_capital_plan_label',
                    'router_capital_plan_date',
                    'router_capital_plan_amount',
                ],
                'required',
            ],
            [['router_capital_plan_date'], 'string'],
            [['router_capital_plan_amount'], 'integer', 'min' => 0],
            [['router_capital_plan_update_memo'], 'string'],
            [['router_capital_plan_label'], 'string', 'max' => 64],
            [['router_capital_plan_desc'], 'string', 'max' => 128],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'router_capital_plan_id'          => '自增id',
            'router_capital_plan_date'        => '资金计划日期',
            'router_capital_plan_label'       => '资金规则编码',
            'router_capital_plan_desc'        => '资金计划描述',
            'router_capital_plan_amount'      => '资金量',
            'router_capital_plan_update_memo' => '更新备注',
            'router_capital_plan_create_at'   => '创建时间',
            'router_capital_plan_update_at'   => '更新时间',
        ];
    }

    /**
     * @inheritDoc
     */
    public function getRule()
    {
        return $this->hasOne(RouterCapitalRule::class, ['router_capital_rule_code' => 'router_capital_plan_label']);
    }

    /**
     * 通过接口添加数据
     *
     * @param array|null $attributes
     *
     * @return bool
     * @throws InvalidConfigException
     * @throws RouterException
     * @inheritDoc
     */
    protected function insertInternal($attributes = null)
    {
        if (!$this->beforeSave($this->isNewRecord)) {
            return false;
        }
        $values = $this->getDirtyAttributes($attributes);
        unset($values['router_capital_plan_update_at']);
        unset($values['router_capital_plan_update_memo']);
        if (empty($values)) {
            Yii::info('无修改');

            return true;
        }

        $grant = RouterHttpComponent::instance();
        if (!$grant->planSaveOrUpdate($this)) {
            throw new RouterException($grant->getError());
        }

        return true;
    }

    /**
     * 通过接口修改数据
     *
     * @param array|null $attributes
     *
     * @return bool
     * @throws InvalidConfigException
     * @throws RouterException
     * @inheritDoc
     */
    protected function updateInternal($attributes = null)
    {
        return $this->insertInternal($attributes);
    }

    /**
     * 通过接口删除数据
     *
     * @throws RouterException
     */
    protected function deleteInternal()
    {
        throw new RouterException('禁止删除');
    }

    /**
     * @param string $code
     * @param string $date
     * @param float  $amount
     *
     * @return RouterCapitalPlan
     * @throws UserException
     */
    public static function savePlan(string $code, string $date, float $amount)
    {
        $plan = self::findOne([
            'router_capital_plan_label' => $code,
            'router_capital_plan_date'  => $date,
        ]);
        if (!$plan) {
            $rule = RouterCapitalRule::findOne(['router_capital_rule_code' => $code]);
            if (!$rule) {
                throw new UserException('规则编码未定义');
            }
            $plan = new self([
                'router_capital_plan_label' => $code,
                'router_capital_plan_date'  => $date,
                'router_capital_plan_desc'  => $rule->router_capital_rule_desc,
            ]);
        }

        $plan->router_capital_plan_amount = intval(round($amount * 10000 / FormatterBehavior::currencyMultiplier()));
        if (!$plan->save()) {
            if ($plan->hasErrors()) {
                $msg = Json::encode($plan->getErrors());
            } else {
                $msg = '保存资金计划失败: ' . Json::encode($plan->attributes());
            }
            throw new UserException($msg);
        }

        return $plan;
    }
}
