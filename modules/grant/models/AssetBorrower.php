<?php

namespace grant\models;

use caradb\models\Borrower;
use caradb\models\BorrowerProfile;
use finance\components\RiskControlHttpComponent;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%asset_borrower}}".
 *
 * @property int $asset_borrower_id
 * @property string $asset_borrower_item_no         资产编号
 * @property string $asset_borrower_card_uuid       借款人在账户中心的uuid，用于放款
 * @property string $asset_borrower_uuid            用户在用户中心ID，每个自然人在不通平台可能会有多个ID
 * @property string $asset_borrower_id_num          借款人身份证号(密文)
 * @property string $asset_borrower_mobile          借款人手机号(密文)
 * @property int|null $asset_borrower_loan_usage      借款用途：0-未知 1-购物 2-娱乐 3-教育 4-旅游 5-装修 6-美容 7-运动 8-购买原材料 9-购买设备 10-购买车辆 11-付房租 12-房屋装修 13-进货 14-经营场所扩大 15-个人消费
 * @property string $asset_borrower_risk_level      风控评级
 * @property string|null $asset_borrower_extend_info     扩展信息
 * @property string $asset_borrower_create_at       创建时间
 * @property string $asset_borrower_update_at       更新时间
 * @property string $asset_borrower_individual_uuid 用户在用户中心ID，每个自然人一个唯一ID
 * @property string $asset_borrower_withdraw_type   代付方式 线上|线下
 * @property string $asset_borrower_card_extend_info
 * @property BorrowerProfile $userBorrower
 */
class AssetBorrower extends ActiveRecord
{
    public const WITHDRAW_TYPE_ONLINE = 'online';
    public const WITHDRAW_TYPE_OFFLINE = 'offline';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset_borrower}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['asset_borrower_loan_usage'], 'integer'],
            [['asset_borrower_create_at', 'asset_borrower_update_at'], 'safe'],
            [['asset_borrower_item_no'], 'string', 'max' => 48],
            [
                [
                    'asset_borrower_card_uuid',
                    'asset_borrower_uuid',
                    'asset_borrower_extend_info',
                    'asset_borrower_individual_uuid',
                ],
                'string',
                'max' => 64,
            ],
            [['asset_borrower_id_num'], 'string', 'max' => 50],
            [['asset_borrower_mobile'], 'string', 'max' => 32],
            [['asset_borrower_risk_level'], 'string', 'max' => 10],
            [['asset_borrower_item_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_borrower_id' => 'Asset Borrower ID',
            'asset_borrower_item_no' => '资产编号',
            'asset_borrower_card_uuid' => '借款人在账户中心的uuid，用于放款',
            'asset_borrower_uuid' => '用户在用户中心ID，每个自然人在不通平台可能会有多个ID',
            'asset_borrower_id_num' => '借款人身份证号(密文)',
            'asset_borrower_mobile' => '借款人手机号(密文)',
            'asset_borrower_loan_usage' => '借款用途：0-未知 1-购物 2-娱乐 3-教育 4-旅游 5-装修 6-美容 7-运动 8-购买原材料 9-购买设备 10-购买车辆 11-付房租 12-房屋装修 13-进货 14-经营场所扩大 15-个人消费',
            'asset_borrower_risk_level' => '风控评级',
            'asset_borrower_extend_info' => '扩展信息',
            'asset_borrower_create_at' => '创建时间',
            'asset_borrower_update_at' => '更新时间',
            'asset_borrower_individual_uuid' => '用户在用户中心ID，每个自然人一个唯一ID',
        ];
    }

    public function fields(): array
    {
        return [
            'withdraw_type' => 'asset_borrower_withdraw_type',
            'user_uuid' => 'asset_borrower_uuid',
            'card_uuid' => 'asset_borrower_card_uuid',
            'card_extend_info' => 'asset_borrower_card_extend_info',
        ];
    }

    public function extraFields(): array
    {
        $riskControl = RiskControlHttpComponent::instance();
        $userInfo = $riskControl->getUserInfo($this->asset_borrower_uuid);
        $bankCard = $riskControl->getBankCardByCardUUID($this->asset_borrower_card_uuid);

        return [
            'user_full_name' => fn() => $userInfo->fullNameEncrypt(),
            'user_phone' => fn() => $userInfo->individual_phone_encrypt,
            'card_num' => fn() => $bankCard->no_encrypt,
            'bank_name' => fn() => $bankCard->name,
            'card_method' => fn() => $bankCard->method,
            'card_method_name' => fn() => $bankCard->method_name,
        ];
    }


    public function getUserBorrower(): ActiveQuery
    {
        return $this->hasOne(BorrowerProfile::class, ['borrower_user_uuid' => 'asset_borrower_uuid']);
    }

    /**
     * @param string|array $itemNo
     *
     * @return array
     */
    public static function borrowerInfo($itemNo): array
    {
        return self::find()
            ->where([
                'asset_borrower_item_no' => $itemNo,
            ])
            ->select([
                'asset_borrower_mobile',
                'asset_borrower_item_no',
            ])
            ->indexBy('asset_borrower_item_no')
            ->asArray()
            ->all();
    }
}
