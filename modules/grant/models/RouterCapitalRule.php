<?php

namespace grant\models;

use grant\components\RouterHttpComponent;
use grant\exceptions\RouterException;
use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%router_capital_rule}}".
 *
 * @property int    $router_capital_rule_id
 * @property string $router_capital_rule_code
 * @property string $router_capital_rule_desc
 * @property string $router_capital_rule_family
 * @property string $router_capital_rule_type
 * @property int    $router_capital_rule_weight
 * @property string $router_capital_rule_content
 * @property string $router_capital_rule_status
 * @property string $router_capital_rule_create_at
 * @property string $router_capital_rule_update_at
 * @property string $router_capital_rule_product_code
 */
class RouterCapitalRule extends ActiveRecord
{
    const TYPE_DEMAND = 'demand';
    const TYPE_SUPPLY = 'supply';

    const STATUS_DRAFT = 'draft';
    const STATUS_RELEASE = 'release';
    const STATUS_DISCARD = 'discard';

    public static function types()
    {
        return [
            self::TYPE_DEMAND => 'demand',
            self::TYPE_SUPPLY => 'supply',
        ];
    }

    public static function status()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_RELEASE => '发布',
            self::STATUS_DISCARD => '废弃',
        ];
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%router_capital_rule}}';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'router_capital_rule_create_at',
                'updatedAtAttribute' => 'router_capital_rule_update_at',
                'value' => function () {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * @return Connection|object the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'router_capital_rule_code',
                    'router_capital_rule_desc',
                    'router_capital_rule_family',
                    'router_capital_rule_content',
                ],
                'required',
            ],
//            [
//                ['router_capital_rule_content'],
//                'filter',
//                'filter' => function ($value) {
//                    $val = json_decode($value, true);
//                    if (empty($val['rules']) && empty($val['output_rules'])) {
//                        $this->addError('router_capital_rule_content', '缺少rules或output_rules配置');
//                    }
//
//                    return $value;
//                },
//            ],
            [['router_capital_rule_weight'], 'integer'],
            [['router_capital_rule_content', 'router_capital_rule_status'], 'string'],
            [['router_capital_rule_create_at', 'router_capital_rule_update_at'], 'safe'],
            [['router_capital_rule_code', 'router_capital_rule_family'], 'string', 'max' => 64],
            [['router_capital_rule_desc'], 'string', 'max' => 128],
            [['router_capital_rule_type'], 'string', 'max' => 32],
            [['router_capital_rule_product_code'], 'string', 'max' => 50],
            [['router_capital_rule_code'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'router_capital_rule_id' => '自增id',
            'router_capital_rule_code' => '规则编码',
            'router_capital_rule_desc' => '规则描述',
            'router_capital_rule_family' => '规则分组',
            'router_capital_rule_type' => '规则分类',
            'router_capital_rule_weight' => '规则权重',
            'router_capital_rule_content' => '规则内容',
            'router_capital_rule_status' => '规则状态',
            'router_capital_rule_create_at' => '创建时间',
            'router_capital_rule_update_at' => '更新时间',
            'router_capital_rule_product_code' => '放款渠道业务产品code',
        ];
    }

    /**
     * 通过接口添加数据
     *
     * @param array|null $attributes
     *
     * @return bool
     * @throws InvalidConfigException
     * @throws RouterException
     * @inheritDoc
     */
    protected function insertInternal($attributes = null)
    {
        if (!$this->beforeSave($this->isNewRecord)) {
            return false;
        }

        $grant = RouterHttpComponent::instance();
        if (!$grant->ruleSaveOrUpdate($this)) {
            throw new RouterException($grant->getError());
        }

        return true;
    }

    /**
     * 通过接口修改数据
     *
     * @param array|null $attributes
     *
     * @return bool
     * @throws InvalidConfigException
     * @throws RouterException
     * @inheritDoc
     */
    protected function updateInternal($attributes = null)
    {
        return $this->insertInternal($attributes);
    }

    /**
     * 通过接口删除数据
     *
     * @throws RouterException
     */
    protected function deleteInternal()
    {
        throw new RouterException('禁止删除');
    }
}
