<?php

namespace grant\models;

use payment\models\WithdrawReceipt;
use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%withdraw_record}}".
 *
 * @property int                  $withdraw_record_id            主键
 * @property string               $withdraw_record_order_no      商户订单号
 * @property string               $withdraw_record_trade_no      商户流水号
 * @property string               $withdraw_record_channel       代付渠道
 * @property string               $withdraw_record_status        状态
 * @property string               $withdraw_record_resp_code     代付响应码
 * @property string               $withdraw_record_resp_message  代付响应信息
 * @property string               $withdraw_record_finish_at     完成时间
 * @property string|null          $withdraw_record_channel_key   通道交易流水编号
 * @property string               $withdraw_record_create_at     创建时间
 * @property string               $withdraw_record_update_at     更新时间
 * @property string|null          $withdraw_record_withdraw_code 线下收款时的取款码
 * @property-read WithdrawOrder   $withdrawOrder
 * @property-read WithdrawReceipt $withdrawReceipt
 */
class WithdrawRecord extends \yii\db\ActiveRecord
{
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAIL = 'fail';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%lds_withdraw_record}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['withdraw_record_order_no', 'withdraw_record_trade_no'], 'required'],
            [['withdraw_record_status'], 'string'],
            [['withdraw_record_finish_at', 'withdraw_record_create_at', 'withdraw_record_update_at'], 'safe'],
            [['withdraw_record_order_no', 'withdraw_record_trade_no'], 'string', 'max' => 64],
            [['withdraw_record_channel', 'withdraw_record_withdraw_code'], 'string', 'max' => 50],
            [['withdraw_record_resp_code', 'withdraw_record_resp_message'], 'string', 'max' => 2555],
            [['withdraw_record_channel_key'], 'string', 'max' => 32],
            [['withdraw_record_trade_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_record_id'            => '主键',
            'withdraw_record_order_no'      => '商户订单号',
            'withdraw_record_trade_no'      => '商户流水号',
            'withdraw_record_channel'       => '代付渠道',
            'withdraw_record_status'        => '状态',
            'withdraw_record_resp_code'     => '代付响应码',
            'withdraw_record_resp_message'  => '代付响应信息',
            'withdraw_record_finish_at'     => '完成时间',
            'withdraw_record_channel_key'   => '通道交易流水编号',
            'withdraw_record_create_at'     => '创建时间',
            'withdraw_record_update_at'     => '更新时间',
            'withdraw_record_withdraw_code' => '线下收款时的取款码',
        ];
    }

    public function getWithdrawOrder()
    {
        return $this->hasOne(WithdrawOrder::class, ['withdraw_order_no' => 'withdraw_record_order_no']);
    }

    public function getWithdrawReceipt(): ActiveQuery
    {
        return $this->hasOne(WithdrawReceipt::class, ['withdraw_receipt_trade_no' => 'withdraw_record_trade_no']);
    }
}
