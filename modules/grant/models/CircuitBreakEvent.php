<?php

namespace grant\models;

use Yii;

/**
 * This is the model class for table "circuit_break_event".
 *
 * @property int $circuit_break_event_id 主键
 * @property int $circuit_break_event_record_id 熔断记录id
 * @property string $circuit_break_event_type 熔断Action的type
 * @property string $circuit_break_event_order_no 熔断事件编号
 * @property string $circuit_break_event_data 熔断事件原始数据
 * @property string $circuit_break_event_status 熔断事件状态
 * @property string $circuit_break_event_memo 备注
 * @property string $circuit_break_event_fire_at 熔断事件发生事件
 * @property string $circuit_break_event_create_at 创建时间
 * @property string $circuit_break_event_update_at 更新时间
 */
class CircuitBreakEvent extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'circuit_break_event';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['circuit_break_event_record_id'], 'integer'],
            [['circuit_break_event_fire_at', 'circuit_break_event_create_at', 'circuit_break_event_update_at'], 'safe'],
            [['circuit_break_event_type', 'circuit_break_event_status'], 'string', 'max' => 32],
            [['circuit_break_event_order_no'], 'string', 'max' => 64],
            [['circuit_break_event_data'], 'string', 'max' => 1024],
            [['circuit_break_event_memo'], 'string', 'max' => 200],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'circuit_break_event_id' => '主键',
            'circuit_break_event_record_id' => '熔断记录id',
            'circuit_break_event_type' => '熔断Action的type',
            'circuit_break_event_order_no' => '熔断事件编号',
            'circuit_break_event_data' => '熔断事件原始数据',
            'circuit_break_event_status' => '熔断事件状态',
            'circuit_break_event_memo' => '备注',
            'circuit_break_event_fire_at' => '熔断事件发生事件',
            'circuit_break_event_create_at' => '创建时间',
            'circuit_break_event_update_at' => '更新时间',
        ];
    }
}
