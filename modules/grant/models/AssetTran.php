<?php

namespace grant\models;

use Yii;

/**
 * This is the model class for table "{{%asset_tran}}".
 *
 * @property int         $asset_tran_id              表主键
 * @property string|null $asset_tran_asset_item_no
 * @property int         $asset_tran_period          期次
 * @property string      $asset_tran_description     交易类型中文描述
 * @property int         $asset_tran_amount          实际交易的金额（分）= repaid_amount + balance_amount
 * @property string      $asset_tran_due_at          每期还款到期日
 * @property string      $asset_tran_type            交易类型： 'grant’:放款 ’repayinterest’：偿还利息 'repayprincipal’，偿还本金 ‘services’：技术服务费 manage:管理费.
 * @property int         $asset_tran_decrease_amount 减免金额（分）
 * @property int         $asset_tran_repaid_amount   已偿还金额（分）
 * @property int         $asset_tran_balance_amount  剩余未偿还金额（分）
 * @property int         $asset_tran_total_amount    总金额（分） = amount + decrease_amount
 * @property string      $asset_tran_status          已还款/未还款
 * @property string      $asset_tran_create_at       创建时间
 * @property string      $asset_tran_finish_at       到卡时间
 * @property string      $asset_tran_update_at       更新时间
 * @property string      $asset_tran_late_status     逾期状态
 * @property string      $asset_tran_remark          备注
 * @property int         $asset_tran_repay_priority  偿还优先级
 * @property string      $asset_tran_trade_at        交易时间
 * @property string      $asset_tran_category        类别
 */
class AssetTran extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset_tran}}';
    }

    public static function statusList()
    {
        return [
            'finish'   => '已还款',
            'nofinish' => '未还款',
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'asset_tran_period',
                    'asset_tran_description',
                    'asset_tran_due_at',
                    'asset_tran_type',
                    'asset_tran_status',
                    'asset_tran_create_at',
                    'asset_tran_finish_at',
                    'asset_tran_repay_priority',
                    'asset_tran_trade_at',
                    'asset_tran_category',
                ],
                'required',
            ],
            [
                [
                    'asset_tran_period',
                    'asset_tran_amount',
                    'asset_tran_decrease_amount',
                    'asset_tran_repaid_amount',
                    'asset_tran_balance_amount',
                    'asset_tran_total_amount',
                    'asset_tran_repay_priority',
                ],
                'integer',
            ],
            [
                [
                    'asset_tran_due_at',
                    'asset_tran_create_at',
                    'asset_tran_finish_at',
                    'asset_tran_update_at',
                    'asset_tran_trade_at',
                ],
                'safe',
            ],
            [['asset_tran_status', 'asset_tran_category'], 'string'],
            [['asset_tran_asset_item_no'], 'string', 'max' => 48],
            [['asset_tran_description'], 'string', 'max' => 64],
            [['asset_tran_type'], 'string', 'max' => 32],
            [['asset_tran_late_status'], 'string', 'max' => 20],
            [['asset_tran_remark'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_tran_id'              => Yii::t('grant', '主键'),
            'asset_tran_asset_item_no'   => Yii::t('grant', '资产编号'),
            'asset_tran_period'          => Yii::t('grant', '期次'),
            'asset_tran_description'     => Yii::t('grant', '交易类型'),
            'asset_tran_amount'          => Yii::t('grant', '实际交易金额'),
            'asset_tran_due_at'          => Yii::t('grant', '到期日'),
            'asset_tran_type'            => Yii::t('grant', '交易类型'),
            'asset_tran_decrease_amount' => Yii::t('grant', '减免金额'),
            'asset_tran_repaid_amount'   => Yii::t('grant', '已偿还金额'),
            'asset_tran_balance_amount'  => Yii::t('grant', '剩余未偿还金额'),
            'asset_tran_total_amount'    => Yii::t('grant', '总金额'),
            'asset_tran_status'          => Yii::t('grant', '状态'),
            'asset_tran_create_at'       => Yii::t('grant', '创建时间'),
            'asset_tran_finish_at'       => Yii::t('grant', '到卡时间'),
            'asset_tran_update_at'       => Yii::t('grant', '更新时间'),
            'asset_tran_late_status'     => Yii::t('grant', '逾期状态'),
            'asset_tran_remark'          => Yii::t('grant', '备注'),
            'asset_tran_repay_priority'  => Yii::t('grant', '偿还优先级'),
            'asset_tran_trade_at'        => Yii::t('grant', '交易时间'),
            'asset_tran_category'        => Yii::t('grant', '类别'),
        ];
    }
}
