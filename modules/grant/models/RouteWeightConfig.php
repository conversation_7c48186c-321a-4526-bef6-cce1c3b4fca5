<?php

namespace grant\models;

use Carbon\Carbon;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%route_weight_config}}".
 *
 * @property int    $route_weight_config_id                  自增id
 * @property string $route_weight_config_channel             资方名字
 * @property int    $route_weight_config_period_count        期数如1，3，6
 * @property string $route_weight_config_period_type         还款类型如day，month
 * @property int    $route_weight_config_period_term         每期期限如0，1，7，30
 * @property int    $route_weight_config_weight              权重值
 * @property int    $route_weight_config_status              配置状态 0 启用, 1不启用
 * @property int    $route_weight_config_first_route_status  一次路由启用
 * @property int    $route_weight_config_second_route_status 二次路由启用
 * @property string $route_weight_config_create_at           创建时间
 * @property string $route_weight_config_update_at           更新时间
 * @property string $route_weight_config_create_name         创建人
 * @property string $route_weight_config_update_name         修改人
 */
class RouteWeightConfig extends ActiveRecord
{
    public function behaviors()
    {
        return [
            [
                'class'              => BlameableBehavior::class,
                'createdByAttribute' => 'route_weight_config_create_name',
                'updatedByAttribute' => 'route_weight_config_update_name',
                'value'              => function () {
                    return Yii::$app->getUser()->getId();
                },
            ],
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => null,
                'updatedAtAttribute' => 'route_weight_config_update_at',
                'value'              => function () {
                    return Carbon::now()->toDateTimeString();
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%route_weight_config}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'route_weight_config_channel',
                    'route_weight_config_period_type',
                    'route_weight_config_period_count',
                    'route_weight_config_period_term',
                    'route_weight_config_weight',
                    'route_weight_config_status',
                    'route_weight_config_first_route_status',
                    'route_weight_config_second_route_status',
                ],
                'required',
            ],
            [
                [
                    'route_weight_config_period_count',
                    'route_weight_config_period_term',
                    'route_weight_config_status',
                    'route_weight_config_first_route_status',
                    'route_weight_config_second_route_status',
                ],
                'integer',
            ],
            [['route_weight_config_weight'], 'integer', 'min' => 0, 'max' => 999999999],
            [['route_weight_config_create_at', 'route_weight_config_update_at'], 'safe'],
            [
                ['route_weight_config_channel', 'route_weight_config_create_name', 'route_weight_config_update_name'],
                'string',
                'max' => 48,
            ],
            [['route_weight_config_period_type'], 'string', 'max' => 24],
            [
                [
                    'route_weight_config_channel',
                    'route_weight_config_period_count',
                    'route_weight_config_period_type',
                ],
                'unique',
                'targetAttribute' => [
                    'route_weight_config_channel',
                    'route_weight_config_period_count',
                    'route_weight_config_period_type',
                ],
            ],
        ];
    }

    public static function statusList()
    {
        return [
            '0' => '启用',
            '1' => '不启用',
        ];
    }

    public static function routeFirstStatusList()
    {
        return [
            '0' => '启用',
            '1' => '不启用',
        ];
    }

    public static function routeSecondStatusList()
    {
        return [
            '0' => '启用',
            '1' => '不启用',
        ];
    }

    public static function periodTypeList()
    {
        return [
            'day'   => '天',
            'month' => '月',
        ];
    }

    public static function periodCountList()
    {
        return [
            '0' => '0期',
            '1' => '1期',
            '3' => '3期',
            '6' => '6期',
        ];
    }

    public static function periodTermList()
    {
        return [
            '0'  => '0',
            '1'  => '1',
            '7'  => '7',
            '14' => '14',
            '30' => '30',
        ];
    }

    public static function channelList()
    {
        return Asset::channelList();
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'route_weight_config_id'                  => Yii::t('grant', '主键'),
            'route_weight_config_channel'             => Yii::t('grant', '资方名字'),
            'route_weight_config_period_count'        => Yii::t('grant', '期限'),
            'route_weight_config_period_type'         => Yii::t('grant', '期限类型'),
            'route_weight_config_period_term'         => Yii::t('grant', '期限单位'),
            'route_weight_config_weight'              => Yii::t('grant', '权重值'),
            'route_weight_config_status'              => Yii::t('grant', '状态'),
            'route_weight_config_first_route_status'  => Yii::t('grant', '一次路由'),
            'route_weight_config_second_route_status' => Yii::t('grant', '二次路由'),
            'route_weight_config_create_at'           => Yii::t('grant', '创建时间'),
            'route_weight_config_update_at'           => Yii::t('grant', '更新时间'),
            'route_weight_config_create_name'         => Yii::t('grant', '创建人'),
            'route_weight_config_update_name'         => Yii::t('grant', '修改人'),
        ];
    }
}
