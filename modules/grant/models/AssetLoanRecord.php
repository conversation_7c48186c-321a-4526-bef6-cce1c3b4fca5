<?php

namespace grant\models;

use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%asset_loan_record}}".
 *
 * @property int                 $asset_loan_record_id                 主键
 * @property string              $asset_loan_record_asset_item_no      资产编号
 * @property int                 $asset_loan_record_amount             放款金额，即本金，单位：分
 * @property int                 $asset_loan_record_withholding_amount 预扣金额，单位：分
 * @property string              $asset_loan_record_channel            放款渠道
 * @property int                 $asset_loan_record_status             放款状态(0-新建，1-进件中，2-进件失败，3-进件成功，4-放款中（审核通过），5-放款失败，6-放款成功，7-审核不通过，8-放款到二类户成功, 9-资金归集成功, 10-放款到二类户/虚户失败, 11-提现中, 12-提现取消)
 * @property string              $asset_loan_record_identifier         编号
 * @property string              $asset_loan_record_trade_no           进件流水号
 * @property string              $asset_loan_record_due_bill_no        借据号
 * @property string              $asset_loan_record_finish_at          放款时间
 * @property string              $asset_loan_record_create_at          创建时间
 * @property string              $asset_loan_record_update_at          更新时间
 * @property string              $asset_loan_record_grant_at           放款到虚户/二类户的时间
 * @property string|null         $asset_loan_record_memo               备注信息
 * @property string              $asset_loan_record_push_at            发起进件请求时间
 * @property-read Asset          $asset
 * @property-read WithdrawRecord[] $withdrawRecords
 */
class AssetLoanRecord extends \yii\db\ActiveRecord
{
    public static function statusList()
    {
        return [
            '0'  => '新建',
            '1'  => '进件中',
            '2'  => '进件失败',
            '3'  => '进件成功',
            '4'  => '放款中（审核通过）',
            '5'  => '放款失败',
            '6'  => '放款成功',
            '7'  => '审核不通过',
            '8'  => '放款到二类户成功',
            '9'  => '资金归集成功',
            '10' => '放款到二类户/虚户失败',
            '11' => '提现中',
            '12' => '提现取消',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset_loan_record}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('gbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'asset_loan_record_asset_item_no',
                    'asset_loan_record_amount',
                    'asset_loan_record_channel',
                    'asset_loan_record_trade_no',
                ],
                'required',
            ],
            [
                ['asset_loan_record_amount', 'asset_loan_record_withholding_amount', 'asset_loan_record_status'],
                'integer',
            ],
            [
                [
                    'asset_loan_record_finish_at',
                    'asset_loan_record_create_at',
                    'asset_loan_record_update_at',
                    'asset_loan_record_grant_at',
                    'asset_loan_record_push_at',
                ],
                'safe',
            ],
            [['asset_loan_record_asset_item_no'], 'string', 'max' => 48],
            [['asset_loan_record_channel'], 'string', 'max' => 32],
            [['asset_loan_record_identifier'], 'string', 'max' => 100],
            [['asset_loan_record_trade_no', 'asset_loan_record_due_bill_no'], 'string', 'max' => 45],
            [['asset_loan_record_memo'], 'string', 'max' => 2048],
            [['asset_loan_record_trade_no'], 'unique'],
            [['asset_loan_record_identifier'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_loan_record_id'                 => Yii::t('grant', '主键'),
            'asset_loan_record_asset_item_no'      => Yii::t('grant', '资产编号'),
            'asset_loan_record_amount'             => Yii::t('grant', '放款金额，即本金，单位：分'),
            'asset_loan_record_withholding_amount' => Yii::t('grant', '预扣金额，单位：分'),
            'asset_loan_record_channel'            => Yii::t('grant', '放款渠道'),
            'asset_loan_record_status'             => Yii::t('grant',
                '放款状态(0-新建，1-进件中，2-进件失败，3-进件成功，4-放款中（审核通过），5-放款失败，6-放款成功，7-审核不通过，8-放款到二类户成功, 9-资金归集成功, 10-放款到二类户/虚户失败, 11-提现中, 12-提现取消)'),
            'asset_loan_record_identifier'         => Yii::t('grant', '编号'),
            'asset_loan_record_trade_no'           => Yii::t('grant', '进件流水号'),
            'asset_loan_record_due_bill_no'        => Yii::t('grant', '借据号'),
            'asset_loan_record_finish_at'          => Yii::t('grant', '放款时间'),
            'asset_loan_record_create_at'          => Yii::t('grant', '创建时间'),
            'asset_loan_record_update_at'          => Yii::t('grant', '更新时间'),
            'asset_loan_record_grant_at'           => Yii::t('grant', '放款到虚户/二类户的时间'),
            'asset_loan_record_memo'               => Yii::t('grant', '备注信息'),
            'asset_loan_record_push_at'            => Yii::t('grant', '发起进件请求时间'),
        ];
    }

    public function getAsset()
    {
        return $this->hasOne(Asset::class, ['asset_item_no' => 'asset_loan_record_asset_item_no']);
    }

    public function getRouterLoadRecord()
    {
        return $this->hasOne(RouterLoadRecord::class, [
            'router_load_record_item_no' => 'asset_loan_record_asset_item_no',
            'router_load_record_channel' => 'asset_loan_record_channel',
        ]);
    }

    public function getWithdrawRecords(): ActiveQuery
    {
        return $this->hasMany(WithdrawRecord::class, ['withdraw_record_order_no' => 'asset_loan_record_due_bill_no']);
    }
}
