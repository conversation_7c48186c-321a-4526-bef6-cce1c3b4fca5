<?php

namespace grant\models;

use kvmanager\KVException;
use kvmanager\models\KeyValue;
use repay\models\WithholdDetail;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%asset}}".
 *
 * @property int                   $asset_id                       资产行号，单表主键 。
 * @property string|null           $asset_item_no                  资产编号
 * @property string                $asset_type                     资产类型
 * @property string                $asset_sub_type                 资产子类型
 * @property string                $asset_period_type              还款周期类型
 * @property int                   $asset_period_count             还款总期数
 * @property string                $asset_product_category         资产类别名称：14天，30天，3个月，6个月
 * @property string                $asset_cmdb_product_number      资产产品在费率系统编号
 * @property string                $asset_create_at                创建时间
 * @property string                $asset_import_at                首次进件时间
 * @property string                $asset_grant_at                 预计放款时间
 * @property string                $asset_effect_at                起息时间
 * @property string                $asset_actual_grant_at          实际放款时间(到卡时间)
 * @property string                $asset_due_at                   资产到期日
 * @property string                $asset_payoff_at                偿清时间
 * @property string                $asset_update_at                更新时间
 * @property string                $asset_from_system              资产的系统来源
 * @property string                $asset_status                   资产状态
 * @property int                   $asset_principal_amount         合同本金
 * @property int                   $asset_granted_principal_amount 实际放款本金
 * @property string                $asset_loan_channel             放款渠道
 * @property string                $asset_alias_name               资产名称
 * @property int                   $asset_interest_amount          利息总金额
 * @property int                   $asset_fee_amount               费总金额
 * @property int                   $asset_balance_amount           剩余未偿还总金额(分）包括：本，息，费
 * @property int                   $asset_repaid_amount            已偿还总金额
 * @property int                   $asset_total_amount             本息费总额。 =principalAmt+interestAmt+feeAmt =balance_amount+repaidAmt
 * @property int|null              $asset_version                  资产修改版本号,主动修改资产时，才递增版本号
 * @property float                 $asset_interest_rate            利率
 * @property string                $asset_from_system_name         资产的系统来源名称
 * @property string                $asset_owner                    资产所有权者(KN-快牛、STB)
 * @property string                $asset_idnum_encrypt            用户身份证密文
 * @property string                $asset_from_app                 马甲包字段
 * @property string                $asset_source_type              场景类型
 * @property string|null           $asset_source_number            相关的其他订单号
 * @property string                $asset_product_name             产品名称
 * @property-read AssetTran[]      $trans
 * @property-read AssetLoanRecord  $loanRecord
 * @property-read WithholdDetail[] $withholdDetail
 * @property-read AssetBorrower    $borrower
 */
class Asset extends ActiveRecord
{
    const LOAN_CHANNEL_NOLOAN = 'noloan';

    const  STATUS_SIGN     = 'sign';
    const  STATUS_SALE     = 'sale';
    const  STATUS_REPAY    = 'repay';
    const  STATUS_PAYOFF   = 'payoff';
    const  STATUS_VOID     = 'void';
    const  STATUS_WRITEOFF = 'writeoff';
    const  STATUS_LATE     = 'late';
    const  STATUS_LATEOFF  = 'lateoff';

    /**
     * @return array
     */
    public static function channelList()
    {
        try {
            return KeyValue::takeAsArray('channel_list');
        } catch (KVException $e) {
            Yii::error((string)$e, __METHOD__);

            return [];
        }
    }

    /**
     * @return array
     */
    public static function periodCountList()
    {
        try {
            $config = KeyValue::take('period_category');
        } catch (KVException $e) {
            Yii::error((string)$e, __METHOD__);

            return [];
        }

        $periodCategory = [
            //            '1.7'  => '7天',
            //            '1.14' => '14天',
            //            '1.30' => '30天',
            //            '3.30' => '3个月',
            //            '6.30' => '6个月',
        ];
        foreach ($config as $period => $group) {
            foreach ($group as $day => $label) {
                $periodCategory[sprintf('%s.%s', $period, $day)] = $label;
            }
        }

        return $periodCategory;
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset}}';
    }

    public static function statusList()
    {
        return [
            self::STATUS_SALE   => '放款中',
            self::STATUS_REPAY  => '还款中',
            self::STATUS_PAYOFF => '已结清',
            self::STATUS_VOID   => '已作废',
            self::STATUS_WRITEOFF => '取消资产',
        ];
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['asset_period_type', 'asset_status'], 'string'],
            [
                [
                    'asset_period_count',
                    'asset_principal_amount',
                    'asset_granted_principal_amount',
                    'asset_interest_amount',
                    'asset_fee_amount',
                    'asset_balance_amount',
                    'asset_repaid_amount',
                    'asset_total_amount',
                    'asset_version',
                ],
                'integer',
            ],
            [
                [
                    'asset_create_at',
                    'asset_import_at',
                    'asset_grant_at',
                    'asset_effect_at',
                    'asset_actual_grant_at',
                    'asset_due_at',
                    'asset_payoff_at',
                    'asset_update_at',
                ],
                'safe',
            ],
            [['asset_interest_rate'], 'number'],
            [['asset_source_type', 'asset_product_name'], 'required'],
            [['asset_item_no', 'asset_cmdb_product_number'], 'string', 'max' => 48],
            [
                [
                    'asset_type',
                    'asset_sub_type',
                    'asset_loan_channel',
                    'asset_source_type',
                    'asset_source_number',
                    'asset_product_name',
                ],
                'string',
                'max' => 32,
            ],
            [['asset_product_category'], 'string', 'max' => 16],
            [['asset_from_system', 'asset_owner'], 'string', 'max' => 24],
            [['asset_alias_name'], 'string', 'max' => 64],
            [['asset_from_system_name'], 'string', 'max' => 10],
            [['asset_idnum_encrypt'], 'string', 'max' => 128],
            [['asset_from_app'], 'string', 'max' => 100],
            [['asset_item_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_id'                       => Yii::t('grant', '主键'),
            'asset_item_no'                  => Yii::t('grant', '资产编号'),
            'asset_type'                     => Yii::t('grant', '资产类型'),
            'asset_sub_type'                 => Yii::t('grant', '资产子类型'),
            'asset_period_type'              => Yii::t('grant', '还款周期类型'),
            'asset_period_count'             => Yii::t('grant', '总期数'),
            'asset_product_category'         => Yii::t('grant', '资产类别'),
            'asset_cmdb_product_number'      => Yii::t('grant', '费率编号'),
            'asset_create_at'                => Yii::t('grant', '创建时间'),
            'asset_import_at'                => Yii::t('grant', '进件时间'),
            'asset_grant_at'                 => Yii::t('grant', '预计放款时间'),
            'asset_effect_at'                => Yii::t('grant', '起息时间'),
            'asset_actual_grant_at'          => Yii::t('grant', '实际放款时间'),
            'asset_due_at'                   => Yii::t('grant', '到期日期'),
            'asset_payoff_at'                => Yii::t('grant', '结清时间'),
            'asset_update_at'                => Yii::t('grant', '更新时间'),
            'asset_from_system'              => Yii::t('grant', '资产的系统来源'),
            'asset_status'                   => Yii::t('grant', '资产状态'),
            'asset_principal_amount'         => Yii::t('grant', '合同本金'),
            'asset_granted_principal_amount' => Yii::t('grant', '放款本金'),
            'asset_loan_channel'             => Yii::t('grant', '放款渠道'),
            'asset_alias_name'               => Yii::t('grant', '资产名称'),
            'asset_interest_amount'          => Yii::t('grant', '利息总金额'),
            'asset_fee_amount'               => Yii::t('grant', '费总金额'),
            'asset_balance_amount'           => Yii::t('grant', '待还金额'),
            'asset_repaid_amount'            => Yii::t('grant', '已还金额'),
            'asset_total_amount'             => Yii::t('grant', '本息费总额'),
            'asset_version'                  => Yii::t('grant', '版本号'),
            'asset_interest_rate'            => Yii::t('grant', '利率'),
            'asset_from_system_name'         => Yii::t('grant', '来源系统'),
            'asset_owner'                    => Yii::t('grant', '资产所有权'),
            'asset_idnum_encrypt'            => Yii::t('grant', '用户身份证密文'),
            'asset_from_app'                 => Yii::t('grant', '马甲包字段'),
            'asset_source_type'              => Yii::t('grant', '场景类型'),
            'asset_source_number'            => Yii::t('grant', '相关的其他订单号'),
            'asset_product_name'             => Yii::t('grant', '产品名称'),
        ];
    }

    public function getTrans()
    {
        return $this->hasMany(AssetTran::class, ['asset_tran_asset_item_no' => 'asset_item_no']);
    }

    public function getLoanRecord()
    {
        return $this->hasOne(AssetLoanRecord::class, [
            'asset_loan_record_asset_item_no' => 'asset_item_no',
            'asset_loan_record_channel'       => 'asset_loan_channel',
        ]);
    }

    public function getWithholdDetail()
    {
        return $this->hasMany(WithholdDetail::class, ['withhold_detail_asset_item_no' => 'asset_item_no']);
    }

    public function getAttachment()
    {
        return $this->hasMany(AssetAttachment::class, ['asset_attachment_asset_item_no' => 'asset_item_no']);
    }

    public static function periodList()
    {
        $result     = [];
        $periodInfo = array_keys(KeyValue::take('period_category'));
        foreach ($periodInfo as $period) {
            $result[$period] = $period . '期';
        }

        return $result;
    }

    public function getActualPayoffAt()
    {
        $repayAsset = \repay\models\Asset::findOne([
            'asset_item_no' => $this->asset_item_no,
        ]);

        return $repayAsset->asset_actual_payoff_at ?? null;
    }

    public function getBorrower(): ActiveQuery
    {
        return $this->hasOne(AssetBorrower::class, ['asset_borrower_item_no' => 'asset_item_no']);
    }
}
