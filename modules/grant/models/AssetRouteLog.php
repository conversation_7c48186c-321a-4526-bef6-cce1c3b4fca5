<?php

namespace grant\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%asset_route_log}}".
 *
 * @property int         $asset_route_log_id
 * @property string|null $asset_route_log_asset_item_no  资产编号
 * @property string      $asset_route_log_asset_type     资产类型
 * @property string|null $asset_route_log_borrower_idnum 借款人身份证号
 * @property string      $asset_route_log_borrower_name  借款人姓名
 * @property string      $asset_route_log_loan_channel   放款渠道
 * @property string      $asset_route_log_message        消息
 * @property string      $asset_route_create_at          创建时间
 * @property string      $asset_route_update_at          更新时间
 * @property int         $asset_route_log_route_type     1:一次路由;2:二次路由
 * @property int         $asset_route_log_period_count   request路由期数如1，3，6
 */
class AssetRouteLog extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%asset_route_log}}';
    }

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['asset_route_log_borrower_name', 'asset_route_create_at'], 'required'],
            [['asset_route_create_at', 'asset_route_update_at'], 'safe'],
            [['asset_route_log_route_type', 'asset_route_log_period_count'], 'integer'],
            [['asset_route_log_asset_item_no'], 'string', 'max' => 64],
            [['asset_route_log_asset_type'], 'string', 'max' => 60],
            [
                ['asset_route_log_borrower_idnum', 'asset_route_log_borrower_name', 'asset_route_log_loan_channel'],
                'string',
                'max' => 32,
            ],
            [['asset_route_log_message'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'asset_route_log_id'             => Yii::t('grant', 'Asset Route Log ID'),
            'asset_route_log_asset_item_no'  => Yii::t('grant', '资产编号'),
            'asset_route_log_asset_type'     => Yii::t('grant', '资产类型'),
            'asset_route_log_borrower_idnum' => Yii::t('grant', '借款人身份证号'),
            'asset_route_log_borrower_name'  => Yii::t('grant', '借款人姓名'),
            'asset_route_log_loan_channel'   => Yii::t('grant', '放款渠道'),
            'asset_route_log_message'        => Yii::t('grant', '消息'),
            'asset_route_create_at'          => Yii::t('grant', '创建时间'),
            'asset_route_update_at'          => Yii::t('grant', '更新时间'),
            'asset_route_log_route_type'     => Yii::t('grant', '1:一次路由;2:二次路由'),
            'asset_route_log_period_count'   => Yii::t('grant', 'request路由期数如1，3，6'),
        ];
    }
}
