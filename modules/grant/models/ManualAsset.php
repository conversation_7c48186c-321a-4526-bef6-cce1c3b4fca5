<?php

namespace grant\models;

use Predis\Client;
use Yii;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "{{%manual_asset}}".
 *
 * @property int                $manual_asset_id                        主键
 * @property string             $manual_asset_item_no                   资产编号
 * @property string             $manual_asset_type                      资产类型 例如：paydayloan
 * @property string             $manual_asset_channel                   放款渠道
 * @property int                $manual_asset_amount                    放款金额(分)
 * @property string             $manual_asset_status                    放款状态
 * @property string             $manual_asset_apply_at                  放款申请时间
 * @property string             $manual_asset_order_no                  通道流水号
 * @property string             $manual_asset_finish_at                 放款完成时间
 * @property string             $manual_asset_account_card_no           出款卡号密文（公司账户）
 * @property string             $manual_asset_receiver_card_no          收款人卡号密文
 * @property string             $manual_asset_receiver_card_branch_name 收款卡开户行
 * @property int                $manual_asset_charge_fee                手续费(分)
 * @property string             $manual_asset_comment                   备注
 * @property string             $manual_asset_create_at                 创建时间
 * @property string             $manual_asset_update_at                 更新时间
 * @property string             $manual_asset_receiver_account_type     用户收款账户类型
 * @property string             $manual_asset_account_card_type         出款卡类型
 * @property-read AssetBorrower $assetBorrower                          借款人
 */
class ManualAsset extends ActiveRecord
{
    const STATUS_SALE        = 'sale';
    const STATUS_REPAY       = 'repay';
    const STATUS_PAYOFF      = 'payoff';
    const STATUS_VOID        = 'void';
    const STATUS_UPDATE_CARD = 'update_card';

    public static function statusList()
    {
        return [
            self::STATUS_SALE        => '待放款',
            self::STATUS_REPAY       => '放款成功',
//            self::STATUS_PAYOFF => '已还清',
            self::STATUS_VOID        => '放款失败',
            self::STATUS_UPDATE_CARD => '换卡中',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%manual_asset}}';
    }

    /**
     * @return Connection|object the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'manual_asset_channel',
                    'manual_asset_amount',
                    'manual_asset_account_card_no',
                    'manual_asset_charge_fee',
                ],
                'required',
            ],
            [['manual_asset_amount', 'manual_asset_charge_fee'], 'integer'],
            [['manual_asset_status'], 'string'],
            [
                ['manual_asset_apply_at', 'manual_asset_finish_at', 'manual_asset_create_at', 'manual_asset_update_at'],
                'safe',
            ],
            [['manual_asset_item_no'], 'string', 'max' => 48],
            [['manual_asset_type', 'manual_asset_channel', 'manual_asset_account_card_no'], 'string', 'max' => 32],
            [
                [
                    'manual_asset_receiver_card_no',
                    'manual_asset_receiver_card_branch_name',
                    'manual_asset_receiver_account_type',
                ],
                'string',
                'max' => 64,
            ],
            [['manual_asset_comment'], 'string', 'max' => 2048],
            [['manual_asset_item_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'manual_asset_id'                        => Yii::t('grant', '主键'),
            'manual_asset_item_no'                   => Yii::t('grant', '资产编号'),
            'manual_asset_type'                      => Yii::t('grant', '资产类型'),
            'manual_asset_channel'                   => Yii::t('grant', '放款渠道'),
            'manual_asset_amount'                    => Yii::t('grant', '放款金额'),
            'manual_asset_status'                    => Yii::t('grant', '放款状态'),
            'manual_asset_apply_at'                  => Yii::t('grant', '放款申请时间'),
            'manual_asset_finish_at'                 => Yii::t('grant', '放款完成时间'),
            'manual_asset_account_card_no'           => Yii::t('grant', '出款卡号'),
            'manual_asset_receiver_card_no'          => Yii::t('grant', '收款人卡号'),
            'manual_asset_receiver_card_branch_name' => Yii::t('grant', '收款卡开户行'),
            'manual_asset_charge_fee'                => Yii::t('grant', '手续费'),
            'manual_asset_comment'                   => Yii::t('grant', '备注'),
            'manual_asset_create_at'                 => Yii::t('grant', '创建时间'),
            'manual_asset_update_at'                 => Yii::t('grant', '更新时间'),
            'manual_asset_receiver_account_type'     => Yii::t('grant', '收款人账户类型'),
            'manual_asset_account_card_type'         => Yii::t('grant', '出款账户类型'),
            'manual_asset_order_no'                  => Yii::t('grant', '通道流水号'),
        ];
    }

    /**
     * @param self|int $id
     * @param int      $expire
     *
     * @return bool|string
     * @throws Exception
     * @throws InvalidConfigException
     */
    public static function lock($id, int $expire = 60)
    {
        if ($id instanceof self) {
            $model = $id;
        } else {
            $model = self::findOne($id);
            if (!$model) {
                return false;
            }
        }

        $token = Yii::$app->getSecurity()->generateRandomString();

        /** @var Client $redis */
        $redis = Yii::$app->get('redis');

        if ($redis->set(self::class . ':' . $model->manual_asset_id, $token, 'EX', $expire, 'NX')) {
            return $token;
        }

        return false;
    }

    /**
     * @param int    $id
     * @param string $token
     *
     * @return void
     * @throws InvalidConfigException
     */
    public static function unlock($id, $token)
    {
        /** @var Client $redis */
        $redis = Yii::$app->get('redis');

        $key = self::class . ':' . trim($id);
        if ($token === $redis->get($key)) {
            $redis->del($key);
        }
    }

    /**
     * @param $ids
     *
     * @return array|false
     * @throws InvalidConfigException
     */
    public static function lockList($ids)
    {
        $ids  = array_unique($ids);
        $keys = array_map(function ($id) {
            return self::class . ':' . trim($id);
        }, $ids);

        /** @var Client $redis */
        $redis = Yii::$app->get('redis');

        return array_combine($ids, $redis->mget(...$keys));
    }

    /**
     * @return ActiveQuery
     */
    public function getAssetBorrower(): ActiveQuery
    {
        return $this->hasOne(AssetBorrower::class, ['asset_borrower_item_no' => 'manual_asset_item_no']);
    }
}
