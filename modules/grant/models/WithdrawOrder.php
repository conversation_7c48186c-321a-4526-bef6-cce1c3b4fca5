<?php

namespace grant\models;

use Yii;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "{{%withdraw_order}}".
 *
 * @property int $withdraw_order_id                       主键
 * @property string $withdraw_order_no                       商户订单号
 * @property string $withdraw_order_asset_item_no            资产编号
 * @property string $withdraw_order_asset_loan_channel       资产渠道
 * @property int $withdraw_order_amount                   金额(分)
 * @property string $withdraw_order_reason                   代付用途
 * @property string $withdraw_order_status                   状态
 * @property string $withdraw_order_resp_code                代付响应码
 * @property string $withdraw_order_resp_message             代付响应信息
 * @property string $withdraw_order_type                     类型
 * @property string $withdraw_order_receiver_card_uuid       收款账号在账户中心的uuid
 * @property string $withdraw_order_receiver_idnum_encrypt   收款人身份证密文
 * @property string $withdraw_order_create_at                创建时间
 * @property string $withdraw_order_update_at                更新时间
 * @property string $withdraw_order_finish_at                完成时间
 * @property string $withdraw_order_payment_subject_key      支付主体key
 * @property string $withdraw_order_receiver_cardnum_encrypt 收款卡密文
 * @property string $withdraw_order_receiver_mobile_encrypt  收款卡预留手机号密文
 * @property string $withdraw_order_receiver_name_encrypt    收款人姓名密文
 * @property string|null $withdraw_order_withdraw_type            收款方式： online - 线上收款， offline - 线下收款
 * @property string|null $withdraw_order_extend_info              扩展信息
 * @property-read WithdrawRecord $withdrawRecord
 */
class WithdrawOrder extends \yii\db\ActiveRecord
{
    public const SPLICE_STR = 'w';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%lds_withdraw_order}}';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                ['withdraw_order_no', 'withdraw_order_amount', 'withdraw_order_reason', 'withdraw_order_type'],
                'required',
            ],
            [['withdraw_order_amount'], 'integer'],
            [['withdraw_order_status'], 'string'],
            [['withdraw_order_create_at', 'withdraw_order_update_at', 'withdraw_order_finish_at'], 'safe'],
            [
                [
                    'withdraw_order_no',
                    'withdraw_order_resp_code',
                    'withdraw_order_receiver_card_uuid',
                    'withdraw_order_receiver_idnum_encrypt',
                    'withdraw_order_receiver_cardnum_encrypt',
                    'withdraw_order_receiver_mobile_encrypt',
                    'withdraw_order_receiver_name_encrypt',
                ],
                'string',
                'max' => 64,
            ],
            [['withdraw_order_asset_item_no', 'withdraw_order_asset_loan_channel'], 'string', 'max' => 48],
            [['withdraw_order_reason'], 'string', 'max' => 128],
            [['withdraw_order_resp_message'], 'string', 'max' => 255],
            [['withdraw_order_type'], 'string', 'max' => 50],
            [['withdraw_order_payment_subject_key'], 'string', 'max' => 40],
            [['withdraw_order_withdraw_type'], 'string', 'max' => 10],
            [['withdraw_order_extend_info'], 'string', 'max' => 1024],
            [
                [
                    'withdraw_order_asset_item_no',
                    'withdraw_order_asset_loan_channel',
                    'withdraw_order_type',
                    'withdraw_order_receiver_cardnum_encrypt',
                ],
                'unique',
                'targetAttribute' => [
                    'withdraw_order_asset_item_no',
                    'withdraw_order_asset_loan_channel',
                    'withdraw_order_type',
                    'withdraw_order_receiver_cardnum_encrypt',
                ],
            ],
            [['withdraw_order_no'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'withdraw_order_id' => '主键',
            'withdraw_order_no' => '商户订单号',
            'withdraw_order_asset_item_no' => '资产编号',
            'withdraw_order_asset_loan_channel' => '资产渠道',
            'withdraw_order_amount' => '金额(分)',
            'withdraw_order_reason' => '代付用途',
            'withdraw_order_status' => '状态',
            'withdraw_order_resp_code' => '代付响应码',
            'withdraw_order_resp_message' => '代付响应信息',
            'withdraw_order_type' => '类型',
            'withdraw_order_receiver_card_uuid' => '收款账号在账户中心的uuid',
            'withdraw_order_receiver_idnum_encrypt' => '收款人身份证密文',
            'withdraw_order_create_at' => '创建时间',
            'withdraw_order_update_at' => '更新时间',
            'withdraw_order_finish_at' => '完成时间',
            'withdraw_order_payment_subject_key' => '支付主体key',
            'withdraw_order_receiver_cardnum_encrypt' => '收款卡密文',
            'withdraw_order_receiver_mobile_encrypt' => '收款卡预留手机号密文',
            'withdraw_order_receiver_name_encrypt' => '收款人姓名密文',
            'withdraw_order_withdraw_type' => '收款方式： online - 线上收款， offline - 线下收款',
            'withdraw_order_extend_info' => '扩展信息',
        ];
    }

    public function getWithdrawRecord(): ActiveQuery
    {
        return $this->hasOne(WithdrawRecord::class, ['withdraw_record_order_no' => 'withdraw_order_no']);
    }

    /**
     * @param array $itemNoList
     * @param bool $success
     * @param array|null $selectFields
     * @param string|callable $indexBy
     *
     * @return array
     */
    public static function withdrawInfo(
        array $itemNoList,
        bool $success = false,
        array $selectFields = null,
        $indexBy = 'withdraw_order_asset_item_no'
    ): array {
        $itemNoList = array_map(function ($itemNo) {
            return $itemNo . self::SPLICE_STR;
        }, $itemNoList);

        $query = self::find()
            ->joinWith('withdrawRecord', false)
            ->where(['withdraw_order_asset_item_no' => $itemNoList]);

        if (!$success) {
            if ($indexBy) {
                // 如果需要按某个字段为键，则获取最后一次代扣的数据
                $maxIds = (clone $query)
                    ->select('max(withdraw_record_id)')
                    ->groupBy('withdraw_order_asset_item_no');

                $query->andWhere([
                    'withdraw_record_id' => $maxIds,
                ]);
            }
        } else {
            $query->andWhere(['withdraw_record_status' => WithdrawRecord::STATUS_SUCCESS]);
        }

        $query->select(
            $selectFields ?? [
            'withdraw_record_channel_key',
            'withdraw_record_channel',
            'withdraw_order_asset_item_no',
        ]
        );

        if ($indexBy) {
            $query->indexBy($indexBy);
        }

        return $query->asArray()->all();
    }
}
