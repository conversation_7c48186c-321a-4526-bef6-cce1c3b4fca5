<?php

namespace grant\models;

use grant\components\GrantHttpComponent;
use grant\components\RouterHttpComponent;
use grant\exceptions\RouterException;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "router_weight".
 *
 * @property integer $router_weight_id
 * @property string  $router_weight_type
 * @property string  $router_weight_code
 * @property string  $router_weight_desc
 * @property string  $router_weight_rule_content
 * @property integer $router_weight_value
 * @property string  $router_weight_status
 * @property string  $router_weight_first_route_status
 * @property string  $router_weight_second_route_status
 * @property string  $router_weight_create_at
 * @property string  $router_weight_update_at
 * @property string  $router_weight_create_name
 * @property string  $router_weight_update_name
 */
class RouterWeight extends ActiveRecord
{
    const STATUS_ACTIVE   = 'active';
    const STATUS_INACTIVE = 'inactive';

    const FIRST_ROUTE_STATUS_ACTIVE   = 'active';
    const FIRST_ROUTE_STATUS_INACTIVE = 'inactive';

    const SECOND_ROUTE_STATUS_ACTIVE   = 'active';
    const SECOND_ROUTE_STATUS_INACTIVE = 'inactive';

    public static function status()
    {
        return [
            self::STATUS_ACTIVE   => '启用',
            self::STATUS_INACTIVE => '禁止',
        ];
    }

    public static function firstRouteStatus()
    {
        return [
            self::FIRST_ROUTE_STATUS_ACTIVE   => '启用',
            self::FIRST_ROUTE_STATUS_INACTIVE => '禁止',
        ];
    }

    public static function secondRouteStatus()
    {
        return [
            self::SECOND_ROUTE_STATUS_ACTIVE   => '启用',
            self::SECOND_ROUTE_STATUS_INACTIVE => '禁止',
        ];
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function types()
    {
        return Keyvalue::takeAsArray('router_weight_type_list');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%router_weight}}';
    }

    public function behaviors()
    {
        return [
            [
                'class'              => BlameableBehavior::class,
                'createdByAttribute' => 'router_weight_create_name',
                'updatedByAttribute' => 'router_weight_update_name',
                'value'              => function () {
                    return Yii::$app->getUser()->getIdentity()->username;
                },
            ],
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => 'router_weight_create_at',
                'updatedAtAttribute' => 'router_weight_update_at',
                'value'              => function () {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * @return Connection|object the database connection used by this AR class.
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        return Yii::$app->get('rbizDb');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'router_weight_type',
                    'router_weight_code',
                    'router_weight_desc',
                    'router_weight_value',
                    'router_weight_status',
                    'router_weight_first_route_status',
                    'router_weight_second_route_status',
                ],
                'required',
            ],
            [
                [
                    'router_weight_status',
                    'router_weight_first_route_status',
                    'router_weight_second_route_status',
                    'router_weight_rule_content',
                ],
                'string',
            ],
            [['router_weight_value'], 'integer', 'min' => 0],
            [['router_weight_create_at', 'router_weight_update_at'], 'safe'],
            [['router_weight_type', 'router_weight_code'], 'string', 'max' => 64],
            [['router_weight_desc'], 'string', 'max' => 128],
            [['router_weight_create_name', 'router_weight_update_name'], 'string', 'max' => 48],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'router_weight_id'                  => '自增id',
            'router_weight_type'                => '路由结果类型',
            'router_weight_code'                => '路由结果编码',
            'router_weight_desc'                => '权重描述',
            'router_weight_rule_content'        => '规则内容',
            'router_weight_value'               => '权重值',
            'router_weight_status'              => '权重状态',
            'router_weight_first_route_status'  => '一次路由启用状态',
            'router_weight_second_route_status' => '二次路由启用状态',
            'router_weight_create_at'           => '创建时间',
            'router_weight_update_at'           => '更新时间',
            'router_weight_create_name'         => '创建人',
            'router_weight_update_name'         => '修改人',
        ];
    }

    /**
     * 通过接口添加数据
     *
     * @param array|null $attributes
     *
     * @return bool
     * @throws InvalidConfigException
     * @throws RouterException
     * @inheritDoc
     */
    protected function insertInternal($attributes = null)
    {
        if (!$this->beforeSave($this->isNewRecord)) {
            return false;
        }

        $values = $this->getDirtyAttributes($attributes);
        unset($values['router_weight_update_at']);
        unset($values['router_weight_update_name']);
        if (empty($values)) {
            Yii::info('无数据更改');

            return true;
        }

        $client = RouterHttpComponent::instance();
        if (!$client->weightSaveOrUpdate($this)) {
            throw new RouterException($client->getError());
        }

        return true;
    }

    /**
     * 通过接口修改数据
     *
     * @param array|null $attributes
     *
     * @return bool
     * @throws InvalidConfigException
     * @throws RouterException
     * @inheritDoc
     */
    protected function updateInternal($attributes = null)
    {
        return $this->insertInternal($attributes);
    }

    /**
     * 通过接口删除数据
     *
     * @throws RouterException
     */
    protected function deleteInternal()
    {
        throw new RouterException('禁止删除');
    }
}
