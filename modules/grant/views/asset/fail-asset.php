<?php

use dashboard\grid\TotalDataColumn;
use xlerr\common\widgets\GridView;


/* @var yii\web\View $this */
/* @var grant\models\AssetSearch $searchModel */
/* @var yii\data\ActiveDataProvider $dataProvider */
/* @var yii\data\ActiveDataProvider $withdrawDataProvider */

$this->title = '放款失败原因统计';

$this->params['breadcrumbs'][] = $this->title;
?>


<?= $this->render('_fail_search', ['model' => $searchModel]); ?>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'layout' => '<div class="box-body table-responsive no-padding">{items}</div><div class="box-footer">{pager}</div>',
    'columns' => [
        [
            'attribute' => 'asset_loan_record_channel',
            'label' => '资金方',
            'footer'    => '合计',
        ],
        [
            'attribute' => 'asset_loan_record_memo',
            'label' => '失败原因',
        ],
        [
            'attribute' => 'cnt',
            'label' => '失败总数',
            'class'     => TotalDataColumn::class,
        ],
    ],
]); ?>



<?= GridView::widget([
    'dataProvider' => $withdrawDataProvider,
    'layout' => '<div class="box-body table-responsive no-padding">{items}</div><div class="box-footer">{pager}</div>',
    'columns' => [
        [
            'attribute' => 'withdraw_receipt_channel_resp_code',
            'label' => '渠道状态码',
            'footer'    => '合计',
        ],
        [
            'attribute' => 'withdraw_receipt_channel_resp_message',
            'label' => '渠道错误消息',
        ],
        [
            'attribute' => 'cnt',
            'label' => '失败总数',
            'class'     => TotalDataColumn::class,
        ],
    ],
]); ?>
