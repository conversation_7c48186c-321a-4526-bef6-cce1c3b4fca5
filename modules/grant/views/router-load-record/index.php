<?php

use common\models\Asset;
use grant\models\RouterLoadRecord;
use grant\models\RouterLoadRecordSearch;
use xlerr\common\grid\ActionColumn;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\GridView;
use xlerr\common\widgets\Select2;
use yii\data\ActiveDataProvider;
use yii\grid\CheckboxColumn;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this View */
/* @var $searchModel RouterLoadRecordSearch */
/* @var $dataProvider ActiveDataProvider */

$this->title = '资金路由管理';
$this->params['breadcrumbs'][] = $this->title;
?>

    <div class="box box-default search">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa fa-search"></i>
                条件筛选
            </div>
        </div>
        <div class="box-body">
            <?php
            $form = ActiveForm::begin([
                'action' => [''],
                'method' => 'get',
                'type' => ActiveForm::TYPE_INLINE,
                'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
            ]) ?>

            <?= $form->field($searchModel, 'router_load_record_key') ?>

            <?= $form->field($searchModel, 'router_load_record_item_no') ?>

            <?= $form->field($searchModel, 'router_load_record_idnum') ?>

            <?= $form->field($searchModel, 'router_load_record_from_system') ?>

            <?= $form->field($searchModel, 'router_load_record_channel') ?>

            <?= $form->field($searchModel, 'router_load_record_status')->widget(Select2::class, [
                'data' => RouterLoadRecord::STATUS_LIST,
                'hideSearch' => true,
                'pluginOptions' => [
                    'allowClear' => true,
                ],
                'options' => [
                    'prompt' => '路由状态',
                ],
            ]) ?>

            <?= $form->field($searchModel, 'createStartDate')->widget(DatePicker::class) ?>

            <?= $form->field($searchModel, 'createEndDate')->widget(DatePicker::class) ?>

            <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>

            <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>

            <?php
            ActiveForm::end() ?>
        </div>
    </div>

<?= GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'class' => CheckboxColumn::class,
            'checkboxOptions' => function (RouterLoadRecord $model) {
                $flag = $model->router_load_record_status !== RouterLoadRecord::STATUS_ROUTED;

                return [
                    'disabled' => $flag,
                    'style' => 'display:' . ($flag ? 'none' : 'block'),
                ];
            },
        ],
        [
            'class' => ActionColumn::class,
            'template' => '{cancel} {update-create-at}',
            'header' => Html::button('批量取消', [
                'class' => 'btn btn-xs btn-danger batch-cancel',
            ]),
            'buttons' => [
                'cancel' => static function ($url, RouterLoadRecord $model) {
                    return Html::button('取消', [
                        'class' => 'btn btn-xs btn-primary cancel-btn',
                        'data' => [
                            'id' => $model->router_load_record_id
                        ]
                    ]);
                },
                'update-create-at' => static function ($url, RouterLoadRecord $model) {
                    return DialogActionColumn::newButton('修改创建时间', $url, [
                        'class' => 'btn btn-xs btn-primary layer-dialog',
                        'data' => [
                            'id' => $model->router_load_record_id
                        ]
                    ]);
                },
            ],
            'visibleButtons' => [
                'cancel' => static function (RouterLoadRecord $model) {
                    return $model->router_load_record_status === RouterLoadRecord::STATUS_ROUTED;
                },
            ]
        ],
        'router_load_record_key',
        'router_load_record_item_no',
        'router_load_record_rule_code',
        'router_load_record_principal_amount',
        [
            'attribute' => 'router_load_record_principal_amount',
            'class' => MoneyDataColumn::class,
        ],
        'router_load_record_channel',
        'router_load_record_from_system',
        'router_load_record_route_day',
        'router_load_record_idnum',
        'router_load_record_create_at',
        [
            'attribute' => 'router_load_record_status',
            'format' => ['in', RouterLoadRecord::STATUS_LIST],
        ],
    ],
]);
$url = Url::to(['batch-cancel']);

$js = <<<JS
    $(document)
        .on('click', 'button.cancel-btn', function (e) {
            const id = e.target.getAttribute("data-id")
            cancelRequest([id])
        })
        .on('click', 'button.batch-cancel', function () {
            const ids = $('[name=\'selection[]\']:checked').map(function () { 
                return $(this).val(); 
            }).get();
            
            if (!ids.length) {
                layer.alert('请先勾选需要修改的数据!');
            } else {
                cancelRequest(ids)
            }
        });

    function cancelRequest(ids) {
        layer.confirm('确定提交吗?', {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.post('{$url}', { ids: ids.join(',') }, function (data, status) {
                if (data.code === 0) {
                    layer.msg(data.message);
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                } else {
                    layer.alert(data.message);
                }
            }).fail(function () {
               layer.alert('网络请求错误,请稍候再试!');
          });
        });
    }
JS;

$this->registerJs($js);


