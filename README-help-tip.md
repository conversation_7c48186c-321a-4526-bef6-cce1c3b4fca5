# 简单问号提示功能使用说明

## 功能介绍
这是一个简单的问号提示功能，可以在表格列标题旁边添加问号图标，鼠标悬停时显示解释说明。

## 特点
- ✅ **极简设计**：只需要一个CSS文件，无JavaScript依赖
- ✅ **数据库支持**：可以从数据库读取提示内容
- ✅ **易于使用**：只需调用辅助函数即可
- ✅ **性能优化**：支持缓存机制

## 快速开始

### 1. 基本使用

```php
use common\helpers\HelpTipHelper;

// 在GridView中使用
echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => HelpTipHelper::createForColumn('业务系统上报的金额归属月份', '业务发生的实际月份，与记账月份可能不同'),
            'attribute' => 'business_month',
            'format' => 'raw', // 重要：必须设置为raw格式
        ],
    ],
]);
```

### 2. 从数据库读取提示内容

```php
[
    'label' => HelpTipHelper::createFromDb('业务系统上报的金额归属月份', 'business_month'),
    'attribute' => 'business_month',
    'format' => 'raw',
]
```

### 3. 直接使用HTML

```php
[
    'label' => '业务日期 <span class="help-tip" data-tip="业务实际发生的具体日期">?</span>',
    'attribute' => 'business_date',
    'format' => 'raw',
]
```

## 数据库配置（可选）

如果要从数据库读取提示内容，可以创建以下表结构：

```sql
CREATE TABLE help_tips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tip_key VARCHAR(100) NOT NULL UNIQUE COMMENT '提示键值',
    tip_content TEXT NOT NULL COMMENT '提示内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tip_key (tip_key)
) COMMENT='帮助提示表';

-- 插入示例数据
INSERT INTO help_tips (tip_key, tip_content) VALUES
('business_month', '业务系统上报的金额归属月份是指业务发生的实际月份，与记账月份可能不同。'),
('report_date', '数据上报到系统的日期，通常为业务发生后的次日或指定上报时间'),
('loan_channel', '提供资金的金融机构或合作伙伴名称');
```

然后修改 `common/helpers/HelpTipHelper.php` 中的 `getTipFromDatabase()` 方法：

```php
private static function getTipFromDatabase($tipKey)
{
    try {
        // 使用缓存提高性能
        $cacheKey = 'help_tip_' . $tipKey;
        $result = \Yii::$app->cache->get($cacheKey);
        
        if ($result === false) {
            $result = \Yii::$app->db->createCommand(
                'SELECT tip_content FROM help_tips WHERE tip_key = :key'
            )->bindValue(':key', $tipKey)->queryScalar();
            
            \Yii::$app->cache->set($cacheKey, $result, 3600); // 缓存1小时
        }
        
        return $result ?: '';
    } catch (\Exception $e) {
        \Yii::error('获取帮助提示失败: ' . $e->getMessage(), __METHOD__);
        return '';
    }
}
```

## API 参考

### HelpTipHelper::create($tipText, $icon = '?', $options = [])
创建问号提示HTML

- `$tipText`: 提示文本内容
- `$icon`: 问号图标文本，默认为 "?"
- `$options`: HTML选项数组

### HelpTipHelper::createForColumn($label, $tipText, $icon = '?')
为GridView列标题创建带问号提示的标题

- `$label`: 列标题
- `$tipText`: 提示文本内容
- `$icon`: 问号图标文本，默认为 "?"

### HelpTipHelper::createFromDb($label, $tipKey, $icon = '?')
从数据库获取提示内容并创建问号提示

- `$label`: 列标题
- `$tipKey`: 提示内容的键值（用于从数据库查询）
- `$icon`: 问号图标文本，默认为 "?"

## 样式自定义

如果需要自定义样式，可以在CSS中覆盖 `.help-tip` 类：

```css
.help-tip {
    background-color: #your-color;
    /* 其他样式... */
}
```

## 注意事项

1. 使用时必须设置 `'format' => 'raw'`
2. 提示文本建议控制在200字以内
3. 建议使用缓存来提高数据库查询性能
4. 在移动端也能正常显示

## 文件结构

```
common/helpers/HelpTipHelper.php          # 辅助函数类
backend/web/css/site.css                  # CSS样式（已添加.help-tip样式）
backend/views/examples/simple-help-tip-example.php  # 使用示例
```

## 实际使用示例

已在以下文件中添加了实际使用示例：
- `modules/dashboard/views/pay-stats/channel-all.php`

您可以参考这个文件来了解如何在实际项目中使用问号提示功能。
