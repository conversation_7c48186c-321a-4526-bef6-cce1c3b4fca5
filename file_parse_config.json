{"superbank_simas": {"normal_repay": {"file": {"rowSeparator": "|", "itemNoColumn": ["AccountID", "TermPayment"], "appendTitle": "AccountID|AccountNO|Currency|PaymentType|PaymentDate|PostingDate|PartnerPaymentID|InterestAmount|PrincipalAmount|Installment|PaymentAmount|OverPayment|TermPayment|LateChargeAmount|EarlyPaymentFee|AnnualFeeAmount|Remarks"}, "import": {"read_rows": 1000, "skip_rows": 0, "title_position": 1, "value_get_method": "", "template": {"channel": "AccountID", "asset_item_no": "AccountID", "serial_no": "AccountID", "loan_amount": "AccountID", "trade_amount": "PaymentAmount", "principal": "PrincipalAmount", "interest": "InterestAmount", "apply_at": "PaymentDate", "trade_at": "PostingDate", "start_period": "TermPayment", "end_period": "TermPayment", "remark": "Remarks", "payment_type": "PaymentType", "account_no": "AccountNO", "reimbursing_order_serial_no": "PartnerPaymentID"}, "formats": {"asset_item_no": [["expression", "getAssetWithAssetLoanRecord(self,'asset_loan_record_asset_item_no')"]], "channel": [["expression", "getAssetWithAssetLoanRecord(self,'asset_loan_record_channel')"]], "loan_amount": [["expression", "getAssetWithAssetLoanRecord(self,'asset_loan_record_amount')"]], "trade_amount": [["String2Amount", 100, true]], "principal": [["String2Amount", 100, true]], "interest": [["String2Amount", 100, true]], "extend_info": [["expression", "{\"payment_type\":payment_type,\"account_no\":account_no,\"reimbursing_order_serial_no\":reimbursing_order_serial_no}"], ["expression", "json_encode(self,320)"]]}, "filter": "expression: filterUnwantedDetailData(getAssetWithAssetLoanRecord(asset_item_no,'asset_loan_record_asset_item_no'),'normal_repay',start_period,end_period,'1')", "model": ["cpop_ext_trans_stmt", "rbizDb"], "complement": {"type": "normal_repay", "status": "open", "biz_status": "initial", "extend_info": ""}}}}}