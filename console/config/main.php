<?php

use Codingheping\StatementComponent\console\MigrateController;
use console\controllers\TransferController;
use dashboard\console\LoanAccountDetailController;
use datasource\console\GenerateController;
use finance\console\AccountController;
use finance\console\MonthlyIncomeController;
use finance\services\CpopOffline\ChannelRechargeServiceForMex;
use finance\services\CpopOffline\ChannelRechargeServiceForPak;
use finance\services\CpopOffline\ChannelRechargeServiceForPhl;
use payment\console\HolidayController;
use Xlerr\CpopOffline\Console\CpopOfflineController;
use xlerr\task\console\TaskController;
use yii\log\FileTarget;

$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'app-console',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log', 'import'],
    'controllerNamespace' => 'console\controllers',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
    ],
    'controllerMap' => [
        'generate-dashboard-work' => GenerateController::class,
        'loan-account-detail' => LoanAccountDetailController::class,
        'finance-account' => AccountController::class,
        'task' => TaskController::class,
        'monthly-income' => MonthlyIncomeController::class,
        'statement-migrate' => MigrateController::class,
        'transfer' => TransferController::class,
        'holiday' => HolidayController::class,
        'cpop-offline' => [
            'class' => CpopOfflineController::class,
            'handlers' => [
                'channel_recharge' => [
                    '墨西哥' => ChannelRechargeServiceForMex::class,
                    '巴基斯坦' => ChannelRechargeServiceForPak::class,
                    '菲律宾' => ChannelRechargeServiceForPhl::class,
                ],
                'channel_cost' => [],
            ],
        ],
    ],
    'components' => [
        'log' => [
            'targets' => [
                [
                    'class' => FileTarget::class,
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
    ],
    'params' => $params,
];
