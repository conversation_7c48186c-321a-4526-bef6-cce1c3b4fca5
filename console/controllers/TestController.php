<?php

namespace console\controllers;

use finance\models\OaApprovalOrder;
use finance\services\OaService;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\UserException;
use yii\console\Controller;

class TestController extends Controller
{
    public function actionIndex()
    {
        // nobody
    }

    public function actionException()
    {
        throw new UserException('test');
    }

    public function actionCleanCache($key, $namespace = 'biz', $group = 'KV')
    {
        $model = KeyValue::findOne([
            'namespace' => $namespace,
            'group' => $group,
            'key' => $key,
        ]);

        if ($model) {
            $model->cleanCache();
        }
    }

    public function actionCleanFlush()
    {
        Yii::$app->cache->flush();
    }

    /**
     * @param $tenantId
     * @param $entryId
     * @return false|string
     * @throws UserException
     */
    public function actionGetData($tenantId, $entryId)
    {
        $oaApprovalData = OaService::getOaApprovalData($tenantId, $entryId);
        echo json_encode($oaApprovalData);
    }

    public function actionUpdateFlowKey()
    {
        $orderList = OaApprovalOrder::find()->where([
            'oa_flow_key' => ''
        ]);
        /** @var OaApprovalOrder $order */
        foreach ($orderList->all() as $order) {
            $oaApprovalData = OaService::getOaApprovalData($order->oa_tenant_id, $order->oa_flow_id);
            $oaData = $oaApprovalData['data'][0] ?? [];
            if ($oaData) {
                $order->oa_flow_key = $oaData['flow_key'] ?? '';
                $order->oa_response = json_encode($oaData);
                $order->save();
            }
        }
    }
}
