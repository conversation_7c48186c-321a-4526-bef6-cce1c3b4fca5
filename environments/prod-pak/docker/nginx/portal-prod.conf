server {
    charset utf-8;
    client_max_body_size 128M;

    listen 80;
    server_name biz.rapidrupiah.com;

    root        /data/www/wwwroot/portal/backend/web;
    index       index.php;

    access_log  /data/logs/nginx/access.log;
    error_log   /data/logs/nginx/error.log debug;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_param HTTPS	on;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_pass 127.0.0.1:9000;

        try_files $uri =404;
    }

    location ~* \.(js|css|less|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg|tpl)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        access_log off;
    }

    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location = /robots.txt {
        log_not_found off;
        access_log off;
    }

    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

}