<?php

namespace backend\behaviors;

use Carbon\Carbon;
use Closure;
use Doctrine\SqlFormatter\NullHighlighter;
use Doctrine\SqlFormatter\SqlFormatter;
use Exception;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use system\components\ExpressionComponent;
use Yii;
use yii\base\InvalidConfigException;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\StringHelper;

class FormatterBehavior extends \xlerr\common\behaviors\FormatterBehavior
{
    public function asNJson($jsonRaw, int $length = 30, string $substring = '...', string $encoding = null)
    {
        $json = json_decode((string)$jsonRaw);
        if (!$json) {
            return $jsonRaw;
        }
        $content = json_encode($json, JSON_UNESCAPED_UNICODE);
        $content = StringHelper::truncate($content, $length, $substring, $encoding);

        return Html::tag('span', $content, [
            'title' => json_encode($json, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES),
        ]);
    }

    /**
     * @param string|Closure|array $key
     * @param string $default
     *
     * @return mixed
     * @throws KVException
     * @example $formatter->format([3,30], ['periodConvert'])
     */
    public function asPeriodCategory($key, $default = '-')
    {
        return ArrayHelper::getValue(KeyValue::take('period_category'), $key, $default);
    }

    /**
     * @param mixed $date
     * @param string $default
     *
     * @return string
     */
    public function asVDate($date, $default = '-')
    {
        $date = Carbon::parse($date);
        if ($date->year < 1980) {
            return $default;
        }

        return $date->toDateString();
    }

    /**
     * @param mixed $date
     * @param string $default
     *
     * @return string
     */
    public function asVTime($date, $default = '-')
    {
        $date = Carbon::parse($date);
        if ($date->year < 1980) {
            return $default;
        }

        return $date->toTimeString();
    }

    /**
     * @param mixed $date
     * @param string $default
     *
     * @return string
     */
    public function asVDateTime($date, $default = '-')
    {
        $date = Carbon::parse($date);
        if ($date->year < 1980) {
            return $default;
        }

        return $date->toDateTimeString();
    }

    /**
     * @param float|int $amount
     * @param bool $format
     *
     * @return string
     * @throws Exception
     */
    public function asFormatAmount($amount, $format = true): string
    {
        $precision = self::currencyPrecision();

        $floatAmount = bcmul($amount, self::currencyMultiplier(), $precision);

        return $format ? number_format(
            $floatAmount,
            $precision,
            self::currencyDecPoint(),
            self::currencySepPoint()
        ) : $floatAmount;
    }

    /**
     * @param float|int|string $amount
     *
     * @return int
     * @throws Exception
     */
    public function asAmount($amount)
    {
        $amount = (float)str_replace(',', '', (string)$amount);

        return (int)bcdiv($amount, self::currencyMultiplier());
    }

    /**
     * @param float|int $amount
     * @param int|null $precision
     *
     * @return string
     * @throws Exception
     */
    public function asFormatAmountWithPrecision($amount, $precision = null)
    {
        $precision = intval($precision ?? self::currencyPrecision());
        $multiplier = self::currencyMultiplier();
        $decPoint = self::currencyDecPoint();
        $sepPoint = self::currencySepPoint();

        $floatAmount = bcmul($amount, $multiplier, $precision);

        return number_format($floatAmount, $precision, $decPoint, $sepPoint);
    }

    /**
     * 金额乘数
     *
     * @return float
     * @throws Exception
     */
    public static function currencyMultiplier()
    {
        static $multiplier;
        if (!isset($multiplier)) {
            $multiplier = (float)ArrayHelper::getValue(Yii::$app->params, 'formatter.currency.multiplier', 1);
        }

        return $multiplier;
    }

    /**
     * 小数位数
     *
     * @return int
     * @throws Exception
     */
    public static function currencyPrecision()
    {
        static $precision;
        if (!isset($precision)) {
            $precision = (int)ArrayHelper::getValue(Yii::$app->params, 'formatter.currency.precision', 0);
        }

        return $precision;
    }

    /**
     * 小数分隔符
     *
     * @return string
     * @throws Exception
     */
    public static function currencyDecPoint()
    {
        static $decPoint;
        if (!isset($decPoint)) {
            $decPoint = (string)ArrayHelper::getValue(Yii::$app->params, 'formatter.currency.decPoint', '.');
        }

        return $decPoint;
    }

    /**
     * 千位分隔符
     *
     * @return string
     * @throws Exception
     */
    public static function currencySepPoint()
    {
        static $sepPoint;
        if (!isset($sepPoint)) {
            $sepPoint = (string)ArrayHelper::getValue(Yii::$app->params, 'formatter.currency.sepPoint', ',');
        }

        return $sepPoint;
    }

    /**
     * 金额单位
     *
     * @return string
     * @throws Exception
     */
    public static function currencyUnit()
    {
        static $unit;
        if (!isset($unit)) {
            $unit = (string)ArrayHelper::getValue(Yii::$app->params, 'formatter.currency.unit', '分');
        }

        return $unit;
    }

    /**
     * 货币编号
     *
     * @return string
     * @throws Exception
     */
    public static function currencyCode()
    {
        static $code;
        if (!isset($code)) {
            $code = (string)ArrayHelper::getValue(Yii::$app->params, 'formatter.currency.code', 'INR');
        }

        return $code;
    }

    /**
     * @param mixed $value
     * @param string $exp
     * @param array $data
     *
     * @return mixed
     * @throws InvalidConfigException
     */
    public function asExpression($value, string $exp, array $data = [])
    {
        if (!isset($data['self'])) {
            $data['self'] = $value;
        }

        return ExpressionComponent::instance()->evaluate($exp, $data);
    }

    public function asTruncate($string, $length = 30, ...$params)
    {
        $content = StringHelper::truncate($string, $length, ...$params);

        return Html::tag('span', $content, [
            'title' => $string,
        ]);
    }


    /**
     * @param string $sql
     * @param string $class
     * @param array $constructorArgs
     * @param string $action
     * @return string
     */
    public function asSqlFormatter(string $sql, string $class = NullHighlighter::class, array $constructorArgs = [], string $action = 'format'): string
    {
        return (new SqlFormatter(new $class($constructorArgs)))->$action($sql);
    }
}
