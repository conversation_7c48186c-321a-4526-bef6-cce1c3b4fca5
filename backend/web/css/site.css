html,
body {
    height: 100%;
}

.wrap {
    min-height: 100%;
    height: auto;
    margin: 0 auto -60px;
    padding: 0 0 60px;
}

.wrap > .container {
    padding: 70px 15px 20px;
}

.footer {
    height: 60px;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
    padding-top: 20px;
}

.jumbotron {
    text-align: center;
    background-color: transparent;
}

.jumbotron .btn {
    font-size: 21px;
    padding: 14px 24px;
}

.not-set {
    color: #c55;
    font-style: italic;
}

/* add sorting icons to gridview sort links */
a.asc:after, a.desc:after {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    padding-left: 5px;
}

a.asc:after {
    content: /*"\e113"*/ "\e151";
}

a.desc:after {
    content: /*"\e114"*/ "\e152";
}

.sort-numerical a.asc:after {
    content: "\e153";
}

.sort-numerical a.desc:after {
    content: "\e154";
}

.sort-ordinal a.asc:after {
    content: "\e155";
}

.sort-ordinal a.desc:after {
    content: "\e156";
}

.grid-view th,
.grid-view td:last-child {
    white-space: nowrap;
}

.grid-view .filters input,
.grid-view .filters select {
    min-width: 50px;
}

.hint-block {
    display: block;
    margin-top: 5px;
    color: #999;
}

.error-summary {
    color: #a94442;
    background: #fdf7f7;
    border-left: 3px solid #eed3d7;
    padding: 10px 20px;
    margin: 0 0 15px 0;
}

/* align the logout "link" (button in form) of the navbar */
.nav li > form > button.logout {
    padding: 15px;
    border: none;
}

@media (max-width: 767px) {
    .nav li > form > button.logout {
        display: block;
        text-align: left;
        width: 100%;
        padding: 10px 15px;
    }
}

.nav > li > form > button.logout:focus,
.nav > li > form > button.logout:hover {
    text-decoration: none;
}

.nav > li > form > button.logout:focus {
    outline: none;
}

/* 滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(50, 50, 50, 0.25);
    /*border: 2px solid transparent;*/
    /*border-radius: 10px;*/
    /*background-clip: padding-box;*/
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(50, 50, 50, 0.5);
}

::-webkit-scrollbar-track {
    background-color: rgba(50, 50, 50, 0.05);
}

/* iframe loading */
.body.loading {
    position: fixed;
    left: 0;
    top: 0;
    padding: 30vh 0 0;
    margin: 0;
    border: 0;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 9999999999999;
    text-align: center;
    font-size: 68px;
}

.three-bounce {
    text-align: center;
    display: -moz-box;
    -moz-box-pack: center;
    -moz-box-align: center;
    display: -webkit-box;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    -o-box-pack: center;
    -o-box-align: center;
}

.three-bounce > div {
    margin: 15px;
    min-height: 30px;
    min-width: 30px;
    background-color: #3c8dbc;
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
    -moz-animation: bouncedelay 1.4s infinite ease-in-out;
    -o-animation: bouncedelay 1.4s infinite ease-in-out;
    animation: bouncedelay 1.4s infinite ease-in-out;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
}

.three-bounce .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.three-bounce .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0.0)
    }
    40% {
        -webkit-transform: scale(1.0)
    }
}

@-moz-keyframes bouncedelay {
    0%, 80%, 100% {
        -moz-transform: scale(0.0)
    }
    40% {
        -moz-transform: scale(1.0)
    }
}

@-o-keyframes bouncedelay {
    0%, 80%, 100% {
        -o-transform: scale(0.0)
    }
    40% {
        -o-transform: scale(1.0)
    }
}

@keyframes bouncedelay {
    0%, 80%, 100% {
        transform: scale(0.0);
    }
    40% {
        transform: scale(1.0);
    }
}

/* ===============================*/
.kv-drp-dropdown .left-ind, .kv-drp-dropdown .right-ind {
    top: 0.7rem !important;
}

.kv-drp-dropdown .range-value {
    padding-left: 3rem !important;
}

.kv-drp-dropdown .kv-clear {
    right: 2.3rem !important;
    line-height: 1.8rem !important;
}
input.range-value {
    min-width: 230px;
}

/* ============================= */
.table th, .table td, .select2-results__option {
    word-break: keep-all;
    white-space: nowrap;
}

.box.search .form-group, .box.search .form-control, .box.search .btn {
    margin-bottom: 10px;
    margin-right: 5px;
}

.box.search .form-group .form-control {
    margin-bottom: 0;
    margin-right: 0;
}

.box.search .box-body {
    padding-bottom: 0 !important;
}

.box.search .box-tools .btn {
    margin-bottom: 0 !important;
    margin-right: 0 !important;
}

/* iframe */
.fixed .content-wrapper, .fixed .right-side {
    padding-top: 0 !important;
}

.nav-tabs-iframe > .nav-tabs {
    margin-top: 55px !important;
}

.nav-tabs-iframe > .tab-content > .tab-pane {
    padding-bottom: 0 !important;
}


/* vue 动画 */
.v-enter-active,
.v-leave-active {
    transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
}