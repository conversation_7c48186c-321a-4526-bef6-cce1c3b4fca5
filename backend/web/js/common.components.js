(function (e, a) {
    typeof exports == "object" && typeof module < "u" ? module.exports = a(require("vue"), require("jquery"), require("select2/dist/css/select2.min.css"), require("select2"), require("inputmask")) : typeof define == "function" && define.amd ? define(["vue", "jquery", "select2/dist/css/select2.min.css", "select2", "inputmask"], a) : (e = typeof globalThis < "u" ? globalThis : e || self, e.vueCommonComponents = a(e.Vue, e.$, null, e.select2init, e.Inputmask))
})(this, function (e, a, q, d, h) {
    "use strict";
    const v = e.defineComponent({
        __name: "Select2",
        props: e.mergeModels({options: {default: () => ({})}}, {modelValue: {required: !0}, modelModifiers: {}}),
        emits: ["update:modelValue"],
        setup(o) {
            typeof d == "function" && d();
            const r = e.useModel(o, "modelValue"),
                l = e.computed(() => ({placeholder: "", minimumResultsForSearch: 6, ...o.options})),
                n = e.useTemplateRef("selectEl"), t = e.computed(() => {
                    if (n.value) return a(n.value)
                }), i = () => {
                    t.value && (t.value.empty(), t.value.select2(l.value), t.value.on("select2:select select2:unselect", u => {
                        r.value = u.target.value
                    }))
                };
            return e.watch(l, () => {
                i()
            }, {deep: !0}), e.watch(r, u => {
                t.value?.val(u), t.value?.trigger("change")
            }, {immediate: !0}), e.onMounted(() => {
                i()
            }), e.onBeforeUnmount(() => {
                t.value?.select2("destroy")
            }), (u, S) => (e.openBlock(), e.createElementBlock("select", {
                ref_key: "selectEl",
                ref: n,
                style: {width: "100%"}
            }, null, 512))
        }
    });
    typeof WorkerGlobalScope < "u" && globalThis instanceof WorkerGlobalScope;
    const f = () => {
    };

    function g(o, r) {
        function l(...n) {
            return new Promise((t, i) => {
                Promise.resolve(o(() => r.apply(this, n), {fn: r, thisArg: this, args: n})).then(t).catch(i)
            })
        }

        return l
    }

    function x(o, r = {}) {
        let l, n, t = f;
        const i = s => {
            clearTimeout(s), t(), t = f
        };
        let u;
        return s => {
            const p = e.toValue(o), c = e.toValue(r.maxWait);
            return l && i(l), p <= 0 || c !== void 0 && c <= 0 ? (n && (i(n), n = void 0), Promise.resolve(s())) : new Promise((m, T) => {
                t = r.rejectOnCancel ? T : m, u = s, c && !n && (n = setTimeout(() => {
                    l && i(l), n = void 0, m(u())
                }, c)), l = setTimeout(() => {
                    n && i(n), n = void 0, m(s())
                }, p)
            })
        }
    }

    function k(o, r = 200, l = {}) {
        return g(x(r, l), o)
    }

    const y = ["value"], M = e.defineComponent({
        __name: "MoneyInput",
        props: e.mergeModels({min: {}, max: {}}, {modelValue: {required: !0}, modelModifiers: {}}),
        emits: ["update:modelValue"],
        setup(o) {
            const r = e.useModel(o, "modelValue"), l = e.useTemplateRef("el"), n = k(t => {
                r.value = t.target.value * 1
            }, 300);
            return e.onMounted(() => {
                l.value && (new h("decimal", {
                    digits: 2,
                    groupSeparator: ",",
                    autoUnmask: !0,
                    rightAlign: !1,
                    enforceDigitsOnBlur: !0,
                    removeMaskOnSubmit: !0,
                    onBeforePaste: t => t.replace(/,/g, ""),
                    onBeforeMask: function (t) {
                        const i = Number(t);
                        return (isNaN(i) ? 0 : i / 100).toFixed(2)
                    },
                    onUnMask: function (t) {
                        return (Number(t.replace(/,/g, "")) * 100).toFixed(0)
                    }
                }).mask(l.value), l.value.addEventListener("input", n), l.value.addEventListener("change", n), l.value.addEventListener("input", t => {
                    const i = t.target, u = Number(i.value);
                    o.min !== void 0 && u < o.min && (i.value = o.min.toString()), o.max !== void 0 && u > o.max && (i.value = o.max.toString())
                }))
            }), (t, i) => (e.openBlock(), e.createElementBlock("input", {
                ref: "el",
                value: r.value,
                type: "text"
            }, null, 8, y))
        }
    }), V = /\d{1,3}(?=(\d{3})+$)/g;
    return {
        components: {Select2: v, MoneyInput: M},
        amountHuman: o => Number(o).toFixed(2).replace(/^(\d+)((\.\d+)?)$/, function (r, l, n) {
            return l.replace(V, "$&,") + n
        })
    }
});
