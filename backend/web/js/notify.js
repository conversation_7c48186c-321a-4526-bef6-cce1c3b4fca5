//首页通知
$(function () {
    const open = $('#openMsgMenu')
    if (open.length) {
        let msgBoxEl = open.find('.msg-box')
        if (!msgBoxEl.length) {
            msgBoxEl = $('<div>');
            msgBoxEl.addClass('msg-box')
            msgBoxEl.css({
                width: '550px',
                maxHeight: '80vh',
                overflow: 'auto'
            })
            msgBoxEl.appendTo(open)
        }

        const label = $('<b class="label label-warning" style="display: none"></b>'),
            url = '/site/notify',
            run = async () => {
                const response = await fetch(url, {cache: 'no-cache'}),
                    data = await response.json(),
                    count = data?.count ?? 0;

                if (count > 0) {
                    label.show(0).text(count)
                    let msgList = [
                        '<div class="title" style="position: sticky; top: 0">消息通知</div>'
                    ];
                    data.list.forEach((msgInfo, i) => {
                        msgList.push(`<a href="${msgInfo.link}" class="layer-dialog">${msgInfo.title}</a>`)
                    })
                    msgList.push(`<a href="${data.link}" class="all" style="position: sticky; bottom: 0; background: #fff">查看所有消息</a>`)
                    msgBoxEl.addClass('active').html(msgList.join(' '))
                } else {
                    label.hide()
                    msgBoxEl.removeClass('active')
                }

                window.setTimeout(run, 60 * 1000)
            };

        msgBoxEl.on('click', 'a', function (e) {
            const self = $(e.currentTarget)
            if (!self.hasClass('all')) {
                e.currentTarget.remove()
                const count = parseInt(label.text()) - 1
                label.text(count)
                if (count <= 0) {
                    label.hide(0)
                    msgBoxEl.removeClass('active')
                }
            } else {
                e.preventDefault()
                window.makeTab(self.attr('href'))
            }
        })

        open.append(label);

        run()

        // $.get(url, {}, (res) => {
        //     if (res.count > 0) {
        //         $('#msg-count').text(res.count)
        //         $('#msg-count-text').text('您有' + res.count + '消息')
        //     } else {
        //         $('#msg-count').text('')
        //         $('#msg-count-text').text('暂无消息')
        //     }
        //     let listEl = $('#msg-list'),
        //         allLinkEl = $('#show-all-link')
        //     if (res.list.length > 0) {
        //         $(res.list).each(function (index, element) {
        //             if (index + 1 <= 5) {
        //                 let html = $(`<li>
        //                                 <a href="${element.link}" target="_blank">
        //                                     <h5>${element.title}</h5>
        //                                     <p style="margin: 0 0 0 0">${element.content}</p>
        //                                 </a>
        //                             </li>`)
        //                 listEl.append(html)
        //             }
        //         })
        //         allLinkEl.parent().css("display", "block")
        //         allLinkEl.attr('href', res.link)
        //         allLinkEl.text('查看全部消息')
        //     }
        // });
    }
});