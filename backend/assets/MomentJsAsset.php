<?php

namespace backend\assets;

use xlerr\common\assets\MomentAsset;
use Yii;
use yii\web\AssetBundle;
use yii\web\View;

/**
 * Main backend application asset bundle.
 */
class MomentJsAsset extends AssetBundle
{
    public $sourcePath = '@npm/moment-timezone/builds';
    public $js = [
        'moment-timezone-with-data.min.js',
    ];

    public $depends = [
        MomentAsset::class,
    ];

    /**
     * @param View $view
     *
     * @return static
     */
    public static function register($view)
    {
        $view->registerJs(sprintf('moment.tz.setDefault(\'%s\');', Yii::$app->timeZone));

        return parent::register($view);
    }
}
