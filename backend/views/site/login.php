<?php

use common\models\LoginForm;
use xlerr\common\assets\LayerAsset;
use yii\bootstrap\ActiveForm;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $form ActiveForm */
/* @var $model LoginForm */

LayerAsset::register($this);
$error = Yii::$app->getSession()->getFlash('error');

if ($error) {
    $this->registerJs(sprintf('layer.msg(\'%s\')', $error));
}

$this->title = Yii::t('common', 'Sign In');

?>
<script>
    if (window.self !== window.top) {
        window.top.location.href = window.self.location.href;
    }
</script>
<div class="hold-transition lockscreen">
    <div class="lockscreen-wrapper" style="margin-top: 30vh">
        <div class="lockscreen-logo">
            <a href="/"><b>资管系统</b><?= SIDE_LOGO ?></a>
        </div>

        <div class="text-center">
            <?= Html::a(Yii::t('common', 'kuainiu login'), [
                '/kuainiu/user/auth',
                'authclient' => 'kuainiu',
            ], [
                'class' => 'btn btn-lg btn-social btn-flat btn-vk',
                'tabindex' => 1,
            ]) ?>
        </div>
    </div>
</div>
