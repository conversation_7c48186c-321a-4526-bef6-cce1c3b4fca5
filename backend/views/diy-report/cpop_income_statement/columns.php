<?php

use Xlerr\Metric\Models\BizMetricInfo;
use dashboard\grid\MoneyTotalDataColumn;
use DiyReport\services\DiyReportService;
use xlerr\common\widgets\GridView;
use yii\data\DataProviderInterface;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var DiyReportService $model
 */

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '资金方',
            'attribute' => 'channel',
            'format' => ['in', capitalChannelList()],
        ],
        [
            "label" => "会计科目",
            "attribute" => "acctTitle",
            "format" => function ($acctTitle) {
                return BizMetricInfo::find()->where([
                    'domain' => 'cp_op_acct_title',
                    'name' => explode(',', $acctTitle)
                ])->select('group_concat(text)')->scalar();
            },
        ],
        [
            "label" => "业务日期",
            "attribute" => "date",
        ],
        [
            "label" => "计算分组",
            "attribute" => "calcGroup",
        ],
        [
            "label" => "计算规则Id",
            'attribute' => 'calcRuleId',
        ],
        [
            "label" => "原始业务值(金额/笔数)",
            "attribute" => "ogAmount",
            'class' => MoneyTotalDataColumn::class
        ],
        [
            "label" => "计算值(比例等)",
            "attribute" => "formula_factor",
        ],
        [
            "label" => "费用值",
            "attribute" => "incAmount",
            'class' => MoneyTotalDataColumn::class
        ],
        [
            "label" => "计算日期",
            "attribute" => "calcDate",
        ],
        [
            "label" => "业务指标编码",
            "attribute" => "metricName",
        ],
        [
            "label" => "业务指标类别",
            "attribute" => "metricCategory",
        ],
    ],
]);
