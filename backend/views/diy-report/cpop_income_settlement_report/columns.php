<?php

use dashboard\grid\MoneyTotalDataColumn;
use DiyReport\services\DiyReportService;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use Xlerr\Metric\Models\BizMetricInfo;
use Xlerr\SettlementFlow\Models\Rule;
use yii\data\DataProviderInterface;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var DiyReportService $model
 */

$feeTypeList = Rule::feeTypeList();

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '资金方',
            'attribute' => 'channel',
            'format' => ['in', capitalChannelList()],
        ],
        [
            "label" => "业务日期",
            "attribute" => "bizDate",
        ],
        [
            "label" => "结算规则",
            'attribute' => 'feeType',
            'format' => 'raw',
            'value' => function ($model) use ($feeTypeList) {
                $text = vsprintf('%s %s (%s)', [
                    $model['msRuleId'],
                    $feeTypeList[$model['feeType']] ?? $model['feeType'],
                    Rule::INC_TYPE_LIST[$model['revenueType']] ?? $model['revenueType'],
                ]);

                return Html::a($text, [
                    '/cpop-settlement/rule/index',
                    'channel' => $model['channel'],
                    'fee_type' => $model['feeType'],
                    'inc_type' => $model['revenueType'],
                ], [
                    'target' => '_blank',
                ]);
            },
        ],
        [
            'label' => '会计科目',
            'attribute' => 'acctTitle',
            "format" => function ($acctTitle) {
                return BizMetricInfo::find()->where([
                    'domain' => 'cp_op_acct_title',
                    'name' => explode(',', $acctTitle),
                ])->select('group_concat(text)')->scalar();
            },
        ],
        [
            "label" => "当日应结总额",
            "attribute" => "incAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            "label" => "当日实结总额",
            "attribute" => "realPayAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            "label" => "当日调账总额",
            "attribute" => "adjustAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            "label" => "累计应结总额(含调账)",
            "attribute" => "cumIncAmount",
            'class' => MoneyDataColumn::class,
        ],
        [
            "label" => "累计实结总额",
            "attribute" => "cumRealPayAmount",
            'class' => MoneyDataColumn::class,
        ],
        [
            "label" => "账户余额(累计实结-累计应结)",
            "attribute" => "balanceAmount",
            'class' => MoneyDataColumn::class,
        ],
    ],
]);
