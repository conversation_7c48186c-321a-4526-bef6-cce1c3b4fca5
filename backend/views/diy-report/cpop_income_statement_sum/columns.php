<?php

use Xlerr\Metric\Models\BizMetricInfo;
use dashboard\grid\MoneyTotalDataColumn;
use DiyReport\services\DiyReportService;
use xlerr\common\widgets\GridView;
use Xlerr\SettlementFlow\Models\Rule;
use yii\data\DataProviderInterface;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var DiyReportService $model
 */

$feeTypeList = Rule::feeTypeList();

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'columns' => [
        [
            'label' => '资金方',
            'attribute' => 'channel',
            'format' => ['in', capitalChannelList()],
        ],
        [
            "label" => "会计科目",
            "attribute" => "acctTitle",
            "format" => function ($acctTitle) {
                return BizMetricInfo::find()->where([
                    'domain' => 'cp_op_acct_title',
                    'name' => explode(',', $acctTitle)
                ])->select('group_concat(text)')->scalar();
            },
        ],
        [
            "label" => "业务日期",
            "attribute" => "bizDate",
        ],
        [
            "label" => "计算分组",
            "attribute" => "calcGroup",
        ],
        [
            "label" => "费用值",
            "attribute" => "incAmount",
            'class' => MoneyTotalDataColumn::class
        ],
        [
            "label" => "结算规则",
            'attribute' => 'feeType',
            'format' => 'raw',
            'value' => function ($model) use ($feeTypeList) {
                $text = vsprintf('%s %s (%s)', [
                    $model['msRuleId'],
                    $feeTypeList[$model['feeType']] ?? $model['feeType'],
                    Rule::INC_TYPE_LIST[$model['revenueType']] ?? $model['revenueType'],
                ]);

                return Html::a($text, [
                    '/cpop-settlement/rule/index',
                    'channel' => $model['channel'],
                    'fee_type' => $model['feeType'],
                    'inc_type' => $model['revenueType']
                ], [
                    'target' => '_blank'
                ]);
            },
        ],
    ],
]);
