<?php

use Carbon\Carbon;
use dashboard\grid\MoneyTotalDataColumn;
use DiyReport\services\DiyReportService;
use xlerr\common\grid\MoneyDataColumn;
use xlerr\common\widgets\GridView;
use Xlerr\SettlementFlow\Models\Rule;
use yii\data\DataProviderInterface;
use yii\helpers\Html;
use yii\web\View;

/**
 * @var View $this
 * @var DataProviderInterface $dataProvider
 * @var DiyReportService $model
 */

$feeTypeList = Rule::feeTypeList();

$today = Carbon::today();

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'rowOptions' => static function ($model) use ($today) {
        if ($today->eq($model['rdate'])) {
            return [
                'style' => 'background-color: #d9edf7;',
            ];
        }

        return [];
    },
    'columns' => [
        [
            'label' => '资金方',
            'attribute' => 'channel',
            'footer' => '合计',
        ],
        [
            "label" => "业务日期",
            "attribute" => "rdate",
            "footer" => null,
        ],
        [
            "label" => "结算规则",
            'attribute' => 'feeType',
            'format' => 'raw',
            'value' => static function ($model) use ($feeTypeList) {
                $text = vsprintf('%s %s (%s)', [
                    $model['ruleId'],
                    $feeTypeList[$model['feeType']] ?? $model['feeType'],
                    Rule::INC_TYPE_LIST[$model['revenueType']] ?? $model['revenueType'],
                ]);

                return Html::a($text, [
                    '/cpop-settlement/rule/index',
                    'channel' => $model['channel'],
                    'fee_type' => $model['feeType'],
                    'inc_type' => $model['revenueType'],
                ], [
                    'target' => '_blank',
                ]);
            },
        ],
        [
            "label" => "当日应还总额",
            "attribute" => "shouldRepayAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            "label" => "当日应偿付总额",
            "attribute" => "finishAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            "label" => "当日实偿付金额",
            "attribute" => "realAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
        [
            "label" => "累计应偿付总额",
            "attribute" => "cumulativeFinishAmount",
            'class' => MoneyDataColumn::class,
            "footer" => null,
        ],
        [
            "label" => "累计实偿付总额",
            "attribute" => "cumulativeRealAmount",
            'class' => MoneyDataColumn::class,
            "footer" => null,
        ],
        [
            "label" => "对账差额",
            "attribute" => "checkingBalanceAmount",
            'class' => MoneyDataColumn::class,
        ],
        [
            "label" => "未结差额(累计实偿付-累计应偿付)",
            "attribute" => "balanceAmount",
            'class' => MoneyTotalDataColumn::class,
        ],
    ],
]);
