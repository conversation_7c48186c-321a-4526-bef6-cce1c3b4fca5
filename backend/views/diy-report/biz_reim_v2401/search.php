<?php

use DiyReport\services\DiyReportService;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use Xlerr\SettlementFlow\Models\Rule;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\View;

/**
 * @var View $this
 * @var ActiveForm $form
 * @var DiyReportService $model
 */

$config = Rule::find()
    ->where([
        'statement_type' => Rule::STATEMENT_TYPE_REIM,
    ])
    ->select('group_concat(channel)')
    ->groupBy('fee_type')
    ->indexBy('fee_type')
    ->column();

$channels = ArrayHelper::filter(capitalChannelList(), explode(',', implode(',', $config)));

echo $form->field($model, 'channel', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => $channels,
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '资金方',
    ]
]);

echo $form->field($model, 'feeType', [
    'options' => [
        'style' => 'min-width: 182px',
    ]
])->widget(Select2::class, [
    'data' => [],
    'pluginOptions' => [
        'allowClear' => true,
    ],
    'options' => [
        'prompt' => '费用类型',
    ]
]);

echo $form->field($model, 'startDate')->widget(DatePicker::class, [
    'options' => [
        'placeholder' => '开始时间',
    ]
]);

echo $form->field($model, 'endDate')->widget(DatePicker::class, [
    'options' => [
        'placeholder' => '结束时间',
    ]
]);
?>

<script>
    <?php $this->beginBlock('changeEvent') ?>
    const feeTypes = <?= Json::htmlEncode($config)?>,
        allFeeTypes = <?= Json::htmlEncode(Rule::feeTypeList())?>,
        channelEl = $('#<?= Html::getInputId($model, 'channel')?>'),
        feeTypeEl = $('#<?= Html::getInputId($model, 'feeType')?>')
    channelEl.on('change', function () {
        const curChannel = channelEl.val()
        feeTypeEl.empty()
        Object.entries(feeTypes).forEach(([feeType, channel]) => {
            if (curChannel === '' || channel.split(',').includes(curChannel)) {
                feeTypeEl.append(new Option(allFeeTypes[feeType] ?? feeType, feeType))
            }
        })
        feeTypeEl.val('<?= $model->feeType ?>').change()
    }).change()
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['changeEvent']) ?>
</script>
