<?php

use common\models\User as UserModel;
use common\web\User;
use datasource\api\DataSourceSyncController;
use finance\api\WithdrawCallbackController;
use kuainiu\Module;
use mdm\admin\components\AccessControl;
use mdm\admin\components\DbManager;
use mdm\admin\controllers\AssignmentController;
use system\helpers\OperateLogHelper;
use waterank\audit\controllers\ApiController;
use yii\helpers\ArrayHelper;
use yii\i18n\PhpMessageSource;
use yii\log\FileTarget;
use yii\web\CacheSession;

$params = ArrayHelper::merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'portal',
    'name' => 'portal',
    'basePath' => dirname(__DIR__),
    'language' => 'zh-CN',
    'controllerNamespace' => 'backend\controllers',
    'layout' => '@vendor/xlerr/yii2-adminlte/src/views/layouts/main.php',
    'bootstrap' => [
        'log',
        'key-value',
        'diy-report',
    ],
    'modules' => [
        'task' => \xlerr\task\Module::class,
        'audit' => \waterank\audit\Module::class,
        'statement' => \Codingheping\StatementComponent\Module::class,
        'diy-report' => \DiyReport\Module::class,
        'event' => \EventManager\Module::class,
        'admin' => [
            'class' => \mdm\admin\Module::class,
            'viewPath' => '@backend/views/admin',
            'controllerMap' => [
                'assignment' => [
                    'class' => AssignmentController::class,
                    'extraColumns' => [
                        'email',
                        [
                            'attribute' => 'status',
                            'format' => ['in', UserModel::statusList()],
                        ],
                    ],
                ],
            ],
        ],
        'system' => \system\Module::class,
        'finance' => \finance\Module::class,
        'api' => [
            'class' => \api\Module::class,
            'controllerMap' => [
                'audit' => ApiController::class,
                'data-source-sync' => DataSourceSyncController::class,
                'withdraw-callback' => WithdrawCallbackController::class,
            ],
        ],
        'grant' => \grant\Module::class,
        'payment' => \payment\Module::class,
        'common-payment' => \CommonPayment\Module::class,
        'pcrawler' => \pcrawler\Module::class,
        'repay' => \repay\Module::class,
        'cmdb' => \cmdb\Module::class,
        'dashboard' => \dashboard\Module::class,
        'crm' => \crm\Module::class,
        'key-value' => \kvmanager\Module::class,
        'kuainiu' => [
            'class' => Module::class,
            'vpEmails' => [],
        ],
        'gridview' => \kartik\grid\Module::class,
    ],
    'as access' => [
        'class' => AccessControl::class,
        'allowActions' => [
            'site/*',
            'api/*',
            'crm/*',
            'kuainiu/*',
        ],
    ],
    'components' => [
        'authManager' => [
            'class' => DbManager::class,
            'cache' => 'cache',
        ],
        'assetManager' => [
            //            'appendTimestamp' => true,
            'linkAssets' => true,
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => PhpMessageSource::class,
                    'forceTranslation' => true,
                ],
            ],
        ],
        'request' => [
            'csrfParam' => '_csrf',
        ],
        'user' => [
            'class' => User::class,
            'identityClass' => UserModel::class,
            'enableAutoLogin' => false,
            'identityCookie' => ['name' => '_ib', 'httpOnly' => true],
            // 登录日志
            'on afterLogin' => [OperateLogHelper::class, 'writeLoginLog'],
        ],
        'session' => [
            'class' => CacheSession::class,
            // this is the name of the session cookie used for login on the backend
            'name' => 'ab',
            'timeout' => 8 * 60 * 60, // seconds
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => FileTarget::class,
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                'login' => '/site/login',
                'logout' => '/site/logout',
                'home' => '/site/home',
            ],
        ],
    ],
    'params' => $params,
];
