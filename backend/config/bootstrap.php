<?php

use common\models\User as UserModel;
use DiyReport\controllers\ReportController;
use finance\models\OaApprovalOrder;
use finance\models\PaymentWithdrawalPendingTasks;
use finance\worker\OaApproval;
use mdm\admin\components\UserStatus;
use system\components\ExpressionComponent;
use system\components\StatsExpressionFunctionProvider;
use system\helpers\OperateLogHelper;
use waterank\audit\controllers\ApiController;
use waterank\audit\models\Audit;
use yii\base\ActionEvent;
use yii\base\Controller;
use yii\base\Event;
use yii\db\BaseActiveRecord;
use yii\web\Response;
use yii\web\User;
use yii\web\UserEvent;

// 使用企业微信首次登陆时，生成授权KEY
Event::on(UserModel::class, UserModel::EVENT_BEFORE_INSERT, function (Event $event) {
    /** @var UserModel $user */
    $user = $event->sender;
    $user->generateAuthKey();
});

// 访问日志
Event::on(Controller::class, Controller::EVENT_AFTER_ACTION, [OperateLogHelper::class, 'writeAccessLog']);

// 操作日志
$handler = [OperateLogHelper::class, 'writeOperateLog'];
Event::on(BaseActiveRecord::class, BaseActiveRecord::EVENT_AFTER_INSERT, $handler);
Event::on(BaseActiveRecord::class, BaseActiveRecord::EVENT_AFTER_UPDATE, $handler);
Event::on(BaseActiveRecord::class, BaseActiveRecord::EVENT_AFTER_DELETE, $handler);

// 登录之前验证用户是否被禁止
Event::on(User::class, User::EVENT_BEFORE_LOGIN, static function (UserEvent $event) {
    /** @var UserModel $user */
    $user = $event->identity;
    if ($user->status !== UserStatus::ACTIVE) {
        Yii::$app->getSession()->setFlash('error', sprintf('用户(%s)已被禁止登录, 请联系管理员', $user->username));
        $event->isValid = false;
    }
});

// 订阅OA审核回调, 匹配指定`flow_key`
Event::on(ApiController::class, Controller::EVENT_BEFORE_ACTION, static function (ActionEvent $event) {
    if ($event->action->id === 'oa-callback') {
        $rawBody = Yii::$app->getRequest()->getRawBody();

        /** @var array{tenant_id: int, event_id: string, event_type: string, event_data: string, event_date: int} $requestData */
        $requestData = (array)json_decode($rawBody, true);
        $tenantId = $requestData['tenant_id'] ?? null;
        $eventData = $requestData['event_data'] ?? [];

        if (is_string($eventData) && $eventData[0] === '{') {
            $eventData = (array)json_decode($eventData, true);
            $eventData = $eventData['entry'] ?? [];
        }

        $flowKey = $eventData['flow_key'] ?? null;

        $matchRules = PaymentWithdrawalPendingTasks::matchRules();

        $key = sprintf('%s:%s', $tenantId, $flowKey);
        if (isset($matchRules[$key])) {
            if (($eventData['status_code'] ?? null) === Audit::OA_AGREE_STATUS) {
                $transaction = OaApprovalOrder::getDb()->beginTransaction();
                try {
                    $oaApprovalOrder = OaApprovalOrder::find()
                        ->where([
                            'oa_tenant_id' => $tenantId,
                            'oa_flow_id'   => $eventData['entry_id'],
                        ])
                        ->one();
                    if (!$oaApprovalOrder) {
                        $oaApprovalOrder = OaApprovalOrder::addRecord(
                            $tenantId,
                            $eventData['entry_id'] ?? 0,
                            $flowKey,
                            $eventData['status_code'],
                            $rawBody
                        );
                        OaApproval::make(
                            ['oaApprovalOrderId' => $oaApprovalOrder->id],
                            ['task_from_system' => 'OA']
                        );
                    }
                    $transaction->commit();
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    throw $e;
                }
            }
            $response = Yii::$app->getResponse();
            $response->format = Response::FORMAT_RAW;
            $response->data = 'success';
            $event->isValid = false;
        }
    }
});

// 登录时设置初始权限
Event::on(User::class, User::EVENT_AFTER_LOGIN, function (UserEvent $event) {
    /** @var UserModel $user */
    $user = $event->identity;
    $authManager = Yii::$app->authManager;
    if (!array_key_exists('general', $authManager->getRolesByUser($user->getId()))) {
        $authManager->assign($authManager->getRole('general'), $user->getId());
    }
});

// 自定义报表注册表达式
Event::on(ReportController::class, ReportController::EVENT_BEFORE_ACTION, static function () {
    ExpressionComponent::instance()->registerProvider(new StatsExpressionFunctionProvider());
});
