<?php

namespace backend\controllers;

use backend\actions\BizSearchLibraryAction;
use common\models\LoginForm;
use datasource\models\AlarmNotify;
use xlerr\adminlte\actions\LocationAction;
use xlerr\adminlte\actions\PageAction;
use xlerr\common\actions\SettingsAction;
use Yii;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\Response;
use yii\web\ErrorAction;

/**
 * Site controller
 */
class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'actions' => ['login', 'error'],
                        'allow' => true,
                    ],
                    [
                        'actions' => [
                            'logout',
                            'index',
                            'home',
                            'setting',
                            'biz-search-library',
                            'notify',
                        ],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'logout' => ['post', 'get'],
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $homeContent = <<<HTML
  <div class="callout callout-danger">
    <p>内部系统禁止对外提供截图</p>
  </div>
HTML;

        return [
            'error' => [
                'class' => ErrorAction::class,
                'layout' => 'main-login',
            ],
            'index' => [
                'class' => LocationAction::class,
            ],
            'home' => [
                'class' => PageAction::class,
                'title' => Yii::t('menu', 'Home'),
                'content' => $homeContent,
            ],
            'setting' => SettingsAction::class,
            'biz-search-library' => BizSearchLibraryAction::class,
        ];
    }

    /**
     * Login action.
     *
     * @return string|Response
     */
    public function actionLogin()
    {
        if (!Yii::$app->getUser()->getIsGuest()) {
            return $this->goHome();
        }

        $model = new LoginForm();
        if ($model->load(Yii::$app->request->post()) && $model->login()) {
            return $this->goBack();
        } else {
            $model->password = '';

            $this->layout = '@vendor/xlerr/yii2-adminlte/src/views/layouts/main-login.php';

            return $this->render('login', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Logout action.
     *
     * @return Response
     */
    public function actionLogout()
    {
        Yii::$app->getUser()->logout();

        return $this->goHome();
    }

    /**
     * 站内通知
     *
     * @return Response
     */
    public function actionNotify(): Response
    {
        /** @var array<int, array{id: int, executorName: string, name: string, memo: string}> $alarmNotifyList */
        $alarmNotifyList = AlarmNotify::find()
            ->alias('an')
            ->innerJoinWith([
                'dataSourceRule dsr',
                'dataSourceRule.dataSource ds',
                'executor exec',
            ], false)
            ->where([
                'an.status' => AlarmNotify::STATUS_UNTREATED,
            ])
            ->select([
                'an.id',
                'executorName' => 'exec.name',
                'ds.name',
                'dsr.memo',
            ])
            ->orderBy(['an.id' => SORT_DESC])
            ->asArray()
            ->all();

        $alarmNotifyList = array_map(static function (array $alarmNotify): array {
            return [
                'title' => vsprintf('【%s】%s:%s', [
                    $alarmNotify['executorName'],
                    $alarmNotify['name'],
                    $alarmNotify['memo'],
                ]),
                'link' => '/dataplatform/alarm-notify/view?id='.$alarmNotify['id'],
            ];
        }, $alarmNotifyList);

        return $this->asJson([
            'count' => count($alarmNotifyList),
            'list' => $alarmNotifyList,
            'link' => '/dataplatform/alarm-notify/index?status=0',
        ]);
    }
}
